{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"lint": "eslint --ext .js,.ts .", "build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "22"}, "main": "lib/index.js", "dependencies": {"@faker-js/faker": "^8.1.0", "@ffmpeg-installer/ffmpeg": "^1.1.0", "@google-cloud/text-to-speech": "^5.3.0", "busboy": "^1.6.0", "ffmpeg-static": "^5.2.0", "firebase-admin": "^11.8.0", "firebase-functions": "^6.4.0", "fluent-ffmpeg": "^2.1.3", "google-auth-library": "^9.15.1", "googleapis": "^149.0.0", "nodemailer": "^6.9.4", "openai": "^4.67.3", "stripe": "^13.7.0", "youtube-dl-exec": "^3.0.10"}, "devDependencies": {"@types/busboy": "^1.5.4", "@types/fluent-ffmpeg": "^2.1.26", "@types/nodemailer": "^6.4.9", "@typescript-eslint/eslint-plugin": "^6.5.0", "@typescript-eslint/parser": "^6.5.0", "eslint": "^8.9.0", "eslint-config-google": "^0.14.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.25.4", "firebase-functions-test": "^3.1.0", "typescript": "^5.2.2"}, "private": true}