import { onCall, HttpsError } from 'firebase-functions/v2/https';
import * as logger from 'firebase-functions/logger';
import * as admin from 'firebase-admin';
import { firestoreStorageBuckets, requireAdmin } from './utils';
import { generatePasswordResetAndSendWelcomeEmail } from './subscriptions/auth';
import * as functions from 'firebase-functions/v1';
import { getStorage } from 'firebase-admin/storage';
import { UserRecord } from 'firebase-admin/lib/auth/user-record';

const bucketLocation = `${firestoreStorageBuckets}/`;

export const createUser = onCall({ cors: true }, async (request) => {
  try {
    const { data } = request;
    const userRecord = await admin.auth().createUser({
      email: data.email,
      password: data.password,
    });

    logger.info('Successfully created new user:', userRecord.uid);
    data.id = userRecord.uid;
    delete data.password;
    admin.firestore().collection('users').doc(data.id).set(data);
    return data;
  } catch (error) {
    throw new HttpsError('unknown', 'Error creating new user');
    logger.error('Error creating new user:', error);
    return error;
  }
});

export const updateUserPass = onCall({ cors: true }, async (request) => {
  const { data, auth } = request;
  // Check for authentication
  if (!auth) {
    throw new HttpsError('unauthenticated', 'Authentication required');
  }

  if (data.id !== auth.uid) {
    requireAdmin(auth);
  }

  try {
    await admin.auth().updateUser(data.id, {
      password: data.password,
    });
    logger.info('Successfully updated user:', data.id);
    return data;
  } catch (error) {
    throw new HttpsError('unknown', 'Error updating user');
    logger.error('Error updating user:', error);
    return error;
  }
});

export const deleteUser = onCall({ cors: true }, async (request) => {
  const { data, auth } = request;
  // Check for authentication
  if (!auth) {
    throw new HttpsError('unauthenticated', 'Authentication required');
  }

  if (data.id !== auth.uid) {
    requireAdmin(auth);
  }
  try {
    await admin.auth().deleteUser(data.id);
    logger.info('Successfully deleted user:', data.id);
    await admin.firestore().collection('users').doc(data.id).delete();
    const querySnapshot = await admin
      .firestore()
      .collection('versions')
      .where('authorId', '==', data.id)
      .get();
    const batch = admin.firestore().batch();

    querySnapshot.forEach(function (doc) {
      batch.delete(doc.ref);
    });

    batch.commit();
    return data;
  } catch (error) {
    throw new HttpsError('unknown', 'Error deleting user');
    logger.error('Error deleting user:', error);
    return error;
  }
});

export const createMetaDoc = functions.auth
  .user()
  .onCreate((user: UserRecord) => {
    const { uid } = user;
    const data = {
      name: user.displayName,
      email: user.email,
      id: uid,
      error: false,
      isPaused: false,
      features: [],
      settings: {},
      assignedAdminId: [],
      messages: [],
      templateId: '043rMnzq5pYLqTdbAYcX',
    };
    logger.info('User created is:', data);
    // merge the user data with stripeId and stripeLink created by stripe
    admin.firestore().doc(`users/${uid}`).set(data, { merge: true });
    admin.firestore().doc(`users/${uid}/meta/counts`).set({
      books: 0,
      allowed_books: 4,
      is_author: true,
    });

    // sends a welcome email with password reset
    generatePasswordResetAndSendWelcomeEmail(uid).then((response) => {
      logger.info(`User welcome email on creation has been sent`, response);
    });
  });

export const deleteUserDoc = functions.auth
  .user()
  .onDelete(async (user: UserRecord) => {
    const querySnapshotBooks = await admin
      .firestore()
      .collection('books')
      .where('authorId', '==', user.uid)
      .get();

    querySnapshotBooks.forEach(async function (doc) {
      await admin.firestore().recursiveDelete(doc.ref);
    });

    const querySnapshotPosts = await admin
      .firestore()
      .collection('posts')
      .where('authorId', '==', user.uid)
      .get();

    querySnapshotPosts.forEach(async function (doc) {
      await admin.firestore().recursiveDelete(doc.ref);
    });

    const ref = admin.firestore().collection('users').doc(user.uid);
    await admin.firestore().recursiveDelete(ref);

    const bucket = getStorage().bucket(bucketLocation);

    const folderPath = `${user.uid}`;
    logger.info(`Deleting folder: ${folderPath}`);
    // List all files in the folder
    const [files] = await bucket.getFiles({ prefix: folderPath });

    if (files.length === 0) {
      logger.info(`No files found in the folder: ${folderPath}`);
      return;
    }

    // Delete each file in the folder
    const deletePromises = files.map((file) => file.delete());
    await Promise.all(deletePromises);

    logger.info(
      `Folder '${folderPath}' and its contents were deleted successfully.`,
    );
  });
