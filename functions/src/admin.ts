import { onCall, HttpsError } from 'firebase-functions/v2/https';
import * as admin from 'firebase-admin';
import { requireAdmin } from './utils';
import type { UserRecord } from 'firebase-admin/auth';
import { sendUserReviewerNoticeEmail } from './subscriptions/email';

export const setUserAdminStatus = onCall({ cors: true }, async (request) => {
  const { data, auth } = request;
  if (!auth) {
    throw new HttpsError('unauthenticated', 'Authentication required');
  }
  requireAdmin(auth);

  // Verify that the body has the required fields
  if (!data || !data.uid || data.isAdmin == null) {
    throw new HttpsError(
      'invalid-argument',
      'The function must be called with the request `uid` and `isAdmin`.',
    );
  }

  const { uid, isAdmin } = data;
  const user = await admin.auth().getUser(uid);
  const { customClaims = {} } = user;
  customClaims.isAdmin = isAdmin;
  await admin.auth().setCustomUserClaims(uid, customClaims);
  return { message: `User ${uid} admin status set to ${isAdmin}` };
});

export const setUserReviewerStatus = onCall({ cors: true }, async (request) => {
  const { data, auth } = request;
  if (!auth) {
    throw new HttpsError('unauthenticated', 'Authentication required');
  }
  requireAdmin(auth);

  // Verify that the body has the required fields
  if (!data || !data.uid || data.isReviewer == null) {
    throw new HttpsError(
      'invalid-argument',
      'The function must be called with the request `uid` and `isReviewer`.',
    );
  }

  const { uid, isReviewer } = data;
  const user = await admin.auth().getUser(uid);
  const { customClaims = {}, email, displayName } = user;
  customClaims.isReviewer = isReviewer;
  await admin.auth().setCustomUserClaims(uid, customClaims);
  if (isReviewer === true) {
    const name = (displayName as string) || (email as string);
    await sendUserReviewerNoticeEmail(email as string, name);
  }
  return { message: `User ${uid} reviewer status set to ${isReviewer}` };
});

export const setPaypalPayment = onCall({ cors: true }, async (request) => {
  const { data, auth } = request;
  if (!auth) {
    throw new HttpsError('unauthenticated', 'Authentication required');
  }
  requireAdmin(auth);

  // Verify that the body has the required fields
  if (!data || !data.uid || data.paidViaPaypal == null) {
    throw new HttpsError(
      'invalid-argument',
      'The function must be called with the request `uid` and `paidViaPaypal`.',
    );
  }

  const { uid, paidViaPaypal } = data;
  const user = await admin.auth().getUser(uid);
  const { customClaims = {} } = user;
  customClaims.paidViaPaypal = paidViaPaypal;
  await admin.auth().setCustomUserClaims(uid, customClaims);
  return {
    message: `User ${uid} PayPal payment status set to ${paidViaPaypal}`,
  };
});

interface UserWithClaims {
  uid: string;
  email: string;
  displayName: string;
  creationTime: string;
  lastSignInTime: string;
  isAdmin: boolean;
  isReviewer: boolean;
  stripeRole?: string;
  paidViaPaypal: boolean;
}

interface ListUsersResponse {
  users: UserWithClaims[];
  nextPageToken?: string;
  totalUsers: number;
}

async function listAllUsers() {
  const users: UserRecord[] = [];
  let pageToken: string | undefined;

  do {
    const result = await admin.auth().listUsers(1000, pageToken);
    users.push(...result.users);
    pageToken = result.pageToken;
  } while (pageToken);

  return users;
}

function userRecordToWithClaims(user: UserRecord): UserWithClaims {
  const { uid, displayName, email, customClaims, metadata } = user;
  return {
    uid,
    displayName: displayName ?? '',
    email: email ?? '',
    isAdmin: customClaims?.isAdmin ?? false,
    isReviewer: customClaims?.isReviewer ?? false,
    stripeRole: customClaims?.stripeRole,
    paidViaPaypal: customClaims?.paidViaPaypal ?? false,
    creationTime: metadata?.creationTime ?? '',
    lastSignInTime: metadata?.lastSignInTime ?? '',
  };
}

export const listUsersWithClaims = onCall({ cors: true }, async (request) => {
  const { data, auth } = request;
  if (!auth) {
    throw new HttpsError('unauthenticated', 'Authentication required');
  }
  requireAdmin(auth);

  const { page, filterByStatus, filterByRole, filter, limit, pageToken } = data;

  if (filter || filterByRole || filterByStatus) {
    let users = await listAllUsers();
    users = users.filter((user) => {
      const searchTerm = filter?.toLowerCase();
      const matchesFilter =
        !filter ||
        user.email?.toLowerCase().includes(searchTerm as string) ||
        user.displayName?.toLowerCase().includes(searchTerm as string);

      const matchesRole =
        !filterByRole || user.customClaims?.[filterByRole] === true;

      const isActive =
        user.customClaims &&
        (user.customClaims?.paidViaPaypal === true ||
          user.customClaims?.isOldAuthor === true ||
          user.customClaims?.stripeRole === 'basic');
      const matchesStatus =
        !filterByStatus || (filterByStatus === 'active' ? isActive : !isActive);

      return matchesFilter && matchesRole && matchesStatus;
    });

    const totalCount = users.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedUsers = users.slice(startIndex, endIndex);

    return {
      users: paginatedUsers.map(userRecordToWithClaims),
      totalUsers: totalCount,
    };
  }

  const { users, pageToken: nextPageToken } = await admin
    .auth()
    .listUsers(limit, pageToken);

  const totalCountSnap = await admin
    .firestore()
    .collection('users')
    .count()
    .get();
  const totalCount = totalCountSnap.data().count;

  const response: ListUsersResponse = {
    users: users.map(userRecordToWithClaims),
    nextPageToken: nextPageToken,
    totalUsers: totalCount,
  };

  return response;
});
