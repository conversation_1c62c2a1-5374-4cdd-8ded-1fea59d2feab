import * as admin from 'firebase-admin';
import { defineSecret } from 'firebase-functions/params';
import * as logger from 'firebase-functions/logger';
import { verifyIfUserIsSubscribed } from './stripe';
import { userDocument } from './subscriptions/collections';
import {
  onDocumentCreatedWithAuthContext,
  onDocumentUpdatedWithAuthContext,
} from 'firebase-functions/v2/firestore';
import { google } from 'googleapis';
import Stripe from 'stripe';
import {
  getGoogleSheetClient,
  googleServiceAccountEmail,
  googleServiceAccountKey,
} from './utils';

// Define secrets for API keys
const apiKey = defineSecret('GETRESPONSE_API_KEY');
const campaignId = defineSecret('GETRESPONSE_CAMPAIGN_ID');
const stripeKey = defineSecret('firestore-stripe-payments-STRIPE_API_KEY');

// Contact interface for type definition
interface Contact {
  name: string;
  email: string;
  dayOfCycle?: string;
  scoring?: number;
  customFields?: { [key: string]: string };
  campaign: { campaignId: string };
}

// Firestore document path for subscriptions
const subscriptionDoc = 'users/{userId}/subscriptions/{subscriptionId}';
const opts = {
  document: subscriptionDoc,
  cors: true,
  secrets: [
    apiKey,
    campaignId,
    stripeKey,
    googleServiceAccountEmail,
    googleServiceAccountKey,
  ],
};

// Cloud Function for handling document creation
export const onUserSubscriptionCreate = onDocumentCreatedWithAuthContext(
  opts,
  async (event) => {
    const snapshot = event.data!;
    const customerId = snapshot.data()!.customer;
    await subscribeOrUnsubscribeUserToGetResponse(customerId, event, true);
  },
);

// Cloud Function for handling document updates
export const onUserSubscriptionUpdate = onDocumentUpdatedWithAuthContext(
  opts,
  async (event) => {
    const userId = event.params.userId;
    const userCollection: admin.firestore.DocumentReference =
      userDocument(userId);
    const user = await userCollection.get();
    const data = user.data()!;
    await subscribeOrUnsubscribeUserToGetResponse(data.stripeId, event, false);
  },
);

/**
 * Subscribe or unsubscribe a user from GetResponse based on their subscription status.
 * @param customerId - The customer ID from Stripe.
 * @param context - The event context containing parameters and more.
 * @param isCreate - Indicates if this is a create event (true) or an update event (false).
 */
const subscribeOrUnsubscribeUserToGetResponse = async (
  customerId: string,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  context: any,
  isCreate: boolean,
): Promise<void> => {
  const userId = context.params.userId;
  logger.info(`Processing user in GetResponse (${userId})`);
  const { uid, email, displayName, customClaims } = await admin
    .auth()
    .getUser(userId);

  const stripeId = customerId;
  if (stripeId) {
    const stripeSubscription = await verifyIfUserIsSubscribed(stripeId, userId);
    if (isCreate === true) {
      await admin
        .firestore()
        .doc(`users/${uid}`)
        .set(
          {
            address: stripeSubscription.customer.address || {},
          },
          { merge: true },
        );
    }
    try {
      // Call the new Google Sheets function
      await updateGoogleSheetSubscriptionStatus(
        email as string,
        (displayName as string) || (email as string) || '',
        stripeSubscription,
      );
    } catch (error) {
      logger.error(
        `Error updating Google Sheet for user (${uid} : ${email})`,
        error,
      );
    }
    if (stripeSubscription.status === false) {
      try {
        await admin.firestore().doc(`users/${uid}/meta/counts`).set(
          {
            is_author: false,
          },
          { merge: true },
        );
        logger.info(`User (${uid} : ${email}) unsubscribed successfully`);
        if (customClaims?.stripeRole) {
          logger.info(
            `Updating subscription claims for user (${uid} : ${email})`,
          );
          const newCustomClaims = {
            isOldAuthor: false,
            paidViaPaypal: false,
            stripeRole: null,
          };
          await admin.auth().setCustomUserClaims(uid, newCustomClaims);
        }
      } catch (error) {
        logger.error(
          `Error occurred while unassigning user author role`,
          error,
        );
      }
      if (!customClaims?.isOldAuthor) {
        try {
          const users = await getUserByEmail(email as string);
          if (users.length && users[0].contactId) {
            await deleteContact(users[0].contactId as string);
          }
          logger.info(`User (${uid} : ${email}) unsubscribed from GetResponse`);
        } catch (error) {
          logger.error(
            `Error occurred during GetResponse contact deletion`,
            error,
          );
        }
      }
    } else {
      try {
        const newCustomClaims = {
          isOldAuthor: false,
          paidViaPaypal: false,
          stripeRole: 'basic',
        };
        await admin.auth().setCustomUserClaims(uid, newCustomClaims);
        await admin.firestore().doc(`users/${uid}/meta/counts`).set(
          {
            is_author: true,
          },
          { merge: true },
        );
        logger.info(`User (${uid} : ${email}) subscribed successfully`);
      } catch (error) {
        logger.error(`Error occurred while assigning user author role`, error);
      }

      try {
        const request: Contact = {
          email: email as string,
          name: (displayName as string) || (email as string),
          campaign: { campaignId: campaignId.value() },
        };

        await createContactInCampaign(request);
        logger.info(`User (${uid} : ${email}) subscribed to Get Response`);
      } catch (error) {
        logger.error(
          `Error occurred during Get Response contact creation`,
          error,
        );
      }
    }
  }
};

/**
 * Create a contact in GetResponse campaign.
 * @param request - The contact data.
 */
const createContactInCampaign = async (request: Contact): Promise<void> => {
  const url = 'https://api.getresponse.com/v3/contacts';

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'X-Auth-Token': `api-key ${apiKey.value()}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(request),
  });

  logger.info(`Contact creation request sent`, {
    url: url,
    'X-Auth-Token': `api-key ${apiKey.value()}`,
    'Content-Type': 'application/json',
    body: JSON.stringify(request),
  });

  if (!response.ok) {
    if (response.status === 429) {
      await new Promise((resolve) => setTimeout(resolve, 100000)); // Delay for 100 seconds
      await createContactInCampaign(request); // Retry the API call recursively
    } else {
      logger.error(
        `Error occurred during Get Response contact creation`,
        await response.json(),
      );
      throw new Error(`Failed to create contacts. Status: ${response.status}`);
    }
  }
};

/**
 * Retrieve user by email from GetResponse.
 * @param email - The email to search for.
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const getUserByEmail = async (email: string): Promise<any> => {
  const url = 'https://api.getresponse.com/v3/contacts';

  const response = await fetch(`${url}?query[email]=${email}`, {
    method: 'GET',
    headers: {
      'X-Auth-Token': `api-key ${apiKey.value()}`,
      'Content-Type': 'application/json',
    },
  });

  logger.info(`User retrieval request sent`, {
    url: `${url}?query[email]=${email}`,
    'X-Auth-Token': `api-key ${apiKey.value()}`,
    'Content-Type': 'application/json',
  });

  if (!response.ok) {
    if (response.status === 429) {
      logger.info('Too Many Requests. Retrying after 100 seconds...');
      await new Promise((resolve) => setTimeout(resolve, 100000)); // Delay for 100 seconds
      return await getUserByEmail(email); // Retry the API call recursively
    } else {
      logger.error(
        `Error occurred during Get Response user retrieval`,
        await response.json(),
      );
      throw new Error(`Failed to get user. Status: ${response.status}`);
    }
  }

  return await response.json();
};

/**
 * Delete a contact from GetResponse.
 * @param userId - The user ID of the contact to delete.
 */
const deleteContact = async (userId: string): Promise<void> => {
  const url = 'https://api.getresponse.com/v3/contacts';

  const response = await fetch(`${url}/${userId}`, {
    method: 'DELETE',
    headers: {
      'X-Auth-Token': `api-key ${apiKey.value()}`,
      'Content-Type': 'application/json',
    },
  });

  logger.info(`Contact deletion request sent`, {
    url: `${url}/${userId}`,
    'X-Auth-Token': `api-key ${apiKey.value()}`,
    'Content-Type': 'application/json',
  });

  if (!response.ok) {
    if (response.status === 429) {
      logger.info('Too Many Requests. Retrying after 100 seconds...');
      await new Promise((resolve) => setTimeout(resolve, 100000)); // Delay for 100 seconds
      await deleteContact(userId); // Retry the API call recursively
    } else {
      logger.error(
        `Error occurred during Get Response contact deletion`,
        await response.json(),
      );
      throw new Error(`Failed to delete contact. Status: ${response.status}`);
    }
  }
};

/**
 * Updates, adds, or removes a user's subscription status and details in a Google Sheet.
 * @param email - User's email address.
 * @param displayName - User's display name (fallback to email).
 * @param stripeSubscription - Object containing Stripe customer and subscription status.
 */
const updateGoogleSheetSubscriptionStatus = async (
  email: string,
  displayName: string,
  stripeSubscription: { customer: Stripe.Customer; status: boolean },
): Promise<void> => {
  const customer = stripeSubscription.customer as Stripe.Customer;
  const address = customer.shipping?.address || customer.address;
  logger.info(`User Address: Country: ${address?.country}`, address);

  if (
    !address?.country ||
    !['us', 'united states', 'united states of america', 'usa'].includes(
      address?.country?.toLowerCase(),
    )
  ) {
    throw new Error('Address country is not valid or not provided.');
    return;
  }

  const auth = await getGoogleSheetClient();
  const sheets = google.sheets({ version: 'v4', auth });

  const spreadsheetId = '1NF-W2FrVsBT9wW8X5YvfBFxXeVLBZ9xlJFrUM3zK8IY';
  const sheetName = 'manny-users-with-addresses';
  const emailColumn = 'B'; // Email is in column B

  try {
    // Step 1: Search for the email in the email column (column B)
    const searchRange = `${sheetName}!${emailColumn}:${emailColumn}`;
    const searchResponse = await sheets.spreadsheets.values.get({
      spreadsheetId,
      range: searchRange,
    });

    const rows = searchResponse.data.values || [];
    let foundRow = -1;

    for (let i = 0; i < rows.length; i++) {
      if (rows[i][0] === email) {
        foundRow = i + 1; // Convert 0-based index to 1-based row number
        break;
      }
    }

    const isSubscribed = stripeSubscription.status;
    const fullName = customer.shipping?.name || customer.name || displayName;
    let { firstName, lastName } = parseName(fullName);

    if (customer.metadata) {
      firstName = customer.metadata?.first_name || firstName || '';
      lastName = customer.metadata?.last_name || lastName || '';
    }

    const newRowValues = [
      [
        '',
        email,
        firstName,
        lastName,
        fullName,
        `${address?.line1 ? address.line1 + ',' : ''} ${
          address?.line2 || ''
        }`.trim() || '',
        address?.city || '',
        address?.state || '',
        address?.postal_code || '',
        address?.country || '',
      ],
    ];

    // Step 4: Handle based on subscription status
    if (isSubscribed) {
      // Add or update row with full data
      const updateRange = `${sheetName}!A${foundRow}:J${foundRow}`;

      if (foundRow >= 0) {
        // Update existing row with full data
        await sheets.spreadsheets.values.update({
          spreadsheetId,
          range: updateRange,
          valueInputOption: 'USER_ENTERED',
          requestBody: {
            values: newRowValues,
          },
        });
        logger.info(`Updated row for ${email} in Google Sheet`);
      } else {
        // Append new row with full data
        await sheets.spreadsheets.values.append({
          spreadsheetId,
          range: `${sheetName}!A:J`,
          valueInputOption: 'USER_ENTERED',
          insertDataOption: 'INSERT_ROWS',
          requestBody: {
            values: newRowValues,
          },
        });
        logger.info(`Added new user ${email} to Google Sheet`);
      }
    } else {
      // Remove row if it exists
      if (foundRow >= 0) {
        // Get sheet ID for the target sheet
        const spreadsheet = await sheets.spreadsheets.get({
          spreadsheetId,
        });

        const sheet = spreadsheet.data.sheets?.find(
          (s) => s.properties?.title === sheetName,
        );

        if (!sheet) {
          throw new Error(`Sheet "${sheetName}" not found`);
        }

        const sheetId = sheet.properties?.sheetId;

        // Delete the row using batch update
        await sheets.spreadsheets.batchUpdate({
          spreadsheetId,
          requestBody: {
            requests: [
              {
                deleteDimension: {
                  range: {
                    sheetId: sheetId,
                    dimension: 'ROWS',
                    startIndex: foundRow - 1,
                    endIndex: foundRow,
                  },
                },
              },
            ],
          },
        });

        logger.info(`Deleted row for ${email} from Google Sheet`);
      } else {
        logger.info(`No row found for ${email} to delete`);
      }
    }
  } catch (error) {
    logger.error(`Error updating Google Sheet for ${email}:`, error);
  }
};

const PREFIXES = new Set<string>([
  'Dr',
  'Mr',
  'Mrs',
  'Ms',
  'Prof',
  'Sir',
  'Lady',
  'Lord',
  'Rev',
]);
const SUFFIXES = new Set<string>([
  'Jr',
  'Sr',
  'II',
  'III',
  'IV',
  'Esq',
  'DDS',
  'MD',
  'PhD',
  'MBA',
]);

/**
 * Parses full name into first and last name using arrow function syntax
 */
const parseName = (
  fullName: string = '',
): {
  firstName: string;
  lastName: string;
} => {
  let parts = fullName
    .trim()
    .split(/\s+/)
    .filter(Boolean)
    .map((part) => part.replace(/([,.\s])$/g, ''));

  // Remove prefixes
  while (parts.length && PREFIXES.has(parts[0])) {
    parts = parts.slice(1);
  }

  // Remove suffixes
  while (parts.length && SUFFIXES.has(parts[parts.length - 1])) {
    parts = parts.slice(0, -1);
  }

  const [firstName = '', ...rest] = parts;
  const lastName = rest.length > 0 ? rest.pop()! : '';

  return { firstName, lastName };
};
