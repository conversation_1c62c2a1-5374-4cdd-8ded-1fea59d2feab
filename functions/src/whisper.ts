import { onCall, HttpsError } from 'firebase-functions/v2/https';
import { defineSecret } from 'firebase-functions/params';
import { requireAuth } from './utils';
import * as logger from 'firebase-functions/logger';
import OpenAI from 'openai';
import { AudioTranscriptionService } from './booktranscribe';
import * as admin from 'firebase-admin';

// WhisperAI API details
const openAIKey = defineSecret('OPENAI_API_KEY');

export const transcribeBookChapterAudioFromSrc = onCall(
  {
    cors: true,
    secrets: [openAIKey],
    cpu: 4,
    memory: '16GiB',
    timeoutSeconds: 3600,
  },
  async (request) => {
    const { data, auth } = request;

    const { audioFileUrl, dataDoc, fileName, prompt, maxDurationLimit } = data;
    if (
      !dataDoc ||
      !audioFileUrl ||
      !fileName ||
      !prompt ||
      !maxDurationLimit
    ) {
      throw new HttpsError(
        'invalid-argument',
        'Bad Request: Missing arguments',
      );
      return false;
    }

    if (!auth) {
      throw new HttpsError('unauthenticated', 'Authentication required');
    }
    requireAuth(auth);
    const chapterDoc = admin.firestore().doc(dataDoc);
    try {
      await chapterDoc.set({ isProcessing: true }, { merge: true });
      const openai = new OpenAI({
        apiKey: openAIKey.value(),
      });
      const transcriptionService = new AudioTranscriptionService(
        openai,
        chapterDoc,
        auth,
        prompt,
        maxDurationLimit,
        audioFileUrl,
        fileName,
      );
      return await transcriptionService.downloadAndTranscribe();
      return false;
    } catch (error) {
      logger.error('Error transcribing:', error);
      let errorMsg = 'Unknown Error.';
      if (error instanceof Error) errorMsg = error.message;
      await chapterDoc.set(
        {
          isProcessing: false,
          transcription: `#-error_by_manny_transcription-#: ${errorMsg}`,
        },
        { merge: true },
      );
      throw new HttpsError('internal', 'Error transcribing: ' + errorMsg);
      return false;
    }
  },
);
