export interface UserObject {
  name?: string;
  email: string;
  stripeId?: string;
  paypalId?: string;
  customClaims?: {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    [key: string]: any;
  };
  id?: string;
  password?: string;
  isPaused?: boolean;
  features?: string[];
  settings?: object;
  assignedAdminId?: string[];
  messages?: string[];
  templateId?: string;
  exists?: boolean | null;
  isOldAuthor?: boolean;
}
