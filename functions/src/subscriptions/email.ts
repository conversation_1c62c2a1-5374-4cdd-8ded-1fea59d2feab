import * as logger from 'firebase-functions/logger';
import * as nodemailer from 'nodemailer';
import { EmailObject } from './types/email';
import { gmailCredentials, siteURL } from '../utils';

/**
 * Sends an email using nodemailer gmail smtp
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function sendEmail(data: EmailObject): Promise<any> {
  const gmailTransport = nodemailer.createTransport({
    service: 'gmail',
    auth: {
      user: gmailCredentials.user,
      pass: gmailCredentials.pass,
    },
  });

  try {
    const response = await gmailTransport.sendMail(data);
    return response;
  } catch (error) {
    logger.error(error);
    return error;
  }
}

/**
 * Sends a custom user password reset email. TODO: find a way to get the password reset template
 */
export async function sendCustomPasswordResetEmail(
  email: string,
  displayName: string,
  link: string,
  password: string,
): Promise<void> {
  await sendEmail({
    to: email,
    from: '<EMAIL>',
    subject: 'Welcome to Manuscriptr',
    text: '',
    html: `<p>Hello and Welcome to Manuscriptr!</p>
            <p>Thank you for subscribing to our AI-assisted book writing software.  Your new writing assistant <PERSON> will help you with every aspect of your book from your title to the stories you tell...and so much more.  We think you're going to love it.</p>
            <p>To get started, just follow the link below to set your password for your account - ${email}. Please note, this link will expire in 1 hour.</p>
            <p><a href="${link}">${link}</a></p>
            <p>Alternatively, you can reset your password anytime by clicking the “Reset Password” button on this page: <a href="${siteURL}">Reset Password</a>.</p>
            <p>You can use the email and password provided below to log in:.</p>
            <p>
            <b>Login URL:</b> <a href="${siteURL}/login">${siteURL}/login</a><br />
            <b>Email:</b> ${email}<br />
            <b>Password:</b> ${password}<br />
            </p>
            <p>For your convenience, here's a video recording on how to reset the password: <a href="https://jam.dev/c/3e044001-d224-43ee-9386-56bba960b0db">Password Reset Video</a>.</p>`,
  });
}

export async function sendPasswordResetEmail(
  email: string,
  displayName: string,
  link: string,
): Promise<void> {
  await sendEmail({
    to: email,
    from: '<EMAIL>',
    subject: 'Welcome to Manuscriptr',
    text: '',
    html: `<p>Hello and Welcome to Manuscriptr!</p>
        <p>Thank you for being a valued user of our AI-assisted book writing software. Your writing assistant, Manny, is here to help you with every aspect of your book, from your title to the stories you tell...and so much more. We think you're going to love it.</p>
        <p>To get started, simply log in to your account using your existing password - ${email}. If you've forgotten your password, please follow the link below to reset it. Note that this link will expire in 1 hour.</p>
        <p><a href="${link}">${link}</a></p>
        <p>There's nothing for you to do except log in and start creating. Your password remains the same. Just go to <a href="${siteURL}" target="_blank">app.manuscriptr.com</a> and start collaborating with this next-generation platform designed to help you finish your book in record time.</p>
        <p>If you need help at any time, just click on the menu at the top right of Manuscriptr, select help, submit your request, and our dedicated support team will be there to assist you.</p>
        <p>For your convenience, here's a video recording on how to reset your password: <a href="https://jam.dev/c/3e044001-d224-43ee-9386-56bba960b0db">Password Reset Video</a>.</p>`,
  });
}

/**
 * Sends a custom user password reset email. TODO: find a way to get the password reset template
 */
export async function sendUserReviewerNoticeEmail(
  email: string,
  displayName: string,
): Promise<void> {
  await sendEmail({
    to: email,
    from: '<EMAIL>',
    subject: 'You Have Been Assigned as a Reviewer',
    text: '',
    html: `<p>Dear ${displayName},</p>
            <p>We are pleased to inform you that you have been assigned the role of a <strong>Reviewer</strong> in Manuscriptr.</p>
            <p>To get started, please log in to your account by visiting <a href="${siteURL}" target="_blank">app.manuscriptr.com</a>. Use your registered email address and password to access the platform.</p>
            <p>Once logged in, you can view if there are any author books available for you to review. Your feedback and expertise are highly valued, and we appreciate your contributions to our community.</p>   
            <p>Thank you for your valuable contributions!</p>
            <p>The Manuscriptr Team</p>`,
  });
}

/**
 * Sends an email when user subscribe to a package.
 */
export async function sendSubscriptionEmail(
  email: string,
  displayName: string,
  link: string,
): Promise<void> {
  const name = displayName ?? 'Dear Author';
  await sendEmail({
    to: email,
    from: '<EMAIL>',
    subject: 'Welcome to Manuscriptr: Your AI Writing Assistant!',
    text: '',
    html: `<p>${name},</p>
            <p>Welcome to Manuscriptr, and thank you for subscribing to our AI-assisted book writing software! We're thrilled to have you on board and excited for the journey ahead.</p>
            <p>To get started, simply use the email address associated with your subscription, ${email}, and the password you set during registration. If you happen to forget your password at any point, don't worry! You can easily reset it by following this link:</p>
            <p><a href="${link}">${link}</a>.</p>
            <p>Just go to <a href="${siteURL}" target="_blank">app.manuscriptr.com</a> and start collaborating with this next-generation platform meant to get you to the finish line with a quality book in record time.</p>
            <p>If you need help at any time, just click on the menu at the top right of Manuscriptr, select help, submit your request, and our crack team of support will be there to assist you.</p>
            <p>Happy writing!</p>
            <p>The Manuscriptr Team</p>`,
  });
}

/**
 * Sends an email when user upgrade to a package.
 */
export async function sendSubscriptionUpgradeEmail(
  email: string,
  displayName: string,
): Promise<void> {
  const name = displayName ?? 'Dear Author';
  await sendEmail({
    to: email,
    from: '<EMAIL>',
    subject: 'Updates to your Manuscriptr Subscription!',
    text: '',
    html: `<p>${name},</p>
            <p>Congratulations!  You have successfully upgraded to a 6 month subscription to our powerful AI-assisted book-writing software, Manuscriptr.  With the help of your assistant Manny, you can create everything from your title to your outline. You can even get suggestions on your content--and so much more.</p>
            <p>There's nothing for you to do except log in and start creating. Your password remains the same. Just go to <a href="${siteURL}" target="_blank">app.manuscriptr.com</a> and start collaborating with this next-generation platform meant to get you to the finish line with a quality book in record time.</p>
            <p>If you need help at any time, just click on the menu at the top right of Manuscriptr, select help, submit your request, and our crack team of support will be there to assist you.</p>
            <p>Happy writing!</p>
            <p>The Manuscriptr Team</p>`,
  });
}

/**
 * Sends an email to support when user has completed the no. of payment has been made.
 */
export async function sendSubscriptionUpdateEmailToSupport(
  clientName: string,
  clientEmail: string,
  noOfPayments: number,
  product: string,
): Promise<void> {
  await sendEmail({
    to: ['<EMAIL>', '<EMAIL>'],
    from: '<EMAIL>',
    subject: 'Update Client Subscription!',
    text: '',
    html: `<p>Hi Team,</p>
           <p>This is an automated message from Manuscriptr.</p>
            <p>This is to inform you that the client, ${clientEmail}, has successfully completed the payment for a ${noOfPayments}-month subscription to ${product}. 
            To ensure uninterrupted access to our services, we kindly request you to review and subscribe the client to the Manuscriptr 97 package.</p>
           <p><b>Client Name:</b> ${clientName}</p>
           <p><b>Client Email:</b> ${clientEmail}</p>
            <p>Thanks!</p>
            <p>Manuscriptr</p>`,
  });
}

/**
 * Sends an email to support when user subscribes to multiple package.
 */
export async function sendExistingSubscriptionEmail(
  clientEmail: string,
  clientName: string,
): Promise<void> {
  await sendEmail({
    to: ['<EMAIL>', '<EMAIL>'],
    from: '<EMAIL>',
    subject: 'Existing Manuscriptr Subscription from a Client!',
    text: '',
    html: `<p>Hi Team,</p>
            <p>This is an automated message from Manuscriptr. This client has recently purchased another subscription, and they currently have two active Manuscriptr Subscriptions. Please review and follow up with the client to cancel one so they are not billed twice.</p>
           <p><b>Client Name:</b> ${clientName}</p>
           <p><b>Client Email:</b> ${clientEmail}</p>
            <p>Thanks!</p>
            <p>Manuscriptr</p>`,
  });
}

/**
 * Sends an email to user and support about account deletion.
 */
export async function sendAccountDeletionRequestEmail(
  clientEmail: string,
  clientName: string,
  deletionRequestDate: string,
  deletionDate30Days: string,
): Promise<void> {
  await sendEmail({
    to: clientEmail,
    cc: [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ],
    from: '<EMAIL>',
    subject: `Account Deletion Confirmation: ${deletionRequestDate}`,
    text: '',
    html: ` <p>Dear ${clientName},</p>
    <p>We have received your request to delete your Manuscriptr account. We are processing your request in accordance with our Deletion Policy:</p>
    <ul>
        <li><strong>Data Deletion Timeline:</strong> Your account and associated data will be deleted from active systems by <strong>${deletionDate30Days}</strong>.</li>
        <li><strong>Retention for Legal Obligations:</strong> Certain data may be retained for legal compliance, dispute resolution, or enforcement of agreements.</li>
    </ul>
    <p>If you did not request this deletion or have concerns, contact us immediately at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
    <p>Thank you for using Manuscriptr. We appreciate being part of your writing journey.</p>
    <p>Best regards,<br><strong>The Manuscriptr Team</strong></p>`,
  });
}
/**
 * Sends an email to support that the account has been successfully deleted.
 */
export async function sendSuccessAccountDeletionEmail(
  clientEmail: string,
  clientName: string,
): Promise<void> {
  await sendEmail({
    to: clientEmail,
    cc: [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ],
    from: '<EMAIL>',
    subject: 'Your Account Has Been Permanently Deleted',
    text: '',
    html: ` <p>Dear ${clientName},</p>
    <p>Your account has been successfully deleted.</p>
    <p>All your data has been permanently removed from our systems.</p>
    <p>Thank you for using Manuscriptr. We appreciate being part of your writing journey.</p>
    <p>Best regards,<br><strong>The Manuscriptr Team</strong></p>`,
  });
}

export async function sendCancellationOfSubscriptionEmail(
  email: string,
): Promise<void> {
  await sendEmail({
    to: email,
    cc: [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ],
    from: '<EMAIL>',
    subject: 'Cancellation of Your Subscription',
    text: '',
    html: `<p>Dear ${email},</p><p>Hello and Thank you for being a valued user of Manuscriptr.</p>
            <p>We understand that you have decided to cancel your subscription. We are sorry to see you go, but we hope that you will consider Manuscriptr in the future.</p>
            <p>Should you decide to return, please reach out to us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
            <p>Thank you for your support.</p>`,
  });
}

/**
 * Sends an email to user where their account will be deleted in 5 days.
 */
export async function sendNoticeToAccountsToBeDeletedEmail(
  daysLeft: number,
  email: string,
  displayName: string,
): Promise<void> {
  await sendEmail({
    to: email,
    cc: [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ],
    from: '<EMAIL>',
    subject: `Notice: Your Account Will Be Deleted in ${daysLeft} Days`,
    text: '',
    html: `<p>Dear ${displayName},</p>

<p>We’re reaching out to let you know that your account is scheduled for deletion in <strong>${daysLeft}</strong> days.</p>

<p>If you wish to keep your account, you can cancel this request at any time before then. Simply go to your profile page and click the <strong>"Cancel Account Deletion"</strong> button, or contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>

<p>Thank you for being part of Manuscriptr.</p>`,
  });
}
