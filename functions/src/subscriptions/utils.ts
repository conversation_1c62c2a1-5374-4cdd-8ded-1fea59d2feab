import * as admin from 'firebase-admin';
import { Timestamp } from 'firebase-admin/firestore';

/**
 * Converts from unix timestamp to FirebaseFirestore.Timestamp
 */
export function toTimestamp(timestamp: number): admin.firestore.Timestamp {
  return Timestamp.fromMillis(timestamp * 1000);
}

/**
 * Converts from string date to FirebaseFirestore.Timestamp
 */
export function fromDateToTimestamp(date: string): admin.firestore.Timestamp {
  return Timestamp.fromDate(new Date(date));
}

/**
 * makes value nullable or FirebaseFirestore.Timestamp
 */
export function toNullableTimestamp(
  timestamp?: number | null | undefined,
): admin.firestore.Timestamp | null {
  if (timestamp === null) {
    return null;
  }
  if (timestamp === undefined) {
    return null;
  }
  return toTimestamp(Number(timestamp));
}
