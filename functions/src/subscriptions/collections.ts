import * as admin from 'firebase-admin';

/**
 * User document
 */
export function userDocument(
  userId: string,
): admin.firestore.DocumentReference<FirebaseFirestore.DocumentData> {
  const userDoc: admin.firestore.DocumentReference = admin
    .firestore()
    .collection('users')
    .doc(userId);
  return userDoc;
}

/**
 * Product collection
 */
export function productCollections(): admin.firestore.CollectionReference<FirebaseFirestore.DocumentData> {
  return admin.firestore().collection('products');
}

/**
 * user document subscription subcollection
 */
export function subsCollections(
  userId: string,
): admin.firestore.CollectionReference<FirebaseFirestore.DocumentData> {
  return userDocument(userId).collection('subscriptions');
}

/**
 * user document payment subcollection
 */
export function paymentCollections(
  userId: string,
): admin.firestore.CollectionReference<FirebaseFirestore.DocumentData> {
  return userDocument(userId).collection('payments');
}

/**
 * user document invoice subcollection
 */
export function invoiceCollections(
  userId: string,
  subscriptionId: string,
): admin.firestore.CollectionReference<FirebaseFirestore.DocumentData> {
  return userDocument(userId)
    .collection('subscriptions')
    .doc(subscriptionId)
    .collection('invoices');
}

/**
 * build the price item
 */
export function buildPriceItem(
  productDoc: admin.firestore.DocumentReference,
  priceId: string,
): admin.firestore.DocumentReference<FirebaseFirestore.DocumentData> {
  return productDoc.collection('prices').doc(priceId as string);
}
