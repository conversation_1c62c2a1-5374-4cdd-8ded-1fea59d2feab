import { UserObject } from './types/auth';
import * as admin from 'firebase-admin';
import * as logger from 'firebase-functions/logger';
import { faker } from '@faker-js/faker';
import { siteURL } from '../utils';
import {
  sendCustomPasswordResetEmail,
  sendPasswordResetEmail,
  sendSubscriptionEmail,
} from './email';
import <PERSON><PERSON> from 'stripe';

/**
 * tries to create a user or return existing. It also sends a reset password email
 */
export async function createUserAndSendResetPassword(
  data: UserObject,
): Promise<UserObject> {
  try {
    // query email to check if there is an existing user
    const querySnapshot = await admin
      .firestore()
      .collection('users')
      .where('email', '==', data.email)
      .get();
    if (!querySnapshot.empty) {
      const { uid, customClaims } = await admin
        .auth()
        .getUserByEmail(data.email);
      data.id = uid;
      data.customClaims = customClaims;
      if (!data.id) {
        data.id = querySnapshot.docs[0].id;
      }
    }
    logger.info('Does user found (first validation)?', data.id);
  } catch (error) {
    logger.info('User does not exists. (NOTE ERROR OCCURED)', error);
  }

  if (!data.id) {
    try {
      const { uid } = await admin.auth().createUser({
        email: data.email,
        password: faker.internet.password({ length: 8 }),
        displayName: data.name,
      });

      // retain user creation here because stripe adds properties stripeId and stripeLink to a user
      data.id = uid;
    } catch (error) {
      const { uid, customClaims } = await admin
        .auth()
        .getUserByEmail(data.email);
      data.id = uid;
      data.customClaims = customClaims;
      logger.info('User cannot be created. (NOTE ERROR OCCURED)', error);
    }
  } else {
    data.exists = true;
  }
  if (data.exists) {
    logger.info('User already exists', data);
  } else {
    logger.info('User successfully created', data);
  }
  return data;
}

/**
 * Checks if user can be created or exists
 */
export async function canCreateUserDocument(
  customerUser: string,
  stripe: Stripe,
): Promise<UserObject> {
  // get the customer from stripe
  const customer: Stripe.Customer = (await stripe.customers.retrieve(
    customerUser,
  )) as Stripe.Customer;

  const data = {
    name: (customer.metadata.name as string) || (customer.name as string) || '',
    email: customer.email as string,
    stripeId: customer.id as string,
    id: '',
    customClaims: {},
    exists: false,
  };
  // creates the user
  const createDoc: UserObject = await createUserAndSendResetPassword(
    data as UserObject,
  );

  // assign role custom claim
  logger.info(`Assign the custom claim stripeRole to ${createDoc.id}`);
  await admin.auth().setCustomUserClaims(createDoc.id as string, {
    ...createDoc.customClaims,
    stripeRole: 'basic',
  });
  return createDoc;
}

/**
 * Generates a reset password link and sends a welcome email.
 */
export async function generatePasswordResetAndSendWelcomeEmail(userId: string) {
  const actionCodeSettings = {
    url: siteURL,
    handleCodeInApp: false,
  };

  const { email, displayName } = await admin.auth().getUser(userId);

  // sends generate reset password link
  admin
    .auth()
    .generatePasswordResetLink(email as string, actionCodeSettings)
    .then(async (link) => {
      const password = faker.internet.password({ length: 8 });
      await admin.auth().updateUser(userId, { password: password });
      await sendCustomPasswordResetEmail(
        email as string,
        displayName as string,
        link,
        password,
      );
      logger.info('Generate password reset email is sent!');
    })
    .catch(function (error) {
      logger.error('Generate password reset email error!', error);
    });
}
export async function generatePasswordResetWelcomeEmail(userId: string) {
  const actionCodeSettings = {
    url: siteURL,
    handleCodeInApp: false,
  };

  const { email, displayName } = await admin.auth().getUser(userId);

  // sends generate reset password link
  return admin
    .auth()
    .generatePasswordResetLink(email as string, actionCodeSettings)
    .then(async (link) => {
      const password = faker.internet.password({ length: 8 });
      await admin.auth().updateUser(userId, { password: password });
      await sendPasswordResetEmail(
        email as string,
        displayName as string,
        link,
      );
      logger.info('Generate password reset email is sent!');
      return link;
    })
    .catch(function (error) {
      logger.error('Generate password reset email error!', error);
      return '';
    });
}

/**
 * sends a welcome email when subscribing to a new product
 */
export async function sendWelcomeEmailSubcription(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  product: any,
  userId: string,
): Promise<void> {
  const actionCodeSettings = {
    url: siteURL,
    handleCodeInApp: false,
  };

  const { email, displayName } = await admin.auth().getUser(userId);

  if (!product.metadata?.sendEmailOnSubscribe) {
    // sends generate reset password link
    admin
      .auth()
      .generatePasswordResetLink(email as string, actionCodeSettings)
      .then(async (link) => {
        await sendSubscriptionEmail(
          email as string,
          displayName as string,
          link,
        );
        logger.info('Generate password reset email is sent!');
      })
      .catch(function (error) {
        logger.error('Generate password reset email error!', error);
      });
  }
}
