import { onCall, HttpsError } from 'firebase-functions/v2/https';
import { requireAuth } from './utils';
import * as admin from 'firebase-admin';
import * as logger from 'firebase-functions/logger';
import { onDocumentWritten } from 'firebase-functions/v2/firestore';
import { FieldValue } from 'firebase-admin/firestore';

/**
 * Cloud function to send push notifications to a user.
 * @param {admin.auth.DecodedIdToken} auth - The authentication token of the user.
 * @param {object} data - Contains the notification data.
 * @param {string} data.userId - The user ID to whom the notification is sent.
 * @param {string} data.title - The title of the notification.
 * @param {string} data.body - The body content of the notification.
 * @returns {Promise<void>} - A promise that resolves when the notification is sent.
 */
export const onPushNotification = onCall(
  {
    cors: true,
    cpu: 2,
    memory: '8GiB',
    timeoutSeconds: 3600,
  },
  async (request): Promise<void> => {
    const { data, auth } = request;

    if (!auth) {
      throw new HttpsError('unauthenticated', 'Authentication required');
    }
    requireAuth(auth);

    const { userId, title, body } = data;

    // Retrieve user document from Firestore
    const userDoc = await admin
      .firestore()
      .collection('users')
      .doc(userId)
      .get();

    // Extract FCM token from user's document
    const fcmToken = userDoc.data()?.fcmToken;

    if (!fcmToken) {
      throw new HttpsError('internal', 'FCM Token not found');
    }

    // Define the message structure for FCM
    const message: admin.messaging.Message = {
      token: fcmToken,
      notification: {
        title: title,
        body: body,
      },
      webpush: {
        headers: {
          Urgency: 'high',
        },
        notification: {
          icon: 'https://app.manuscriptr.com/robot.png',
          vibrate: [100, 50, 100],
        },
      },
    };

    try {
      // Send the notification via FCM
      await admin.messaging().send(message);
      logger.info(`Notification sent to ${userId}`);
    } catch (error) {
      logger.error('Error sending notification:', error);
      let errorMsg = 'Unknown Error.';
      if (error instanceof Error) errorMsg = error.message;
      throw new HttpsError(
        'internal',
        'Error sending notification: ' + errorMsg,
      );
    }
  },
);

// Firestore document path for notifications
const notificationDoc = 'users/{userId}/notifications/{notificationId}';

/**
 * Cloud Function triggered on creation of a notification document.
 * @param {EventContext} event - The event context of the created document.
 */
export const onNotificationCreate = onDocumentWritten(
  notificationDoc,
  async (event): Promise<void> => {
    try {
      // Extract the userId from the event parameters
      const authorId: string = event.params.userId;

      // Initialize Firestore database
      const db = admin.firestore();

      const beforeData = event.data?.before.exists
        ? event.data?.before.data()
        : null;
      const afterData = event.data?.after.exists
        ? event.data?.after.data()
        : null;

      const beforeIsRead = beforeData?.isRead ?? false;
      const afterIsRead = afterData?.isRead ?? false;

      let notificationReadIncrement = 0;
      let notificationIncrement = 0;

      if (!event.data?.before.exists && event.data?.after.exists) {
        // A new notification is created, increment total notifications
        notificationIncrement = 1;

        try {
          const notifData = event.data?.after.data();
          if (!notifData) {
            throw new HttpsError(
              'internal',
              `Notification not found to ${authorId}`,
            );
          }

          // Retrieve user document from Firestore
          const userDoc = await admin
            .firestore()
            .collection('users')
            .doc(authorId)
            .get();

          // Extract FCM tokens from user document
          const tokens = userDoc.data()?.fcmTokens || [];

          if (!Array.isArray(tokens) || tokens.length === 0) {
            throw new HttpsError(
              'internal',
              `No FCM tokens found in Firestore to ${authorId}`,
            );
          }

          // Extract only the token values
          const fcmTokens = tokens
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            .map((entry: any) => entry.token)
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            .filter((token: any) => !!token);

          if (fcmTokens.length === 0) {
            throw new HttpsError(
              'internal',
              `No valid FCM tokens found to ${authorId}`,
            );
          }

          logger.info(
            `Sending notification to ${authorId} with tokens:`,
            fcmTokens,
          );

          if (!fcmTokens || fcmTokens.length === 0) {
            throw new HttpsError(
              'internal',
              `No FCM tokens found to ${authorId}`,
            );
          }

          const stripHtml = (html: string) =>
            html.replace(/<[^>]+>/g, '').trim();
          const shortenText = (text: string, length = 100) =>
            text.length > length ? text.substring(0, length) + '...' : text;

          // Usage in FCM message
          const message: admin.messaging.MulticastMessage = {
            tokens: fcmTokens,
            notification: {
              title: notifData.title,
              body: shortenText(stripHtml(notifData.notification)), // Clean and shorten
            },
          };
          // Send the notification via FCM
          await admin.messaging().sendEachForMulticast(message);
          logger.info(`Notification sent to ${authorId}`, notifData);
        } catch (error) {
          logger.error('Error sending notification:', error);
        }
      }

      if (!beforeIsRead && afterIsRead) {
        notificationReadIncrement = 1;
      } else if (beforeIsRead && !afterIsRead) {
        notificationReadIncrement = -1;
      }

      // Update user meta document
      await db.doc(`users/${authorId}/meta/counts`).set(
        {
          notifications: FieldValue.increment(notificationIncrement),
          notifications_read: FieldValue.increment(notificationReadIncrement),
        },
        { merge: true },
      );
    } catch (error) {
      logger.error(
        'Error fetching notifications data, updating user meta count, or updating user document:',
        error,
      );
    }
  },
);
