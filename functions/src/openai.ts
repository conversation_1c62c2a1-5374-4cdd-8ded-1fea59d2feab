import { onC<PERSON>, HttpsError, onRequest } from 'firebase-functions/v2/https';
import * as admin from 'firebase-admin';
import { FieldValue } from 'firebase-admin/firestore';
import { invokeOpenAI, openAIKey, requireAuth } from './utils';
import OpenAI from 'openai/index';
import * as logger from 'firebase-functions/logger';

// Redirect requests to OpenAI API, using the API key stored in the secrets
export const requestOpenAI = onCall(
  {
    cors: true,
    secrets: [openAIKey],
    cpu: 4,
    memory: '16GiB',
    timeoutSeconds: 3600,
  },
  async (request) => {
    const { data, auth } = request;
    if (!auth) {
      throw new HttpsError('unauthenticated', 'Authentication required');
    }
    requireAuth(auth);

    // Verify that the body has the required fields (type and prompt)
    if (!data || !data.type || !data.prompt) {
      throw new HttpsError(
        'invalid-argument',
        'The function must be called with the request `type` and `prompt`.',
      );
    }

    const { type, prompt } = data;
    if (!['compose', 'simplify'].includes(type)) {
      throw new HttpsError(
        'invalid-argument',
        'The `type` must be either "compose" or "simplify".',
      );
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let params: any = {
      model: 'gpt-4o',
      messages: [
        {
          role: 'system',
          content: prompt,
        },
      ],
    };
    if (type === 'compose') {
      params = {
        ...params,
        temperature: 0.7,
        max_completion_tokens: 16384,
      };
    } else if (type === 'simplify') {
      params = {
        ...params,
        temperature: 0.3,
        top_p: 1.0,
        max_completion_tokens: 16384,
        frequency_penalty: 0.0,
        presence_penalty: 0.0,
      };
    }

    try {
      // Send request to OpenAI API
      const invokeOpenai = invokeOpenAI();
      const responseData = await invokeOpenai.chat.completions.create(params);
      if (responseData.usage) {
        const { total_tokens, prompt_tokens, completion_tokens } =
          responseData.usage;
        const db = admin.firestore();

        const usageDoc = db.doc(`users/${auth.uid}/conversations/usage`);
        const logDoc = db.collection(`users/${auth.uid}/conversations`).doc();
        const batch = db.batch();
        batch.create(logDoc, responseData);
        batch.set(
          usageDoc,
          {
            total_tokens: FieldValue.increment(total_tokens),
            total_prompt_tokens: FieldValue.increment(prompt_tokens),
            total_completion_tokens: FieldValue.increment(completion_tokens),
          },
          { merge: true },
        );
        return batch
          .commit()
          .then(() => responseData.choices[0].message.content);
      } else {
        logger.error('OpenAI API returned an error');
        throw new Error('OpenAI API returned an error');
      }
    } catch (error) {
      // Handle errors
      logger.error('Error generating content:', error);
      throw new Error(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        'OpenAI API returned an error: ' + (error as any).message,
      );
    }
  },
);

export const streamOpenAI = onRequest(
  {
    cors: true,
    secrets: [openAIKey],
    cpu: 4,
    memory: '16GiB',
    timeoutSeconds: 3600,
  },
  async (req, res) => {
    if (req.method === 'OPTIONS') {
      res.set('Access-Control-Allow-Methods', 'POST');
      res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
      res.set('Access-Control-Max-Age', '3600');
      res.status(204).send('');
      return;
    }

    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).send('Unauthorized');
      return;
    }
    const idToken = authHeader.split('Bearer ')[1];

    let decodedToken;
    try {
      decodedToken = await admin.auth().verifyIdToken(idToken);
    } catch (error) {
      logger.error('Error verifying token:', error);
      res.status(401).send('Unauthorized');
      return;
    }

    const { uid } = decodedToken;
    try {
      logger.info('Streaming content for user:', uid);
      await admin.auth().getUser(uid);
    } catch (error) {
      logger.error('Error getting user:', error);
      res.status(401).send('Unauthorized');
      return;
    }

    const { type, prompt } = req.body;
    if (!type || !prompt) {
      res
        .status(400)
        .send('The function must be called with `type` and `prompt`.');
      return;
    }

    if (!['compose', 'simplify'].includes(type)) {
      res
        .status(400)
        .send('The `type` must be either "compose" or "simplify".');
      return;
    }

    const params: OpenAI.Chat.ChatCompletionCreateParamsStreaming = {
      model: 'gpt-4o',
      messages: [
        {
          role: 'system',
          content: prompt,
        },
      ],
      stream: true,
    };

    if (type === 'compose') {
      params.temperature = 0.7;
      params.max_tokens = 16384;
    } else if (type === 'simplify') {
      params.temperature = 0.3;
      params.top_p = 1.0;
      params.max_tokens = 16384;
      params.frequency_penalty = 0.0;
      params.presence_penalty = 0.0;
    }
    params.stream_options = { include_usage: true };
    try {
      const db = admin.firestore();
      const batch = db.batch();

      const openai = invokeOpenAI();
      const stream = await openai.chat.completions.create(params);

      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');

      for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content || '';
        if (content) {
          res.write(`data: ${JSON.stringify({ content })}\n\n`);

          if (chunk.usage && uid) {
            const { total_tokens, prompt_tokens, completion_tokens } =
              chunk.usage;

            // Log the response in Firestore
            const usageDoc = db.doc(`users/${uid}/conversations/usage`);
            const logDoc = db.collection(`users/${uid}/conversations`).doc();
            const batch = db.batch();

            batch.create(logDoc, chunk);
            batch.set(
              usageDoc,
              {
                total_tokens: FieldValue.increment(total_tokens),
                total_prompt_tokens: FieldValue.increment(prompt_tokens),
                total_completion_tokens:
                  FieldValue.increment(completion_tokens),
              },
              { merge: true },
            );
          }
        }
      }

      res.end();
      // Commit the batch to Firestore
      await batch.commit();
    } catch (error) {
      logger.error('Error generating content:', error);

      res
        .status(500)
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .send('OpenAI API returned an error: ' + (error as any).message);
    }
  },
);

// Function to generate content using OpenAI and log to Firestore
export async function generateContentFromOpenAI(
  openai: OpenAI,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  auth: any,
  prompt: string,
  type: string,
) {
  // Define base parameters
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const params: any = {
    model: 'gpt-4o', // Adjust model name as necessary
    messages: [
      {
        role: 'system',
        content: prompt,
      },
    ],
  };

  // Adjust parameters based on the type of request
  if (type === 'compose') {
    Object.assign(params, {
      temperature: 0.7,
      max_completion_tokens: 16384,
    });
  } else if (type === 'simplify') {
    Object.assign(params, {
      temperature: 0.3,
      top_p: 1.0,
      max_completion_tokens: 16384,
      frequency_penalty: 0.0,
      presence_penalty: 0.0,
    });
  }

  try {
    // Send request to OpenAI API
    const response = await openai.chat.completions.create(params);

    // Extract usage data
    if (response.usage) {
      const { total_tokens, prompt_tokens, completion_tokens } = response.usage;

      // Log the response in Firestore
      const db = admin.firestore();
      const usageDoc = db.doc(`users/${auth.uid}/conversations/usage`);
      const logDoc = db.collection(`users/${auth.uid}/conversations`).doc();
      const batch = db.batch();

      batch.create(logDoc, response);
      batch.set(
        usageDoc,
        {
          total_tokens: FieldValue.increment(total_tokens),
          total_prompt_tokens: FieldValue.increment(prompt_tokens),
          total_completion_tokens: FieldValue.increment(completion_tokens),
        },
        { merge: true },
      );

      // Commit the batch to Firestore
      await batch.commit();
    }

    // Return the generated content
    return response.choices[0].message.content; // Adjusted to access the content correctly
  } catch (error) {
    // Handle errors
    logger.error('Error generating content:', error);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    throw new Error('OpenAI API returned an error: ' + (error as any).message);
  }
}
