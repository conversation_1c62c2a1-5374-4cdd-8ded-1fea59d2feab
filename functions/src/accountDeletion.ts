import { onSchedule } from 'firebase-functions/v2/scheduler';
import <PERSON><PERSON> from 'stripe';
import * as logger from 'firebase-functions/logger';
import * as admin from 'firebase-admin';
import {
  sendAccountDeletionRequestEmail,
  sendNoticeToAccountsToBeDeletedEmail,
  sendSuccessAccountDeletionEmail,
} from './subscriptions/email';
import { onDocumentUpdated } from 'firebase-functions/v2/firestore';
import { cancelAllSubscriptions } from './stripe/subscriptions';
import { defineSecret } from 'firebase-functions/params';

// Document path for user data in Firestore
const userDoc = 'users/{userId}';
const stripeKey = defineSecret('firestore-stripe-payments-STRIPE_API_KEY');

/**
 * invokes stripe
 */
let stripeInstance: Stripe | null = null;

function getStripeInstance(): Stripe {
  if (!stripeInstance) {
    try {
      stripeInstance = new Stripe(stripeKey.value(), {
        apiVersion: '2023-08-16',
        timeout: 10000, // 10 second timeout
      });
    } catch (error) {
      logger.error('Failed to initialize Stripe:', error);
      throw error;
    }
  }
  return stripeInstance;
}

/**
 * Sends an email to the user when they request account deletion and updates the user document.
 * @param change - The Firestore document snapshot change.
 * @returns A promise resolved with void.
 */
export const sendAccountDeletionEmail = onDocumentUpdated(
  userDoc,
  async (change): Promise<void> => {
    const beforeData = change.data?.before?.data();
    const afterData = change.data?.after?.data();

    // Check if the accountDeletionRequestedAt property was added
    if (
      !beforeData?.accountDeletionRequestedAt &&
      afterData?.accountDeletionRequestedAt
    ) {
      const userRef = change.data?.after?.ref;
      const userId = userRef?.id || afterData?.id; // Assuming the user document has an id field that matches the Firebase Auth UID

      try {
        // Fetch user data from Firebase Authentication
        const userRecord = await admin.auth().getUser(userId);
        const userEmail = userRecord.email as string; // User's email from Firebase Auth
        const displayName = userRecord.displayName || 'User'; // User's display name or a fallback

        const deletionRequestDate = new Date(
          afterData?.accountDeletionRequestedAt,
        ).toLocaleDateString();

        // Calculate the 30-day and 90-day deletion timeline dates
        const deletionDate30Days = new Date(
          afterData?.accountDeletionRequestedAt + 30 * 24 * 60 * 60 * 1000,
        ).toLocaleDateString();
        // const deletionDate90Days = new Date(
        //   afterData?.accountDeletionRequestedAt + 90 * 24 * 60 * 60 * 1000,
        // ).toLocaleDateString();

        // Send the email
        await sendAccountDeletionRequestEmail(
          userEmail,
          displayName,
          deletionRequestDate,
          deletionDate30Days,
        );
        logger.info(`Deletion request email sent to ${userEmail}`);

        // Update the user document with the actual deletion date
        await userRef?.update({
          accountDeletionScheduledDate:
            afterData?.accountDeletionRequestedAt + 30 * 24 * 60 * 60 * 1000, // 30 days from request
        });
        logger.info(
          `User document updated with deletion date for user: ${userId}`,
        );
      } catch (error) {
        logger.error('Error processing account deletion request:', error);
      }
    }
  },
);

/**
 * Deletes user documents whose accountDeletionScheduledDate has passed.
 * @returns A promise resolved with void.
 */
export const deleteExpiredAccounts = onSchedule(
  {
    schedule: 'every day 00:00',
    secrets: [stripeKey],
    cpu: 4,
    memory: '16GiB',
    timeoutSeconds: 3600,
  },
  async (): Promise<void> => {
    const now = Date.now(); // Current time in milliseconds
    const usersRef = admin.firestore().collection('users');

    try {
      const snapshot = await usersRef
        .where('accountDeletionScheduledDate', '<=', now)
        .get();

      if (snapshot.empty) {
        logger.info('No expired accounts found for deletion.');
        return;
      }

      const stripe = getStripeInstance();

      // Loop through each user document and delete the user from Firestore and Firebase Auth
      for (const doc of snapshot.docs) {
        const userId = doc.ref.id; // The document reference
        try {
          // Fetch user data from Firebase Authentication
          const userRecord = await admin.auth().getUser(userId);
          const userEmail = userRecord.email as string; // User's email from Firebase Auth
          const displayName = userRecord.displayName || 'User'; // User's display name or a fallback
          const data = doc.data();
          const customerId = data.stripeId;

          // Send a confirmation email to the user
          await sendSuccessAccountDeletionEmail(userEmail, displayName);

          // Delete the user from Firebase Authentication
          await admin.auth().deleteUser(userId);
          logger.info(
            `User ${userId} successfully deleted from Firebase Authentication.`,
          );

          if (customerId) {
            await cancelAllSubscriptions(stripe, customerId);
          }
        } catch (error) {
          logger.error(`Error during deletion of user ${userId}:`, error);
        }
      }

      logger.info('Expired accounts deletion process completed successfully.');
    } catch (error) {
      logger.error(
        'Error during retrieval or deletion of expired accounts:',
        error,
      );
    }
  },
);

export const sendNoticeBeforeDeletion = onSchedule(
  'every day 00:00',
  async (): Promise<void> => {
    try {
      const usersRef = admin.firestore().collection('users');
      const now = Date.now();
      const daysToCheck = [5, 4, 3];

      // Process each day in parallel
      await Promise.all(
        daysToCheck.map(async (days) => {
          const remainingDaysInMs = days * 24 * 60 * 60 * 1000;
          const windowStart = now + remainingDaysInMs;
          const windowEnd = windowStart + 24 * 60 * 60 * 1000;

          const snapshot = await usersRef
            .where('accountDeletionScheduledDate', '>=', windowStart)
            .where('accountDeletionScheduledDate', '<', windowEnd)
            .get();

          if (snapshot.empty) {
            logger.info(`No accounts to be deleted in the next ${days} days.`);
            return;
          }
          const noticeIsSent: string[] = [];
          // Process users in parallel
          await Promise.all(
            snapshot.docs.map(async (doc) => {
              const userId = doc.ref.id;
              try {
                const userRecord = await admin.auth().getUser(userId);
                const userEmail = userRecord.email as string;
                const displayName = userRecord.displayName || 'User';
                if (!noticeIsSent.includes(userEmail)) {
                  await sendNoticeToAccountsToBeDeletedEmail(
                    days,
                    userEmail,
                    displayName,
                  );
                }

                noticeIsSent.push(userEmail);
                logger.info(
                  `User ${userId} successfully sent notice to be deleted.`,
                );
              } catch (error) {
                logger.error(
                  `Error sending deletion notice to user ${userId}:`,
                  error,
                );
              }
            }),
          );
        }),
      );

      logger.info('Account deletion notices sent successfully.');
    } catch (error) {
      logger.error('Error in sendNoticeBeforeDeletion:', error);
    }
  },
);
