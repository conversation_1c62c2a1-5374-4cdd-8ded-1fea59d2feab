import { onCall, onRequest, HttpsError } from 'firebase-functions/v2/https';
import * as logger from 'firebase-functions/logger';
import { defineSecret } from 'firebase-functions/params';
import { paypalApiURL, requireAuth } from './utils';
import {
  addUserSubscriptions,
  removeOtherSubscriptions,
} from './paypal/subscription';
import {
  cancelSubscriptionById,
  paypalApiCall,
  paypalAuthURL,
  paypalClientID,
  paypalClientSecret,
} from './paypal/api';

const paypalWebhookID = defineSecret('PAYPAL_WEBHOOK_ID');
const paypalVerifyURL = `${paypalApiURL}/v1/notifications/verify-webhook-signature`;

export const paypalCustomSubscription = onRequest(
  {
    cors: true,
    secrets: [paypalClientID, paypalClientSecret, paypalWebhookID],
  },
  async (req, res) => {
    const payload = req.body;
    const transmissionID = req.headers['paypal-transmission-id'];
    const transmissionTime = req.headers['paypal-transmission-time'];
    const certURL = req.headers['paypal-cert-url'];
    const authAlgo = req.headers['paypal-auth-algo'];
    const transmissionSig = req.headers['paypal-transmission-sig'];
    let accessToken;

    const body = {
      webhook_id: paypalWebhookID.value(),
      transmission_id: transmissionID,
      transmission_time: transmissionTime,
      cert_url: certURL,
      auth_algo: authAlgo,
      transmission_sig: transmissionSig,
      webhook_event: payload,
    };

    if (!paypalWebhookID.value() || !paypalVerifyURL) {
      const errorParams =
        'Paypal Webhook Error: webhookID or verifyURL was found to be empty';
      logger.error(errorParams);
      res.status(400).send(errorParams);
    }

    try {
      accessToken = await generateAccessToken();
    } catch (err) {
      const errorConstructEvent =
        'Paypal Webhook Error in generateAccessToken: ';
      logger.error(errorConstructEvent, err);
      if (err instanceof Error)
        res.status(400).send(`${errorConstructEvent} ${err.message}`);
    }

    try {
      // let Paypal verify if this payload was actually sent by them
      const response = await fetch(paypalVerifyURL, {
        method: 'POST',
        body: JSON.stringify(body),
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
      });
      const responseJson = await response.json();
      if (responseJson.verification_status !== 'SUCCESS') {
        const errorConstructEvent =
          'Paypal Webhook Error, payload verification failure.';
        logger.error(
          'Paypal Webhook Error, payload verification failure.',
          errorConstructEvent,
        );
        res.status(400).send(errorConstructEvent);
      }
    } catch (err) {
      const errorConstructEvent = 'Paypal Webhook Error in verify: ';
      logger.error(errorConstructEvent, err);
      if (err instanceof Error)
        res.status(400).send(`${errorConstructEvent} ${err.message}`);
    }
    logger.info('Event called', payload.event_type);
    try {
      // Handle the event
      switch (payload.event_type) {
        case 'BILLING.SUBSCRIPTION.CREATED': {
          logger.info('BILLING.SUBSCRIPTION.CREATED', payload);
          break;
        }
        case 'BILLING.SUBSCRIPTION.CANCELLED': {
          logger.info('BILLING.SUBSCRIPTION.CANCELLED', payload);
          await removeOtherSubscriptions(payload.resource);
          res.status(200).send({
            success: 'User is unsubscribed.',
          });
          break;
        }
        case 'BILLING.SUBSCRIPTION.ACTIVATED': {
          logger.info('BILLING.SUBSCRIPTION.ACTIVATED', payload);

          await addUserSubscriptions(payload.resource);
          res.status(200).send({
            success: 'User is subscribed.',
          });
          break;
        }
        case 'BILLING.SUBSCRIPTION.RE-ACTIVATED': {
          logger.info('BILLING.SUBSCRIPTION.RE-ACTIVATED', payload);
          break;
        }
        default: {
          logger.info('Webhook Not supported.');
          res.status(400).send('Webhook Not supported.');
        }
      }
    } catch (err) {
      const errorConstructEvent =
        'Webhook handler failed. View function logs in Firebase.';
      logger.error(errorConstructEvent, err);
      if (err instanceof Error)
        res.status(400).send(`${errorConstructEvent} ${err.message}`);
    }
    res.status(200).send();
    return;
  },
);

/**
 * Access token is used to authenticate all REST API requests to Paypal
 * This function is taken from https://developer.paypal.com/docs/checkout/advanced/integrate/#link-generateclienttoken
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
async function generateAccessToken(): Promise<any> {
  const data = await paypalApiCall(
    paypalAuthURL,
    'POST',
    'grant_type=client_credentials',
  );
  return data.access_token;
}

/**
 * Cancel paypal subscription through api call
 */
export const cancelPaypalSubscription = onCall(
  {
    cors: true,
    secrets: [paypalClientID, paypalClientSecret, paypalWebhookID],
  },
  async (request) => {
    const { auth, data } = request;
    if (!auth) {
      throw new HttpsError('unauthenticated', 'Authentication required');
    }
    requireAuth(auth);
    try {
      // cancel paypal subscription by id
      return await cancelSubscriptionById(data.subscription);
    } catch (error) {
      throw new HttpsError('unknown', 'Error cancelling the subscription.');
      logger.error('Error cancelling the subscription:', error);
      return false;
    }
  },
);
