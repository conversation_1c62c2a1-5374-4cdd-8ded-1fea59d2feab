import { defineSecret } from 'firebase-functions/params';
import * as logger from 'firebase-functions/logger';
import { onCall, HttpsError } from 'firebase-functions/v2/https';

// Define your Amplitude API key as a secret
const AMPLITUDE_API_KEY = defineSecret('AMPLITUDE_API_KEY');

// Amplitude HTTP API endpoint
const AMPLITUDE_API_URL = 'https://api2.amplitude.com/2/httpapi';

export const recordAmplitudeEvent = onCall(
  {
    cors: true,
    secrets: [AMPLITUDE_API_KEY],
  },
  async (request) => {
    const { data, auth } = request;

    // Check for authentication
    if (!auth) {
      throw new HttpsError('unauthenticated', 'Authentication required');
    }

    const { user_id, event_type, event_properties } = data;

    // eslint-disable-next-line @typescript-eslint/naming-convention,camelcase
    if (!user_id || !event_type) {
      logger.error('user_id and event_type are required');
      throw new HttpsError(
        'invalid-argument',
        'user_id and event_type are required',
      );
    }

    try {
      // Prepare the data to send to Amplitude
      const data = {
        api_key: AMPLITUDE_API_KEY.value(), // Access the secret value
        events: [
          {
            user_id,
            event_type,
            event_properties,
          },
        ],
      };

      // Send the event data to Amplitude
      const response = await fetch(AMPLITUDE_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        logger.error(`Error recording event: ${response.statusText}`);
        throw new HttpsError(
          'internal',
          `Error recording event: ${response.statusText}`,
        );
      }

      logger.info('Event recorded successfully');
      return { success: true };
    } catch (error) {
      logger.error('Error recording event:', error);
      throw new HttpsError('internal', 'Error recording event');
    }
  },
);
