import * as admin from 'firebase-admin';
import { FieldValue } from 'firebase-admin/firestore';
import {
  onDocumentCreated,
  onDocumentDeleted,
} from 'firebase-functions/v2/firestore';
import * as logger from 'firebase-functions/logger';

const bookDoc = 'books/{bookId}';

export const onBookCreate = onDocumentCreated(bookDoc, async (event) => {
  try {
    const snapshot = event.data!;
    const authorId = snapshot.data().authorId;
    const db = admin.firestore();
    await db.doc(`users/${authorId}/meta/counts`).set(
      {
        books: FieldValue.increment(1),
      },
      {
        merge: true,
      },
    );
  } catch (error) {
    logger.error(
      'Error fetching book data, updating user meta count, or updating user document:',
      error,
    );
  }
});

export const onBookDelete = onDocumentDeleted(bookDoc, async (event) => {
  try {
    const snapshot = event.data!;
    const authorId = snapshot.data().authorId;
    const db = admin.firestore();
    await db.doc(`users/${authorId}/meta/counts`).set(
      {
        books: FieldValue.increment(-1),
      },
      {
        merge: true,
      },
    );
  } catch (error) {
    logger.error(
      'Error fetching book data, updating user meta count, or updating user document:',
      error,
    );
  }
});
