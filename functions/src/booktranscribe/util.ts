import OpenAI from 'openai';
import * as fs from 'fs';
import * as logger from 'firebase-functions/logger';
import * as ffmpeg from 'fluent-ffmpeg';
import { path as ffmpegPath } from '@ffmpeg-installer/ffmpeg';
import * as path from 'path';
import { existsSync } from 'fs';
import { generateContentFromOpenAI } from '../openai';

ffmpeg.setFfmpegPath(ffmpegPath || '');

export class OpenAITranscriptionService {
  private readonly openai: OpenAI;
  private readonly chunkDuration: number;
  private readonly chunkSize: number;
  private readonly tempDir: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private readonly dataDoc: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private readonly auth: any;
  private readonly prompt: string;
  private readonly maxDurationLimit: number;
  private audioFileUrl: string;

  constructor(
    openaiLib: OpenAI,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    dataDoc: any,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    auth: any,
    prompt: string,
    maxDurationLimit: number,
    audioFileUrl: string,
  ) {
    this.openai = openaiLib;
    this.chunkSize = 24 * 1024 * 1024; // 24 MB in bytes;
    this.tempDir = '/tmp';
    this.chunkDuration = 400;
    this.dataDoc = dataDoc;
    this.auth = auth;
    this.prompt = prompt;
    this.maxDurationLimit = maxDurationLimit;
    this.audioFileUrl = audioFileUrl;
  }
  setAudioPath(newPath: string): void {
    this.audioFileUrl = newPath;
  }

  private async getAudioDuration(): Promise<number> {
    return new Promise<number>((resolve, reject) => {
      ffmpeg.ffprobe(this.audioFileUrl, (err, metadata) => {
        if (err) {
          reject(err);
        } else {
          const duration = metadata.format.duration || 0;
          logger.info(`File Duration is: ${duration}`);
          resolve(duration);
        }
      });
    });
  }

  private async splitFile(): Promise<string[]> {
    const filePath = this.audioFileUrl;
    const fileParts: string[] = [];
    const fileExtension = path.extname(filePath); // Get the file extension
    const baseName = path.basename(filePath, fileExtension); // Get the base file name

    // Get the audio duration in seconds
    const audioDuration = await this.getAudioDuration();

    // Calculate the number of chunks based on the duration of the audio
    const numberOfChunks = Math.ceil(audioDuration / this.chunkDuration);

    for (let index = 0; index < numberOfChunks; index++) {
      const chunkPath = path.join(
        this.tempDir,
        `${baseName}_part_${index}${fileExtension}`,
      );
      const startTime = index * this.chunkDuration;

      await new Promise<void>((resolve, reject) => {
        ffmpeg(filePath)
          .setFfmpegPath(ffmpegPath || '')
          .seekInput(startTime)
          .duration(this.chunkDuration)
          .output(chunkPath)
          .on('end', () => {
            fileParts.push(chunkPath);
            resolve();
          })
          .on('error', (err) => {
            console.error(`Error splitting file: ${err.message}`);
            reject(err);
          })
          .run();
      });
    }

    return fileParts;
  }
  private async generateTranscript(filePath: string): Promise<string> {
    try {
      const transcription = await this.openai.audio.transcriptions.create({
        file: fs.createReadStream(filePath),
        model: 'whisper-1',
      });
      // saving each transcriptions should there be errors
      await this.dataDoc.set(
        {
          isProcessing: true,
          transcription: transcription.text.trim(),
        },
        { merge: true },
      );
      return transcription.text;
    } catch (error) {
      let errorMsg = 'Unknown Error.';
      if (error instanceof Error) errorMsg = error.message;
      logger.error(`Error generating transcript: ${errorMsg}`);
      throw error;
    } finally {
      await this.removeFile(filePath);
    }
  }

  async checkIfTranscriptExists(): Promise<boolean> {
    await this.dataDoc.set(
      {
        isProcessing: true,
      },
      { merge: true },
    );
    const postMainSnap = await this.dataDoc.get();
    const data = postMainSnap.data()!;
    if (
      postMainSnap.exists &&
      data &&
      data.transcription &&
      data.transcription.length > 0
    ) {
      logger.info(`Transcript Exists for book chapter: ${postMainSnap.ref}`);
      await this.generateFromOpenAI(data.transcription);
      return true;
    } else {
      logger.info(
        `Transcript Does Not Exists for book chapter: ${postMainSnap.ref}`,
      );
      return false;
    }
  }
  async generateFullTranscript(): Promise<boolean> {
    const filePath = this.audioFileUrl;
    const fileStats = fs.statSync(filePath);
    let fullTranscript = '';
    logger.info(`File Size is: ${fileStats.size}`);
    await this.dataDoc.set(
      {
        isProcessing: true,
      },
      { merge: true },
    );
    const audioDuration = await this.getAudioDuration();
    if (audioDuration > this.maxDurationLimit) {
      const minutes = this.maxDurationLimit / 60;
      throw new Error(
        `The media duration exceeds the ${minutes}-minute limit. Please reduce the length to comply with the allowed limit.`,
      );
    }

    if (fileStats.size > this.chunkSize) {
      // If file size is greater than 24 MB, split the file into parts
      const fileParts = await this.splitFile();
      for (const part of fileParts) {
        const transcript = await this.generateTranscript(part);
        fullTranscript += `${transcript}\n`; // Combine transcripts with new lines in between
      }
      // Clean up temporary files after processing
      for (const part of fileParts) {
        if (existsSync(part)) fs.unlinkSync(part);
      }
      await this.dataDoc.set(
        {
          transcription: fullTranscript.trim(),
          isProcessing: true,
        },
        { merge: true },
      );
      await this.removeFile(filePath);
      await this.generateFromOpenAI(fullTranscript);
      await this.dataDoc.set({ isProcessing: false }, { merge: true });

      return true;
    } else {
      // If file size is less than or equal to 24 MB, process the whole file directly
      fullTranscript = await this.generateTranscript(filePath);
      await this.generateFromOpenAI(fullTranscript);

      return true;
    }
    return false;
  }

  private async generateFromOpenAI(fullTranscript: string): Promise<void> {
    try {
      const promptFinal: string = this.prompt.replace(
        '[transcribeText]',
        fullTranscript,
      );
      const responseOpenAI = await generateContentFromOpenAI(
        this.openai,
        this.auth,
        promptFinal,
        'compose',
      );
      await this.dataDoc.set(
        {
          transcription:
            responseOpenAI ??
            `#-error_by_manny_transcription-#: Content is null`,
          isProcessing: false,
        },
        { merge: true },
      );
    } catch (error) {
      let errorMsg = 'Unknown Error.';
      if (error instanceof Error) errorMsg = error.message;
      await this.dataDoc.set(
        {
          transcription: `#-error_by_manny_transcription-#: ${errorMsg}`,
          isProcessing: false,
        },
        { merge: true },
      );
      throw error;
      logger.error(`Error from OpenAI: ${errorMsg}`);
    }
  }

  private async removeFile(filePath: string): Promise<void> {
    try {
      await fs.promises.unlink(filePath);
      logger.info(`File removed: ${filePath}`);
    } catch (error) {
      let errorMsg = 'Unknown Error.';
      if (error instanceof Error) errorMsg = error.message;
      logger.error(`Error removing file: ${filePath}, ${errorMsg}`);
    }
  }

  generateUniqueFilename(): string {
    return `${Date.now()}_${Math.random().toString(36).substring(2, 15)}`; // Unique filename using timestamp and random string
  }
}
