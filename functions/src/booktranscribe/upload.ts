import OpenAI from 'openai/index';
import { getStorage } from 'firebase-admin/storage';
import { join } from 'path';
import { createWriteStream } from 'fs';
import * as logger from 'firebase-functions/logger';
import { OpenAITranscriptionService } from './util';
import { firestoreStorageBuckets } from '../utils';

const bucketLocation = `${firestoreStorageBuckets}/`;

export class AudioTranscriptionService {
  private readonly openai: OpenAI;
  private readonly transcriptService: OpenAITranscriptionService;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private readonly dataDoc: any;
  private readonly audioFileUrl: string;
  private readonly fileName: string;

  constructor(
    openaiLib: OpenAI,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    chapterDoc: any,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    auth: any,
    prompt: string,
    maxDurationLimit: number,
    audioFileUrl: string,
    fileName: string,
  ) {
    this.openai = openaiLib;
    this.dataDoc = chapterDoc;
    this.audioFileUrl = audioFileUrl;
    this.fileName = fileName;
    this.transcriptService = new OpenAITranscriptionService(
      this.openai,
      this.dataDoc,
      auth,
      prompt,
      maxDurationLimit,
      audioFileUrl,
    );
  }

  async downloadAndTranscribe(): Promise<boolean> {
    const isExists = await this.transcriptService.checkIfTranscriptExists();
    if (isExists === true) {
      return true;
    }

    // Fetch the audio file from Firebase Storage
    const bucket = getStorage().bucket(bucketLocation);

    // Create a reference to the file in Firebase Storage
    const file = bucket.file(this.audioFileUrl);

    // Define a temporary path for the downloaded audio using the current timestamp
    const outputDir = '/tmp';
    const audioPath = join(outputDir, this.fileName); // Name the file using the timestamp

    // Function to download the file from Firebase Storage to the local file system
    const downloadFile = async () => {
      try {
        // Create a write stream for the audio file
        const writeStream = createWriteStream(audioPath);

        // Create a read stream from the Firebase Storage file and pipe it to the write stream
        await new Promise<void>((resolve, reject) => {
          file
            .createReadStream()
            .pipe(writeStream)
            .on('finish', resolve)
            .on('error', reject);
        });

        logger.info(`Download finished for ${this.audioFileUrl}`);
      } catch (error) {
        let errorMsg = 'Unknown Error.';
        if (error instanceof Error) errorMsg = error.message;
        logger.error(`Error downloading the file: ${errorMsg}`);
        throw error;
        return false;
      }
    };

    // Call the function to execute the file download
    await downloadFile();

    // Use OpenAIService to generate transcript
    this.transcriptService.setAudioPath(audioPath);
    const transcript = await this.transcriptService.generateFullTranscript();

    logger.info(`Transcript generated for ${this.audioFileUrl}`);
    return transcript;
  }
}
