import { onCall, onRequest, HttpsError } from 'firebase-functions/v2/https';
import Stripe from 'stripe';
import * as logger from 'firebase-functions/logger';
import { defineSecret } from 'firebase-functions/params';
import {
  getUserByStripeId,
  removeStripeRoleFromCustomClaim,
} from './stripe/auth';
import { UserObject } from './subscriptions/types/auth';
import {
  addInvoiceRecord,
  addUserPaymentIntent,
  addUserSubscriptions,
  cancelAllSubscriptions,
  getStripeActiveSubcscriptions,
  isMannySubscription,
  sendUpdateSubscriptionEmailToSupport,
  updateUserSubscriptionDocument,
  updateUserSubscriptionStatus,
} from './stripe/subscriptions';
import { requireAuth } from './utils';
import { canCreateUserDocument } from './subscriptions/auth';

const stripeKey = defineSecret('firestore-stripe-payments-STRIPE_API_KEY');
const signingKey = defineSecret('STRIPE_WEBHOOK_SECRET');

/**
 * invokes stripe
 */
function invokeStripe(): Stripe {
  // invoke stripe
  return new Stripe(stripeKey.value(), {
    apiVersion: '2023-08-16',
  });
}

/**
 * Creates and/or subscribes the user
 */
export const stripeCustomSubscription = onRequest(
  {
    cors: true,
    secrets: [stripeKey, signingKey],
  },
  async (request, response) => {
    // invoke stripe
    const stripe = invokeStripe();

    let event: Stripe.Event;
    const sig: string = request.headers['stripe-signature'] as string;

    // create an event
    try {
      event = stripe.webhooks.constructEvent(
        request.rawBody,
        sig,
        signingKey.value(),
      );
    } catch (error) {
      logger.error('Error in event construct');
      response.status(401).send('Error in event construct');
      return;
    }

    logger.info('Event called', event.type);
    try {
      // Handle the event
      switch (event.type) {
        case 'customer.subscription.created': {
          const subscription = event.data.object as Stripe.Subscription;
          const currentSubscription = await stripe.subscriptions.retrieve(
            subscription.id,
            {
              expand: ['default_payment_method', 'items.data.price.product'],
            },
          );
          const price: Stripe.Price = currentSubscription.items.data[0].price;
          if (isMannySubscription(price.product as Stripe.Product)) {
            logger.info('customer.subscription.created called', subscription);
            const createDoc: UserObject = await canCreateUserDocument(
              subscription.customer as string,
              stripe,
            );
            if (createDoc.id) {
              await addUserSubscriptions(
                createDoc.id,
                subscription.id,
                stripe,
                subscription,
                currentSubscription,
              );
              logger.info('User is subscribed', createDoc);
              response.json({
                success: 'User is subscribed.',
                ...createDoc,
              });
            } else {
              response.json({ receive: true });
            }
          } else {
            response.json({
              error:
                'Product is not supported by Manny. Use product metadata mannySubscription = true',
            });
          }
          break;
        }
        case 'payment_intent.succeeded': {
          try {
            const paymentIntent: Stripe.PaymentIntent = event.data
              .object as Stripe.PaymentIntent;

            // checkout sessions
            const checkoutSessions = await stripe.checkout.sessions.list({
              payment_intent: paymentIntent.id,
              expand: ['data.line_items'],
            });

            const price = checkoutSessions.data[0]?.line_items?.data[0]
              ?.price as Stripe.Price;
            const product: Stripe.Product = (await stripe.products.retrieve(
              price.product as string,
            )) as Stripe.Product;
            if (isMannySubscription(product)) {
              logger.info('payment_intent.succeeded called');
              const createDoc: UserObject = await canCreateUserDocument(
                paymentIntent.customer as string,
                stripe,
              );
              if (createDoc.id) {
                await addUserPaymentIntent(createDoc.id, paymentIntent, stripe);
                logger.info('User is subscribed', createDoc);
                response.json({
                  success: 'User is subscribed.',
                  ...createDoc,
                });
              } else {
                response.json({ receive: true });
              }
            } else {
              response.json({
                error:
                  'Product is not supported by Manny. Use product metadata mannySubscription = true',
              });
            }
          } catch (err) {
            logger.error('Error in payment intent:', err);
            response.json({
              error:
                'Product is not supported by Manny. Use product metadata mannySubscription = true',
            });
          }
          break;
        }
        case 'invoice.payment_succeeded': {
          try {
            const invoice = event.data.object as Stripe.Invoice;
            const subscription = await stripe.subscriptions.retrieve(
              invoice.subscription as string,
            );
            const price: Stripe.Price = subscription.items.data[0].price;
            const product: Stripe.Product = (await stripe.products.retrieve(
              price.product as string,
            )) as Stripe.Product;
            if (isMannySubscription(product)) {
              logger.info(`${event.type} called`);
              const createDoc: UserObject = await canCreateUserDocument(
                invoice.customer as string,
                stripe,
              );
              if (createDoc.id) {
                logger.info('User is subscribed', createDoc);
                await sendUpdateSubscriptionEmailToSupport(
                  createDoc.id,
                  price,
                  product,
                  invoice,
                  stripe,
                );
                await addInvoiceRecord(createDoc.id, invoice);
                response.json({
                  success: 'Invoice for the user is created.',
                  ...createDoc,
                });
              } else {
                response.json({ receive: true });
              }
            } else {
              response.json({
                error:
                  'Product is not supported by Manny. Use product metadata mannySubscription = true',
              });
            }
          } catch (err) {
            logger.error('Error in invoice paid:', err);
            response.json({ receive: true });
          }
          break;
        }
        case 'customer.subscription.updated': {
          const subscriptionItem = event.data.object as Stripe.Subscription;
          const subscription = await stripe.subscriptions.retrieve(
            subscriptionItem.id,
          );
          const userId = await getUserByStripeId(
            subscription.customer as string,
          );
          if (!userId) {
            response.json({
              error: `User could not be found ${subscription.customer}`,
            });
          }
          const price: Stripe.Price = subscription.items.data[0].price;
          const product: Stripe.Product = (await stripe.products.retrieve(
            price.product as string,
          )) as Stripe.Product;
          if (isMannySubscription(product) === true && userId) {
            await updateUserSubscriptionStatus(userId as string, subscription);
            response.json({
              success: 'User subscription is successfully updated.',
              ...subscription,
            });
          } else {
            response.json({
              error:
                'Product is not supported by Manny. Use product metadata mannySubscription = true',
            });
          }
          break;
        }
        case 'customer.subscription.deleted': {
          logger.info(`${event.type} called`);
          const subscription = event.data.object as Stripe.Subscription;
          if (
            [
              'canceled',
              'incomplete_expired',
              'past_due',
              'unpaid',
              'paused',
            ].includes(subscription.status)
          ) {
            logger.info(`subscription status called ${subscription.status}`);
            logger.info(`cancel the subscription ${subscription.id}`);
            const CustomerId: string = subscription.customer as string;
            const customer: Stripe.Customer = (await stripe.customers.retrieve(
              CustomerId,
            )) as Stripe.Customer;
            if (customer) {
              logger.info(`customer found?`, customer);
              await removeStripeRoleFromCustomClaim(
                customer.email as string,
                subscription.id,
                stripe,
              );
            }
          }
          response.json({
            success: 'User subscription is successfully removed.',
          });
          break;
        }
        case 'invoice.payment_failed':
        case 'invoice.marked_uncollectible': {
          logger.info(`${event.type} called`);
          const invoice = event.data.object as Stripe.Invoice;
          if (invoice.subscription) {
            await removeStripeRoleFromCustomClaim(
              invoice.customer_email as string,
              invoice.subscription as string,
              stripe,
            );
          }
          response.json({
            success: 'User subscription is successfully removed.',
          });
          break;
        }
        default: {
          logger.error('Webhook Not supported.');
          response.json({
            error: 'Webhook Not supported.',
          });
        }
      }
    } catch (err) {
      logger.error('Error webhook integration in stripe:', err);
      response.json({
        error: 'Webhook handler failed. View function logs in Firebase.',
      });
    }
    return;
  },
);

/**
 * Cancel stripe subscription through api call
 */
export const cancelStripeSubscription = onCall(
  {
    cors: true,
    secrets: [stripeKey],
  },
  async (request) => {
    const { auth, data } = request;
    if (!auth) {
      throw new HttpsError('unauthenticated', 'Authentication required');
    }
    requireAuth(auth);
    try {
      const customerId = data.subscription;
      const stripe = invokeStripe();

      await cancelAllSubscriptions(stripe, customerId);

      return true;
    } catch (error) {
      throw new HttpsError('unknown', 'Error cancelling the subscription.');
      logger.error('Error cancelling the subscription:', error);
      return false;
    }
  },
);

/**
 * Get the number of paid subscription invoices
 */
export const getSubscriptionInvoicesPaid = onCall(
  {
    cors: true,
    secrets: [stripeKey],
  },
  async (request) => {
    const { auth, data } = request;
    if (!auth) {
      throw new HttpsError('unauthenticated', 'Authentication required');
    }
    requireAuth(auth);
    try {
      // invoke stripe
      const stripe = invokeStripe();

      const invoices = await stripe.invoices.list({
        subscription: data.subscription,
        status: 'paid',
      });
      logger.info('Does it has invoices?', invoices.data);
      return invoices.data.length;
    } catch (error) {
      logger.error('Error getting the subscription invoices:', error);
      return 0;
    }
  },
);

export async function verifyIfUserIsSubscribed(
  customerId: string,
  userId: string,
): Promise<{ customer: Stripe.Customer; status: boolean }> {
  // invoke stripe
  const stripe = invokeStripe();

  const stripeSubscriptions = await getStripeActiveSubcscriptions(
    stripe,
    customerId,
  );

  const customer = (await stripe.customers.retrieve(
    customerId,
  )) as Stripe.Customer;

  if (!stripeSubscriptions) {
    return { customer: customer, status: false };
  }

  for (const subscription of stripeSubscriptions.data) {
    const price: Stripe.Price = subscription.items.data[0].price;
    let pid = price.product;
    if (typeof pid !== 'string' && pid.id) {
      pid = pid.id;
    }
    const product: Stripe.Product = await stripe.products.retrieve(
      pid as string,
    );

    // Check if it's a valid subscription
    if (
      isMannySubscription(product) &&
      ['active', 'trialing'].includes(subscription.status)
    ) {
      await updateUserSubscriptionDocument(subscription, userId);
      return { customer: customer, status: true }; // Return true as soon as we find a valid subscription
    }
  }

  // If no valid subscription was found, return false
  return { customer: customer, status: false };
}
