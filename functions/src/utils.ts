import { HttpsError } from 'firebase-functions/v2/https';
import type { CallableRequest } from 'firebase-functions/v2/https';
import { defineSecret } from 'firebase-functions/params';
import OpenAI from 'openai';
import { google } from 'googleapis';
import * as logger from 'firebase-functions/logger';

export const openAIKey = defineSecret('OPENAI_API_KEY');
export const googleServiceAccountEmail = defineSecret(
  'GOOGLE_SERVICE_ACCOUNT_EMAIL',
);
export const googleServiceAccountKey = defineSecret(
  'GOOGLE_SERVICE_ACCOUNT_KEY',
);

// Function to authenticate with Google Drive
export async function getGoogleSheetClient() {
  try {
    // Authenticate using JWT
    const auth = new google.auth.JWT({
      email: googleServiceAccountEmail.value(),
      key: googleServiceAccountKey.value().replace(/\\n/gm, '\n'),
      scopes: ['https://www.googleapis.com/auth/spreadsheets'],
    });

    return auth;
  } catch (error) {
    logger.error('Error authenticating with Google Drive:', error);
    throw new Error('Failed to authenticate with Google Drive.');
  }
}

/**
 * invokes OpenAI
 */
export function invokeOpenAI(): OpenAI {
  // invoke OpenAI
  return new OpenAI({
    apiKey: openAIKey.value(),
  });
}

// Refactored requireAuth function
export function requireAuth(
  auth: CallableRequest['auth'] | undefined | null,
): CallableRequest['auth'] {
  if (!auth) {
    throw new HttpsError(
      'unauthenticated',
      'Only authenticated users can invoke this function.',
    );
  }
  return auth;
}

// Refactored requireAdmin function
export function requireAdmin(
  auth: CallableRequest['auth'],
): CallableRequest['auth'] {
  requireAuth(auth); // Ensure the user is authenticated
  if (auth && auth.token.isAdmin !== true) {
    throw new HttpsError(
      'permission-denied',
      'Only admins can invoke this function.',
    );
  }
  return auth;
}

export async function getDriveClient() {
  try {
    // Authenticate using JWT
    const auth = new google.auth.JWT({
      email: googleServiceAccountEmail.value(),
      key: googleServiceAccountKey.value().replace(/\\n/gm, '\n'),
      scopes: ['https://www.googleapis.com/auth/drive'],
    });

    return auth;
  } catch (error) {
    logger.error('Error authenticating with Google Drive:', error);
    throw new Error('Failed to authenticate with Google Drive.');
  }
}

// Configurations
export const siteURL =
  process.env.GCLOUD_PROJECT === 'manuscriptr-dev'
    ? 'https://manuscriptr-dev.firebaseapp.com'
    : 'https://bspub-3085b.firebaseapp.com';

export const paypalApiURL =
  process.env.GCLOUD_PROJECT === 'manuscriptr-dev'
    ? 'https://api-m.sandbox.paypal.com'
    : 'https://api-m.paypal.com';

export const firestoreBackupBuckets =
  process.env.GCLOUD_PROJECT === 'manuscriptr-dev' ? 'manuscriptr' : 'bspub';

export const firestoreStorageBuckets =
  process.env.GCLOUD_PROJECT === 'manuscriptr-dev'
    ? 'gs://manuscriptr-dev.appspot.com'
    : 'gs://bspub-storage';

export const gmailCredentials = {
  user: '<EMAIL>',
  pass: 'lyeicusyivktlwnj',
  // pass: 'gbQnMT9d65LV8gR*',
};

export const folderPaths = ['audiobooks', 'bookimages', 'booktranscriptions'];
