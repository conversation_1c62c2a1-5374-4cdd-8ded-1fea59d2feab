import { requireAuth } from './utils';
import * as logger from 'firebase-functions/logger';
import * as textToSpeech from '@google-cloud/text-to-speech';
import * as protos from '@google-cloud/text-to-speech/build/protos/protos';
import { HttpsError, onCall } from 'firebase-functions/v2/https';

const client = new textToSpeech.TextToSpeechLongAudioSynthesizeClient();
const clientTTS = new textToSpeech.TextToSpeechClient();

/**
 * Fetches the available voices from the Google Text-to-Speech API.
 * Filters to include only WaveNet voices.
 * @returns {Promise<any>} A promise that resolves when the voices have been fetched and processed.
 */
export const fetchVoices = onCall({ cors: true }, async (request) => {
  const { auth } = request;
  if (!auth) {
    throw new HttpsError('unauthenticated', 'Authentication required');
  }
  requireAuth(auth);

  try {
    // Fetch all available voices
    const [response] = await clientTTS.listVoices({});

    if (!response.voices) {
      return [];
    }

    // Filter voices to include only native English speakers
    const englishVoices = response.voices?.filter(
      (voice) =>
        voice.languageCodes?.some(
          (code) => code === 'en-US' || code === 'en-GB' || code === 'en-AU',
        ),
    );
    return englishVoices;
  } catch (error) {
    logger.error('Error fetching voices.', error);
    console.error('Error fetching voices:', error);
    throw new HttpsError('unknown', 'Error fetching voices.');
    return [];
  }
});

/**
 * Converts the provided text to speech using the selected voice (Long Audio).
 * Updates the audioUrl with the base64 encoded MP3 audio content.
 * @returns {Promise<string>} A promise that resolves when the text has been converted to speech.
 */
export const convertTextToSpeechLongAudio = onCall(
  { cors: true },
  async (request) => {
    const { auth, data } = request;
    if (!auth) {
      throw new HttpsError('unauthenticated', 'Authentication required');
    }
    requireAuth(auth);
    /**
     * @typedef {Object} RequestBody
     * @property {string} text - The text to be converted to speech.
     * @property {string} voiceName - The name of the selected voice.
     */

    /** @type {RequestBody} */
    const {
      text,
      voiceName,
      languageCode,
      storageLocation,
      filename,
      projectId,
    } = data;

    logger.info(
      'Params to TTS.',
      `${voiceName} : ${languageCode} : ${storageLocation} : ${filename} : ${projectId}`,
    );
    if (
      !text ||
      !voiceName ||
      !languageCode ||
      !storageLocation ||
      !filename ||
      !projectId
    ) {
      throw new HttpsError(
        'invalid-argument',
        'Bad Request: Missing text or voiceName',
      );
      return '';
    }

    try {
      // Construct the request
      const request: protos.google.cloud.texttospeech.v1.ISynthesizeLongAudioRequest =
        {
          parent: `projects/${projectId}/locations/us-central`,
          input: { text },
          voice: { languageCode, name: voiceName },
          audioConfig: {
            audioEncoding: 1,
          },
          outputGcsUri: `${storageLocation}/${filename}.wav`,
        };

      // Perform the text-to-speech conversion
      const [operation] = await client.synthesizeLongAudio(request);

      // Wait for the operation to complete
      await operation.promise();
      return 'success';
    } catch (error) {
      logger.error('Error converting text to speech.', error);
      throw new HttpsError(
        'invalid-argument',
        'Bad Request: Missing text or voiceName',
      );
      return '';
    }
  },
);

/**
 * Converts the provided text to speech using the selected voice (Short Audio).
 * Updates the audioUrl with the base64 encoded MP3 audio content.
 * @returns {Promise<string>} A promise that resolves when the text has been converted to speech.
 */
export const convertTextToSpeech = onCall({ cors: true }, async (request) => {
  const { auth, data } = request;
  if (!auth) {
    throw new HttpsError('unauthenticated', 'Authentication required');
  }
  requireAuth(auth);
  /**
   * @typedef {Object} RequestBody
   * @property {string} text - The text to be converted to speech.
   * @property {string} voiceName - The name of the selected voice.
   */

  /** @type {RequestBody} */
  const { text, voiceName, languageCode } = data;

  logger.info('Params to TTS.', `${voiceName} : ${languageCode}`);
  if (!text || !voiceName) {
    throw new HttpsError(
      'invalid-argument',
      'Bad Request: Missing text or voiceName',
    );
    return '';
  }

  try {
    // Construct the request
    const request: protos.google.cloud.texttospeech.v1.ISynthesizeSpeechRequest =
      {
        input: { text },
        voice: { languageCode, name: voiceName },
        audioConfig: {
          audioEncoding: 2,
        },
      };

    // Perform the text-to-speech conversion
    const [response] = await clientTTS.synthesizeSpeech(request);
    const audioContent: Buffer = response.audioContent as Buffer;
    if (audioContent) {
      // Convert the audio content buffer to a base64 string
      const base64Audio = audioContent.toString('base64');
      return base64Audio;
    } else {
      logger.error('Error converting text to speech.');
      throw new HttpsError('unknown', 'Error converting text to speech.');
      return '';
    }
  } catch (error) {
    logger.error('Error converting text to speech.', error);
    throw new HttpsError('unknown', 'Error converting text to speech.');
    return '';
  }
});
