import { onCall, HttpsError } from 'firebase-functions/v2/https';
import * as admin from 'firebase-admin';
import { firestoreStorageBuckets, folderPaths, requireAuth } from './utils';
import * as logger from 'firebase-functions/logger';
import { getStorage } from 'firebase-admin/storage';

const bucketLocation = `${firestoreStorageBuckets}/`;
export const deleteBookRecursively = onCall({ cors: true }, async (request) => {
  const { data, auth } = request;
  if (!auth) {
    throw new HttpsError('unauthenticated', 'Authentication required');
  }
  requireAuth(auth);

  try {
    const { bookId } = data;
    const ref = admin.firestore().collection('books').doc(bookId);
    await admin.firestore().recursiveDelete(ref);

    const bucket = getStorage().bucket(bucketLocation);

    // Process each folder path
    const deleteOperations = folderPaths.map(async (folderMainPath) => {
      const folderPath = `${auth.uid}/${folderMainPath}/${bookId}`;
      logger.info(`Deleting folder: ${folderPath}`);
      // List all files in the folder
      const [files] = await bucket.getFiles({ prefix: folderPath });

      if (files.length === 0) {
        logger.info(`No files found in the folder: ${folderPath}`);
        return;
      }

      // Delete each file in the folder
      const deletePromises = files.map((file) => file.delete());
      await Promise.all(deletePromises);

      logger.info(
        `Folder '${folderPath}' and its contents were deleted successfully.`,
      );
    });

    // Wait for all folders to be processed
    await Promise.all(deleteOperations);

    logger.info('Successfully deleted book.', bookId);
  } catch (err) {
    throw new HttpsError('unknown', 'Error deleting book.');
    logger.error('Error deleting book.', err);
  }
});
