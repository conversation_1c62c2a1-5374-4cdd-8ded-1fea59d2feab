import * as logger from 'firebase-functions/logger';
import { paypalApiURL } from '../utils';
import { defineSecret } from 'firebase-functions/params';

export const paypalClientID = defineSecret('PAYPAL_CLIENT_ID');
export const paypalClientSecret = defineSecret('PAYPAL_CLIENT_SECRET');
export const paypalAuthURL = `${paypalApiURL}/v1/oauth2/token`;
/**
 * get the bearer token auth
 */
function getAuthToken(): string {
  if (
    !paypalClientID.value() ||
    !paypalClientSecret.value() ||
    !paypalAuthURL
  ) {
    const errorParams =
      'generateAccessToken(): clientID, authURL or clientSecret was found to be empty';
    throw new Error(errorParams);
  }

  return Buffer.from(
    paypalClientID.value() + ':' + paypalClientSecret.value(),
  ).toString('base64');
}

/**
 * call paypal api
 */
export async function paypalApiCall(
  url: string,
  method?: string | null,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  params?: any | null,
) {
  const auth = getAuthToken();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const parameters: any = {
    method: method ?? 'GET',
    headers: {
      Authorization: `Basic ${auth}`,
      'Content-Type': 'application/json',
    },
  };
  if (params) {
    parameters.body = params;
  }
  const response = await fetch(url, parameters);
  return response.status !== 204 ? await response.json() : true;
}

/**
 * get paypal plan by id
 */
export async function getPlanById(planId: string) {
  try {
    const plan = await paypalApiCall(
      `${paypalApiURL}/v1/billing/plans/${planId}`,
    );
    logger.info('Paypal plan successfully retrieved.', plan);
    return plan;
  } catch (err) {
    logger.error('Paypal could not get the subscription.', err);
    throw new Error('Paypal could not get the subscription.');
  }
  return false;
}

/**
 * cancel paypal subscription by id
 */
export async function cancelSubscriptionById(subscriptionId: string) {
  try {
    const sub = await paypalApiCall(
      `${paypalApiURL}/v1/billing/subscriptions/${subscriptionId}/cancel`,
      'POST',
      JSON.stringify({ reason: 'canceled by user thru manuscriptr app.' }),
    );
    return sub.name ? false : true;
  } catch (err) {
    logger.error('Paypal could not cancel the subscription.', err);
    throw new Error('Paypal could not cancel the subscription.');
  }
  return false;
}
