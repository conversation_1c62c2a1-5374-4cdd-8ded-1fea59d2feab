import * as admin from 'firebase-admin';
import * as logger from 'firebase-functions/logger';
import {
  buildPriceItem,
  productCollections,
  subsCollections,
  userDocument,
} from '../subscriptions/collections';
import { getPlanById } from './api';
import { sendSubscriptionUpgradeEmail } from '../subscriptions/email';
import { UserRecord } from 'firebase-admin/auth';
import {
  fromDateToTimestamp,
  toNullableTimestamp,
} from '../subscriptions/utils';
import { UserObject } from '../subscriptions/types/auth';
import { createUserAndSendResetPassword } from '../subscriptions/auth';
import { sendEmailIfUserHasMultipleSubs } from '../stripe/subscriptions';

/**
 * adds user subscriptions
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function addUserSubscriptions(subscription: any) {
  try {
    const productCollection: admin.firestore.CollectionReference =
      productCollections();

    const plan = await getPlanById(subscription.plan_id);
    if (plan) {
      let productDoc;
      productDoc = productCollection.doc((plan.product_id as string).trim());
      const product = await productDoc.get();
      if (isMannySubscription(product.data()!)) {
        const createDoc: UserObject = await createUserDocumentFromPaypal(
          subscription.subscriber,
        );
        if (createDoc.id) {
          const subsCollection: admin.firestore.CollectionReference =
            subsCollections(createDoc.id);
          const userCollection: admin.firestore.DocumentReference =
            userDocument(createDoc.id);
          await updateUserPaypalId(
            userCollection,
            subscription.subscriber.payer_id,
          );

          if (plan.product_id !== plan.name) {
            productDoc = productCollection.doc((plan.name as string).trim());
          }
          const prices = [];
          prices.push(
            buildPriceItem(productDoc, (plan.description as string).trim()),
          );

          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const subscriptionItem: Record<string, any> = {
            ...subscription,
            ...getFormattedSubscription(subscription),
            status: subscription.status.toLowerCase() as string,
            product: productDoc,
            price: productDoc
              .collection('prices')
              .doc((plan.description as string).trim()),
            prices,
          };

          await subsCollection
            .doc(subscriptionItem.id as string)
            .set(subscriptionItem, { merge: true });

          if (product.data()?.metadata?.sendEmailOnSubscribe == 'true') {
            // sends email when user subscribe
            await sendEmailOnSubscription(
              plan.product_id as string,
              createDoc.id,
            );
          }

          // sends email when user has multiple subscriptions
          await sendEmailIfUserHasMultipleSubs(createDoc.id);
          logger.info('User is subscribed');
        } else {
          logger.error('User is not subscribed', createDoc);
          throw new Error('Subscription could not be processed.');
        }
      } else {
        logger.error('Not a manuscriptr product');
        throw new Error('Not a manuscriptr product');
      }
    } else {
      logger.error('Subscription could not be processed.');
      throw new Error('Subscription could not be processed.');
    }
  } catch (err) {
    logger.error('Subscription could not be processed.', err);
    throw new Error('Subscription could not be processed.');
  }
}

/**
 * removes user subscriptions
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function removeOtherSubscriptions(subscription: any) {
  try {
    const productCollection: admin.firestore.CollectionReference =
      productCollections();

    const plan = await getPlanById(subscription.plan_id);
    if (plan) {
      const productDoc = productCollection.doc(
        (plan.product_id as string).trim(),
      );
      const product = await productDoc.get();
      if (isMannySubscription(product.data()!)) {
        const { uid, customClaims } = await admin
          .auth()
          .getUserByEmail(subscription.subscriber.email_address);
        if (!uid) {
          logger.error(`User unsubscription failed`);
          throw new Error('Un-Subscription could not be processed.');
        } else {
          const subsCollection: admin.firestore.CollectionReference =
            subsCollections(uid);

          await subsCollection
            .doc(subscription.id as string)
            .set({ status: 'canceled' }, { merge: true });

          // un-assign role custom claim
          logger.info(`Un-Assign the custom claim paypalRole to ${uid}`);
          await admin.auth().setCustomUserClaims(uid as string, {
            ...customClaims,
            paidViaPaypal: false,
          });
          logger.info('User is unsubscribed');
        }
      } else {
        logger.error('Not a manuscriptr product');
        throw new Error('Not a manuscriptr product');
      }
    } else {
      logger.error('Un-Subscription could not be processed.');
      throw new Error('Un-Subscription could not be processed.');
    }
  } catch (err) {
    logger.error('Subscription could not be processed.', err);
    throw new Error('Subscription could not be processed.');
  }
}
/**
 * sends an email when user subscribes
 */
async function sendEmailOnSubscription(
  product: string,
  userId: string,
): Promise<void> {
  try {
    const user: UserRecord = await admin.auth().getUser(userId);
    await sendSubscriptionUpgradeEmail(
      user.email as string,
      user.displayName as string,
    );
    logger.info(`subscription to ${product} email is sent!`);
  } catch (err) {
    logger.error(`subscription email is not sent!`);
  }
}

/**
 * Get the formatted subscription object from number to Timestamp
 */
function getFormattedSubscription(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  subscription: any,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
): Record<string, any> {
  return {
    ...subscription,
    current_period_start: fromDateToTimestamp(subscription.start_time),
    current_period_end: fromDateToTimestamp(
      subscription.billing_info.next_billing_time,
    ),
    cancel_at: toNullableTimestamp(null),
    canceled_at: toNullableTimestamp(null),
    created: fromDateToTimestamp(subscription.create_time),
    ended_at: toNullableTimestamp(null),
    trial_end: toNullableTimestamp(null),
    trial_start: toNullableTimestamp(null),
  };
}

/**
 * updates user document paypalId
 */
async function updateUserPaypalId(
  userDoc: admin.firestore.DocumentReference,
  customerId: string,
): Promise<void> {
  await userDoc.set(
    {
      paypalId: customerId,
    },
    { merge: true },
  );
  const user = await userDoc.get();
  logger.info('User must exists with stripeId', user.data());
}

/**
 * Checks if user can be created or exists
 */
async function createUserDocumentFromPaypal(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  subscriber: any,
): Promise<UserObject> {
  const data: UserObject = {
    name:
      (`${subscriber.name.given_name} ${subscriber.name.surname}` as string) ||
      '',
    email: subscriber.email_address as string,
    paypalId: subscriber.payer_id as string,
    id: '',
    customClaims: {},
    exists: false,
  };
  // creates the user
  const createDoc: UserObject = await createUserAndSendResetPassword(data);

  // assign role custom claim
  logger.info(`Assign the custom claim paypalRole to ${createDoc.id}`);
  await admin.auth().setCustomUserClaims(createDoc.id as string, {
    ...createDoc.customClaims,
    paidViaPaypal: true,
  });
  return createDoc;
}

/**
 * Check if the product is a manny subscription
 */
function isMannySubscription(product: admin.firestore.DocumentData): boolean {
  logger.info(
    `is manny product? ${product.metadata?.mannySubscription}`,
    product,
  );
  return product.metadata?.mannySubscription == 'true' ? true : false;
}
