import * as admin from 'firebase-admin';
import Stripe from 'stripe';
import * as logger from 'firebase-functions/logger';
import {
  sendCancellationOfSubscriptionEmail,
  sendExistingSubscriptionEmail,
  sendSubscriptionUpdateEmailToSupport,
  sendSubscriptionUpgradeEmail,
} from '../subscriptions/email';
import { auth } from 'firebase-admin';
import UserRecord = auth.UserRecord;
import {
  buildPriceItem,
  invoiceCollections,
  paymentCollections,
  productCollections,
  subsCollections,
  userDocument,
} from '../subscriptions/collections';
import { toNullableTimestamp, toTimestamp } from '../subscriptions/utils';

/**
 * attach subscription to a user in compliance to . Please check {@link https://github.com/invertase/stripe-firebase-extensions/blob/9ab8170a70535137a37cd3f328ae78ed42520fff/firestore-stripe-web-sdk/src/subscription.ts stripe-firebase-extensions}.
 */
export async function addUserPaymentIntent(
  userId: string,
  paymentIntent: Stripe.PaymentIntent,
  stripe: Stripe,
  checkoutSession?: Stripe.Checkout.Session,
): Promise<void> {
  try {
    const userCollection: admin.firestore.DocumentReference =
      userDocument(userId);
    const paymentsCollection: admin.firestore.CollectionReference =
      paymentCollections(userId);
    const subsCollection: admin.firestore.CollectionReference =
      subsCollections(userId);
    const productCollection: admin.firestore.CollectionReference =
      productCollections();

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const payment: Record<string, any> = paymentIntent;
    if (checkoutSession) {
      const lineItems = await stripe.checkout.sessions.listLineItems(
        checkoutSession.id,
      );
      const prices = [];
      for (const item of lineItems.data) {
        const productDoc: admin.firestore.DocumentReference =
          productCollection.doc(item.price?.product as string);
        prices.push(buildPriceItem(productDoc, item.price?.id as string));
      }
      payment['prices'] = prices;
      payment['items'] = lineItems.data;
    }

    // Write to invoice to a subcollection on the subscription doc.
    await paymentsCollection
      .doc(paymentIntent.id)
      .set(payment, { merge: true });

    // checkout sessions
    const checkoutSessions = await stripe.checkout.sessions.list({
      payment_intent: payment.id,
      expand: ['data.line_items'],
    });

    checkoutSessions.data.forEach(function (checkoutSession) {
      checkoutSession.line_items?.data.forEach(
        function (checkoutSessionLineItem) {
          payment.priceSession = checkoutSessionLineItem.price?.id;
        },
      );
      userCollection.collection('checkout_sessions').add({
        cancel_url: checkoutSession.cancel_url,
        client: 'web',
        created: checkoutSession.created,
        mode: checkoutSession.mode,
        price: payment.priceSession,
        sessionId: checkoutSession.id,
        success_url: checkoutSession.success_url,
        url: checkoutSession.url,
      });
    });

    // subscriptions
    const userSubscriptions: admin.firestore.QuerySnapshot =
      await subsCollection
        .where('customer', '==', payment.customer)
        .where('status', 'in', ['active', 'trialing'])
        .get();

    const subscriptions = await stripe.subscriptions.list({
      status: 'all',
      customer: payment.customer as string,
    });
    const filteredSubs = subscriptions.data.filter(
      (sub) => sub.status === 'active' || sub.status === 'trialing',
    );
    const subs: Set<string> = new Set<string>();

    for (const doc of userSubscriptions.docs) {
      subs.add(doc.id);
    }
    await Promise.all(
      filteredSubs?.map(async (subscription) => {
        logger.info('has subscription?', subscription.id);
        if (!subs.has(subscription.id)) {
          await addUserSubscriptions(
            userId,
            subscription.id,
            stripe,
            subscription,
            subscription,
          );
        }
      }),
    );
    // await updateUserStripeId(
    //   userCollection,
    //   paymentIntent.livemode,
    //   paymentIntent.customer as string,
    // );
  } catch (err) {
    logger.error('Payment intent could not be processed.', err);
    throw new Error('Payment intent could not be processed.');
  }
}

/**
 * adds user subscriptions
 */
export async function addUserSubscriptions(
  userId: string,
  subscriptionId: string,
  stripe: Stripe,
  subscription: Stripe.Subscription,
  currentSubscription: Stripe.Subscription,
) {
  try {
    const productCollection: admin.firestore.CollectionReference =
      productCollections();
    const subsCollection: admin.firestore.CollectionReference =
      subsCollections(userId);
    const userCollection: admin.firestore.DocumentReference =
      userDocument(userId);

    await updateUserStripeId(
      userCollection,
      subscription.livemode,
      subscription.customer as string,
    );

    let priceValue: Stripe.Price;
    let subItems = [];
    let productDoc;
    if (!subscription.items.data.length) {
      priceValue = currentSubscription.items.data[0].price;
      subItems = currentSubscription.items.data;
      const product: Stripe.Product = priceValue.product as Stripe.Product;
      productDoc = productCollection.doc(product.id);
    } else {
      priceValue = subscription.items.data[0].price;
      subItems = subscription.items.data;
      productDoc = productCollection.doc(priceValue.product as string);
    }

    logger.info('Does sub has subItems?', subItems);
    if (!productDoc.id) {
      throw new Error('Subscription could not be processed. Product not found');
    }
    const prodExists = await productDoc.get();
    logger.info('Does sub has a product?', prodExists);
    const role = prodExists.data()!.metadata?.firebaseRole ?? null;
    const prices = [];
    for (const item of subItems) {
      prices.push(buildPriceItem(productDoc, item.price.id));
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const subscriptionItem: Record<string, any> = {
      ...getFormattedSubscription(subscription),
      metadata: subscription.metadata,
      role,
      status: subscription.status as string,
      stripeLink: `https://dashboard.stripe.com${
        subscription.livemode ? '' : '/test'
      }/subscriptions/${subscription.id}`,
      product: productDoc,
      price: productDoc.collection('prices').doc(priceValue.id),
      prices,
      quantity: subItems[0].quantity ?? null,
      items: subItems,
    };
    if (!subscriptionItem) {
      throw new Error('Subscription could not be processed.');
    }

    // set the subscription document
    await subsCollection
      .doc(subscriptionItem.id as string)
      .set(subscriptionItem, { merge: true });

    // proration set to none
    await stripe.subscriptions.update(subscription.id, {
      items: [
        {
          id: currentSubscription.items.data[0].id,
          price: priceValue.id,
        },
      ],
      proration_behavior: 'none',
    });

    // retains only one stripe subscription
    // await removeOtherSubscriptions(userId, subscription, stripe);

    // sends email when user subscribe
    await sendEmailOnSubscription(prodExists, userId);

    // sends email when user has multiple subscriptions
    await sendEmailIfUserHasMultipleSubs(userId);
  } catch (err) {
    logger.error('Subscription could not be processed.', err);
    throw new Error('Subscription could not be processed.');
  }
}

/**
 * updates the user subscription status
 */
export async function updateUserSubscriptionStatus(
  userId: string,
  subscription: Stripe.Subscription,
) {
  try {
    await updateUserSubscriptionDocument(subscription, userId);
  } catch (err) {
    logger.error('Subscription status update could not be processed.', err);
    throw new Error('Subscription status update could not be processed.');
  }
}

/**
 * Updates the user subscription collection document to ensure that the newly created subscription remain active
 */
export async function updateUserSubscriptionDocument(
  subscription: Stripe.Subscription,
  userId: string,
) {
  const subsCollection: admin.firestore.CollectionReference =
    subsCollections(userId);

  const productCollection: admin.firestore.CollectionReference =
    productCollections();

  const userCollection: admin.firestore.DocumentReference =
    userDocument(userId);

  let priceValue: Stripe.Price;
  let subItems = [];
  let productDoc;
  if (!subscription.items.data.length) {
    priceValue = subscription.items.data[0].price;
    subItems = subscription.items.data;
    const product: Stripe.Product = priceValue.product as Stripe.Product;
    productDoc = productCollection.doc(product.id);
  } else {
    priceValue = subscription.items.data[0].price;
    subItems = subscription.items.data;
    productDoc = productCollection.doc(priceValue.product as string);
  }

  await updateUserStripeId(
    userCollection,
    subscription.livemode,
    subscription.customer as string,
  );

  logger.info('Does sub has subItems?', subItems);
  if (!productDoc.id) {
    throw new Error('Subscription could not be processed. Product not found');
  }
  const prodExists = await productDoc.get();
  logger.info('Does sub has a product?', prodExists);
  const role = prodExists.data()!.metadata?.firebaseRole ?? null;
  const prices = [];
  for (const item of subItems) {
    prices.push(buildPriceItem(productDoc, item.price.id));
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const subscriptionItem: Record<string, any> = {
    ...getFormattedSubscription(subscription),
    metadata: subscription.metadata,
    role,
    status: subscription.status as string,
    stripeLink: `https://dashboard.stripe.com${
      subscription.livemode ? '' : '/test'
    }/subscriptions/${subscription.id}`,
    product: productDoc,
    price: productDoc.collection('prices').doc(priceValue.id),
    prices,
    quantity: subItems[0].quantity ?? null,
    items: subItems,
  };
  if (!subscriptionItem) {
    throw new Error('Subscription could not be processed.');
  }

  logger.info(
    `Subscription ${subscription.id} is found updating the status ${subscription.status}.`,
    subscription,
  );

  await subsCollection
    .doc(subscription.id)
    .set(subscriptionItem, { merge: true });
}

/**
 * sends an email subscription update notice to the support team based on the number of invoices paid.
 * The price metadata allowCancelWhenInvoicePaid will be used.
 */
export async function sendUpdateSubscriptionEmailToSupport(
  userId: string,
  price: Stripe.Price,
  product: Stripe.Product,
  invoice: Stripe.Invoice,
  stripe: Stripe,
): Promise<void> {
  try {
    // Check if the no. of payments allowed in cancel metadata exists
    if (price.metadata?.allowCancelWhenInvoicePaid) {
      const { displayName, email } = await admin.auth().getUser(userId);
      const invoices = await stripe.invoices.list({
        subscription: invoice.subscription as string,
        status: 'paid',
      });

      // If the no. of payments matches the metadata. Send an email to the support team to update the user subscription
      if (
        parseInt(price.metadata?.allowCancelWhenInvoicePaid) ==
        invoices.data.length
      ) {
        await sendSubscriptionUpdateEmailToSupport(
          displayName as string,
          email as string,
          invoices.data.length,
          product.name,
        );
        logger.info(
          `Update subscription to Manuscriptr 97 for ${email}, email is sent! No of payments made: ${invoices.data.length}`,
        );
      }
    }
  } catch (err) {
    logger.info(
      'Sending of email to support based on the no. of payments could not be processed.',
      invoice,
    );
    logger.info(
      'Error. Sending of email to support based on the no. of payments could not be processed.',
      err,
    );
  }
}

/**
 * adds user invoice
 */
export async function addInvoiceRecord(
  userId: string,
  invoice: Stripe.Invoice,
): Promise<void> {
  try {
    const invoicesCollection: admin.firestore.CollectionReference =
      invoiceCollections(userId, invoice.subscription as string);
    const paymentsCollection: admin.firestore.CollectionReference =
      paymentCollections(userId);
    const productCollection: admin.firestore.CollectionReference =
      productCollections();

    await invoicesCollection.doc(invoice.id).set(invoice, { merge: true });

    const prices = [];
    for (const item of invoice.lines.data) {
      const productDoc: admin.firestore.DocumentReference =
        productCollection.doc(item.price?.product as string);
      prices.push(buildPriceItem(productDoc, item.price?.id as string));
    }
    const recordId: string = (invoice.payment_intent as string) ?? invoice.id;
    await paymentsCollection.doc(recordId).set({ prices }, { merge: true });
    // await updateUserStripeId(
    //   userCollection,
    //   invoice.livemode,
    //   invoice.customer as string,
    // );
  } catch (err) {
    logger.error('Invoice could not be processed.', err);
    throw new Error('Invoice could not be processed.');
  }
}

/**
 * Get the formatted subscription object from number to Timestamp
 */
function getFormattedSubscription(
  subscription: Stripe.Subscription,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
): Record<string, any> {
  return {
    ...subscription,
    current_period_start: toTimestamp(subscription.current_period_start),
    current_period_end: toTimestamp(subscription.current_period_end),
    cancel_at: toNullableTimestamp(subscription.cancel_at),
    canceled_at: toNullableTimestamp(subscription.canceled_at),
    cancel_at_period_end: subscription.cancel_at_period_end,
    created: toTimestamp(subscription.created),
    ended_at: toNullableTimestamp(subscription.ended_at),
    trial_end: toNullableTimestamp(subscription.trial_end),
    trial_start: toNullableTimestamp(subscription.trial_start),
  };
}

/**
 * Cancels other product subscription aside from current
 */
export async function removeOtherSubscriptions(
  userId: string,
  currentSubscription: Stripe.Subscription,
  stripe: Stripe,
): Promise<void> {
  try {
    const userSubscriptions: admin.firestore.QuerySnapshot =
      await getUserActiveAndTrialingSubscriptions(userId).get();
    await Promise.all(
      userSubscriptions.docs?.map(async (doc) => {
        const userSubData = doc.data();
        const productsSnapshot = await productCollections()
          .doc(userSubData?.product.id)
          .get();
        if (
          productsSnapshot.exists &&
          productsSnapshot.data()?.metadata?.mannySubscription == 'true' &&
          doc.id.includes('sub_')
        ) {
          try {
            if (
              currentSubscription.id !== doc.id &&
              ![
                'canceled',
                'incomplete_expired',
                'past_due',
                'unpaid',
              ].includes(currentSubscription.status)
            ) {
              logger.info(`Cancel sub? ${currentSubscription.id} == ${doc.id}`);
              const cancelSubscription = await stripe.subscriptions.cancel(
                doc.id,
              );
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              const cancelSubscriptionItem: Record<string, any> = {
                ...getFormattedSubscription(cancelSubscription),
              };
              await doc.ref.set(cancelSubscriptionItem, { merge: true });
            } else if (currentSubscription.id == doc.id) {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              const currentSubscriptionItem: Record<string, any> = {
                ...getFormattedSubscription(currentSubscription),
              };
              await doc.ref.set(currentSubscriptionItem, { merge: true });
            }
          } catch (err) {
            if (['active', 'trialing'].includes(currentSubscription.status))
              await doc.ref.delete();
            logger.error(
              'Removing subscription thru stripe could not be processed.',
              err,
            );
          }
        }
      }),
    );
  } catch (err) {
    logger.error('Removing subscription could not be processed.', err);
    throw new Error('Removing subscription could not be processed.');
  }
}

/**
 * Get User Active and trialing subscriptions
 */
export function getUserActiveAndTrialingSubscriptions(userId: string) {
  const userActiveSubscriptions = subsCollections(userId).where(
    'status',
    'in',
    ['active', 'trialing'],
  );
  return userActiveSubscriptions;
}

/**
 * Get number of User Active and trialing subscriptions and sends email if it has multiple subs
 */
export async function sendEmailIfUserHasMultipleSubs(userId: string) {
  try {
    const { email, displayName } = await admin.auth().getUser(userId);

    const userSubscriptions = await getUserActiveAndTrialingSubscriptions(
      userId,
    )
      .count()
      .get();
    if (userSubscriptions.data().count > 1) {
      await sendExistingSubscriptionEmail(
        email as string,
        displayName as string,
      );
      logger.info(
        `Multiple Subscription (${
          userSubscriptions.data().count
        }) email has bent sent. User is . ${email} ${displayName}`,
      );
    }
  } catch (err) {
    logger.info('Multiple Subscription email could not be processed.', err);
  }
}

/**
 * sends an email when user subscribes depending on the product metadata sendEmailOnSubscribe
 */
async function sendEmailOnSubscription(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  product: any,
  userId: string,
): Promise<void> {
  if (product.metadata?.sendEmailOnSubscribe == 'true') {
    try {
      const user: UserRecord = await admin.auth().getUser(userId);
      await sendSubscriptionUpgradeEmail(
        user.email as string,
        user.displayName as string,
      );
      logger.info(`subscription to ${product.name} email is sent!`);
    } catch (err) {
      logger.error(`subscription email is not sent!`);
    }
  }
}

/**
 * updates user document stripeId and stripeLink
 */
async function updateUserStripeId(
  userDoc: admin.firestore.DocumentReference,
  livemode: boolean,
  customerId: string,
): Promise<void> {
  await userDoc.set(
    {
      stripeLink: `https://dashboard.stripe.com${
        livemode ? '' : '/test'
      }/customers/${customerId}`,
      stripeId: customerId,
    },
    { merge: true },
  );
  const user = await userDoc.get();
  logger.info('User must exists with stripeId', user.data());
}

/**
 * Check if the product is a manny subscription
 */
export function isMannySubscription(product: Stripe.Product): boolean {
  logger.info(`is manny product? ${product.metadata?.mannySubscription}`);
  return product.metadata?.mannySubscription == 'true' ? true : false;
}

export const cancelAllSubscriptions = async (
  stripe: Stripe,
  customerId: string,
) => {
  logger.info('Cancel subscription', customerId);

  const stripeSubscriptions = await getStripeActiveSubcscriptions(
    stripe,
    customerId,
  );

  const customer = (await stripe.customers.retrieve(
    customerId,
  )) as Stripe.Customer;

  if (!stripeSubscriptions) {
    return false;
  }
  let hasSubscription = false;
  for (const subscription of stripeSubscriptions.data) {
    const price: Stripe.Price = subscription.items.data[0].price;
    let pid = price.product;
    if (typeof pid !== 'string' && pid.id) {
      pid = pid.id;
    }
    const product: Stripe.Product = await stripe.products.retrieve(
      pid as string,
    );

    // Check if it's a valid subscription
    if (
      isMannySubscription(product) &&
      ['active', 'trialing'].includes(subscription.status)
    ) {
      logger.info('Cancel subscription', subscription.id);
      await stripe.subscriptions.cancel(subscription.id);
      hasSubscription = true;
    }
  }
  if (hasSubscription && customer.email) {
    await sendCancellationOfSubscriptionEmail(customer.email as string);
    return true;
  }
  return true;
};

/**
 * Get the active stripe subscriptions
 */
export const getStripeActiveSubcscriptions = async (
  stripe: Stripe,
  customerId: string,
) => {
  try {
    const activeSubscriptions = await stripe.subscriptions.list({
      status: 'all', // Filters only active subscriptions
      customer: customerId,
      limit: 10, // You can set a limit on the number of subscriptions to retrieve (optional)
    });
    return activeSubscriptions;
  } catch (error) {
    return null;
  }
};
