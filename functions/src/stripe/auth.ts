import * as admin from 'firebase-admin';
import * as logger from 'firebase-functions/logger';
import Stripe from 'stripe';
import {
  getUserActiveAndTrialingSubscriptions,
  isMannySubscription,
  removeOtherSubscriptions,
} from './subscriptions';

/**
 * Removes the stripeRole and isOldAuthor claims
 *
 */
export async function removeStripeRoleFromCustomClaim(
  email: string,
  subscriptionId: string,
  stripe: Stripe,
): Promise<void> {
  // TODO: Deprecate this function once the removing of claim from stripe firebase extension works
  try {
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    const price: Stripe.Price = subscription.items.data[0].price;
    const product: Stripe.Product = (await stripe.products.retrieve(
      price.product as string,
    )) as Stripe.Product;
    if (isMannySubscription(product) === true) {
      const { uid, customClaims } = await admin.auth().getUserByEmail(email);
      await removeOtherSubscriptions(uid, subscription, stripe);

      const userSubscriptions: admin.firestore.QuerySnapshot =
        await getUserActiveAndTrialingSubscriptions(uid).get();
      if (userSubscriptions.docs.length === 0) {
        logger.info(
          `No subscriptions found remove the custom claim stripeRole to ${uid}`,
        );
        await admin.auth().setCustomUserClaims(uid, {
          ...customClaims,
          stripeRole: null,
        });
        if (customClaims?.isOldAuthor == true) {
          logger.info(
            `No subscriptions found remove the custom claim isOldAuthor to ${uid}`,
          );
          await admin.auth().setCustomUserClaims(uid, {
            customClaims,
            isOldAuthor: false,
          });
        }
      }
      logger.info('Does user found?', uid);
    }
  } catch (error) {
    logger.info('User is not found. Nothing to remove claims');
  }
}

/**
 * Get User by StripeId
 */
export async function getUserByStripeId(stripeId: string) {
  const userDocs = await admin
    .firestore()
    .collection('users')
    .where('stripeId', '==', stripeId)
    .get();
  if (userDocs.docs.length !== 0) {
    return userDocs.docs[0].id;
  }
  return null;
}
