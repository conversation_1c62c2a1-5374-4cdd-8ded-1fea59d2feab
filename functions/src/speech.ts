import { onCall, HttpsError } from 'firebase-functions/v2/https';
import { defineSecret } from 'firebase-functions/params';
import { requireAuth } from './utils';

const assemblyAiKey = defineSecret('ASSEMBLYAI_API_KEY');

export const getSpeechRecognitionToken = onCall(
  {
    cors: true,
    secrets: [assemblyAiKey],
  },
  async (request) => {
    const { auth } = request;
    if (!auth) {
      throw new HttpsError('unauthenticated', 'Authentication required');
    }
    requireAuth(auth);

    const response = await fetch(
      'https://api.assemblyai.com/v2/realtime/token',
      {
        method: 'POST',
        headers: {
          Authorization: assemblyAiKey.value(),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ expires_in: 3600 }),
      },
    );
    return await response.json();
  },
);
