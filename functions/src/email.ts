import * as logger from 'firebase-functions/logger';
import * as admin from 'firebase-admin';
import * as nodemailer from 'nodemailer';
import { gmailCredentials, requireAuth } from './utils';
import { onCall, HttpsError } from 'firebase-functions/v2/https';
import { generatePasswordResetWelcomeEmail } from './subscriptions/auth';

export const sendEmail = onCall({ cors: true }, async (request) => {
  const { data, auth } = request;
  if (!auth) {
    throw new HttpsError('unauthenticated', 'Authentication required');
  }
  requireAuth(auth);
  const gmailTransport = nodemailer.createTransport({
    service: 'gmail',
    auth: {
      user: gmailCredentials.user,
      pass: gmailCredentials.pass,
    },
  });

  const msg = {
    to: data.to,
    from: data.from,
    subject: data.subject,
    text: data.text,
    html: data.html,
    cc: data.cc || undefined,
  };

  if (data.id) {
    const doc = await admin.firestore().collection('users').doc(data.id).get();
    msg.to = doc.data()?.email;
  }

  try {
    const response = await gmailTransport.sendMail(msg);
    return response;
  } catch (error) {
    logger.error(error);
    throw new HttpsError('unknown', 'Error sending email.');
    return error;
  }
});

export const getResetPasswordLink = onCall({ cors: true }, async (request) => {
  const { data, auth } = request;
  if (!auth) {
    throw new HttpsError('unauthenticated', 'Authentication required');
  }
  requireAuth(auth);

  const { userId } = data;
  if (!userId) {
    throw new HttpsError('invalid-argument', 'Bad Request: Missing arguments');
    return '';
  }
  return generatePasswordResetWelcomeEmail(userId).then((response) => {
    logger.info(`User welcome email on creation has been sent`, response);
    return response;
  });
});
