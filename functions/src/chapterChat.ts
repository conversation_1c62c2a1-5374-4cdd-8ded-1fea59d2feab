import { HttpsError, onCall } from 'firebase-functions/v2/https';
import { invokeOpenAI, openAIKey, requireAuth } from './utils';
import * as logger from 'firebase-functions/logger';
import * as admin from 'firebase-admin';
import { FieldValue } from 'firebase-admin/firestore';
import OpenAI from 'openai';
import { ThreadCreateParams } from 'openai/src/resources/beta/threads/threads';

// Function to process and store the 10,000-word chapter
async function processBookChapter(
  openai: OpenAI,
  longText: string,
  docId: string,
) {
  const assistantIdVals = docId.split('-');
  const bookOutlineDoc = `books/${assistantIdVals[0]}/outlines/${assistantIdVals[1]}`;
  logger.info('Does it exists:', bookOutlineDoc);
  const createParams: ThreadCreateParams = {
    messages: [
      {
        role: 'assistant',
        content:
          "You are an AI writing assistant that helps refine and expand book chapter content across various genres. Whether the book is fiction, non-fiction, fantasy, mystery, romance, sci-fi, or any other genre, your role is to provide relevant insights, ideas, and clarifications strictly based on the provided chapter content. You may offer creative suggestions that align with the material but must not fabricate information unrelated to the text. If a question is outside the scope of the chapter, politely redirect the writer back to relevant content. Maintain a constructive and engaging tone, ensuring coherence and depth in the chapter's development. If the user asks whether you are based on a specific AI model or platform, simply respond that you are Manny AI, a dedicated writing assistant designed to help with book chapters.",
      },
      {
        role: 'user',
        content: longText, // Provide the book chapter content separately
      },
    ],
  };
  try {
    const assistantThreadsRef = admin.firestore().doc(bookOutlineDoc);
    const doc = await assistantThreadsRef.get();
    const assistantThreads = doc.data()!;
    if (
      !assistantThreads?.assistantbot ||
      !assistantThreads?.assistantbot?.thread ||
      !assistantThreads?.assistantbot?.isUpdate
    ) {
      try {
        const myThread = await openai.beta.threads.create(createParams);
        assistantThreadsRef.set(
          {
            assistantbot: {
              thread: myThread.id,
              isUpdate: false,
              dateUpdated: FieldValue.serverTimestamp(),
            },
          },
          { merge: true },
        );
        logger.info('New thread created with ID: ', myThread.id, '\n');
        return true;
      } catch (error) {
        logger.error('Error creating thread:', error);
        throw error;
      }
    } else {
      try {
        logger.info('Fetching existing thread', assistantThreads?.assistantbot);
        await openai.beta.threads
          .del(assistantThreads?.assistantbot?.thread as string)
          .catch((error) => logger.error('Error deleting thread:', error));
        const myThread = await openai.beta.threads.create(createParams);
        assistantThreadsRef.set(
          {
            assistantbot: {
              thread: myThread.id,
              isUpdate: false,
              dateUpdated: FieldValue.serverTimestamp(),
            },
          },
          { merge: true },
        );
        logger.info('New thread updated with ID: ', myThread.id, '\n');
        return true;
      } catch (error) {
        logger.error('Error creating thread:', error);
        throw error;
      }
    }
  } catch (error) {
    logger.error('Error fetching book chapter document:', error);
    throw error;
  }
}

// Function to handle user query using the Assistant API
async function handleUserQuery(
  openai: OpenAI,
  assistantId: string,
  query: string,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  messageHistory?: any,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  openAiParams?: any,
) {
  logger.info('handleUserQuery', assistantId, query);

  const assistantIdVals = assistantId.split('-');
  const bookOutlineDoc = `books/${assistantIdVals[0]}/outlines/${assistantIdVals[1]}`;
  logger.info('Does it exists:', bookOutlineDoc);
  let threadByBookChapter = '';
  let messages = [];
  try {
    const assistantThreadsRef = admin.firestore().doc(bookOutlineDoc);
    const doc = await assistantThreadsRef.get();
    const assistantThreads = doc.data()!;
    if (
      !assistantThreads?.assistantbot ||
      !assistantThreads?.assistantbot?.thread
    ) {
      throw new Error('Error creating thread: Book chapter not found');
    } else {
      threadByBookChapter = assistantThreads?.assistantbot?.thread;
      logger.info(`Found thread: ${threadByBookChapter}`);
    }
  } catch (error) {
    logger.error('Error fetching book chapter document:', error);
    throw error;
  }
  if (!threadByBookChapter) {
    throw new Error('Error creating thread: Book chapter not found');
  }

  // Add a Message to the Thread
  try {
    const myThreadMessage = await openai.beta.threads.messages.create(
      threadByBookChapter, // Use the stored thread ID for this user
      {
        role: 'user',
        content: query,
      },
    );

    if (messageHistory) {
      // Limit message history to the last 10 messages
      const limitedMessageHistory = messageHistory?.slice(-10);
      // Ensure the message history is in the correct format
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      messages = limitedMessageHistory.map((msg: any) => ({
        role: msg.role,
        content: msg.text,
      }));
      messages.push({
        role: 'assistant',
        content: 'You are a helpful AI that remembers past interactions.',
      });
      const messageContent = myThreadMessage.content[0];
      if (messageContent.type === 'text') {
        logger.info('User Text: ', messageContent.text.value);
      } else if (messageContent.type === 'image_file') {
        // Handle image file content if needed
        logger.info('User Image File: ', messageContent.image_file.file_id);
      } else if (messageContent.type === 'image_url') {
        // Handle image file content if needed
        logger.info('User Image URL: ', messageContent.image_url.url);
      } else if (messageContent.type === 'refusal') {
        // Handle image file content if needed
        logger.info('User Refusal: ', messageContent.refusal);
      } else {
        logger.info('User Unknown content type:');
      }
    }
    // Run the Assistant
    const myRun = await openai.beta.threads.runs.create(
      threadByBookChapter, // Use the stored thread ID for this user
      {
        model: 'gpt-4o',
        assistant_id: 'asst_WYMz982RHWvdIXWnwGFtShqs',
        instructions: `If the user asks whether you are based on a specific AI model or platform, who you are, what you are made of, simply respond that you are Manny AI, a dedicated writing assistant designed to help with book chapters`, // Your instructions here
        additional_messages: messages,
        ...(openAiParams || {}),
      },
    );
    logger.info('This is the run object: ', myRun);

    // Periodically retrieve the Run to check on its status
    const retrieveRun = async () => {
      let keepRetrievingRun;

      while (myRun.status !== 'completed') {
        keepRetrievingRun = await openai.beta.threads.runs.retrieve(
          threadByBookChapter, // Use the stored thread ID for this user
          myRun.id,
        );

        logger.info(`Run status: ${keepRetrievingRun.status}`);

        if (keepRetrievingRun.status === 'completed') {
          logger.info('completed');
          break;
        }
      }
    };

    // Retrieve the Messages added by the Assistant to the Thread
    const waitForAssistantMessage = async () => {
      await retrieveRun();

      const allMessages = await openai.beta.threads.messages.list(
        threadByBookChapter, // Use the stored thread ID for this user
      );

      const messageContent = allMessages.data[0].content[0];
      if (messageContent.type === 'text') {
        logger.info('Assistant Text: ', messageContent.text.value);
        return messageContent.text.value;
      } else if (messageContent.type === 'image_file') {
        // Handle image file content if needed
        logger.info(
          'Assistant Image File: ',
          messageContent.image_file.file_id,
        );
        return messageContent.image_file.file_id;
      } else if (messageContent.type === 'image_url') {
        // Handle image file content if needed
        logger.info('Assistant Image URL: ', messageContent.image_url.url);
        return messageContent.image_url.url;
      } else if (messageContent.type === 'refusal') {
        // Handle image file content if needed
        logger.info('Assistant Refusal: ', messageContent.refusal);
        return messageContent.refusal;
      } else {
        logger.info('Assistant Unknown content type:');
        return 'Assistant Unknown content type:';
      }
    };
    return await waitForAssistantMessage();
  } catch (error) {
    logger.error('Error getting assistant', error);
    throw error;
  }
}

/**
 * Handles new long text processing
 */
export const processLongTextForAI = onCall(
  {
    cors: true,
    secrets: [openAIKey],
    cpu: 4,
    memory: '16GiB',
    timeoutSeconds: 3600,
  },
  async (request) => {
    const { data, auth } = request;
    if (!auth) {
      throw new HttpsError('unauthenticated', 'Authentication required');
    }
    requireAuth(auth);

    const { longText, docId } = data;
    if (!longText || !docId) {
      throw new HttpsError(
        'invalid-argument',
        'Bad Request: Missing arguments',
      );
    }

    try {
      const openai = invokeOpenAI();
      await processBookChapter(openai, longText, docId);
      logger.info('Book chapter processed and stored in Firestore.');

      return true;
    } catch (error) {
      // Handle errors
      logger.error('Error processing long content to openAI assistant:', error);
      throw new Error(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        'OpenAI API returned an error: ' + (error as any).message,
      );
    }
  },
);

/**
 * Handles chat queries
 */
export const chatWithManny = onCall(
  {
    cors: true,
    secrets: [openAIKey],
    cpu: 4,
    memory: '16GiB',
    timeoutSeconds: 3600,
  },
  async (request) => {
    const { data, auth } = request;
    if (!auth) {
      throw new HttpsError('unauthenticated', 'Authentication required');
    }
    requireAuth(auth);

    const { query, docId, messageHistory, openAiParams } = data;
    if (!query || !docId) {
      throw new HttpsError(
        'invalid-argument',
        'Bad Request: Missing arguments',
      );
    }

    try {
      const openai = invokeOpenAI();

      // Handle user query using the Assistant
      return await handleUserQuery(
        openai,
        docId,
        query,
        messageHistory,
        openAiParams,
      );
    } catch (error) {
      // Handle errors
      logger.error('Error chatting with Manny:', error);
      throw new Error(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        'OpenAI API returned an error: ' + (error as any).message,
      );
    }
  },
);
