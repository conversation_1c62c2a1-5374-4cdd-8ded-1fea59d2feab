import * as firestore from '@google-cloud/firestore';
import * as logger from 'firebase-functions/logger';
import * as admin from 'firebase-admin';
import { firestoreBackupBuckets } from './utils';
import { onSchedule } from 'firebase-functions/v2/scheduler';

// Firestore admin client instance
const client = new firestore.v1.FirestoreAdminClient();

// Define the storage bucket for backups
const bucket = `gs://${firestoreBackupBuckets}-backups/scheduled_backups/`;

/**
 * Cloud Function to schedule Firestore exports daily.
 * @returns A Promise representing the operation status.
 */
export const scheduledFirestoreExport = onSchedule(
  'every day 00:00',
  async (): Promise<void> => {
    // Get the project ID from Firebase admin
    const projectId: string = admin.instanceId().app.options
      .projectId as string;
    // Construct the database path
    const databaseName: string = client.databasePath(projectId, '(default)');
    // Format the current date for the backup folder name
    const dateFormat: string = new Date()
      .toISOString()
      .replace(/[:-]/g, '')
      .replace('T', '_')
      .split('.')[0];

    // Start the export process
    return client
      .exportDocuments({
        name: databaseName,
        outputUriPrefix: bucket + dateFormat,
        collectionIds: [],
      })
      .then((responses) => {
        const response = responses[0];
        logger.info(`Operation Name: ${response['name']}`);
      })
      .catch((err) => {
        logger.error('Export operation failed', err);
        throw new Error('Export operation failed');
      });
  },
);
