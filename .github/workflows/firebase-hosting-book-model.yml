name: Deploy to Firebase Hosting on book-model-update

'on':
  push:
    branches:
      - book-model-update

jobs:
  build_and_deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - run: npm ci && npm run build
        env:
          FIREBASE_APP: books
      - uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_MANUSCRIPTR_BOOKS_MIGRATION }}'
          channelId: live
          projectId: manuscriptr-books-migration
