name: Deploy to Firebase Hosting on develop

'on':
  push:
    branches:
      - develop

jobs:
  build_and_deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - run: npm ci && npm run build
        env:
          FIREBASE_APP: dev
      - uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_MANUSCRIPTR_DEV }}'
          channelId: live
          projectId: manuscriptr-dev
