name: Check formatting

on:
  pull_request:
  push:

jobs:
  check-formatting:
    name: Check formatting with <PERSON><PERSON><PERSON>
    runs-on: ubuntu-latest
    steps:
      - name: Checkout 🛎️
        uses: actions/checkout@v4

      - name: Set up Node 18 📦
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install dependencies 🧰
        run: npm ci

      - name: Check formatting 🔬
        run: npm run format:check
