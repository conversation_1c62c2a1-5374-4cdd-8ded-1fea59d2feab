# This file was auto-generated by the Firebase CLI
# https://github.com/firebase/firebase-tools

name: Deploy to Firebase Hosting on PR to setup-book-migration-project

on:
  pull_request:
    branches:
      - setup-book-migration-project

permissions:
  checks: write
  contents: read
  pull-requests: write

jobs:
  build_and_preview:
    if: '${{ github.event.pull_request.head.repo.full_name == github.repository }}'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - run: npm ci && npm run build
        env:
          FIREBASE_APP: books
      - uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_MANUSCRIPTR_BOOKS_MIGRATION }}'
          projectId: manuscriptr-books-migration
