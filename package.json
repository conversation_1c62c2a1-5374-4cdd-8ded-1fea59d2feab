{"name": "manuscriptr", "version": "0.0.1", "description": "Online book writing software", "productName": "Manus<PERSON><PERSON>", "author": "<PERSON> <<EMAIL>>", "private": true, "scripts": {"emulators": "firebase emulators:start --import=./.firebase/test-db --export-on-exit", "deploy": "quasar build && firebase deploy", "dev": "quasar dev", "build": "quasar build", "format": "prettier . --write", "format:check": "prettier . --check"}, "dependencies": {"@highlightjs/vue-plugin": "^2.1.0", "@quasar/extras": "^1.15.6", "@stripe/firestore-stripe-payments": "^0.0.6", "@vueuse/core": "^10.3.0", "core-js": "^3.6.5", "highlightjs-vue": "github:highlightjs/highlightjs-vue", "lodash": "^4.17.21", "mammoth": "^1.8.0", "markdown-it": "^14.1.0", "pinia": "^2.3.1", "quasar": "^2.12.2", "recordrtc": "^5.6.2", "sanitize-html": "^2.13.0", "vue": "^3.3.4", "vue-audio-visual": "^3.0.11", "vue-draggable-resizable-ts": "^0.1.3", "vue-eventer": "^1.0.0", "vue-router": "^4.2.4"}, "overrides": {"@firebase/app": "^0.9.14", "@firebase/auth": "^1.0.0", "@firebase/firestore": "^4.0.0"}, "devDependencies": {"@quasar/app-webpack": "^3.9.2", "@quasar/quasar-app-extension-qpdfviewer": "^2.0.0-alpha.6", "@tinymce/tinymce-vue": "^5.1.0", "@types/file-saver": "^2.0.6", "@types/lodash": "^4.14.197", "@types/recordrtc": "^5.6.11", "file-saver": "^2.0.2", "filepond": "^4.20.1", "filepond-plugin-file-validate-size": "^2.2.1", "filepond-plugin-file-validate-type": "^1.2.5", "filepond-plugin-image-crop": "^2.0.4", "filepond-plugin-image-resize": "^2.0.7", "filepond-plugin-image-transform": "^3.7.4", "filepond-plugin-image-validate-size": "^1.2.4", "firebase": "^10.0.0", "html-word-count": "^2.0.0", "jimp": "^0.16.1", "jszip": "^3.5.0", "jszip-utils": "^0.1.0", "node-polyfill-webpack-plugin": "^2.0.1", "pagedjs": "^0.1.42", "prettier": "^3.0.0", "shortid": "^2.2.15", "vue-filepond": "^7.0.3", "vuedraggable": "^4.1.0", "vuex-persistedstate": "^4.1.0"}, "browserslist": ["last 10 Chrome versions", "last 10 Firefox versions", "last 4 Edge versions", "last 7 Safari versions", "last 8 Android versions", "last 8 ChromeAndroid versions", "last 8 FirefoxAndroid versions", "last 10 iOS versions", "last 5 Opera versions"], "engines": {"node": ">= 10.18.1", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}