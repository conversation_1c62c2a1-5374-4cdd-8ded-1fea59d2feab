rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow admins to read and write all documents
    match /{document=**} {
      allow read, write: if hasAnyRole(["ADMIN", "OWNER"]);
    }

    // Rowy: Allow signed in users to read Rowy configuration and admins to write
    match /_rowy_/{docId} {
      allow read: if request.auth.token.roles.size() > 0;
      allow write: if hasAny<PERSON><PERSON>(["ADMIN", "OWNER"]);
    	match /{document=**} {
        allow read: if request.auth.token.roles.size() > 0;
        allow write: if hasAnyRole(["ADMIN", "OWNER"]);
      }
      match /schema/{tableId} {
        allow update: if canModify(tableId,'pc')
        match /{document=**} {allow read,write: if canModify(tableId,'pc')}
      }
       match /groupSchema/{tableId} {
        allow update: if canModify(tableId,'cg')
        match /{document=**} {allow read,write: if canModify(tableId,'cg')}
      }
    	function canModify(tableId,tableType) {
      	return hasAnyRole(get(/databases/$(database)/documents/_rowy_/settings)
        .data.tablesSettings[tableType][tableId].modifiableBy)
	   	}
    }
    // Rowy: Allow users to edit their settings
    match /_rowy_/userManagement/users/{userId} {
      allow get, update, delete: if isDocOwner(userId);
      allow create: if request.auth.token.roles.size() > 0;
    }
    // Rowy: Allow public to read public Rowy configuration
    match /_rowy_/publicSettings {
      allow get: if true;
    }

    // Rowy: Utility functions
    function isDocOwner(docId) {
      return request.auth != null && (request.auth.uid == resource.id || request.auth.uid == docId);
    }
    function hasAnyRole(roles) {
      return request.auth != null && request.auth.token.roles.hasAny(roles);
    }


    function isSignedIn() {
      return request.auth.uid != null;
    }

    function isMainAdmin() {
      return request.auth.uid == "bVF0QnYqanWnD270ORPCP0HvlMI2" || request.auth.uid == "YXT3EEAbQiTbN3TaMQjiZHV0fDF2";
    }

    function isAdmin() {
      return isSignedIn() && request.auth.token.get("isAdmin", false) == true;
    }

    function isReviewer() {
      return isSignedIn() && request.auth.token.get("isReviewer", false) == true;
    }

    function isAuthor() {
      return isSignedIn() && request.auth.token.get("stripeRole", "") in ['basic', 'plus', 'premium'];
    }

    function hasStripeRole(role) {
      return isSignedIn() && request.auth.token.get("stripeRole", "") == role;
    }

    function isOldAuthor() {
      // Checks if the user is an author that existed before the Stripe integration
      // This is marked by the temporary property that should be removed after Stripe migration is complete for all users
      return isSignedIn() && request.auth.token.get("isOldAuthor", false) == true;
    }

    function paidViaPaypal() {
      // Checks if the user is an author that existed before the Stripe integration
      // This is marked by the temporary property that should be removed after Stripe migration is complete for all users
      return isSignedIn() && request.auth.token.get("paidViaPaypal", false) == true;
    }


    match /templates/{template} {
      allow read: if isSignedIn();
      allow write: if isMainAdmin() || isAdmin();
    }

    match /users/{userId} {
      function isMyDoc() {
        return isSignedIn() && request.auth.uid == userId;
      }
      function isMyReviewer() {
      	return get(/databases/$(database)/documents/users/$(userId)).data.assignedAdminId == request.auth.uid;
      }

      function isAuthorToReview() {
        return get(/databases/$(database)/documents/users/$(userId)).data.authorsToReview[request.auth.uid].id == request.auth.uid;
        // return isAuthor();
      }

      allow read, write: if isMyDoc() || isMainAdmin() || isAdmin();
      allow read: if isMyReviewer() || isReviewer();

      // Stripe-specific collections
      match /checkout_sessions/{id} {
        allow read, write: if isMyDoc();
      }
      match /subscriptions/{id} {
        allow read: if isMyDoc();
      }
      match /payments/{id} {
        allow read: if isMyDoc();
      }

      match /meta/{id} {
        allow read: if isMyDoc() || isAdmin() || isMyReviewer() || isReviewer();
        // Can only be written by cloud functions
        allow write: if false || isAdmin();
      }

      match /notifications/{id} {
        allow read, update, write: if isMyDoc() || isAdmin()  ||  isReviewer() || isMyReviewer() || isAuthorToReview();
        allow delete: if isAdmin() || isMyDoc();
      }
    }

    match /books/{book} {
      function getAuthorId(bookId) {
        return get(/databases/$(database)/documents/books/$(bookId)).data.authorId;
      }
      function getBookCount(authorId) {
        return get(/databases/$(database)/documents/users/$(authorId)/meta/counts).data.get("books", 0);
      }
      function getAllowedBookCount(authorId) {
        return get(/databases/$(database)/documents/users/$(authorId)/meta/counts).data.get("allowed_books", 4);
      }
      function canAddBook() {
        let books = getBookCount(request.auth.uid);
        let allowed_book_count = getAllowedBookCount(request.auth.uid);
        return (hasStripeRole('basic') && books < allowed_book_count)
            // Plus and Premium are not currently available. Same treatment as basic
            || (hasStripeRole('plus') && books < allowed_book_count)
            || (hasStripeRole('premium') && books < allowed_book_count)
            || (paidViaPaypal() && books < allowed_book_count)
            || (isOldAuthor() && books < allowed_book_count);
      }
      allow create: if (isAdmin() || canAddBook()) && request.resource.data.authorId == request.auth.uid;
      allow read, update, delete: if request.auth.uid == resource.data.authorId || isMainAdmin() || isAdmin();
			allow read, update: if isReviewer();

      match /chapters/{chapter} {
        allow create: if isAdmin() || (getAuthorId(book) == request.auth.uid);
        allow read, update, delete: if (getAuthorId(book) == request.auth.uid) || isMainAdmin() || isAdmin();
        allow read: if isReviewer();

        match /transcriptions/{transcription} {
          allow create: if isAdmin() || (getAuthorId(book) == request.auth.uid);
          allow read, update, delete: if (getAuthorId(book) == request.auth.uid) || isMainAdmin() || isAdmin();
          allow read: if isReviewer();
      	}

        match /chats/{chatId} {
          allow create: if isAdmin() || (getAuthorId(book) == request.auth.uid);
          allow read, update, delete: if (getAuthorId(book) == request.auth.uid) || isMainAdmin() || isAdmin();
      	}
      }

      match /outlines/{chapter} {
        allow create: if isAdmin() || (getAuthorId(book) == request.auth.uid);
        allow read, update, delete: if (getAuthorId(book) == request.auth.uid) || isMainAdmin() || isAdmin();
        allow read: if isReviewer();
      }

      match /versions/{versionId} {
        allow create: if (getAuthorId(book) == request.auth.uid) || isMainAdmin() || isAdmin();
        allow read, delete: if (getAuthorId(book) == request.auth.uid) || isMainAdmin() || isAdmin();
        // Backups cannot be modified (except for the name and description)
        allow update: if (request.resource.data.diff(resource.data).affectedKeys().hasOnly(['name', 'description']));
				allow read: if isReviewer();

        match /chapters/{chapter} {
          allow create: if (getAuthorId(book) == request.auth.uid) || isMainAdmin() || isAdmin();
          allow read, delete: if (getAuthorId(book) == request.auth.uid) || isMainAdmin() || isAdmin();
          // Previous versions are read-only
          allow update: if false;
          allow read: if isReviewer();

          match /transcriptions/{transcription} {
            allow create: if (getAuthorId(book) == request.auth.uid) || isMainAdmin() || isAdmin();
            allow read, update, delete: if (getAuthorId(book) == request.auth.uid) || isMainAdmin() || isAdmin();
            allow read: if isReviewer();
      		}
        }

        match /outlines/{chapter} {
          allow create: if (getAuthorId(book) == request.auth.uid) || isMainAdmin() || isAdmin();
          allow read, delete: if (getAuthorId(book) == request.auth.uid) || isMainAdmin() || isAdmin();
          // Previous versions are read-only
          allow update: if false;
          allow read: if isReviewer();
        }

      }
    }

    match /versions/{version} {
      allow create: if (isAdmin() || isAuthor() || isOldAuthor() || paidViaPaypal()) && request.resource.data.authorId == request.auth.uid;
      allow read, update, delete: if request.auth.uid == resource.data.authorId || isMainAdmin() || isAdmin();
      allow read: if isReviewer();
    }

    match /logs/{log} {
      allow create: if (isAdmin() || isAuthor() || isOldAuthor() || paidViaPaypal()) && request.resource.data.authorId == request.auth.uid;
      allow read, update, delete: if request.auth.uid == resource.data.authorId || isMainAdmin() || isAdmin();
    }

    match /posts/{post} {
      function getAuthorIdFromPost(bookId) {
        return get(/databases/$(database)/documents/books/$(bookId)).data.authorId;
      }
      function canAddPost() {
        return (hasStripeRole('basic'))
            // Plus and Premium are not currently available. Same treatment as basic
            || (hasStripeRole('plus'))
            || (hasStripeRole('premium'))
            || (paidViaPaypal())
            || (isOldAuthor());
      }
      allow create: if (isAdmin() || canAddPost()) && request.resource.data.authorId == request.auth.uid;
      allow read, update, delete: if request.auth.uid == resource.data.authorId || isMainAdmin() || isAdmin();

      match /socials/{social} {
        allow create: if isAdmin() || (getAuthorIdFromPost(post) == request.auth.uid);
        allow read, update, delete: if (getAuthorIdFromPost(post) == request.auth.uid) || isMainAdmin() || isAdmin();
      }
    }

    match /settings/{setting} {
      allow read;
   	  allow update: if isMainAdmin() || isAdmin();
    }

    // Also for Stripe
    match /products/{id} {
      allow read;

      match /prices/{id} {
        allow read;
      }

      match /tax_rates/{id} {
        allow read;
      }
    }

  }
}
