// Give the service worker access to Firebase Messaging.
// Note that you can only use Firebase Messaging here. Other Firebase libraries
// are not available in the service worker.
// Replace 10.13.2 with latest version of the Firebase JS SDK.
importScripts(
  'https://www.gstatic.com/firebasejs/10.13.2/firebase-app-compat.js',
);
importScripts(
  'https://www.gstatic.com/firebasejs/10.13.2/firebase-messaging-compat.js',
);

// Initialize the Firebase app in the service worker by passing in
// your app's Firebase config object.
// https://firebase.google.com/docs/web/setup#config-object
firebase.initializeApp({
  apiKey: 'AIzaSyDkkEKASGfX4zfU9dBS21RVc75ao12DDKY',
  authDomain: 'bspub-3085b.firebaseapp.com',
  databaseURL: 'https://bspub-3085b.firebaseio.com',
  projectId: 'bspub-3085b',
  storageBucket: 'bspub-storage',
  messagingSenderId: '424636372865',
  appId: '1:424636372865:web:3e81269b8db2eab0af3a80',
  measurementId: 'G-HGE6EG62MD',
});

// Retrieve an instance of Firebase Messaging so that it can handle background
// messages.
const messaging = firebase.messaging();

messaging.onBackgroundMessage((payload) => {
  console.log(
    '[firebase-messaging-sw-prod.js] Received background message ',
    payload,
  );

  const messageData = payload.notification;

  if (messageData) {
    return self.registration.showNotification(messageData.title, {
      ...messageData,
      icon: 'https://app.manuscriptr.com/robot.svg',
    });
  }
});
