/* For WebKit browsers */
::-webkit-scrollbar {
  height: 12px;
  width: 14px;
  background: transparent;
  z-index: 12;
  overflow: visible;
}

::-webkit-scrollbar-thumb {
  width: 10px;
  background-color: #00569b;
  border-radius: 10px;
  z-index: 12;
  border: 4px solid rgba(0, 0, 0, 0);
  background-clip: padding-box;
  -webkit-transition: background-color 0.28s ease-in-out;
  transition: background-color 0.28s ease-in-out;
  margin: 4px;
  min-height: 32px;
  min-width: 32px;
}

::-webkit-scrollbar-thumb:hover {
  background: #00b4ff;
}

/* For Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: #00569b transparent; /* Replace with your primary color variable */
}

/* For Internet Explorer, Edge (Legacy) */
* {
  -ms-overflow-style: -ms-autohiding-scrollbar;
}

/* General styling for other browsers */
.scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #00569b transparent; /* Replace with your primary color variable */
}
