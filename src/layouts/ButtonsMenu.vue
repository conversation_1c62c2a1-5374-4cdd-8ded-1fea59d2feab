<script setup lang="ts">
import { TopBarNotifications } from 'src/entities/notification';
import { isTopNavDefault } from 'src/entities/setting';
import { isReviewMode, loginAs, useAuthorStore, user } from 'src/entities/user';
import { useQuasar } from 'quasar';
import { computedAsync } from '@vueuse/core';
import { onMounted, onUnmounted, ref } from 'vue';

const $q = useQuasar();

const authorStore = useAuthorStore();
/**
 * Computed property to handle logging as another user.
 */
const loggingAs = computedAsync(async () => {
  if (!loginAs.value) return null;

  const author = await authorStore.getAuthorWithoutSync(loginAs.value);
  return `LoginAs ${author.name || author.email}`;
}, null);
/**
 * Computed property to handle review mode display.
 */
const reviewing = computedAsync(async () => {
  if (!isReviewMode.value) return null;

  const author = await authorStore.getAuthorWithoutSync(isReviewMode.value);
  return `is reviewing ${author.name}`;
}, null);

const browserWidth = ref(window.innerWidth);

/** Function to update the browser width */
const updateWidth = (): void => {
  browserWidth.value = window.innerWidth;
};

onMounted(() => {
  window.addEventListener('resize', updateWidth);
});
onUnmounted(() => {
  window.removeEventListener('resize', updateWidth);
});
</script>

<template>
  <div class="action-buttons items-center row q-gutter-sm">
    <!-- Review mode chip -->
    <div class="q-mr-sm" v-if="isReviewMode">
      <q-chip
        outline
        color="white"
        text-color="white"
        :icon="$q.screen.gt.sm ? 'preview' : 'visibility'"
        style="font-size: 1.2em"
      >
        <span v-if="$q.screen.gt.sm">
          {{ user.displayName || user.email }}
          <span v-if="reviewing !== null"> &nbsp;{{ reviewing }}</span>
        </span>
        <span v-else>
          <span v-if="$q.screen.sm"> Review Mode </span>
          <q-tooltip>
            {{ user.displayName || user.email }}
            <span v-if="reviewing !== null"> &nbsp;{{ reviewing }}</span>
          </q-tooltip>
        </span>
      </q-chip>
    </div>
    <!-- Login-as exit button -->
    <div class="q-mr-sm" v-if="loginAs && browserWidth > 767">
      <q-btn
        color="primary"
        :dense="isTopNavDefault"
        :stretch="isTopNavDefault"
        v-if="loggingAs !== null"
        to="/setting-users"
        icon="close"
        outline
      >
        EXIT LOGIN-AS
        <q-tooltip>Exit {{ loggingAs }}</q-tooltip>
      </q-btn>
    </div>

    <!-- Review status teleport target -->
    <div id="review-status">
      <!-- This will be filled by the book editing component -->
    </div>

    <!-- Right drawer teleport target -->
    <div id="right-drawer" class="q-gutter-sm">
      <!-- This will be filled by the book editing component -->
    </div>

    <!-- Notifications -->
    <Suspense>
      <TopBarNotifications />
    </Suspense>

    <!-- Fullscreen toggle -->
    <q-btn
      dense
      color="white"
      flat
      @click="$q.fullscreen.toggle()"
      :icon="$q.fullscreen.isActive ? 'fullscreen_exit' : 'fullscreen'"
    >
      <q-tooltip>{{
        $q.fullscreen.isActive ? 'Exit Full Screen' : 'Enter Full Screen'
      }}</q-tooltip>
    </q-btn>
  </div>
</template>

<style scoped lang="scss"></style>
