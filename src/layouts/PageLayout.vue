<template>
  <div class="page-layout" :class="{ 'page-layout--full-width': fullWidth }">
    <div class="page-container" :class="containerClass">
      <!-- Page Header -->
      <div v-if="title || $slots.header" class="page-header q-mb-lg">
        <slot name="header">
          <div class="row items-center justify-center">
            <div class="col-auto">
              <div class="page-title-section text-center">
                <div>
                  <h1 class="page-title text-h4 q-mb-xs">
                    <q-icon
                      v-if="icon"
                      :name="icon"
                      :size="iconSize"
                      class="page-title-icon"
                    />
                    {{ title }}
                  </h1>
                  <p
                    v-if="subtitle"
                    class="page-subtitle text-subtitle1 q-mt-lg q-mb-none"
                  >
                    {{ subtitle }}
                  </p>
                </div>
              </div>
            </div>
            <div v-if="$slots.actions" class="col-auto">
              <slot name="actions" />
            </div>
          </div>
        </slot>
      </div>

      <!-- Page Content -->
      <div class="page-content" :class="contentClass">
        <slot />
      </div>

      <!-- Loading Overlay -->
      <q-inner-loading
        :showing="loading"
        :color="$q.dark.isActive ? 'white' : 'primary'"
        :label="loadingLabel"
        label-class="text-weight-medium"
        label-style="font-size: 1.1em"
      >
        <div class="column items-center">
          <q-spinner-dots
            :color="$q.dark.isActive ? 'white' : 'primary'"
            size="3em"
          />
          <div v-if="loadingIcon" class="row items-center q-mt-sm">
            <q-icon :name="loadingIcon" class="q-mr-sm" size="md" />
            <span>{{ loadingLabel || 'Loading...' }}</span>
          </div>
        </div>
      </q-inner-loading>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useQuasar } from 'quasar';

interface Props {
  title?: string;
  subtitle?: string;
  icon?: string;
  iconSize?: string;
  iconColor?: string;
  loading?: boolean;
  loadingLabel?: string;
  loadingIcon?: string;
  fullWidth?: boolean;
  maxWidth?: string;
  padding?: boolean;
  containerClass?: string;
  contentClass?: string;
}

const props = withDefaults(defineProps<Props>(), {
  iconSize: '2rem',
  iconColor: 'primary',
  loadingLabel: 'Loading...',
  loadingIcon: 'img:robot.png',
  maxWidth: '1200px',
  padding: true,
});

const $q = useQuasar();
</script>

<style lang="scss" scoped>
.page-layout {
  min-height: calc(100vh - 120px);
  padding: 1.5rem;
  max-width: 1170px;
  margin: 0 auto;
  @media (max-width: 599px) {
    padding: 1rem;
  }

  &--full-width {
    padding: 0;

    .page-container {
      max-width: none !important;
      margin: 0 !important;
    }
  }

  .page-container {
    max-width: v-bind(maxWidth);
    margin: 0 auto;
    width: 100%;
    position: relative;
  }

  .page-header {
    .page-title-section {
      display: flex;
      align-items: center;

      .page-title {
        font-weight: 600;
        margin: 0;
        color: var(--q-primary);

        body.body--dark & {
          color: var(--q-primary-light);
        }
      }

      .page-subtitle,
      .page-title-icon {
        opacity: 0.8;
        color: var(--q-text-color);
      }
    }
  }

  .page-content {
    position: relative;
  }
}

// Dark mode enhancements
body.body--dark {
  .page-layout {
    background: var(--q-dark-page);
  }
}
</style>
