<script setup lang="ts">
import { auth } from 'src/firebase';
import { watch, onMounted, ref, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { User, signOut } from 'firebase/auth';
import { useQuasar } from 'quasar';
import {
  HelpCard,
  isAdmin,
  isAuthor,
  isReviewer,
  isReviewMode,
  loadingState,
  loginAs,
  user,
  useAuthorStore,
  useSubscriptionStore,
  isLoggedIn,
  useAuthStore,
  getUserDetailsForOnboarding,
} from 'src/entities/user';
import { UserOnboarding } from 'src/entities/user';
import {
  isMaintenanceMode,
  isTopNavDefault,
  useBookSettingsStore,
} from 'src/entities/setting';
import { TOS, PrivacyPolicy } from 'src/entities/setting';
import { serverTimestamp } from 'firebase/firestore';
import {
  PushNotificationService,
  useNotificationStore,
} from 'src/entities/notification';
import ButtonsMenu from 'layouts/ButtonsMenu.vue';

// Import theme functions from boot file
import {
  toggleThemeMode,
  changeThemePalette,
  getCurrentThemeConfig,
  watchSystemTheme,
} from 'src/boot/theme';

// Enhanced theme configuration imports
import {
  getAvailablePalettes,
  type ThemeMode,
  type PaletteName,
  type ThemeConfig,
  getPalettePreview,
} from 'src/config/theme';

// Left drawer state
const leftDrawerOpen = ref(false);

// Reactive references for browser width and div size
const browserWidth = ref(window.innerWidth);

/** Function to update the browser width */
const updateWidth = (): void => {
  browserWidth.value = window.innerWidth;
};

// Vue Router and Vuex store instances
const router = useRouter();
const route = useRoute();
const $q = useQuasar();
const helpDialogForm = ref(true);
const showhelpDialogForm = ref(false);
const iAgreeCheck = ref(false);
const tosModal = ref(false);
const authorStore = useAuthorStore();
const authStore = useAuthStore();
const subscriptionStore = useSubscriptionStore();
const bookSettingStore = useBookSettingsStore();
const notificationStore = useNotificationStore();

// Add onboarding state
const showOnboarding = ref(false);

// Enhanced theme state - now managed by boot file
const currentThemeConfig = ref<ThemeConfig | null>(null);

// Enhanced theme toggle function - now uses boot file
const toggleTheme = () => {
  const newConfig = toggleThemeMode();
  currentThemeConfig.value = newConfig;

  // Update Quasar's dark mode
  $q.dark.set(newConfig.mode === 'dark');
};

// New function to change color palette - now uses boot file
const changePalette = (paletteName: PaletteName) => {
  const newConfig = changeThemePalette(paletteName);
  currentThemeConfig.value = newConfig;
};

// Get current theme and palette for UI display
const getCurrentTheme = (): ThemeMode => {
  return currentThemeConfig.value?.mode || 'dark';
};

const getCurrentPalette = (): PaletteName => {
  return currentThemeConfig.value?.palette || 'default';
};

// Initialize theme on mount
onMounted(() => {
  // Get current theme config from boot file
  currentThemeConfig.value = getCurrentThemeConfig();

  // Watch for system theme changes
  const cleanup = watchSystemTheme((isDark) => {
    if (currentThemeConfig.value?.mode === 'auto') {
      $q.dark.set(isDark);
    }
  });

  // Store cleanup function for unmount
  onUnmounted(cleanup);

  // Rest of existing onMounted logic...
  unSubs();
  if (route.path.startsWith('/help')) {
    showhelpDialogForm.value = false;
  }
  window.addEventListener('resize', updateWidth);
  if (isLoggedIn.value) {
    console.log('Logged in.');
    checkIfNewUser(user?.value as User);
    getFcmToken(user?.value as User);
  }
});

// Rest of your existing component logic remains the same...
// [Include all the existing functions and watchers from the original component]

/**
 * Display a subscription notification.
 * @param message - Notification message.
 * @param color - Notification color.
 * @param url - URL to redirect to.
 * @param url_label - Label for the URL action.
 */
const subscriptionNotifs = (
  message: string,
  color: string,
  url: string,
  url_label: string,
): void => {
  const subscribeNotif = $q.notify({
    message,
    color,
    multiLine: true,
    avatar: 'robot.png',
    actions: [
      {
        label: url_label,
        color: 'yellow',
        handler: () => {
          subscribeNotif();
          router.push(url);
        },
      },
    ],
  });
};

/** Log out the current user. */
const logOut = async (): void => {
  unSubs();
  await authStore.logOut();
};

/** Unsubs snapshots. */
const unSubs = (): void => {
  isReviewMode.value = '';
  loginAs.value = '';
  authorStore.stopListeningUserLoggedIn();
  subscriptionStore.cleanupListeners();
  bookSettingStore.cleanupListeners();
};

/** Handle help dialog display based on conditions */
const handleHelpClick = (): void => {
  if (browserWidth.value < 600 || route.path.startsWith('/help')) {
    router.push('/help');
  } else {
    showhelpDialogForm.value = !showhelpDialogForm.value;
    helpDialogForm.value = true;
  }
};

// Watchers
watch(subscriptionStore, (newVal) => {
  if (!newVal.isSubscribe) {
    notificationStore.createUserNotification(user?.value?.uid as string, {
      title: `Account Subscription Cancelled `,
      notification: `<p>You no longer have an active subscription.</p>`,
      url: `/profile`,
    });
    subscriptionNotifs(
      'You no longer have an active subscription.',
      'red',
      '/',
      'Subscribe Now!',
    );
  }
});

watch(isMaintenanceMode, (newVal) => {
  if (newVal && route.meta?.maintenanceMode !== false) {
    window.location.href = '/maintenance';
  }
});

onUnmounted(() => {
  window.removeEventListener('resize', updateWidth);
  unSubs();
});

/**
 * Retrieves and manages the FCM token for push notifications.
 * @param result The user object containing user details.
 */
const getFcmToken = async (result: User): Promise<void> => {
  // Check if the user has a valid UID
  if (result.uid) {
    // Request permission for notifications if no token exists
    await PushNotificationService.requestPermission(result.uid);
  }

  // Listen for incoming push messages
  PushNotificationService.listenForMessages();
};

const userLogged = ref<null | object>(null);

/** Check if the user is new and handle first sign-in logic */
const checkIfNewUser = async (result: User): Promise<void> => {
  if (!result.uid)
    throw new Error('User could not be found. Please try again!');

  userLogged.value = await authorStore.getAuthorWithoutSync(result.uid);

  if (userLogged.value && !userLogged.value?.firstSignIn && !isAdmin.value) {
    tosModal.value = true;
  }
};

const onBoardingUser = ref<null | object>(null);

/** Handle user agreement action */
const clickIagree = async (): Promise<void> => {
  tosModal.value = false;
  onBoardingUser.value = getUserDetailsForOnboarding(userLogged.value);

  // Show onboarding instead of redirecting
  showOnboarding.value = true;
};

/** Handle onboarding completion */
const completeOnboarding = async (userInfoData: any): Promise<void> => {
  try {
    // Update the user profile with the collected information
    await authorStore.updateAuthor(user.value?.uid as string, {
      ...userInfoData,
      onboardingCompleted: true,
      firstSignIn: serverTimestamp(),
    });

    // Navigate to home page after successful onboarding
    router.push('/');

    // Show welcome notification
    $q.notify({
      color: 'positive',
      message: `Welcome, ${userInfoData.firstName}! Your profile is now set up.`,
      icon: 'check_circle',
      timeout: 5000,
    });
  } catch (error) {
    console.error('Error updating user profile:', error);
    $q.notify({
      color: 'negative',
      message: 'There was an error saving your profile. Please try again.',
      icon: 'error',
    });
  }
};
</script>

<template>
  <q-layout view="hHh lpR fFf" :class="route.name">
    <q-header
      elevated
      :style="isTopNavDefault ? 'position: fixed' : 'position: fixed'"
    >
      <q-toolbar class="main-toolbar">
        <!-- Left Toolbar Section -->
        <div class="toolbar-left">
          <!-- Logo and Menu Section -->
          <div class="logo-section">
            <q-btn
              flat
              icon="menu"
              @click="leftDrawerOpen = !leftDrawerOpen"
              class="menu-toggle-btn"
            >
              <q-tooltip>Menu</q-tooltip>
            </q-btn>
            <h1 class="logo-text">MANUSCRIPTR</h1>
          </div>

          <!-- Header title -->
          <div id="header-title" class="header-title-section">
            <!-- This will be filled by the book editing component -->
          </div>
        </div>

        <!-- Right Toolbar Section -->
        <div class="toolbar-right">
          <!-- Enhanced Palette Selector -->
          <q-btn-dropdown
            flat
            round
            icon="palette"
            class="theme-palette-btn q-mr-sm"
            color="white"
            v-show="false"
          >
            <q-tooltip>Color Palette</q-tooltip>
            <q-list class="palette-dropdown">
              <q-item-label header class="text-weight-bold">
                Choose Color Palette
              </q-item-label>
              <q-separator />
              <q-item
                v-for="palette in getAvailablePalettes()"
                :key="palette.name"
                clickable
                @click="changePalette(palette.name)"
                :class="{
                  'bg-primary text-white': currentPalette === palette.name,
                  'palette-item': true,
                }"
              >
                <q-item-section avatar>
                  <div class="palette-preview">
                    <div
                      class="palette-color primary"
                      :style="`background: ${
                        getPalettePreview(palette.name, currentTheme).primary
                      }`"
                    ></div>
                    <div
                      class="palette-color secondary"
                      :style="`background: ${
                        getPalettePreview(palette.name, currentTheme).secondary
                      }`"
                    ></div>
                    <div
                      class="palette-color accent"
                      :style="`background: ${
                        getPalettePreview(palette.name, currentTheme).accent
                      }`"
                    ></div>
                  </div>
                </q-item-section>
                <q-item-section>
                  <q-item-label class="text-weight-medium">{{
                    palette.displayName
                  }}</q-item-label>
                  <q-item-label caption>{{ palette.description }}</q-item-label>
                </q-item-section>
                <q-item-section side v-if="currentPalette === palette.name">
                  <q-icon name="check_circle" color="positive" />
                </q-item-section>
              </q-item>
            </q-list>
          </q-btn-dropdown>

          <!-- Enhanced Theme Toggle -->
          <q-btn
            flat
            round
            v-show="false"
            class="theme-toggle-btn q-mr-sm"
            :icon="$q.dark.isActive ? 'light_mode' : 'dark_mode'"
            @click="toggleTheme"
            color="white"
          >
            <q-tooltip>{{
              $q.dark.isActive ? 'Switch to Light Mode' : 'Switch to Dark Mode'
            }}</q-tooltip>
          </q-btn>

          <!-- Action buttons with proper wrapper -->
          <div class="book-actions-section">
            <ButtonsMenu />
          </div>
        </div>
      </q-toolbar>

      <!-- Loading progress indicator -->
      <div class="loading-indicator">
        <q-linear-progress
          indeterminate
          dark
          :color="$q.dark.isActive ? 'white' : 'primary'"
          v-if="loadingState"
        />
      </div>
    </q-header>

    <!-- Left Drawer -->
    <q-drawer
      v-model="leftDrawerOpen"
      :show-if-above="true"
      behavior="mobile"
      bordered
      :overlay="false"
      elevated
      :width="280"
      :breakpoint="1023"
      class="main-drawer"
      :class="{ 'bg-grey-2': !$q.dark.isActive }"
    >
      <q-scroll-area class="fit">
        <q-list padding>
          <!-- App Logo/Brand -->
          <q-item class="q-py-md" to="/books" clickable>
            <q-item-section>
              <div class="row items-center justify-center">
                <q-avatar size="50px">
                  <q-icon name="img:robot.png" size="50px" />
                </q-avatar>
                <div class="text-h6 q-ml-md text-primary">Manuscriptr</div>
              </div>
            </q-item-section>
          </q-item>

          <q-separator />

          <!-- New Book Create Button -->
          <q-item
            class="q-py-md"
            v-if="
              ['bookdetails', 'booklist', 'main'].includes(route.name) &&
              (isAuthor || isAdmin)
            "
          >
            <q-item-section>
              <div id="new-book-create" class="full-width">
                <!-- This will be filled by the book editing component -->
              </div>
            </q-item-section>
          </q-item>

          <q-separator />

          <!-- Navigation Links -->
          <q-item
            clickable
            v-ripple
            to="/books"
            active-class="active-menu-link"
            v-if="
              (isAuthor || isAdmin || isReviewer) &&
              !['booklist', 'main'].includes(route.name)
            "
          >
            <q-item-section avatar>
              <q-icon name="home" color="primary" class="drawer-icon" />
            </q-item-section>
            <q-item-section>Books</q-item-section>
          </q-item>

          <q-item
            clickable
            v-ripple
            to="/profile"
            active-class="active-menu-link"
          >
            <q-item-section avatar>
              <q-icon name="verified" color="primary" class="drawer-icon" />
            </q-item-section>
            <q-item-section>Subscriptions</q-item-section>
          </q-item>

          <q-item
            clickable
            v-ripple
            @click="handleHelpClick"
            active-class="active-menu-link"
          >
            <q-item-section avatar>
              <q-icon name="support" color="primary" class="drawer-icon" />
            </q-item-section>
            <q-item-section>Help</q-item-section>
          </q-item>

          <q-item clickable v-ripple to="/tos" active-class="active-menu-link">
            <q-item-section avatar>
              <q-icon name="fact_check" color="primary" class="drawer-icon" />
            </q-item-section>
            <q-item-section>Terms of Service</q-item-section>
          </q-item>

          <q-item
            clickable
            v-ripple
            @click="logOut"
            active-class="active-menu-link"
          >
            <q-item-section avatar>
              <q-icon name="logout" color="primary" class="drawer-icon" />
            </q-item-section>
            <q-item-section>Logout</q-item-section>
          </q-item>

          <!-- Admin Links -->
          <template v-if="isAdmin">
            <q-separator />
            <q-item-label header class="text-weight-bold q-mt-md">
              Admin
            </q-item-label>

            <q-item
              clickable
              v-ripple
              to="/setting-users"
              active-class="active-menu-link"
            >
              <q-item-section avatar>
                <q-icon name="group" color="primary" class="drawer-icon" />
              </q-item-section>
              <q-item-section>Users</q-item-section>
            </q-item>

            <q-item
              clickable
              v-ripple
              to="/setting-pages"
              active-class="active-menu-link"
            >
              <q-item-section avatar>
                <q-icon name="edit_note" color="primary" class="drawer-icon" />
              </q-item-section>
              <q-item-section>Pages (CMS)</q-item-section>
            </q-item>

            <q-item
              v-if="1 != 1"
              clickable
              v-ripple
              to="/setting-book-category"
              active-class="active-menu-link"
            >
              <q-item-section avatar>
                <q-icon name="category" color="primary" class="drawer-icon" />
              </q-item-section>
              <q-item-section>Book Categories</q-item-section>
            </q-item>
          </template>
        </q-list>
      </q-scroll-area>
    </q-drawer>

    <!-- Main Content -->
    <q-page-container
      :style="
        !isTopNavDefault
          ? 'margin-top: 100px; padding-top: 0px !important'
          : 'margin-top: 0px; padding-top: 0px !important'
      "
    >
      <q-inner-loading
        :showing="loadingState"
        :color="$q.dark.isActive ? 'white' : 'primary'"
        label-class="text-primary"
        label-style="font-size: 1.1em"
        style="z-index: 1001"
      >
        <div class="fixed-center">
          &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          <q-spinner-dots
            :color="$q.dark.isActive ? 'white' : 'primary'"
            size="2em"
          />
          <div class="align-center">
            <q-icon name="img:robot.png" class="q-ml-xs" size="xl" />
            Hang tight, Loading....
          </div>
        </div>
      </q-inner-loading>

      <Suspense>
        <router-view />
      </Suspense>
    </q-page-container>

    <!-- Help Dialog -->
    <q-page-sticky
      position="bottom-right"
      :offset="[18, 18]"
      v-show="showhelpDialogForm"
    >
      <q-fab
        label="Help"
        label-position="left"
        color="primary"
        icon="support"
        direction="left"
        v-model="helpDialogForm"
      >
        <q-fab-action class="help-popup" disable v-if="browserWidth >= 600">
          <HelpCard
            @minimizeToggle="
              helpDialogForm = false;
              showhelpDialogForm = true;
            "
            @closeToggle="showhelpDialogForm = false"
          />
        </q-fab-action>
        <q-fab-action
          color="purple"
          v-else
          icon="link"
          label="Help"
          to="/help"
        />
      </q-fab>
    </q-page-sticky>
  </q-layout>

  <!-- Terms of Service Modal -->
  <q-dialog v-model="tosModal" persistent>
    <q-card style="min-width: 70vw">
      <q-card-section class="q-pa-none">
        <TOS pageHeight="650" pageWidth="100%" />
      </q-card-section>
      <q-separator />
      <q-card-section class="q-pa-none">
        <PrivacyPolicy pageHeight="650" pageWidth="100%" />
      </q-card-section>
      <q-separator />
      <q-card-actions align="between">
        <q-checkbox
          right-label
          v-model="iAgreeCheck"
          label="I agree to the Terms and Conditions and Privacy Policy."
          checked-icon="task_alt"
          unchecked-icon="highlight_off"
        />
        <q-btn
          color="primary"
          :disable="!iAgreeCheck"
          icon-right="check"
          @click="clickIagree"
        >
          Submit and Continue
        </q-btn>
      </q-card-actions>
    </q-card>
  </q-dialog>

  <UserOnboarding
    v-model:show="showOnboarding"
    @complete="completeOnboarding"
    :user="onBoardingUser as object"
  />
</template>

<style lang="scss">
// Enhanced palette selector styles with better previews
.palette-dropdown {
  min-width: 280px;
  max-height: 400px;

  .palette-item {
    padding: 12px 16px;
    transition: all 0.2s ease;
    border-radius: 8px;
    margin: 4px 8px;

    &:hover:not(.bg-primary) {
      background: color-mix(in srgb, var(--theme-primary) 10%, transparent);
    }

    &.bg-primary {
      background: var(--theme-primary) !important;
      border-radius: 8px;

      .q-item__section {
        color: white;
      }
    }
  }

  .q-item-label[header] {
    padding: 12px 16px 8px;
    font-size: 13px;
    color: var(--theme-text-primary);
  }
}

.palette-preview {
  display: flex;
  gap: 2px;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.1);

  .theme-dark & {
    border-color: rgba(255, 255, 255, 0.2);
  }

  .palette-color {
    flex: 1;
    height: 100%;

    &.primary {
      width: 40%;
    }

    &.secondary {
      width: 35%;
    }

    &.accent {
      width: 25%;
    }
  }
}

.theme-palette-btn {
  color: white;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
  }

  .theme-dark & {
    color: var(--theme-neutral-200);

    &:hover {
      background: rgba(255, 255, 255, 0.05);
    }
  }

  .q-icon {
    font-size: 20px;
  }
}

// Enhanced theme toggle with better visual feedback
.theme-toggle-btn {
  color: white;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
  }

  .theme-dark & {
    color: var(--theme-neutral-200);

    &:hover {
      background: rgba(255, 255, 255, 0.05);
    }
  }

  .q-icon {
    font-size: 20px;
    transition: transform 0.3s ease;
  }

  // Rotation animation for theme switch
  &:active .q-icon {
    transform: rotate(180deg);
  }
}

// Preserve all your existing styles with theme support
$toolbar-bg: #0a2647;
$toolbar-height: 80px;
$toolbar-padding-vertical: 24px;
$toolbar-padding-horizontal: 32px;

// ===================================
// MAIN HEADER & TOOLBAR WITH ENHANCED THEME SUPPORT
// ===================================

.main-toolbar {
  height: $toolbar-height;
  padding: $toolbar-padding-vertical $toolbar-padding-horizontal;
  justify-content: space-between;
  align-items: center;
  background: transparent;

  // Mobile adjustments
  @media (max-width: 599px) {
    padding: 16px 20px;
    height: 64px;
    max-width: 100%;
  }

  // Tablet adjustments
  @media (max-width: 1023px) {
    padding: 20px 24px;
    height: 72px;
  }
}

// Continue with rest of existing styles...
.toolbar-left {
  display: flex;
  align-items: center;
  gap: $spacing-lg;
  flex: 1;

  @media (max-width: 599px) {
    gap: $spacing-md;
  }
}

.logo-section {
  display: flex;
  align-items: center;
  gap: $spacing-md;

  @media (max-width: 599px) {
    gap: $spacing-sm;
  }
}

.logo-text {
  font-weight: 900;
  font-size: 24px;
  line-height: 1;
  color: white;
  margin: 0;
  white-space: nowrap;
  transition: color 0.3s ease;

  .theme-dark & {
    color: var(--theme-neutral-100);
  }

  @media (max-width: 599px) {
    font-size: 18px;
  }

  @media (max-width: 1023px) {
    font-size: 20px;
  }
}

.menu-toggle-btn {
  color: white;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.05);
  }

  .theme-dark & {
    color: var(--theme-neutral-200);

    &:hover {
      background: rgba(255, 255, 255, 0.05);
    }
  }

  .q-icon {
    font-size: 24px;

    @media (max-width: 599px) {
      font-size: 20px;
    }
  }
}

.header-title-section {
  font-weight: 500;
  font-size: 18px;
  color: rgba(white, 0.9);
  margin-left: $spacing-md;
  transition: color 0.3s ease;

  .theme-dark & {
    color: var(--theme-neutral-200);
  }

  @media (max-width: 599px) {
    font-size: 14px;
    margin-left: $spacing-sm;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  @media (max-width: 1023px) {
    font-size: 16px;
  }
}

// ===================================
// RIGHT TOOLBAR SECTION
// ===================================

.toolbar-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;

  @media (max-width: 599px) {
    flex-shrink: 0;
  }
}

.book-actions-section {
  display: flex;
  align-items: center;
  gap: $spacing-sm;

  // Override button styles for toolbar
  :deep(.q-btn) {
    color: white;
    background: transparent;
    border: 1px solid rgba(white, 0.2);
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 14px;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(white, 0.1);
      border-color: rgba(white, 0.3);
      transform: translateY(-1px);
    }

    &.q-btn--standard {
      background: var(--theme-primary-light);
      border-color: var(--theme-primary-light);

      &:hover {
        background: var(--theme-primary-accent);
      }
    }

    .theme-dark & {
      color: var(--theme-neutral-200);
      border-color: rgba(255, 255, 255, 0.2);

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
      }
    }

    @media (max-width: 599px) {
      padding: 4px 8px;
      font-size: 12px;
      min-width: auto;

      .q-icon {
        font-size: 18px;
      }

      // Hide text on mobile, show only icons
      .q-btn__content {
        .q-icon ~ div {
          display: none;
        }
      }
    }
  }
}

// ===================================
// DRAWER STYLING WITH ENHANCED THEME SUPPORT
// ===================================

.main-drawer {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  top: $toolbar-height;
  background: var(--theme-background-primary);
  color: var(--theme-text-primary);
  transition: all 0.3s ease;

  .theme-dark & {
    background: var(--theme-background-primary);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    border-right: 1px solid var(--theme-border-light);
  }

  @media (max-width: 599px) {
    top: 64px;
  }

  @media (max-width: 1023px) {
    top: 72px;
  }

  .q-item {
    border-radius: 8px;
    margin: 2px 8px;
    transition: all 0.2s ease;
    color: var(--theme-text-primary);

    &:hover:not(.q-item--active) {
      background-color: color-mix(
        in srgb,
        var(--theme-primary) 8%,
        transparent
      );
    }

    &.active-menu-link {
      background-color: color-mix(
        in srgb,
        var(--theme-primary) 15%,
        transparent
      );
      color: var(--theme-primary);
      font-weight: 500;

      .q-icon {
        color: var(--theme-primary) !important;
      }
    }
  }

  .q-item__section--avatar {
    min-width: 40px;
  }

  .q-separator {
    background-color: var(--theme-border-light);
  }
}

// ===================================
// LOADING INDICATOR
// ===================================

.loading-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  z-index: 1001;
}

// ===================================
// PAGE CONTAINER ADJUSTMENT
// ===================================

:deep(.q-page-container) {
  padding-top: $toolbar-height;
  // background: var(--theme-background-body);
  transition: background 0.3s ease;

  @media (max-width: 599px) {
    padding-top: 64px;
  }

  @media (max-width: 1023px) {
    padding-top: 72px;
  }
}

// ===================================
// PRESERVE EXISTING STYLES
// ===================================

.help-popup {
  align-items: self-end;
  justify-content: center;
  align-self: self-end;
  border-radius: 0;
  padding: 0;
  &.q-btn.disabled {
    opacity: 1 !important;
    cursor: default !important;
    * {
      cursor: default !important;
    }
  }
}

.force-justify {
  justify-content: flex-end !important;
}

#left-panel .q-toolbar {
  border-bottom: 1px solid var(--theme-border-light);
}

.hide-separator {
  display: none;
}
</style>
