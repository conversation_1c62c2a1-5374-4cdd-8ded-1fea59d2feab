<template>
  <q-card
    class="page-card"
    :class="[
      cardClass,
      {
        'page-card--elevated': elevated,
        'page-card--outlined': outlined,
        'full-width': fullWidth,
      },
    ]"
    :flat="flat"
  >
    <!-- Card Header -->
    <q-card-section
      v-if="title || subtitle || $slots.header"
      class="page-card__header"
      :class="headerClass"
    >
      <slot name="header">
        <div class="row items-center no-wrap">
          <div class="col">
            <div class="row items-center">
              <q-icon
                v-if="icon"
                :name="icon"
                :size="iconSize"
                :color="iconColor"
                class="q-mr-md"
              />
              <div>
                <div class="page-card__title text-h5">{{ title }}</div>
                <div v-if="subtitle" class="page-card__subtitle text-subtitle2">
                  {{ subtitle }}
                </div>
              </div>
            </div>
          </div>
          <div v-if="$slots.actions" class="col-auto">
            <slot name="actions" />
          </div>
        </div>
      </slot>
    </q-card-section>

    <q-separator v-if="(title || subtitle || $slots.header) && !noSeparator" />

    <!-- Card Content -->
    <q-card-section
      v-if="$slots.default"
      class="page-card__content"
      :class="contentClass"
    >
      <slot />
    </q-card-section>

    <!-- Card Footer -->
    <template v-if="$slots.footer">
      <q-separator v-if="!noSeparator" />
      <q-card-section class="page-card__footer" :class="footerClass">
        <slot name="footer" />
      </q-card-section>
    </template>

    <!-- Loading Overlay -->
    <q-inner-loading
      :showing="loading"
      :color="$q.dark.isActive ? 'white' : 'primary'"
      :label="loadingLabel"
      label-class="text-weight-medium"
      label-style="font-size: 1.1em"
    >
      <div class="column items-center">
        <q-spinner-dots
          :color="$q.dark.isActive ? 'white' : 'primary'"
          size="2.5em"
        />
        <div v-if="loadingIcon" class="row items-center q-mt-sm">
          <q-icon :name="loadingIcon" class="q-mr-sm" size="md" />
          <span>{{ loadingLabel || 'Loading...' }}</span>
        </div>
      </div>
    </q-inner-loading>
  </q-card>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';

interface Props {
  title?: string;
  subtitle?: string;
  icon?: string;
  iconSize?: string;
  iconColor?: string;
  loading?: boolean;
  loadingLabel?: string;
  loadingIcon?: string;
  flat?: boolean;
  elevated?: boolean;
  outlined?: boolean;
  fullWidth?: boolean;
  noSeparator?: boolean;
  cardClass?: string;
  headerClass?: string;
  contentClass?: string;
  footerClass?: string;
}

withDefaults(defineProps<Props>(), {
  iconSize: '1.5rem',
  iconColor: 'primary',
  loadingLabel: 'Loading...',
  loadingIcon: 'img:robot.png',
  flat: false,
  elevated: true,
  outlined: false,
});

const $q = useQuasar();
</script>

<style lang="scss" scoped>
.page-card {
  border-radius: 16px;
  background: var(--q-card-bg, var(--q-background));
  color: var(--q-on-background);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  border: 1px solid transparent;

  // Elevated variant
  &--elevated {
    // Light mode shadows
    box-shadow:
      0 2px 4px rgba(0, 0, 0, 0.04),
      0 8px 16px rgba(0, 0, 0, 0.08);

    &:hover {
      transform: translateY(-2px);
      box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.08),
        0 12px 24px rgba(0, 0, 0, 0.12);
    }

    // Dark mode shadows
    .body--dark & {
      box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.2),
        0 8px 16px rgba(0, 0, 0, 0.3);

      &:hover {
        box-shadow:
          0 4px 8px rgba(0, 0, 0, 0.3),
          0 12px 24px rgba(0, 0, 0, 0.4);
      }
    }
  }

  // Outlined variant
  &--outlined {
    border: 1px solid var(--q-separator-color, var(--q-border));
    box-shadow: none;

    .body--dark & {
      border-color: var(--q-separator-dark, rgba(255, 255, 255, 0.12));
    }
  }

  // Flat variant
  &.q-card--flat {
    box-shadow: none;
  }

  // Header section
  &__header {
    background: linear-gradient(
      135deg,
      var(--q-primary) 0%,
      color-mix(in srgb, var(--q-primary) 80%, black 20%) 100%
    );
    color: white;
    padding: 1.5rem;

    .page-card__title {
      font-weight: 600;
      margin: 0;
      color: white;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .page-card__subtitle {
      opacity: 0.95;
      margin: 0.25rem 0 0 0;
      color: rgba(255, 255, 255, 0.95);
      text-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
    }

    // Dark mode adjustments for header
    .body--dark & {
      background: linear-gradient(
        135deg,
        var(--q-primary) 0%,
        color-mix(in srgb, var(--q-primary) 70%, white 10%) 100%
      );
    }

    // Icon color override in header
    .q-icon {
      color: white !important;
    }
  }

  // Content section
  &__content {
    padding: 1.5rem;
    background: var(--q-card-bg, var(--q-background));
    color: var(--q-on-background);

    @media (max-width: 599px) {
      padding: 1rem;
    }
  }

  // Footer section
  &__footer {
    padding: 1rem 1.5rem;
    background: color-mix(in srgb, var(--q-primary) 5%, var(--q-background));
    color: var(--q-on-background);
    border-top: 1px solid var(--q-separator-color, var(--q-border));

    .body--dark & {
      background: color-mix(in srgb, var(--q-primary) 8%, var(--q-background));
      border-top-color: var(--q-separator-dark, rgba(255, 255, 255, 0.12));
    }

    @media (max-width: 599px) {
      padding: 1rem;
    }
  }

  // Separator customization
  .q-separator {
    background: var(--q-separator-color, var(--q-border));

    .body--dark & {
      background: var(--q-separator-dark, rgba(255, 255, 255, 0.12));
    }
  }

  // Mobile responsive adjustments
  @media (max-width: 599px) {
    border-radius: 0;

    &--elevated {
      box-shadow: none !important;
    }

    &.full-width {
      margin: 0 -1rem;
    }
  }

  // Loading overlay adjustments
  .q-inner-loading {
    background: rgba(var(--q-background-rgb, 255, 255, 255), 0.8);
    color: var(--q-on-background);

    .body--dark & {
      background: rgba(var(--q-background-rgb, 0, 0, 0), 0.9);
    }
  }

  // Ensure proper contrast for all text elements
  .text-h5,
  .text-subtitle2,
  .text-body1,
  .text-body2,
  .text-caption {
    color: inherit;
  }

  // Action buttons in header
  &__header .q-btn {
    color: white;
    border-color: rgba(255, 255, 255, 0.3);

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.5);
    }
  }
}

// Global dark mode overrides for better integration
.body--dark {
  .page-card {
    // Enhanced dark mode card background
    // background: var(--q-dark-page, #121212);

    // Better text contrast in dark mode
    color: rgba(255, 255, 255, 0.87);

    // Improved separator visibility
    .q-separator {
      background: rgba(255, 255, 255, 0.12);
    }

    // Footer styling for dark mode
    .page-card__footer {
      background: color-mix(
        in srgb,
        var(--q-primary) 10%,
        var(--q-dark-page, #121212)
      );
    }
  }
}

// Light mode specific enhancements
.body--light {
  .page-card {
    background: white;
    color: rgba(0, 0, 0, 0.87);

    .q-separator {
      background: rgba(0, 0, 0, 0.12);
    }

    .page-card__footer {
      background: color-mix(in srgb, var(--q-primary) 3%, white);
    }
  }
}

// Transition improvements for theme switching
.page-card,
.page-card__header,
.page-card__content,
.page-card__footer {
  transition:
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease,
    box-shadow 0.3s ease;
}

// Disable transitions during theme switching
.theme-transitioning {
  .page-card,
  .page-card__header,
  .page-card__content,
  .page-card__footer {
    transition: none !important;
  }
}
</style>
