export type ThemeMode = 'light' | 'dark' | 'auto';
export type PaletteName = 'default' | 'ocean' | 'forest' | 'sunset' | 'royal';

export interface QuasarColors {
  primary: string;
  secondary: string;
  accent: string;
  dark: string;
  'dark-page': string;
  positive: string;
  negative: string;
  info: string;
  warning: string;
}

export interface ThemeColors extends QuasarColors {
  // Gray scale using Quasar variables
  gray100: string;
  gray200: string;
  gray300: string;
  gray400: string;
  gray500: string;
  gray600: string;
  gray700: string;
  gray800: string;
  gray900: string;

  // Primary Colors with variations
  primaryLight: string;
  primaryAccent: string;
  primaryInputIcon: string;
  primaryLightest: string;
  primaryDark: string;

  // Secondary Colors with variations
  secondaryLight: string;
  secondaryDark: string;
  secondaryAccent: string;

  // Neutral Colors using Quasar's blue-grey scale
  neutral50: string;
  neutral100: string;
  neutral200: string;
  neutral300: string;
  neutral400: string;
  neutral500: string;
  neutral600: string;
  neutral700: string;
  neutral800: string;
  neutral900: string;

  // Semantic Colors
  backgroundPrimary: string;
  backgroundBody: string;
  backgroundCard: string;
  backgroundDialog: string;
  textPrimary: string;
  textSecondary: string;
  textMuted: string;
  textColor?: string;
  textLabel: string;
  borderLight: string;
  borderDefault: string;
  borderStrong: string;

  // Special Colors
  tooltipTitle: string;
  bodyBackground: string;
  shadowColor: string;
  separatorColor: string;
}

export interface ThemePalette {
  name: PaletteName;
  displayName: string;
  description: string;
  light: ThemeColors;
  dark: ThemeColors;
  backgroundImage?: {
    light: string;
    dark: string;
  };
}

export interface ThemeConfig {
  mode: ThemeMode;
  palette: PaletteName;
  colors: ThemeColors;
}

// Default Palette - Professional Blue Theme
const defaultPalette: ThemePalette = {
  name: 'default',
  displayName: 'Default',
  description: 'Professional blue theme with clean aesthetics',
  backgroundImage: {
    light: "url('/images/ui/body-bg-light.jpg')",
    dark: 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%)',
  },
  light: {
    // Quasar Colors - Blue theme
    primary: '#1976D2', // blue-8
    secondary: '#26A69A', // teal-5
    accent: '#9C27B0', // purple-6
    dark: '#1d1d1d',
    'dark-page': '#121212',
    positive: '#21BA45', // green-6
    negative: '#C10015', // red-7
    info: '#31CCEC', // cyan-5
    warning: '#F2C037', // amber-6

    // Gray scale using Quasar grey variables
    gray100: '#fafafa', // grey-1
    gray200: '#f5f5f5', // grey-2
    gray300: '#eeeeee', // grey-3
    gray400: '#e0e0e0', // grey-4
    gray500: '#bdbdbd', // grey-5
    gray600: '#9e9e9e', // grey-6
    gray700: '#757575', // grey-7
    gray800: '#616161', // grey-8
    gray900: '#424242', // grey-9

    // Primary Colors with blue variations
    primaryLight: '#42A5F5', // blue-5
    primaryAccent: '#2196F3', // blue-6
    primaryInputIcon: '#1565C0', // blue-9
    primaryLightest: '#E3F2FD', // blue-1
    primaryDark: '#0D47A1', // blue-10

    // Secondary Colors with teal variations
    secondaryLight: '#4DB6AC', // teal-4
    secondaryDark: '#00695C', // teal-9
    secondaryAccent: '#009688', // teal-6

    // Neutral Colors using blue-grey scale
    neutral50: '#ECEFF1', // blue-grey-1
    neutral100: '#CFD8DC', // blue-grey-2
    neutral200: '#B0BEC5', // blue-grey-3
    neutral300: '#90A4AE', // blue-grey-4
    neutral400: '#78909C', // blue-grey-5
    neutral500: '#607D8B', // blue-grey-6
    neutral600: '#546E7A', // blue-grey-7
    neutral700: '#455A64', // blue-grey-8
    neutral800: '#37474F', // blue-grey-9
    neutral900: '#263238', // blue-grey-10

    // Semantic Colors
    backgroundPrimary: '#ffffff',
    backgroundBody: '#f8fafc',
    backgroundCard: '#ffffff',
    backgroundDialog: '#ffffff',
    textPrimary: '#1a202c',
    textSecondary: '#4a5568',
    textMuted: '#718096',
    textLabel: '#a0aec0',
    borderLight: '#e2e8f0',
    borderDefault: '#cbd5e0',
    borderStrong: '#a0aec0',

    // Special Colors
    tooltipTitle: '#2d3748',
    bodyBackground: "url('/images/ui/body-bg-light.jpg')",
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    separatorColor: 'rgba(0, 0, 0, 0.12)',
  },
  dark: {
    // Quasar Colors - Dark Blue theme
    primary: '#2196F3', // blue-6
    secondary: '#4DB6AC', // teal-4
    accent: '#E040FB', // purple-12
    dark: '#121212',
    'dark-page': '#1d1d1d',
    positive: '#4CAF50', // green-6
    negative: '#F44336', // red-6
    info: '#00BCD4', // cyan-6
    warning: '#FF9800', // orange-6

    // Gray scale for dark theme
    gray100: '#f5f5f5',
    gray200: '#eeeeee',
    gray300: '#e0e0e0',
    gray400: '#bdbdbd',
    gray500: '#9e9e9e',
    gray600: '#757575',
    gray700: '#616161',
    gray800: '#424242',
    gray900: '#212121',

    // Primary Colors for dark theme
    primaryLight: '#64B5F6', // blue-4
    primaryAccent: '#42A5F5', // blue-5
    primaryInputIcon: '#2196F3', // blue-6
    primaryLightest: '#0D47A1', // blue-10 (darker for dark theme)
    primaryDark: '#E3F2FD', // blue-1 (inverted)

    // Secondary Colors for dark theme
    secondaryLight: '#80CBC4', // teal-3
    secondaryDark: '#B2DFDB', // teal-2
    secondaryAccent: '#26A69A', // teal-5

    // Neutral Colors for dark theme
    neutral50: '#263238', // blue-grey-10 (inverted)
    neutral100: '#37474F', // blue-grey-9
    neutral200: '#455A64', // blue-grey-8
    neutral300: '#546E7A', // blue-grey-7
    neutral400: '#607D8B', // blue-grey-6
    neutral500: '#78909C', // blue-grey-5
    neutral600: '#90A4AE', // blue-grey-4
    neutral700: '#B0BEC5', // blue-grey-3
    neutral800: '#CFD8DC', // blue-grey-2
    neutral900: '#ECEFF1', // blue-grey-1

    // Semantic Colors for dark theme
    backgroundPrimary: '#1a202c',
    backgroundBody: '#0f172a',
    backgroundCard: '#2d3748',
    backgroundDialog: '#2d3748',
    textPrimary: '#f7fafc',
    textSecondary: '#e2e8f0',
    textMuted: '#cbd5e0',
    textLabel: '#a0aec0',
    borderLight: '#4a5568',
    borderDefault: '#2d3748',
    borderStrong: '#718096',

    // Special Colors for dark theme
    tooltipTitle: '#f7fafc',
    bodyBackground:
      'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%)',
    shadowColor: 'rgba(0, 0, 0, 0.3)',
    separatorColor: 'rgba(255, 255, 255, 0.12)',
  },
};

// Default Palette - Professional Navy Theme
const admiralPalette: ThemePalette = {
  name: 'default',
  displayName: 'Default',
  description: 'Professional navy theme with warm accents',
  backgroundImage: {
    light: "url('/images/ui/body-bg.jpg')  !important",
    dark: "url('/images/ui/body-bg.jpg') !important",
  },
  light: {
    // Quasar Colors - Navy theme with warm accents
    primary: '#0A2647', // Deep navy blue - main brand color
    secondary: '#FF8C42', // Warm orange for contrast and warmth
    accent: '#20B2AA', // Light sea green for fresh accents
    dark: '#1d1d1d',
    'dark-page': '#121212',
    positive: '#21BA45', // green-6
    negative: '#C10015', // red-7
    info: '#17A2B8', // Darker info blue to complement navy
    warning: '#FF9F43', // Warm amber that works with orange secondary

    // Gray scale using neutral tones that complement navy
    gray100: '#fafafa', // grey-1
    gray200: '#f5f5f5', // grey-2
    gray300: '#eeeeee', // grey-3
    gray400: '#e0e0e0', // grey-4
    gray500: '#bdbdbd', // grey-5
    gray600: '#9e9e9e', // grey-6
    gray700: '#757575', // grey-7
    gray800: '#616161', // grey-8
    gray900: '#424242', // grey-9

    // Primary Colors with navy variations
    primaryLight: '#4A7BA7', // Lighter navy for hover states
    primaryAccent: '#2E5D8A', // Medium navy for active states
    primaryInputIcon: '#0A2647', // Main navy for icons
    primaryLightest: '#E8F1FC', // Very light navy tint for backgrounds
    primaryDark: '#061B34', // Even darker navy

    // Secondary Colors with warm orange variations
    secondaryLight: '#FFB366', // Lighter orange
    secondaryDark: '#E6792B', // Darker orange
    secondaryAccent: '#FF6B1A', // Vibrant orange accent

    // Neutral Colors using blue-grey scale that complements navy
    neutral50: '#F8FAFC', // Very light blue-grey
    neutral100: '#F1F5F9', // Light blue-grey
    neutral200: '#E2E8F0', // Light blue-grey
    neutral300: '#CBD5E1', // Medium-light blue-grey
    neutral400: '#94A3B8', // Medium blue-grey
    neutral500: '#64748B', // Medium blue-grey
    neutral600: '#475569', // Medium-dark blue-grey
    neutral700: '#334155', // Dark blue-grey
    neutral800: '#1E293B', // Very dark blue-grey
    neutral900: '#0F172A', // Near-black blue-grey

    // Semantic Colors for light theme
    backgroundPrimary: '#ffffff',
    backgroundBody: '#f8fafc',
    backgroundCard: '#ffffff',
    backgroundDialog: '#ffffff',
    textPrimary: '#0F172A', // Very dark blue-grey instead of pure black
    textSecondary: '#475569', // Navy-influenced dark grey
    textMuted: '#64748B', // Medium blue-grey
    textLabel: '#94A3B8', // Light blue-grey
    borderLight: '#E2E8F0', // Light blue-grey
    borderDefault: '#CBD5E1', // Medium-light blue-grey
    borderStrong: '#94A3B8', // Medium blue-grey

    textColor: '#fff', // text color for light theme

    // Special Colors
    tooltipTitle: '#1E293B',
    bodyBackground: "url('/images/ui/body-bg-light.jpg')",
    shadowColor: 'rgba(10, 38, 71, 0.1)', // Navy-tinted shadow
    separatorColor: 'rgba(10, 38, 71, 0.12)', // Navy-tinted separator
  },
  dark: {
    // Quasar Colors - Dark Navy theme
    primary: '#0A2647', // Lighter navy for dark mode visibility
    secondary: '#FFB366', // Lighter orange for dark mode
    accent: '#4FD1C7', // Brighter teal for dark mode
    dark: '#0F172A',
    'dark-page': '#0A2647', // Use original navy as dark page background
    positive: '#4CAF50', // green-6
    negative: '#F44336', // red-6
    info: '#26C6DA', // Bright cyan for dark mode
    warning: '#FFB74D', // Warm amber for dark mode

    // Gray scale for dark theme - warmer greys
    gray100: '#f5f5f5',
    gray200: '#eeeeee',
    gray300: '#e0e0e0',
    gray400: '#bdbdbd',
    gray500: '#9e9e9e',
    gray600: '#757575',
    gray700: '#616161',
    gray800: '#424242',
    gray900: '#212121',

    // Primary Colors for dark theme
    primaryLight: '#6B9BC9', // Even lighter navy for dark mode
    primaryAccent: '#5A8DB8', // Medium-light navy
    primaryInputIcon: '#4A7BA7', // Main primary for dark mode
    primaryLightest: '#2E5D8A', // Darker for dark theme (inverted logic)
    primaryDark: '#E8F1FC', // Light navy for dark theme text

    // Secondary Colors for dark theme
    secondaryLight: '#FFCC80', // Very light orange
    secondaryDark: '#FF8C42', // Original orange
    secondaryAccent: '#FF6B1A', // Vibrant orange

    // Neutral Colors for dark theme
    neutral50: '#0F172A', // Darkest (inverted)
    neutral100: '#1E293B', // Very dark
    neutral200: '#334155', // Dark
    neutral300: '#475569', // Medium-dark
    neutral400: '#64748B', // Medium
    neutral500: '#94A3B8', // Medium-light
    neutral600: '#CBD5E1', // Light
    neutral700: '#E2E8F0', // Very light
    neutral800: '#F1F5F9', // Near white
    neutral900: '#F8FAFC', // Lightest

    // Semantic Colors for dark theme
    backgroundPrimary: '#0F172A', // Very dark navy-grey
    backgroundBody: '#0A2647', // Original navy
    backgroundCard: '#1E293B', // Dark navy-grey
    backgroundDialog: '#1E293B', // Dark navy-grey
    textPrimary: '#F8FAFC', // Very light
    textSecondary: '#E2E8F0', // Light
    textMuted: '#CBD5E1', // Medium-light
    textLabel: '#94A3B8', // Medium
    borderLight: '#334155', // Dark
    borderDefault: '#475569', // Medium-dark
    borderStrong: '#64748B', // Medium

    textColor: '#fff', // text color for light theme

    // Special Colors for dark theme
    tooltipTitle: '#F8FAFC',
    bodyBackground:
      'linear-gradient(135deg, #0A2647 0%, #1A3A5C 50%, #2E5D8A 100%)',
    shadowColor: 'rgba(0, 0, 0, 0.3)', // Standard dark shadow
    separatorColor: 'rgba(255, 255, 255, 0.12)', // Standard dark separator
  },
};

// Ocean Palette - Cyan/Teal Theme
const oceanPalette: ThemePalette = {
  name: 'ocean',
  displayName: 'Ocean',
  description: 'Refreshing ocean-inspired cyan and teal theme',
  backgroundImage: {
    light: 'linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 50%, #80deea 100%)',
    dark: 'linear-gradient(135deg, #006064 0%, #00838f 50%, #0097a7 100%)',
  },
  light: {
    ...defaultPalette.light,
    primary: '#00ACC1', // cyan-7
    secondary: '#26C6DA', // cyan-5
    accent: '#00BCD4', // cyan-6
    primaryLight: '#4DD0E1', // cyan-4
    primaryAccent: '#26C6DA', // cyan-5
    primaryInputIcon: '#0097A7', // cyan-8
    primaryLightest: '#E0F7FA', // cyan-1
    primaryDark: '#006064', // cyan-10
    secondaryLight: '#80DEEA', // cyan-3
    secondaryDark: '#00838F', // cyan-9
    secondaryAccent: '#18FFFF', // cyan-12
    bodyBackground:
      'linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 50%, #80deea 100%)',
  },
  dark: {
    ...defaultPalette.dark,
    primary: '#26C6DA', // cyan-5
    secondary: '#4DD0E1', // cyan-4
    accent: '#18FFFF', // cyan-12
    primaryLight: '#80DEEA', // cyan-3
    primaryAccent: '#4DD0E1', // cyan-4
    primaryInputIcon: '#26C6DA', // cyan-5
    primaryLightest: '#006064', // cyan-10
    primaryDark: '#E0F7FA', // cyan-1
    secondaryLight: '#B2EBF2', // cyan-2
    secondaryDark: '#E0F7FA', // cyan-1
    secondaryAccent: '#84FFFF', // cyan-11
    bodyBackground:
      'linear-gradient(135deg, #006064 0%, #00838f 50%, #0097a7 100%)',
  },
};

// Forest Palette - Green Theme
const forestPalette: ThemePalette = {
  name: 'forest',
  displayName: 'Forest',
  description: 'Natural green forest theme with earthy tones',
  backgroundImage: {
    light: 'linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 50%, #a5d6a7 100%)',
    dark: 'linear-gradient(135deg, #1b5e20 0%, #2e7d32 50%, #388e3c 100%)',
  },
  light: {
    ...defaultPalette.light,
    primary: '#43A047', // green-7
    secondary: '#66BB6A', // green-5
    accent: '#4CAF50', // green-6
    primaryLight: '#81C784', // green-4
    primaryAccent: '#4CAF50', // green-6
    primaryInputIcon: '#2E7D32', // green-9
    primaryLightest: '#E8F5E9', // green-1
    primaryDark: '#1B5E20', // green-10
    secondaryLight: '#A5D6A7', // green-3
    secondaryDark: '#388E3C', // green-8
    secondaryAccent: '#00E676', // green-13
    bodyBackground:
      'linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 50%, #a5d6a7 100%)',
  },
  dark: {
    ...defaultPalette.dark,
    primary: '#66BB6A', // green-5
    secondary: '#81C784', // green-4
    accent: '#69F0AE', // green-12
    primaryLight: '#A5D6A7', // green-3
    primaryAccent: '#81C784', // green-4
    primaryInputIcon: '#4CAF50', // green-6
    primaryLightest: '#1B5E20', // green-10
    primaryDark: '#E8F5E9', // green-1
    secondaryLight: '#C8E6C9', // green-2
    secondaryDark: '#E8F5E9', // green-1
    secondaryAccent: '#B9F6CA', // green-11
    bodyBackground:
      'linear-gradient(135deg, #1b5e20 0%, #2e7d32 50%, #388e3c 100%)',
  },
};

// Sunset Palette - Orange/Red Theme
const sunsetPalette: ThemePalette = {
  name: 'sunset',
  displayName: 'Sunset',
  description: 'Warm sunset colors with orange and red tones',
  backgroundImage: {
    light: 'linear-gradient(135deg, #fff3e0 0%, #ffe0b2 50%, #ffcc80 100%)',
    dark: 'linear-gradient(135deg, #bf360c 0%, #d84315 50%, #f4511e 100%)',
  },
  light: {
    ...defaultPalette.light,
    primary: '#F4511E', // deep-orange-7
    secondary: '#FF7043', // deep-orange-5
    accent: '#FF5722', // deep-orange-6
    primaryLight: '#FF8A65', // deep-orange-4
    primaryAccent: '#FF5722', // deep-orange-6
    primaryInputIcon: '#D84315', // deep-orange-9
    primaryLightest: '#FBE9E7', // deep-orange-1
    primaryDark: '#BF360C', // deep-orange-10
    secondaryLight: '#FFAB91', // deep-orange-3
    secondaryDark: '#E64A19', // deep-orange-8
    secondaryAccent: '#FF3D00', // deep-orange-13
    bodyBackground:
      'linear-gradient(135deg, #fff3e0 0%, #ffe0b2 50%, #ffcc80 100%)',
  },
  dark: {
    ...defaultPalette.dark,
    primary: '#FF7043', // deep-orange-5
    secondary: '#FF8A65', // deep-orange-4
    accent: '#FF6E40', // deep-orange-12
    primaryLight: '#FFAB91', // deep-orange-3
    primaryAccent: '#FF8A65', // deep-orange-4
    primaryInputIcon: '#FF5722', // deep-orange-6
    primaryLightest: '#BF360C', // deep-orange-10
    primaryDark: '#FBE9E7', // deep-orange-1
    secondaryLight: '#FFCCBC', // deep-orange-2
    secondaryDark: '#FBE9E7', // deep-orange-1
    secondaryAccent: '#FF9E80', // deep-orange-11
    bodyBackground:
      'linear-gradient(135deg, #bf360c 0%, #d84315 50%, #f4511e 100%)',
  },
};

// Royal Palette - Purple Theme
const royalPalette: ThemePalette = {
  name: 'royal',
  displayName: 'Royal',
  description: 'Elegant royal purple theme with luxury appeal',
  backgroundImage: {
    light: 'linear-gradient(135deg, #f3e5f5 0%, #e1bee7 50%, #ce93d8 100%)',
    dark: 'linear-gradient(135deg, #4a148c 0%, #6a1b9a 50%, #7b1fa2 100%)',
  },
  light: {
    ...defaultPalette.light,
    primary: '#7B1FA2', // purple-8
    secondary: '#9C27B0', // purple-6
    accent: '#AB47BC', // purple-5
    primaryLight: '#BA68C8', // purple-4
    primaryAccent: '#9C27B0', // purple-6
    primaryInputIcon: '#4A148C', // purple-10
    primaryLightest: '#F3E5F5', // purple-1
    primaryDark: '#4A148C', // purple-10
    secondaryLight: '#CE93D8', // purple-3
    secondaryDark: '#8E24AA', // purple-7
    secondaryAccent: '#D500F9', // purple-13
    bodyBackground:
      'linear-gradient(135deg, #f3e5f5 0%, #e1bee7 50%, #ce93d8 100%)',
  },
  dark: {
    ...defaultPalette.dark,
    primary: '#AB47BC', // purple-5
    secondary: '#BA68C8', // purple-4
    accent: '#E040FB', // purple-12
    primaryLight: '#CE93D8', // purple-3
    primaryAccent: '#BA68C8', // purple-4
    primaryInputIcon: '#9C27B0', // purple-6
    primaryLightest: '#4A148C', // purple-10
    primaryDark: '#F3E5F5', // purple-1
    secondaryLight: '#E1BEE7', // purple-2
    secondaryDark: '#F3E5F5', // purple-1
    secondaryAccent: '#EA80FC', // purple-11
    bodyBackground:
      'linear-gradient(135deg, #4a148c 0%, #6a1b9a 50%, #7b1fa2 100%)',
  },
};

// All available palettes
export const themePalettes: Record<PaletteName, ThemePalette> = {
  default: admiralPalette,
  ocean: oceanPalette,
  forest: forestPalette,
  sunset: sunsetPalette,
  royal: royalPalette,
};

// Default theme configuration
export const defaultThemeConfig: ThemeConfig = {
  mode: 'light',
  palette: 'default',
  colors: defaultPalette.dark,
};

// Theme configuration getter
export const getThemeConfig = (
  mode: ThemeMode,
  palette: PaletteName = 'default',
): ThemeConfig => {
  const selectedPalette = themePalettes[palette];

  let effectiveMode = mode;

  if (mode === 'auto') {
    effectiveMode = window.matchMedia('(prefers-color-scheme: dark)').matches
      ? 'dark'
      : 'light';
  }

  return {
    mode: effectiveMode,
    palette,
    colors:
      effectiveMode === 'dark' ? selectedPalette.dark : selectedPalette.light,
  };
};

// Get all available palettes for UI selection
export const getAvailablePalettes = (): ThemePalette[] => {
  return Object.values(themePalettes);
};

// Utility function to get palette preview colors
export const getPalettePreview = (
  paletteName: PaletteName,
  mode: ThemeMode = 'light',
) => {
  const palette = themePalettes[paletteName];
  const colors = mode === 'dark' ? palette.dark : palette.light;
  return {
    primary: colors.primary,
    secondary: colors.secondary,
    accent: colors.accent,
  };
};
