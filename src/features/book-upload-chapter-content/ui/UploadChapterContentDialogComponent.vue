<template>
  <q-dialog v-model="showDialog" persistent>
    <q-card style="width: 500px; max-width: 80vw">
      <q-card-section class="q-pa-none">
        <q-bar class="bg-primary text-white">
          <q-icon name="img:robot.png" />

          <div>Upload Chapter Content</div>

          <q-space />

          <q-btn dense flat icon="close" @click="showDialog = false">
            <q-tooltip class="bg-white text-primary">Close</q-tooltip>
          </q-btn>
        </q-bar>
      </q-card-section>

      <q-separator />

      <q-card-section>
        <div
          :class="{
            'fade-content': true,
            'fade-enter-active': entering,
            'fade-leave-active': leaving,
          }"
        >
          <MessageError
            icon="info"
            icon-color="primary"
            message="When you upload your chapter content, it will automatically set a chapter title if you use a <h1> heading 1 at the very top of your document."
            text-color="text-black"
          />
          <div class="q-mt-sm" v-html="dialogMessage"></div>
        </div>
      </q-card-section>

      <q-separator />

      <q-card-actions align="left">
        <q-btn
          v-if="showUploadFileButton && !showAddContentButton"
          flat
          color="primary"
          label="Choose File to Upload"
          @click="$refs.fileInput.click()"
          icon="upload"
        >
          <q-tooltip
            >Select a Microsoft Word file to add the content to the selected
            chapter.</q-tooltip
          >
        </q-btn>
        <q-btn
          v-if="showAddContentButton"
          flat
          color="primary"
          label="Add Content"
          @click="handleAddContent"
          icon="add"
        >
          <q-tooltip>Add content to the selected chapter.</q-tooltip>
        </q-btn>
        <q-btn flat label="Close" @click="showDialog = false"></q-btn>
      </q-card-actions>
      <input
        type="file"
        ref="fileInput"
        @change="handleFileChange"
        @click="handleFileClick"
        style="display: none"
        accept=".docx"
      />
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import mammoth from 'mammoth';
import type { Editor } from 'tinymce';
import {
  isH1OnTop,
  saveBookVersionOnUploadContent,
  setButtonTooltip,
} from 'src/features/book-upload-chapter-content';
import MessageError from 'src/entities/book/ui/MessageError.vue';

/**
 * Custom component for file upload and confirmation dialog
 * @component UploadDialogComponent
 * @prop {String} bookId - The ID of the book to which content will be uploaded.
 * @prop {Function} onUpdate - Callback function to be called after content update.
 * @prop {Function} setEnabled - Function to enable/disable editor.
 * @prop {Object} editor - The editor object (assumed to be TinyMCE) for content manipulation.
 */
const props = defineProps<{
  bookId: string;
  onUpdate?: () => void;
  setEnabled?: (enabled: boolean) => void;
  editor?: Editor;
}>();

const fileInput = ref<HTMLInputElement | null>(null);
const dialogMessage = ref<string>(
  'Please choose a file to upload. Ensure that the file format is .docx.',
);
const showDialog = ref<boolean>(true);
const showUploadFileButton = ref<boolean>(true);
const showAddContentButton = ref<boolean>(false);
let previousFile: File | null = null;

/**
 * Handles file click event when a file is selected for upload. We will empty the file to trigger on change
 * @param {Event} event - The change event containing the selected file.
 */
const handleFileClick = async (event: Event) => {
  // empty the file upload
  event.target.value = '';
};

/**
 * Handles file change event when a file is selected for upload.
 * @param {Event} event - The change event containing the selected file.
 */
const handleFileChange = async (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0];
  showUploadFileButton.value = true;
  // Check if the file exists
  if (!file) {
    dialogMessage.value =
      'Unsupported file format. Please upload a .docx file.';
    return;
  }
  // Check if the file is the same as the previously uploaded file
  if (previousFile && file.name === previousFile.name) {
    dialogMessage.value =
      'The uploaded file is currently in use. Please select a different file.';
    return;
  }
  if (
    file.type ===
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ) {
    dialogMessage.value = `<strong>Filename:</strong> ${file.name}`;
    // dialogMessage.value = `Your Chapter Title will be generated from the heading in your document <br> <strong>Filename:</strong> ${file.name}`;
    showUploadFileButton.value = false;
    showAddContentButton.value = true; // Show "Add Content" button after successful file selection
  } else {
    dialogMessage.value =
      'Unsupported file format. Please upload a .docx file.';
  }
  // Store the uploaded file
  previousFile = file;
};

/**
 * Handles the "Add Content" button click event.
 */
const handleAddContent = async () => {
  const file = fileInput.value?.files?.[0];

  const arrayBuffer = await file.arrayBuffer();
  const result = await mammoth.convertToHtml({ arrayBuffer });
  // if (!result.value.trim() || !isH1OnTop(result.value)) {
  if (!result.value.trim()) {
    showUploadFileButton.value = true;
    dialogMessage.value =
      'Document did not contain any content, or did not match the expected format.';
    showUploadFileButton.value = true;
    showAddContentButton.value = false;
  } else {
    // hide the dialog
    showDialog.value = false;
    // Update editor content, disabled state and tooltip
    props.setEnabled(false);
    props.editor.setContent(result.value);
    setButtonTooltip(false);
    // Save a new book version
    await saveBookVersionOnUploadContent(props.bookId, file.name);

    props.onUpdate?.();

    dialogMessage.value = 'Your content is successfully uploaded!';
    showUploadFileButton.value = false;
    showAddContentButton.value = false;
  }
};
</script>

<style scoped lang="scss">
.fade-content {
  transition: opacity 0.3s ease-in-out;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease-in-out;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
