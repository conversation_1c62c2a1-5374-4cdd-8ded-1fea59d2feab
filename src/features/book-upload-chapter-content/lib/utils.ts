import { getTodayFormattedDateTime } from 'src/features/book-prompts';
import { createBookVersion } from 'src/features/book-versioning';

/**
 * Checks if the <h1> tag is at the top of the HTML content.
 * @param {string} htmlContent - The HTML content to check.
 * @returns {boolean} True if <h1> tag is at the top, false otherwise.
 */
export const isH1OnTop = (htmlContent: string): boolean => {
  const h1Regex = /<h1\b[^>]*>.*?<\/h1>/i;
  const h1Matches = htmlContent.match(h1Regex);
  return !h1Matches || htmlContent.indexOf(h1Matches[0]) === 0;
};

/**
 * Saves the book version when content is successfully uploaded.
 * @param {string} bookId - The ID of the book.
 * @param {string} filename - The name of the uploaded file.
 */
export const saveBookVersionOnUploadContent = async (
  bookId: string,
  filename: string,
): Promise<void> => {
  const chapterTitle = window.selectedChapter; // Assuming window.selectedChapter is defined elsewhere
  const formattedDateTime = getTodayFormattedDateTime();
  const versionNote = {
    Date: formattedDateTime,
    Prompt: `Upload chapter content from a .docx file.`,
    Filename: filename,
    Chapter: chapterTitle,
  };

  const description = Object.entries(versionNote)
    .map(([key, value]) => `${key}: ${value}`)
    .join('\n\n');

  await createBookVersion(bookId, formattedDateTime, description);
};

/**
 * Sets the tooltip title for a specific button based on the upload content button's disabled state.
 * @param {boolean} isUploadContentButtonDisabled - Indicates whether the upload content button is disabled.
 */
export const setButtonTooltip = (isUploadContentButtonDisabled: boolean) => {
  const button = document.querySelector(
    'button[aria-label="upload-chapter-content"]',
  ) as HTMLElement;
  if (button) {
    button.setAttribute(
      'title',
      !isUploadContentButtonDisabled
        ? 'Content can be uploaded to new chapters.'
        : 'Select a Microsoft Word file to upload your chapter content.',
    );
  }
};
