// @ts-ignore
import MarkdownIt from 'markdown-it';

/**
 * Converts Markdown content to HTML.
 *
 * This function uses the markdown-it library to parse and convert
 * the provided Markdown content into HTML. It initializes markdown-it
 * with the 'commonmark' preset and enables the typographer option for
 * better typography (e.g., replacing straight quotes with curly quotes).
 *
 * @param {string} markDownContent - The Markdown content to be converted.
 * @returns {string} - The resulting HTML content.
 */
export function markdownToHtmlConverter(markDownContent: string): string {
  // Initialize markdown-it with the 'commonmark' preset and typographer enabled.
  const md = new MarkdownIt('commonmark', {
    xhtmlOut: true,
    breaks: true,
    typographer: true,
  });

  // Convert the Markdown content to HTML and return it.
  return md.render(markDownContent);
}
