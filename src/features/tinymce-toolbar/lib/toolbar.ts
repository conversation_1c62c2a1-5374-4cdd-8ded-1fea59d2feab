import { Screen } from 'quasar';
import { userLoggedIn } from 'src/entities/user';
import { isSafari } from 'src/entities/setting';

/**
 * @class ToolbarManager
 * @description Manages the toolbar buttons and menu bar configuration for TinyMCE.
 */
export class ToolbarManager {
  private readonly allowUploadChapterContent: boolean;
  private readonly canPageSetup: boolean;
  private readonly canPrintBook: boolean;
  private readonly isReviewMode: boolean;
  private readonly moreButtons: string;
  private readonly selectionMenus: string;

  /**
   * @constructor
   * @param {boolean} allowUploadChapterContent - Determines if chapter content upload is allowed.
   * @param {boolean} canPageSetup - Determines if the page setup is allowed.
   * @param {boolean} canPrintBook - Determines if printing the book is allowed.
   * @param {boolean} isReviewMode - Determines if the editor is in review mode.
   */
  constructor(
    allowUploadChapterContent: boolean,
    canPageSetup: boolean,
    canPrintBook: boolean,
    isReviewMode: string,
  ) {
    this.allowUploadChapterContent = allowUploadChapterContent;
    this.canPageSetup = canPageSetup;
    this.canPrintBook = canPrintBook;
    this.isReviewMode = isReviewMode.length > 0 ? true : false;
    this.moreButtons =
      'removeformat searchreplace bullist numlist alignleft aligncenter alignright alignjustify';
    this.selectionMenus = 'rewrite simplify write add-story edit';
  }

  /**
   * @private
   * @method removeButton
   * @description Removes a specific button from a toolbar configuration string.
   * @param {string} button - The button to remove.
   * @param {string} toolbar - The toolbar configuration string.
   * @returns {string} - The updated toolbar configuration string.
   */
  private removeButton(button: string, toolbar: string): string {
    return toolbar
      .replace(new RegExp(`\\s*\\|\\s*${button}\\s*`, 'g'), '')
      .replace(/\s*\|\s*$/, ''); // To handle trailing separator
  }

  /**
   * @public
   * @method getDefaultToolbarButtons
   * @description Returns the default toolbar buttons configuration.
   * @returns {string} - The default toolbar buttons.
   */
  public getDefaultToolbarButtons(): string {
    const mannyToolbar = Screen.gt.sm ? '| manny' : '';
    let defaultBtns = `undo redo | bookchapterselect | blocks | fontsize ${mannyToolbar} | uploadchaptercontent | transcribechaptermedia | bookspeech | bold italic underline forecolor | link image-insert | `;

    if (!this.allowUploadChapterContent) {
      defaultBtns = this.removeButton('uploadchaptercontent', defaultBtns);
    }

    // defaultBtns = this.removeButton('bookspeech', defaultBtns);

    return defaultBtns;
  }

  /**
   * @public
   * @method getMannyButtons
   * @description Returns the manny buttons configuration.
   * @returns {string} - The manny buttons configuration.
   */
  public getMannyButtons(): string {
    let defaultBtns =
      'generatechaptercontent | bookspeech | transcribechaptermedia | uploadchaptercontent | saveversion | exporttoword | exportbookimages | bookpagesetup | bookprinter |  ';

    if (!this.allowUploadChapterContent) {
      defaultBtns = this.removeButton('uploadchaptercontent', defaultBtns);
    }
    if (!this.canPageSetup) {
      defaultBtns = this.removeButton('bookpagesetup', defaultBtns);
    }
    if (!this.canPrintBook || isSafari()) {
      defaultBtns = this.removeButton('bookprinter', defaultBtns);
    }
    return defaultBtns;
  }

  /**
   * @public
   * @method getMenuMannyButtons
   * @description Returns the manny buttons configuration.
   * @returns {string} - The manny buttons configuration.
   */
  public getMenuMannyButtons(): string {
    let defaultBtns = this.getMannyButtons() + this.selectionMenus;
    defaultBtns = this.removeButton('saveversion', defaultBtns);
    defaultBtns = this.removeButton('exporttoword', defaultBtns);
    defaultBtns = this.removeButton('bookpagesetup', defaultBtns);
    defaultBtns = this.removeButton('bookprinter', defaultBtns);
    return defaultBtns;
  }
  /**
   * @public
   * @method getMannyFileButtons
   * @description Returns the manny file buttons con~figuration.
   * @returns {string} - The manny file buttons configuration.
   */
  public getMannyFileButtons(): string {
    let defaultBtns =
      'missionstatement | saveversion | exporttoword | exportbookimages | bookprinter | bookpagesetup';

    if (!this.canPrintBook) {
      defaultBtns = this.removeButton('bookprinter', defaultBtns);
    }
    if (!this.canPageSetup) {
      defaultBtns = this.removeButton('bookpagesetup', defaultBtns);
    }
    return defaultBtns;
  }

  /**
   * @public
   * @method getMannyFileButtons
   * @description Returns the manny file buttons con~figuration.
   * @returns {string} - The manny file buttons configuration.
   */
  public getSelectionButtons(): string {
    let addComment = '';
    if (
      userLoggedIn?.value &&
      userLoggedIn?.value?.features?.includes('review')
    ) {
      addComment = ' | comment |';
    }
    if (this.isReviewMode) {
      addComment = ' | comment |';
    }

    let menus =
      this.selectionMenus +
      ' | ' +
      addComment +
      this.getMoreButtons() +
      ' bold italic underline';
    if (this.isReviewMode) {
      menus = this.selectionMenus + addComment;
    }
    return menus;
  }
  /**
   * @public
   * @method getToolbarButtons
   * @description Returns the complete toolbar buttons configuration, ensuring no duplicate buttons are included.
   * @param {boolean} isScreenSmall - Determines if the screen is small.
   * @returns {string} - The complete toolbar buttons configuration.
   */
  public getToolbarButtons(isScreenSmall: boolean): string {
    if (this.isReviewMode) {
      return 'bookchapterselect';
    }

    const defaultButtons = this.getDefaultToolbarButtons();
    const mannyButtons = this.getMannyButtons();
    const moreButtons = this.getMoreButtons();

    const allButtons = isScreenSmall
      ? `${defaultButtons} | ${mannyButtons} | ${moreButtons}`
      : `${defaultButtons} | more`;

    // Remove duplicate button entries
    const uniqueButtons = Array.from(new Set(allButtons.split(' | '))).join(
      ' | ',
    );
    return uniqueButtons;
  }

  /**
   * @public
   * @method getMenuBars
   * @description Returns the menu bars configuration.
   * @returns {string} - The menu bars configuration.
   */
  public getMenuBars(): string {
    return this.isReviewMode ? '' : 'file manuscriptr edit insert format tools';
  }

  /**
   * @public
   * @method getMoreButtons
   * @description Returns the additional toolbar buttons configuration.
   * @returns {string} - The more buttons configuration.
   */
  public getMoreButtons(): string {
    return this.moreButtons;
  }

  /**
   * @public
   * @method getSelectionMenus
   * @description Returns the selection menus configuration.
   * @returns {string} - The selection menus configuration.
   */
  public getSelectionMenus(): string {
    return this.selectionMenus;
  }
}
