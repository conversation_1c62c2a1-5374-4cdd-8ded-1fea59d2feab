import type TinyMC<PERSON> from 'tinymce';
import type { Editor } from 'tinymce';

export function createReviewPlugin(
  tinymce: typeof TinyMCE,
  onSelect?: () => void,
) {
  tinymce.PluginManager.add('custom-comment', (editor: Editor) => {
    editor.ui.registry.addButton('comment', {
      icon: 'comment-add',
      text: 'Add Comment',
      onAction: ({ setEnabled, setText }) => {
        setText('Loading...');
        setEnabled(false);

        setTimeout(() => {
          onSelect?.();
          setText('Add Comment');
          setEnabled(true);
        }, 1500);
      },
    });
  });
}
