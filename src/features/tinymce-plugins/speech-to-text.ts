import type TinyMC<PERSON> from 'tinymce';
import type { Editor } from 'tinymce';
import RecordRTC, { StereoAudioRecorder } from 'recordrtc';
import { readAsDataURL } from 'src/shared/lib/file-reader';
import { Notify } from 'quasar';
import { getSpeechRecognitionToken } from 'src/shared/api/speech';

export function registerSpeechToTextPlugin(
  tinymce: typeof TinyMCE,
  onUpdate?: (transcript: string) => void,
) {
  const pluginConstructor = (editor: Editor) => {
    editor.ui.registry.addIcon(
      'mic',
      '<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="M480-400q-50 0-85-35t-35-85v-240q0-50 35-85t85-35q50 0 85 35t35 85v240q0 50-35 85t-85 35Zm-40 280v-123q-104-14-172-93t-68-184h80q0 83 58.5 141.5T480-320q83 0 141.5-58.5T680-520h80q0 105-68 184t-172 93v123h-80Z"/></svg>',
    );
    editor.ui.registry.addIcon(
      'stop-recording',
      '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24" height="24" viewBox="0 0 72 72"><use xlink:href="#B" fill="#ea5a47"/><use xlink:href="#B" fill="none" stroke="#000" stroke-linejoin="round" stroke-width="2"/><defs ><path id="B" d="M59.035 60h-46.07a.968.968 0 0 1-.965-.965v-46.07a.968.968 0 0 1 .965-.965h46.07a.968.968 0 0 1 .965.965v46.07a.968.968 0 0 1-.965.965z"/></defs></svg>',
    );

    let isRecording = false;
    let socket: WebSocket | null = null;
    let recorder: RecordRTC | null = null;

    editor.ui.registry.addButton('dictate', {
      tooltip: 'Dictation',
      icon: 'mic',
      async onAction({ setText, setIcon, setEnabled }) {
        if (isRecording) {
          window.dictation = '';

          // Clean up

          recorder?.stopRecording();
          recorder?.destroy();
          recorder = null;

          socket?.send(JSON.stringify({ terminate_session: true }));
          socket?.close();
          socket = null;

          setText('');
          setIcon('mic');
        } else {
          window.dictation = window.selectedChapterId;

          setText('Loading...');
          setEnabled(false);

          // Get AssemblyAI token from backend and initialize connection
          const token = await getSpeechRecognitionToken();
          socket = new WebSocket(
            `wss://api.assemblyai.com/v2/realtime/ws?sample_rate=16000&token=${token}`,
          );
          socket.onerror = (event) => {
            console.error(event);
            socket!.close();
          };
          socket.onclose = (event) => {
            console.log(event);
            socket = null;
          };

          interface MessageBase {
            audio_start: number;
            audio_end: number;
            confidence: number;
            text: string;
            words: Array<{
              text: string;
              confidence: number;
              start: number;
              end: number;
            }>;
            /** ISO timestamp */
            created: string;
          }
          interface MessagePartial extends MessageBase {
            message_type: 'PartialTranscript';
          }
          interface MessageFinal extends MessageBase {
            message_type: 'FinalTranscript';
            punctuated: boolean;
            text_formatted: boolean;
          }
          type TranscriptionMessage = MessagePartial | MessageFinal;

          let texts: Record<number, string> = {};
          let node: Text;
          socket.onmessage = ({ data }) => {
            const res = JSON.parse(data) as TranscriptionMessage;

            // Combine all the draft pieces together
            texts[res.audio_start] = res.text;
            const keys = Object.keys(texts).map((start) => Number(start));
            keys.sort((a, b) => a - b);
            let msg = '';
            for (const key of keys) {
              if (texts[key]) {
                msg += ' ' + texts[key];
              }
            }

            try {
              if (msg) {
                const range = editor.selection.getRng();
                if (
                  range.commonAncestorContainer.ownerDocument?.activeElement &&
                  (range.commonAncestorContainer.ownerDocument?.activeElement
                    ?.id == 'editor' ||
                    range.commonAncestorContainer.ownerDocument?.activeElement
                      ?.id == 'tinymce')
                ) {
                  if (range.collapsed) {
                    // Nothing selected, add new text
                    node = document.createTextNode(msg);
                    range.insertNode(node);
                  } else {
                    // Update existing selection (refining the recognized text)
                    node.data = msg;
                  }

                  // Finalized a piece of text. Save the book and start a new sentence (selection)
                  if (res.message_type === 'FinalTranscript') {
                    onUpdate?.(msg);
                    range.collapse(false);
                    texts = {};
                  }
                } else {
                  Notify.create({
                    badgeStyle: 'opacity: 0',
                    message:
                      'Please move the mouse cursor back to the text editor.',
                  });
                }
              }
            } catch (e) {
              console.log('error in placing the dictation message to node');
            }
          };
          socket.onopen = async () => {
            // begin recording
            const stream = await navigator.mediaDevices.getUserMedia({
              audio: true,
            });
            recorder = new RecordRTC(stream, {
              type: 'audio',
              mimeType: 'audio/webm;codecs=pcm', // endpoint requires 16bit PCM audio
              recorderType: StereoAudioRecorder,
              timeSlice: 250, // set 250 ms intervals of data
              desiredSampRate: 16000,
              numberOfAudioChannels: 1, // real-time requires only one channel
              bufferSize: 4096,
              audioBitsPerSecond: 128000,
              ondataavailable: async (blob) => {
                const base64data = await readAsDataURL(blob);
                // audio data must be sent as a base64 encoded string
                socket?.send(
                  JSON.stringify({
                    audio_data: base64data.split('base64,')[1],
                  }),
                );
              },
            });

            recorder.startRecording();

            setText('');
            setIcon('stop-recording');
            setEnabled(true);
          };
        }
        isRecording = !isRecording;
      },
    });
  };
  tinymce.PluginManager.add('speech-to-text', pluginConstructor);
}
