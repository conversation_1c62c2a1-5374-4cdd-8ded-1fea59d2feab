import type <PERSON><PERSON><PERSON> from 'tinymce';
import type { Editor } from 'tinymce';
import { ref, watch } from 'vue';

interface PageSizeItem {
  label: string;
  value: string;
}
export function bookChapterSelect(
  tinymce: typeof TinyMCE,
  chapterOptions: any,
  selectedChapterId: any,
  chapterTitle: any,
  onUpdate?: () => void,
) {
  tinymce.PluginManager.add('book-chapter-select', (editor: Editor) => {
    window.selectedChapterIdInToolbar = chapterOptions.value[0].value;
    editor.ui.registry.addMenuButton('bookchapterselect', {
      text: chapterOptions.value[0].label,
      tooltip: 'Select A Chapter',
      fetch: (callback) => {
        const items = getItemsCallback();
        callback(items);
      },
    });
    editor.on('init', function () {
      setSelectedChapter(selectedChapterId, chapterTitle.value);
      const stopWatchSelectedChapterId = watch(
        selectedChapterId,
        () => {
          setSelectedChapter(selectedChapterId, chapterTitle.value);
        },
        { immediate: true },
      );
      const stopWatchSelectedChapterTitle = watch(
        chapterTitle,
        () => {
          setSelectedChapter(selectedChapterId, chapterTitle.value);
        },
        { immediate: true },
      );
      const stopWatchChapterOptions = watch(
        chapterOptions,
        () => {
          getReorderedPageSizeItems(selectedChapterId.value);
        },
        { immediate: true },
      );
      return () => {
        stopWatchChapterOptions();
        stopWatchSelectedChapterId();
        stopWatchSelectedChapterTitle();
      };
    });

    const getItemsCallback = (): any => {
      return getReorderedPageSizeItems(selectedChapterId.value).map(
        (chapter: any) => ({
          type: 'menuitem',
          text: chapter.label,
          onAction: () => {
            const selectedChapterIdNew = ref(chapter.value);
            setSelectedChapter(selectedChapterIdNew, chapter.label);
          },
        }),
      );
    };

    const getReorderedPageSizeItems = (selectedValue: string): any => {
      const selectedItem = chapterOptions.value.find((item: any) => {
        if (item && item.value) return item.value === selectedValue;
        else return true;
      });
      const otherItems = chapterOptions.value.filter((item: any) => {
        if (item && item.value) return item.value !== selectedValue;
        else return true;
      });

      return selectedItem
        ? [selectedItem, ...otherItems]
        : chapterOptions.value;
    };

    const setSelectedChapter = (
      selectedChapterId: any,
      selectedValue: string,
    ) => {
      window.selectedChapterIdInToolbar = selectedChapterId.value;
      const button = document.querySelector(
        'button[aria-label="Select A Chapter"] .tox-tbtn__select-label',
      ) as HTMLElement;
      if (button) {
        button.innerText = selectedValue;
        onUpdate?.();
      }
    };
  });
}
