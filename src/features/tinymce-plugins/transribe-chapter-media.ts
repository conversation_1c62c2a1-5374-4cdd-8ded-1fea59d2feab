import type TinyMC<PERSON> from 'tinymce';
import type { Editor } from 'tinymce';
import { watch } from 'vue';
import { Book } from 'src/entities/book';
import {
  AudioUploadDialogComponent,
  setButtonTooltip,
} from 'src/features/book-transcribe';

/**
 * Registers the TinyMCE plugin to upload chapter content from a .docx file.
 * @param {typeof TinyMCE} tinymce - The TinyMCE instance.
 * @param {string} bookId - The bookId.
 * @param {boolean} canUploadTranscripts - Vue computed property to check if the upload content button is disabled.
 * @param {boolean} canUploadTranscripts - Vue computed property to check if the upload chapter content button should be shown.
 * @param {() => void} [onUpdate] - Optional callback for when content is updated.
 * @param {() => Promise<boolean>} [onClick] - Optional callback for button click to confirm upload.
 */
export function transcribeChapterMedia(
  tinymce: typeof TinyMCE,
  book: Book,
  selectedChapter: any,
  canUploadTranscripts: any,
  onClick?: () => void,
  onUpdate?: () => void,
) {
  const pluginConstructor = (editor: Editor) => {
    editor.ui.registry.addIcon(
      'transcribeicon',
      '<svg width="24px" height="24px" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><defs><style>.a{fill:none;stroke:#000000;stroke-linecap:round;stroke-linejoin:round;}</style></defs><path class="a" d="M32.7554,34.8415H8.1671A2.7945,2.7945,0,0,1,5.3727,32.047V8.2944A2.7944,2.7944,0,0,1,8.1671,5.5H22.531Z"></path><path class="a" d="M25.2,13.1585H39.8329a2.7945,2.7945,0,0,1,2.7944,2.7945V39.7056A2.7944,2.7944,0,0,1,39.8329,42.5H25.469L22.8,34.8414"></path><line class="a" x1="32.7554" y1="34.8415" x2="25.469" y2="42.5"></line><path class="a" d="M16.0441,11.0706h0a3.96,3.96,0,0,1,3.96,3.96v4.8958a3.96,3.96,0,0,1-3.96,3.96h0a3.96,3.96,0,0,1-3.96-3.96h0V15.0307a3.96,3.96,0,0,1,3.96-3.96Z"></path><path class="a" d="M9.4018,21.1048a6.7645,6.7645,0,0,0,13.2847,0"></path><line class="a" x1="16.0441" y1="26.5891" x2="16.0441" y2="29.9251"></line><line class="a" x1="27.9687" y1="21.1048" x2="39.2192" y2="21.1048"></line><line class="a" x1="31.3835" y1="30.9044" x2="39.2192" y2="30.9044"></line><line class="a" x1="29.7307" y1="26.0046" x2="39.2192" y2="26.0046"></line></g></svg>',
    );
    const registerButtonOrMenuItem = (name: string) => {
      const btnConfig = getButtonConfig();
      editor.ui.registry.addMenuItem(name, btnConfig);
      btnConfig.text = '';
      editor.ui.registry.addButton(name, btnConfig);
    };

    const getButtonConfig = () => ({
      icon: 'transcribeicon',
      text: 'Transcribe',
      tooltip: 'transcribe-chapter-media',
      onSetup: setupButtonState,
      onAction: handleAction,
    });

    const setupButtonState = ({
      setEnabled,
    }: {
      setEnabled: (state: boolean) => void;
    }) => {
      const updateButtonState = () => {
        const button = document.querySelector(
          `button[aria-label="transcribe-chapter-media"]`,
        ) as HTMLElement;

        if (button) {
          button.style.visibility = 'visible';
          button.style.width = 'auto';
          setButtonTooltip(canUploadTranscripts.value);
          setEnabled(canUploadTranscripts.value);
        }
      };

      const stopWatchSelectedChapter = watch(selectedChapter, () => {}, {
        immediate: true,
      });
      const stopWatchAllowUpload = watch(
        canUploadTranscripts,
        updateButtonState,
        { immediate: true },
      );

      return () => {
        stopWatchSelectedChapter();
        stopWatchAllowUpload();
      };
    };

    const handleAction = async ({
      setEnabled,
    }: {
      setEnabled: (state: boolean) => void;
    }) => {
      if (!canUploadTranscripts.value) {
        setEnabled(false);
        return;
      }

      await onClick?.();
    };

    registerButtonOrMenuItem('transcribechaptermedia');
  };

  tinymce.PluginManager.add('transcribe-chapter-media', pluginConstructor);
}
