import type <PERSON>MC<PERSON> from 'tinymce';
import type { Editor } from 'tinymce';

export function createImagePlugin(
  tinymce: typeof TinyMCE,
  onClick?: () => void,
) {
  tinymce.PluginManager.add('custom-image', (editor: Editor) => {
    editor.ui.registry.addButton('image-insert', {
      icon: 'image',
      tooltip: 'Upload & Insert Reference',
      onAction: () => {
        onClick?.();
      },
    });
  });
}
