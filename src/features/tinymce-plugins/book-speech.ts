import type TinyMCE from 'tinymce';
import type { Editor } from 'tinymce';
import { watch } from 'vue';

export function bookSpeech(tinymce: typeof TinyMCE, onClick?: () => void) {
  const pluginConstructor = (editor: Editor) => {
    editor.ui.registry.addIcon(
      'mic',
      '<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="M480-400q-50 0-85-35t-35-85v-240q0-50 35-85t85-35q50 0 85 35t35 85v240q0 50-35 85t-85 35Zm-40 280v-123q-104-14-172-93t-68-184h80q0 83 58.5 141.5T480-320q83 0 141.5-58.5T680-520h80q0 105-68 184t-172 93v123h-80Z"/></svg>',
    );
    const registerButtonOrMenuItem = (name: string) => {
      const btnConfig = getButtonConfig();
      editor.ui.registry.addMenuItem(name, btnConfig);
      btnConfig.text = '';
      editor.ui.registry.addButton(name, btnConfig);
    };
    const getButtonConfig = () => ({
      icon: 'mic',
      text: 'Dictation',
      tooltip: 'Dictation',

      onAction: handleAction,
    });

    const handleAction = ({
      setEnabled,
    }: {
      setEnabled: (state: boolean) => void;
    }) => {
      setEnabled(true);
      onClick?.();
    };

    registerButtonOrMenuItem('bookspeech');
  };

  tinymce.PluginManager.add('book-speech', pluginConstructor);
}
