import type TinyMCE from 'tinymce';
import type { Editor } from 'tinymce';
import { Dialog } from 'quasar';
import { Book, BookOutlines } from 'src/entities/book';
import { exportBookImages } from 'src/features/book-export';

export function createExportBookImagesPlugin(
  tinymce: typeof TinyMCE,
  book: Book,
  outline: BookOutlines,
  onUpdate?: () => void,
  onClick?: () => void,
) {
  tinymce.PluginManager.add('export-book-images', (editor: Editor) => {
    editor.ui.registry.addIcon(
      'exportbookimagesicon',
      '<svg width="24px" height="24px" viewBox="0 0 17 17" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M1 1v15h15v-15h-15zM15 15h-13v-2h13v2zM2 12v-10h13v10h-13zM14.203 10.165l-0.697 0.717-2.417-2.349-1.554 1.676-2.486-4.415-3.401 4.975-0.826-0.564 4.31-6.303 2.604 4.622 1.317-1.422 3.15 3.063z" fill="#000000"></path> </g></svg>',
    );

    editor.ui.registry.addMenuItem('exportbookimages', {
      icon: 'exportbookimagesicon',
      text: 'Download Book Images',
      onSetup({ setEnabled }) {
        if (!book.images.length) setEnabled(false);
        return () => setEnabled(true);
      },
      onAction: async ({ setEnabled }) => {
        onClick?.();
        await exportBookImagesAction(book, outline, onUpdate);
      },
    });

    editor.ui.registry.addButton('exportbookimages', {
      icon: 'exportbookimagesicon',
      tooltip: 'Download Book Images',
      onSetup({ setEnabled }) {
        if (!book.images.length) setEnabled(false);
        return () => setEnabled(true);
      },
      onAction: async () => {
        onClick?.();
        await exportBookImagesAction(book, outline, onUpdate);
      },
    });
  });
}

async function exportBookImagesAction(
  book: Book,
  outline: BookOutlines,
  onUpdate?: () => void,
) {
  const proceed = await new Promise((resolve) => {
    Dialog.create({
      title: 'Download Book Images',
      message: 'Are you sure you want to download your book images?',
      ok: {
        color: 'primary',
      },
      cancel: {
        outline: true,
      },
    })
      .onOk(() => resolve(true))
      .onCancel(() => resolve(false));
  });
  if (!proceed) {
    onUpdate?.();
    return;
  }

  try {
    await exportBookImages(book, outline);
  } catch (e) {
    console.log(e);
    onUpdate?.();
  } finally {
    onUpdate?.();
  }
}
