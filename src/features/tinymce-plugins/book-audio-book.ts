import type TinyMC<PERSON> from 'tinymce';
import type { Editor } from 'tinymce';

export function createAudioBookPlugin(
  tinymce: typeof TinyMCE,
  onClick?: () => void,
) {
  tinymce.PluginManager.add('book-audio-book', (editor: Editor) => {
    editor.ui.registry.addIcon(
      'bookaudiobookicon',
      '<svg fill="#000000" width="24px" height="24px" viewBox="0 0 512 512" enable-background="new 0 0 512 512" id="Layer_1" version="1.1" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g> <g> <path d="M444.99,357.438H67.01c-5.522,0-10-4.477-10-10V82.061c0-5.523,4.478-10,10-10h377.98c5.522,0,10,4.477,10,10v265.377 C454.99,352.961,450.513,357.438,444.99,357.438z M77.01,337.438h357.98V92.061H77.01V337.438z"></path> </g> <g> <path d="M190.956,299.857c-1.727,0-3.453-0.447-5-1.34c-3.094-1.786-5-5.087-5-8.66V139.643c0-3.573,1.906-6.874,5-8.66 s6.906-1.787,10,0l130.088,75.106c3.094,1.786,5,5.087,5,8.66s-1.906,6.874-5,8.66l-130.088,75.107 C194.409,299.41,192.683,299.857,190.956,299.857z M200.956,156.963v115.573l100.088-57.787L200.956,156.963z"></path> </g> <g> <path d="M444.99,415.939H143c-5.522,0-10-4.477-10-10s4.478-10,10-10h301.99c5.522,0,10,4.477,10,10 S450.513,415.939,444.99,415.939z"></path> </g> <g> <path d="M100,415.939H67.01c-5.522,0-10-4.477-10-10s4.478-10,10-10H100c5.522,0,10,4.477,10,10S105.522,415.939,100,415.939z"></path> </g> <g> <path d="M131,439.939h-19c-12.131,0-22-9.869-22-22v-24c0-12.131,9.869-22,22-22h19c12.131,0,22,9.869,22,22v24 C153,430.07,143.131,439.939,131,439.939z M112,391.939c-1.103,0-2,0.897-2,2v24c0,1.103,0.897,2,2,2h19c1.103,0,2-0.897,2-2v-24 c0-1.103-0.897-2-2-2H112z"></path> </g> </g> </g></svg>',
    );

    editor.ui.registry.addButton('bookaudiobook', {
      icon: 'bookaudiobookicon',
      tooltip: 'Generate Audio Book',
      onAction: () => {
        onClick?.();
      },
    });
  });
}
