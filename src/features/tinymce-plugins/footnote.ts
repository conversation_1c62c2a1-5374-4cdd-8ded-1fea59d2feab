import type <PERSON><PERSON><PERSON> from 'tinymce';
import type { Editor } from 'tinymce';

export function createFootnotePlugin(tinymce: typeof TinyMCE) {
  tinymce.PluginManager.add('custom-footnote', (editor: Editor) => {
    editor.ui.registry.addButton('footnote', {
      icon: 'superscript',
      text: 'Add Footnote',
      onAction: () => {
        // TODO: fix this function to follow the updated data model
        /*
        let text = tinymce.activeEditor.selection.getContent({
          format: 'text',
        });

        text = text.trim();

        if (!text) return;

        let html = tinymce.activeEditor.selection.getContent({
          format: 'HTML',
        });

        let hasSimilar = this.checkHTMLForSimilar(html, 'footnote');

        if (hasSimilar) return;

        let id = shortid.generate();

        let book = this.book;

        let existing = _.find(book.footnotes, ['selection', text]);

        if (existing) return;

        let newFootnote = {
          id: id,
          createdAt: Date.now(),
          chapterId: this.selectedChapterId,
          content: '',
          selection: text,
        };

        book.footnotes.push(newFootnote);

        tinymce.activeEditor.selection.setContent(
          tinymce.activeEditor.dom.createHTML(
            'span',
            { class: 'footnote', id: id },
            html,
          ),
        );

        tinymce.activeEditor.bodyElement.normalize();

        this.$nextTick(() => {
          tinymce.activeEditor.selection.collapse();
          document.activeElement.blur();
          this.setBookContent(book);
          this.$store.commit('app/setSelectedFootnote', newFootnote);
          this.setSideView('footnotes');
        });
        */
      },
    });
  });
}
