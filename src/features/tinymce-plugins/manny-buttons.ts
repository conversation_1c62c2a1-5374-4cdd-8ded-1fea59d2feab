import type <PERSON><PERSON><PERSON> from 'tinymce';
import type { Editor } from 'tinymce';

export function registerMannyButtonsPlugin(
  tinymce: typeof TinyMCE,
  onClick?: (promptId: string) => void,
) {
  const toolTip =
    'the highlighted content and then click the "Generate Content" button.';

  const createButton = (
    editor: Editor,
    name: string,
    text: string,
    promptId: string,
    condition?: (text: string) => boolean,
  ) => {
    const actionHandler = async ({
      setEnabled,
    }: {
      setEnabled: (state: boolean) => void;
    }) => {
      onClick?.(promptId);
      setEnabled(false);

      // Reset to original state
      setEnabled(true);
    };

    const setupHandler = ({
      setEnabled,
    }: {
      setEnabled: (state: boolean) => void;
    }) => {
      const content = editor.selection.getContent({ format: 'text' });
      if (condition && condition(content)) {
        setEnabled(false);
      }
      return () => setEnabled(true);
    };

    const getButtonConfig = () => ({
      icon: 'ai-prompt',
      text,
      tooltip: `${text} ${toolTip}`,
      onSetup: setupHandler,
      onAction: actionHandler,
    });

    editor.ui.registry.addButton(name, getButtonConfig());
    editor.ui.registry.addMenuItem(name, getButtonConfig());
  };

  tinymce.PluginManager.add('manny-buttons', (editor: Editor) => {
    createButton(
      editor,
      'edit',
      'Edit',
      'grammar',
      (text) => text.length < 100,
    );
    createButton(
      editor,
      'rewrite',
      'Rewrite',
      'rewrite',
      (text) => text.length < 100,
    );
    createButton(
      editor,
      'simplify',
      'Simplify',
      'simplify',
      (text) => text.length < 100,
    );
    createButton(
      editor,
      'write',
      'Add content',
      'addContent',
      (text) => text.length < 100,
    );
    createButton(
      editor,
      'add-story',
      'Add story',
      'addStory',
      (text) => text.length < 100,
    );
  });
}
