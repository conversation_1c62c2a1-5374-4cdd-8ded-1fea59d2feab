import type <PERSON><PERSON><PERSON> from 'tinymce';
import type { Editor } from 'tinymce';
import { Dialog } from 'quasar';
import { watch } from 'vue';
import {
  setButtonTooltip,
  UploadChapterContentDialogComponent,
} from 'src/features/book-upload-chapter-content';

/**
 * Registers the TinyMCE plugin to upload chapter content from a .docx file.
 * @param {typeof TinyMCE} tinymce - The TinyMCE instance.
 * @param {string} bookId - The bookId.
 * @param {boolean} isUploadContentButtonDisabled - Vue computed property to check if the upload content button is disabled.
 * @param {boolean} allowUploadChapterContent - Vue computed property to check if the upload chapter content button should be shown.
 * @param {() => void} [onUpdate] - Optional callback for when content is updated.
 * @param {() => Promise<boolean>} [onClick] - Optional callback for button click to confirm upload.
 */
export function registerUploadChapterContentPlugin(
  tinymce: typeof TinyMCE,
  bookId: string,
  isUploadContentButtonDisabled: any,
  allowUploadChapterContent: any,
  onUpdate?: () => void,
  onClick?: () => Promise<boolean>,
) {
  const pluginConstructor = (editor: Editor) => {
    editor.ui.registry.addIcon(
      'msword',
      '<svg fill="#000000" width="24px" height="24px" viewBox="0 0 64 64" data-name="Layer 1" id="Layer_1" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><title></title><path d="M48.5,58.5h-5a2,2,0,1,1,0-4h3V21.55l-9.4-12H17.5v45H34.59a2,2,0,0,1,0,4H15.5a2,2,0,0,1-2-2V7.5a2,2,0,0,1,2-2H38.07a2,2,0,0,1,1.58.77L50.08,19.63a2,2,0,0,1,.42,1.23V56.5A2,2,0,0,1,48.5,58.5Z"></path><path d="M37.07,38.49a2,2,0,0,1-1.42-.58L32,34.26l-3.65,3.65a2,2,0,0,1-2.83-2.83L30.59,30a2,2,0,0,1,2.82,0l5.07,5.07a2,2,0,0,1,0,2.83A2,2,0,0,1,37.07,38.49Z"></path><path d="M32,50.36a2,2,0,0,1-2-2V31.43a2,2,0,0,1,4,0V48.36A2,2,0,0,1,32,50.36Z"></path></g></svg>',
    );
    const registerButtonOrMenuItem = (name: string) => {
      const btnConfig = getButtonConfig();
      editor.ui.registry.addMenuItem(name, btnConfig);
      btnConfig.text = '';
      editor.ui.registry.addButton(name, btnConfig);
    };

    const getButtonConfig = () => ({
      text: 'Upload',
      icon: 'msword',
      tooltip: 'upload-chapter-content',
      onSetup: setupButtonState,
      onAction: handleAction,
    });

    const setupButtonState = ({
      setEnabled,
    }: {
      setEnabled: (state: boolean) => void;
    }) => {
      const updateButtonState = () => {
        const button = document.querySelector(
          `button[aria-label="upload-chapter-content"]`,
        ) as HTMLElement;

        if (button) {
          button.style.visibility = allowUploadChapterContent.value
            ? 'visible'
            : 'hidden';
          button.style.width = allowUploadChapterContent.value ? 'auto' : '0px';
          setButtonTooltip(isUploadContentButtonDisabled.value);
          setEnabled(isUploadContentButtonDisabled.value);
        }
      };

      const stopWatchDisableState = watch(
        isUploadContentButtonDisabled,
        updateButtonState,
        { immediate: true },
      );
      const stopWatchAllowUpload = watch(
        allowUploadChapterContent,
        updateButtonState,
        { immediate: true },
      );

      return () => {
        stopWatchDisableState();
        stopWatchAllowUpload();
      };
    };

    const handleAction = async ({
      setEnabled,
    }: {
      setEnabled: (state: boolean) => void;
    }) => {
      if (!isUploadContentButtonDisabled.value) {
        setEnabled(false);
        return;
      }

      const proceedUpload = await onClick?.();
      if (proceedUpload) {
        Dialog.create({
          component: UploadChapterContentDialogComponent,
          componentProps: {
            bookId,
            onUpdate,
            setEnabled,
            editor,
          },
        });
      }
    };

    registerButtonOrMenuItem('uploadchaptercontent');
  };

  tinymce.PluginManager.add('book-upload-chapter-content', pluginConstructor);
}
