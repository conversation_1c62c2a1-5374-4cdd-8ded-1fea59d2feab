import type <PERSON><PERSON><PERSON> from 'tinymce';
import type { Editor } from 'tinymce';
import { watch } from 'vue';

export function createBookWordCountPlugin(
  tinymce: typeof TinyMCE,
  totalWordCount: any,
  selectedChapterOutlineTitle: any,
) {
  const pluginConstructor = (editor: Editor) => {
    editor.on('init', function () {
      // Function to update the book chapter title's state
      const updateChapterTitleState = () => {
        const statusbar =
          editor.editorContainer.querySelector('.tox-statusbar');
        const statusbarChapterTitle = editor.editorContainer.querySelector(
          '.sts-bar-book-chapter-title',
        );
        if (statusbar) {
          if (statusbarChapterTitle) {
            statusbarChapterTitle.textContent =
              selectedChapterOutlineTitle.value;
          } else {
            const customElement3 = document.createElement('div');
            customElement3.className =
              'tox-statusbar__path sts-bar-book-chapter-title';
            customElement3.textContent = selectedChapterOutlineTitle.value;
            statusbar.prepend(customElement3);
          }
        }
      };
      // Function to update the book word count's state
      const updateBookWordCountState = () => {
        const statusbar =
          editor.editorContainer.querySelector('.tox-statusbar');
        const statusbarWordCount = editor.editorContainer.querySelector(
          '.sts-bar-book-word-count',
        );
        const rightContainer = editor.editorContainer.querySelector(
          '.tox-statusbar__right-container',
        );
        if (statusbar && rightContainer) {
          if (statusbarWordCount) {
            statusbarWordCount.textContent = `Book: ${totalWordCount.value} words`;

            const customElement2 = document.createElement('div');
            customElement2.className =
              'tox-statusbar__path sts-bar-chapter-word-count';
            customElement2.style.marginLeft = '10px';

            customElement2.textContent = `Chapter:`;
            statusbarWordCount.append(customElement2);
          } else {
            const customElement = document.createElement('div');
            customElement.className =
              'tox-statusbar__path sts-bar-book-word-count';
            customElement.style.marginLeft = '10px';
            customElement.textContent = `Book: ${totalWordCount.value} words`;
            rightContainer.prepend(customElement);

            const customElement2 = document.createElement('div');
            customElement2.className =
              'tox-statusbar__path sts-bar-chapter-word-count';
            customElement2.style.marginLeft = '10px';

            customElement2.textContent = `Chapter:`;
            customElement.append(customElement2);
          }

          // editor.on('setcontent beforeaddundo keyup', update);
        }
      };

      // Watch for changes to the computed properties and update button state accordingly
      const stopWatchWordCountState = watch(
        totalWordCount,
        updateBookWordCountState,
        {
          immediate: true,
        },
      );
      const stopWatchChapterState = watch(
        selectedChapterOutlineTitle,
        updateChapterTitleState,
        {
          immediate: true,
        },
      );
      // Cleanup watcher when the editor is destroyed
      return function () {
        stopWatchWordCountState();
        stopWatchChapterState();
      };
    });
  };

  // Register the plugin with TinyMCE
  tinymce.PluginManager.add('book-word-count', pluginConstructor);
}
