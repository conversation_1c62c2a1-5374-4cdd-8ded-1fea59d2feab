import type <PERSON><PERSON><PERSON> from 'tinymce';
import type { Editor } from 'tinymce';
import { Book } from 'src/entities/book';
import { onOpenAIButtons } from 'src/features/book-prompts';
import { mannyIconSVG } from 'src/features/tinymce-plugins/manny-icon';
import { watch } from 'vue';

export function generateChapterInitialContent(
  tinymce: typeof TinyMCE,
  book: Book,
  selectedChapterId: any,
  selectedChapterOutlineTitle: any,
  onClick?: () => void,
  onUpdate?: () => void,
) {
  tinymce.PluginManager.add('generate-chapter-content', (editor: Editor) => {
    editor.ui.registry.addButton('generatechaptercontent', {
      text: 'Write Content',
      onAction: async () => {
        const proceedGenerate = await onClick?.();
        if (proceedGenerate)
          await mannyGenerateChapterContent(
            editor,
            book,
            selectedChapterId,
            selectedChapterOutlineTitle,
            onUpdate,
          );
      },
    });
    editor.ui.registry.addIcon('mannyicon', mannyIconSVG);
    editor.ui.registry.addButton('generatechaptercontent', {
      icon: 'mannyicon',
      text: 'Write Content',
      tooltip: 'Write Content',
      onAction: async () => {
        const proceedGenerate = await onClick?.();
        if (proceedGenerate) {
          await mannyGenerateChapterContent(
            editor,
            book,
            selectedChapterId,
            selectedChapterOutlineTitle,
            onUpdate,
          );
        }
      },
    });
    editor.ui.registry.addMenuItem('generatechaptercontent', {
      icon: 'mannyicon',
      text: 'Write Content',
      onAction: async () => {
        const proceedGenerate = await onClick?.();
        if (proceedGenerate) {
          await mannyGenerateChapterContent(
            editor,
            book,
            selectedChapterId,
            selectedChapterOutlineTitle,
            onUpdate,
          );
        }
      },
    });
    editor.on('init', async function () {
      const stopWatchSelectedChapterId = watch(selectedChapterId, () => {}, {
        immediate: true,
      });
      const stopWatchSelectedChapterOutlineTitle = watch(
        selectedChapterOutlineTitle,
        () => {},
        { immediate: true },
      );
      return () => {
        stopWatchSelectedChapterId();
        stopWatchSelectedChapterOutlineTitle();
      };
    });
  });
}

async function mannyGenerateChapterContent(
  editor: Editor,
  book: Book,
  selectedChapterId: any,
  selectedChapterOutlineTitle: any,
  onUpdate?: () => void,
) {
  try {
    const selChapterId = selectedChapterId.value;
    let prompt = `You are writing a book titled "${book.title}" with the subtitle "${book.subtitle}".
    Write a 250-word passage for `;
    if (['introduction', 'conclusion'].includes(selChapterId)) {
      prompt += `the book's ${selChapterId} chapter.`;
    } else {
      prompt += `"${selectedChapterOutlineTitle.value}"."`;
    }
    prompt +=
      '\nFocus on the body content without including the chapter heading.';

    const response = (await onOpenAIButtons('main', prompt, book.id)) as string;
    if (response) editor.setContent(response);
  } catch (e) {
    onUpdate?.();
  } finally {
    onUpdate?.();
  }
}
