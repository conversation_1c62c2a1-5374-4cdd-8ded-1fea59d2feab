import Tiny<PERSON><PERSON>, { Editor } from 'tinymce';
import {
  Book,
  BookOutlines,
  ChapterOutline,
  getChapter,
} from 'src/entities/book';
import { Dialog } from 'quasar';
import { unref } from 'vue';
import { bookPrinterService } from 'src/features/book-print';

export function createBookPrinterPlugin(
  tinymce: typeof TinyMCE,
  book: Book,
  outline: BookOutlines,
  onUpdate?: () => void,
  onClick?: () => void,
) {
  tinymce.PluginManager.add('book-printer', (editor: Editor) => {
    editor.ui.registry.addMenuItem('bookprinter', {
      icon: 'print',
      text: 'Print and Preview',
      onAction: async () => {
        onClick?.();
        await printBook(book, outline, onUpdate);
      },
    });

    editor.ui.registry.addButton('bookprinter', {
      icon: 'print',
      tooltip: 'Print and Preview',
      onAction: async () => {
        onClick?.();
        await printBook(book, outline, onUpdate);
      },
    });

    // Register the keyboard shortcut
    editor.addShortcut('ctrl+p', 'Print and Preview', async () => {
      onClick?.();
      await printBook(book, outline, onUpdate);
    });
  });
}

async function printBook(
  book: Book,
  outline: BookOutlines,
  onUpdate?: () => void,
) {
  const proceed = await new Promise((resolve) => {
    Dialog.create({
      title: 'Print and preview',
      message: 'Are you sure you want to print and preview your book?',
      ok: {
        color: 'primary',
      },
      cancel: {
        outline: true,
      },
    })
      .onOk(() => resolve(true))
      .onCancel(() => resolve(false));
  });
  if (!proceed) {
    onUpdate?.();
    return;
  }

  try {
    const bookOutlines: ChapterOutline[] = [
      outline.introduction as ChapterOutline,
      ...(outline.outlines?.filter(
        (chapter) => !chapter.isSection,
      ) as ChapterOutline[]),
      outline.conclusion as ChapterOutline,
    ];
    const chapters = await Promise.all(
      bookOutlines.map((outline) => getChapter(book.id, outline.id)),
    );

    // Create a BookPrinter instance using the factory function
    const printer = bookPrinterService(book, bookOutlines, chapters.map(unref));

    // Print the book content
    printer.printHtmlContent();
  } catch (e) {
    console.log(e);
    onUpdate?.();
  } finally {
    onUpdate?.();
  }
}
