import type TinyMC<PERSON> from 'tinymce';
import type { Editor } from 'tinymce';
import { Dialog } from 'quasar';
import {
  Book,
  BookOutlines,
  ChapterOutline,
  getChapter,
} from 'src/entities/book';
import { bookHtmlToDocx } from 'src/features/book-export';
import { unref } from 'vue';

export function createMissionStatementPlugin(
  tinymce: typeof TinyMCE,
  onClick?: () => void,
) {
  tinymce.PluginManager.add('mission-statement', (editor: Editor) => {
    editor.ui.registry.addIcon(
      'missionstatementicon',
      '<svg fill="#000000" width="64px" height="64px" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" id="book" enable-background="new 0 0 32 32" xml:space="preserve"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <rect x="12" y="13" width="12" height="2"></rect> <rect x="12" y="9" width="12" height="2"></rect> <path d="M4 3v26h24V3H4zM6 27V5h2v22H6zM26 27H10V5h16V27z"></path> </g></svg>',
    );

    editor.ui.registry.addMenuItem('missionstatement', {
      icon: 'missionstatementicon',
      text: 'Book Foundations',
      onAction: async () => {
        onClick?.();
      },
    });
  });
}
