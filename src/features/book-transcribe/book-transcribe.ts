import { pollApiWithProgress } from 'src/features/book-transcribe';
import { ChapterMedia } from 'src/entities/book';

export async function onBookChapterAudioTranscribe(mediaData: ChapterMedia) {
  return pollApiWithProgress(mediaData)
    .then((responseData) => {
      if (responseData.success === true) {
        return responseData;
      }
      let error: string;
      error = String(responseData.response).replace(
        "OpenAI API returned an error: 400 This model's maximum context length is 8192 tokens. However, your messages resulted in 12083 tokens. Please reduce the length of the messages.",
        'The content is too long to process. Please reduce its length and try again.',
      );
      return { success: false, response: error as string };
    })
    .catch((error) => {
      let errorMsg = 'Unknown Error.';
      if (error instanceof Error) errorMsg = error.message;
      return { success: false, response: errorMsg as string };
    });
}
