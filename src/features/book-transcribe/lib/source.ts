import { storage } from 'src/firebase';
import {
  ref as storageRef,
  getDownloadURL,
  uploadBytesResumable,
  deleteObject,
} from 'firebase/storage';
import { user } from 'src/entities/user';
import { ChapterMedia } from 'src/entities/book';

export const getAudioTranscribeRef = (
  chaptermedia: ChapterMedia,
  selectedFile: File,
) => {
  const audioFileUrl = `${user?.value?.uid}/booktranscriptions/${chaptermedia.bookId}/${chaptermedia.chapterId}/${chaptermedia.id}.mp3`;
  return storageRef(storage, audioFileUrl);
};

export const deleteAudioTranscribeSource = async (
  chaptermedia: ChapterMedia,
) => {
  const storageReference = storageRef(
    storage,
    `${user?.value?.uid}/booktranscriptions/${chaptermedia.bookId}/${chaptermedia.chapterId}/${chaptermedia.id}.mp3`,
  );
  try {
    await deleteObject(storageReference);
  } catch (errorDelete) {
    console.log(
      'Error deleting audio transcribe file. It does not exists:',
      errorDelete,
    );
  }
};
/**
 * Saves the audio transcribe for a chaptermedia to Firebase Storage and updates the outlines.
 * @param {Post} chaptermedia - The chaptermedia to save the audio transcribe for.
 * @param {File} selectedFile - The file to be uploaded
 * @returns {Promise<void>}
 */
export const storeAudioSource = async (
  chaptermedia: ChapterMedia,
  selectedFile: File,
) => {
  const storageReference = getAudioTranscribeRef(chaptermedia, selectedFile);
  await deleteAudioTranscribeSource(chaptermedia);

  const uploadTask = uploadBytesResumable(storageReference, selectedFile);

  const downloadURLPromise = new Promise<string>((resolve, reject) => {
    uploadTask.on(
      'state_changed',
      (snapshot) => {
        // Update upload progress
      },
      (error) => {
        console.error('Upload failed:', error);
        reject(error);
      },
      () => {
        getDownloadURL(uploadTask.snapshot.ref)
          .then((downloadURL) => {
            resolve(downloadURL);
          })
          .catch((error) => {
            reject(error);
          });
      },
    );
  });
  return downloadURLPromise;
};
