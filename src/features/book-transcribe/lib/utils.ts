import { transcribeBookChapterAudioSrc } from 'src/shared/api/whisperai';
import { user } from 'src/entities/user';
import { ChapterMedia } from 'src/entities/book';
import { maxChapterMediaDurationLimit } from 'src/entities/setting';
import Tiny<PERSON><PERSON>, { Editor, EditorSelection } from 'tinymce';

declare const tinymce: typeof TinyMCE;
/**
 * Make open AI call
 */
export async function pollApiWithProgress(
  mediaData: ChapterMedia,
): Promise<any> {
  const checkApiResponse = async (): Promise<any> => {
    try {
      if (!mediaData.id) {
        return Promise.resolve({
          success: false,
          response: 'An error has occured. Please try again!',
        });
      }
      const audioFileUrl = `${user?.value?.uid}/booktranscriptions/${mediaData.bookId}/${mediaData.chapterId}/${mediaData.id}.mp3`;
      const dataDoc = `books/${mediaData.bookId}/chapters/${mediaData.chapterId}/transcriptions/${mediaData.id}`;
      const prompt = getFormattingPrompt();
      // Make the API call
      const params = {
        audioFileUrl: audioFileUrl,
        fileName: mediaData.fileName as string,
        prompt: prompt as string,
        dataDoc: dataDoc,
        maxDurationLimit: maxChapterMediaDurationLimit.value,
      };
      const responseData = await transcribeBookChapterAudioSrc(params);

      return Promise.resolve({
        success: responseData,
        response: 'An error has occured. Please try again!',
      });
    } catch (error) {
      let errorMsg = 'Unknown Error.';
      if (error instanceof Error) errorMsg = error.message;
      return Promise.resolve({ success: false, response: errorMsg as string });
    }
  };
  return checkApiResponse();
}

/**
 * Get the formatting prompt
 */
export function getFormattingPrompt() {
  return 'Please structure it into well-organized paragraphs and add proper punctuation for clarity and readability, but do not alter or remove any of the content. Please ensure that the answer is inserted within HTML tags and formatted correctly using paragraph elements. Do not wrap the answer in code snippets. Here is the transcript: [transcribeText].';
}

/**
 * Sets the tooltip title for a specific button based on the upload content button's disabled state.
 * @param {boolean} canUploadTranscripts - Indicates whether the upload content button is disabled.
 */
export const setButtonTooltip = (canUploadTranscripts: boolean) => {
  const button = document.querySelector(
    'button[aria-label="transcribe-chapter-media"]',
  ) as HTMLElement;
  if (button) {
    button.setAttribute(
      'title',
      !canUploadTranscripts
        ? 'Limit reached. Delete transcripts to upload'
        : 'Upload a media file to transcribe',
    );
  }
};

export const onInsertContent = (isAdd = false, content: string) => {
  const editor = tinymce.get('editor');
  if (editor) {
    tinymce.setActive(editor);
    const { selection } = editor;
    setHightlightedText(editor, selection, content, isAdd);
  }
};

const setHightlightedText = (
  editor: Editor,
  selection: EditorSelection,
  response: string,
  action?: boolean,
) => {
  const evId = 'insert-' + Date.now();
  let contentResponse = `<div id="${evId}">${response}</div>`;

  if (action) {
    contentResponse = `<div id="${evId}">${contentResponse}</div>`;
  }
  if (action) {
    editor.selection.collapse();
    editor.insertContent(contentResponse, { format: 'html' });
  } else {
    selection.setContent(contentResponse, { format: 'html' });
  }

  // selects the updated content
  const bodyElement = editor.getBody();
  const textNode = bodyElement.querySelector(`#${evId}`) as Node;
  if (textNode) {
    selection.select(textNode);
    // bodyElement.querySelector(`#${evId}`).scrollIntoView();
  } else {
    console.error('Node not found.');
  }
};
