<template>
  <q-dialog
    v-model="compareDialog"
    persistent
    transition-show="scale"
    transition-hide="scale"
    :maximized="maximizedToggle"
  >
    <q-card style="min-width: 60vw">
      <q-card-section class="q-pa-none">
        <q-bar class="bg-primary text-white">
          <q-icon name="img:robot.png" />

          <div>Generated Content</div>

          <q-space />

          <q-btn
            dense
            flat
            :icon="!maximizedToggle ? 'fullscreen' : 'fullscreen_exit'"
            @click="maximizedToggle = !maximizedToggle"
          >
            <q-tooltip v-if="!maximizedToggle" class="bg-white text-primary"
              >Maximize</q-tooltip
            >
          </q-btn>
          <q-btn
            dense
            flat
            icon="close"
            @click="onTranscriptionClose"
            v-close-popup
          >
            <q-tooltip class="bg-white text-primary">Close</q-tooltip>
          </q-btn>
        </q-bar>
      </q-card-section>

      <q-card-section class="q-py-none" style="height: calc(100vh - 250px)">
        <div class="q-pa-md splitter-container">
          <div class="text-h6 sticky-header">
            <q-img src="robot.png" width="30px" /> Generated Result
          </div>
          <q-separator />
          <q-editor
            paragraph-tag="p"
            v-model="chaptermedia.transcription"
            :toolbar="toolbarItems"
            min-height="15em"
            height="calc(100vh - 370px)"
          />
        </div>
      </q-card-section>
      <q-card-actions
        align="right"
        :class="maximizedToggle ? 'absolute-bottom' : ''"
      >
        <q-btn
          flat
          label="Cancel"
          color="danger"
          v-close-popup
          @click="onTranscriptionClose"
        />
        <q-btn
          flat
          label="Insert After Selection"
          color="primary"
          icon="add"
          @click="
            onInsertContent(true, chaptermedia.transcription);
            onTranscriptionClose;
          "
          v-close-popup
        >
          <q-tooltip
            >Insert text immediately after the selected area.</q-tooltip
          >
        </q-btn>
        <q-btn
          flat
          label="Replace in Selection"
          color="primary"
          icon="cached"
          @click="
            onInsertContent(false, chaptermedia.transcription);
            onTranscriptionClose;
          "
          v-close-popup
        >
          <q-tooltip
            >Replace text within the currently selected area.</q-tooltip
          >
        </q-btn>
      </q-card-actions>
    </q-card>
  </q-dialog>

  <q-dialog v-model="showDialog" persistent>
    <q-card style="width: 500px; max-width: 80vw">
      <q-card-section class="q-pa-none">
        <q-bar class="bg-primary text-white">
          <q-icon name="img:robot.png" />

          <div>Transcribe Media</div>

          <q-space />

          <q-btn dense flat icon="close" @click="onTranscriptionClose">
            <q-tooltip class="bg-white text-primary">Close</q-tooltip>
          </q-btn>
        </q-bar>
      </q-card-section>

      <q-separator />

      <q-card-section>
        <AudioUploadComponent
          v-model:chaptermedia="chaptermedia"
          v-model:onLoading="onLoading"
          @changeLinkSource="changeLinkSource"
          @hasUploadError="hasUploadError"
          v-model:hasError="hasError"
        />
      </q-card-section>

      <q-inner-loading
        :showing="onLoading"
        color="primary"
        label-class="text-primary"
        label-style="font-size: 1.1em"
        style="z-index: 1"
      >
        <q-spinner-dots color="primary" size="2em" />
        <div class="align-center">
          <q-icon name="img:robot.png" class="q-ml-xs" size="xl" />
          Hang tight, Loading....
        </div>
      </q-inner-loading>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { useVModels } from '@vueuse/core/index';
import AudioUploadComponent from 'src/features/book-transcribe/ui/AudioUploadComponent.vue';
import {
  ChapterMedia,
  createEmptyChapterTranscription,
  deleteChapterTranscription,
  setupChapterTranscriptionListener,
} from 'src/entities/book';
import { ref, watch } from 'vue';
import {
  onBookChapterAudioTranscribe,
  onInsertContent,
} from 'src/features/book-transcribe';

const props = defineProps<{
  chaptermedia: ChapterMedia;
  onLoadingChapterMedia: boolean;
  onHasError: string | null;
  onShowTranscription: boolean;
}>();

const emit = defineEmits<{
  'update:onShowTranscription': [onShowTranscription: boolean];
  'update:chaptermedia': [chaptermedia: ChapterMedia];
  'update:onLoadingChapterMedia': [onLoadingChapterMedia: boolean];
  onTranscriptDone: (chaptermedia: ChapterMedia) => void;
  onTranscriptError: (hasError: string) => void;
  onTranscriptionClose: () => void;
}>();

const { chaptermedia, onLoadingChapterMedia, onShowTranscription, onHasError } =
  useVModels(props);
const compareDialog = ref(false);
const showDialog = ref(onShowTranscription.value);
const maximizedToggle = ref(false);

const onLoading = ref(onLoadingChapterMedia.value as boolean);
watch(onLoadingChapterMedia, (currentIsLoading) => {
  onLoading.value = currentIsLoading as boolean;
});
watch(onLoading, (currentOnLoading) => {
  onLoadingChapterMedia.value = currentOnLoading as boolean;
});
watch(onShowTranscription, (currentOnShow) => {
  showDialog.value = currentOnShow as boolean;
});
watch(showDialog, (currentOnShowDialog) => {
  if (currentOnShowDialog === true) hasError.value = '';
  onShowTranscription.value = currentOnShowDialog as boolean;
});
const hasError = ref(onHasError.value);
watch(hasError, (currentHasError) => {
  hasError.value = currentHasError as string;
});

let unsubscribeData: (() => void) | null = null;
if (unsubscribeData) unsubscribeData();
const toolbarItems = [
  [
    'bold',
    'italic',
    'underline',
    'unordered',
    'outdent',
    'indent',
    'undo',
    'redo',
  ],
];

// Callback to set document data
const setPostData = (data: ChapterMedia) => {
  chaptermedia.value = { ...data };
  chaptermedia.value.transcription = data.transcription;
};
const changeLinkSource = async (createdMedia: ChapterMedia) => {
  onLoading.value = true;
  createdMedia.isProcessing = true;
  chaptermedia.value = createdMedia;
  unsubscribeData = setupChapterTranscriptionListener(
    createdMedia,
    setPostData,
  );

  const response = await onBookChapterAudioTranscribe(createdMedia);
  if (response.success === true) {
    showDialog.value = false;
    compareDialog.value = true;
    emit('onTranscriptDone', chaptermedia.value);
  } else {
    showDialog.value = true;
    compareDialog.value = false;
    hasError.value = response.response;
    deleteChapterTranscription(chaptermedia.value);
    chaptermedia.value = {
      ...createEmptyChapterTranscription(),
      bookId: chaptermedia.value.id,
      chapterId: chaptermedia.value.id,
    };
    emit('onTranscriptError', hasError.value);
  }
  if (unsubscribeData) unsubscribeData();
  onLoading.value = false;
};

const hasUploadError = (uploadHasError: any) => {
  if (unsubscribeData) unsubscribeData();
  if (uploadHasError) {
    showDialog.value = true;
    compareDialog.value = false;
    hasError.value = uploadHasError;
    emit('onTranscriptError', hasError.value);
  } else {
    hasError.value = '';
  }
};

const onTranscriptionClose = () => {
  showDialog.value = false;
  compareDialog.value = false;
  emit('onTranscriptionClose');
};
</script>

<style scoped lang="scss">
.splitter-container {
  position: relative; /* Container for sticky positioning */
  height: 100%; /* Full height to ensure scrolling */
  overflow: auto; /* Enable scrolling */
  padding-top: 0 !important;
  scroll-behavior: smooth;
}

.sticky-header {
  position: sticky;
  top: 0;
  padding-top: 10px;
  padding-bottom: 10px;
  background: white; /* Ensure the header has a solid background */
  z-index: 1; /* Ensure the header is above other content */
}
</style>
