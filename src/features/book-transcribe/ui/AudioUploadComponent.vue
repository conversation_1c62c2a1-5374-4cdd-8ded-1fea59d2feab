<template>
  <input
    type="file"
    ref="fileInput"
    @change="onFileChange"
    accept=".mp3, .mp4, .mpweg, .mpga, .m4a, .wav, .webm"
    hidden
  />
  <div
    class="drop-zone"
    @dragover.prevent
    @drop.prevent="onDrop"
    :class="{ 'drop-zone--active': isDragging }"
  >
    <div>
      <p>
        Drag & Drop your file here or click
        <q-btn dense @click="triggerFileInput" flat label="here" /> to upload
      </p>
      <p v-if="hasError" class="error-message text-red">{{ hasError }}</p>
    </div>
  </div>

  <q-card-section v-if="uploadProgress > 0">
    <q-linear-progress :value="uploadProgress" color="primary" />
  </q-card-section>
</template>

<script setup lang="ts">
import { useVModels } from '@vueuse/core';
import { maxChapterMediaFileSizeUpload } from 'src/entities/setting';
import { computed, ref } from 'vue';
import { storeAudioSource } from 'src/features/book-transcribe';
import { ChapterMedia, updateChapterTranscription } from 'src/entities/book';

const props = defineProps<{
  chaptermedia: ChapterMedia;
  onLoading: boolean;
  hasError: string | null;
}>();

const emit = defineEmits<{
  'update:chaptermedia': [chaptermedia: ChapterMedia];
  'update:onLoading': [onLoading: boolean];
  changeLinkSource: (chaptermedia: ChapterMedia) => void;
  hasUploadError: (uploadHasError: string | null) => void;
}>();

const { chaptermedia, onLoading, hasError } = useVModels(props, emit);

/**
 * Reference to the file input element
 */
const fileInput = ref<HTMLInputElement | null>(null);

/**
 * Selected file for upload
 */
const file = ref<File | null>(null);

/**
 * Indicates whether a file is being dragged over the drop zone
 */
const isDragging = ref(false);

/**
 * Upload progress percentage
 */
const uploadProgress = ref(0);

/**
 * Maximum file size limit in bytes (100MB)
 */
const MAX_FILE_SIZE = computed(
  () => maxChapterMediaFileSizeUpload.value * 1024 * 1024,
); // 100 MB

/**
 * Triggers the file input dialog
 * @returns {void}
 */
const triggerFileInput = (): void => {
  fileInput.value?.click();
};

/**
 * Handles the file input change event
 * @param {Event} event - The file input change event
 * @returns {void}
 */
const onFileChange = (event: Event): void => {
  onLoading.value = true;
  const target = event.target as HTMLInputElement;
  const files = target.files;
  if (files && files.length > 0) {
    validateAndUploadFile(files[0]);
  }
};

/**
 * Handles the drop event when a file is dragged and dropped into the drop zone
 * @param {DragEvent} event - The drag event
 * @returns {void}
 */
const onDrop = (event: DragEvent): void => {
  onLoading.value = true;
  event.preventDefault();
  isDragging.value = false;
  if (event.dataTransfer?.files && event.dataTransfer.files.length > 0) {
    validateAndUploadFile(event.dataTransfer.files[0]);
  }
};

/**
 * Clears the file input and resets the file reference
 */
const clearFileInput = (): void => {
  if (fileInput.value) {
    fileInput.value.value = ''; // Reset input element
  }
  file.value = null;
};

/**
 * Allowed file extensions
 */
const ALLOWED_EXTENSIONS = ['mp3', 'mp4', 'mpeg', 'mpga', 'm4a', 'wav', 'webm'];

/**
 * Allowed file MIME types
 */
const ALLOWED_MIME_TYPES = [
  'audio/mpeg', // .mp3
  'audio/mp4', // .mp4
  'audio/wav', // .wav
  'audio/x-m4a', // .m4a
  'audio/webm', // .webm
  'video/mp4', // .mp4 video
];

/**
 * Validates the selected file's size and uploads it if valid
 * @param {File} selectedFile - The file to be uploaded
 * @returns {void}
 */
const validateAndUploadFile = (selectedFile: File): void => {
  hasError.value = '';
  if (selectedFile.size > MAX_FILE_SIZE.value) {
    hasError.value = `File size exceeds ${maxChapterMediaFileSizeUpload.value}MB. Please select a smaller file.`;
    emit(
      'hasUploadError',
      `File size exceeds ${maxChapterMediaFileSizeUpload.value}MB. Please select a smaller file.`,
    );
    clearFileInput(); // Clear file input
    onLoading.value = false;
    return;
  }

  // Check file format
  const fileExtension = selectedFile.name.split('.').pop()?.toLowerCase();
  if (!fileExtension || !ALLOWED_EXTENSIONS.includes(fileExtension)) {
    hasError.value = `Invalid file format. Please upload a file with one of the following extensions: ${ALLOWED_EXTENSIONS.join(
      ', ',
    )}.`;
    emit(
      'hasUploadError',
      `Invalid file format. Please upload a file with one of the following extensions: ${ALLOWED_EXTENSIONS.join(
        ', ',
      )}.`,
    );
    clearFileInput(); // Clear file input
    onLoading.value = false;
    return;
  }

  // Check file MIME type
  if (!ALLOWED_MIME_TYPES.includes(selectedFile.type)) {
    hasError.value = `Invalid file format. Please upload a file with a valid format.`;
    emit(
      'hasUploadError',
      `Invalid file format. Please upload a file with one of the following formats: ${ALLOWED_MIME_TYPES.join(
        ', ',
      )}.`,
    );
    clearFileInput(); // Clear file input
    onLoading.value = false;
    return;
  }
  hasError.value = '';
  handleFileUpload(selectedFile);
  clearFileInput(); // Clear file input
};

/**
 * Uploads the selected file to Firebase Storage and tracks the upload progress
 * @param {File} selectedFile - The file to be uploaded
 * @returns {void}
 */
const handleFileUpload = (selectedFile: File) => {
  onLoading.value = true;
  file.value = selectedFile;

  if (!chaptermedia.value.id) {
    hasError.value = `File upload failed. Please try again.`;
    emit('hasUploadError', `File upload failed. Please try again.`);
    onLoading.value = false;
    return;
  }
  storeAudioSource(chaptermedia.value, selectedFile)
    .then((downloadURL) => {
      console.log('File uploaded successfully. Download URL:', downloadURL);

      chaptermedia.value.fileName = `${selectedFile.name}`;
      chaptermedia.value.src = downloadURL;
      chaptermedia.value.openai_response = '';
      chaptermedia.value.isProcessing = false;
      chaptermedia.value.transcription = '';
      updateChapterTranscription(
        chaptermedia.value.bookId,
        chaptermedia.value.chapterId,
        chaptermedia.value.id,
        chaptermedia.value,
      );
      onLoading.value = false;
      emit('changeLinkSource', chaptermedia.value);
    })
    .catch((error) => {
      console.error('File upload failed:', error);
      hasError.value = `File upload failed. Please try again.`;
      emit('hasUploadError', 'File upload failed. Please try again.');
      onLoading.value = false;
    });
};
</script>

<style scoped lang="scss">
/* Styling for the drop zone */
.drop-zone {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: border-color 0.3s;
}

/* Active state for drop zone when dragging a file over it */
.drop-zone--active {
  border-color: #1976d2;
}

/* Error message styling */
.error-message {
  color: red;
  margin-top: 10px;
}
</style>
