import { Book, Chapter, ChapterOutline } from 'src/entities/book';

/**
 * Class representing a Printer that prints HTML content with inline CSS.
 */
class BookPrinter {
  private book: Book;
  private outlines: ChapterOutline[];
  private chapters: Chapter[];

  /**
   * Creates an instance of BookPrinter.
   * @param {Book} book - The book object containing metadata and settings.
   * @param {ChapterOutline[]} outlines - The array of chapter outlines.
   * @param {Chapter[]} chapters - The array of chapter contents.
   */
  constructor(book: Book, outlines: ChapterOutline[], chapters: Chapter[]) {
    this.book = book;
    this.outlines = outlines;
    this.chapters = chapters;
  }

  /**
   * Prints the given book content as HTML with inline CSS.
   */
  public printHtmlContent(): void {
    const printIframe = document.createElement('iframe');
    printIframe.style.position = 'absolute';
    printIframe.style.width = '0px';
    printIframe.style.height = '0px';
    printIframe.style.border = 'none';

    document.body.appendChild(printIframe);

    const doc = printIframe.contentWindow?.document;
    if (!doc) {
      console.error('Failed to create print iframe document');
      return;
    }

    doc.open();
    doc.write(this.generateHtmlDocument());
    doc.close();

    // Print the content of the iframe
    printIframe.contentWindow?.print();

    // Remove the iframe after printing
    printIframe.remove();
  }

  /**
   * Generates the full HTML document to be printed.
   * @returns {string} The full HTML document string.
   */
  private generateHtmlDocument(): string {
    const headings = this.generateHeadings();
    const toc = this.generateTableOfContents(headings);
    const chaptersHtml = this.generateChaptersHtml();

    const pageSetup = this.book.pageSetup || {};
    const {
      marginbottom = 1, // default values
      marginleft = 1,
      marginright = 1,
      margintop = 1,
      pagesize = 'A4',
      style = '',
    } = pageSetup;

    // Define margins in inches (converted to inches as the default for print)
    const margins = `
      margin: ${margintop}in ${marginright}in ${marginbottom}in ${marginleft}in;
    `;

    // Define paper size
    const pageSize = pagesize === 'letter' ? '8.5in 11in' : pagesize;

    // Define the overall style
    const printStyle = `
      @media print {
        body {
          ${margins}
          width: ${pageSize.split(' ')[0]}; /* Width of the paper size */
          min-height: ${pageSize.split(' ')[1]}; /* Height of the paper size */
          ${style}
        }
        .page-break {
          page-break-before: always;
        }
      }
    `;

    return `
    <html>
        <head>
          <meta charset='utf-8'>
          <title>${this.book.title}</title>
          <style>
            ${printStyle}
          </style>
        </head>
        <body>
          ${toc}
          ${chaptersHtml}
        </body>
      </html>
    `;
  }

  /**
   * Generates the headings for the table of contents based on chapter outlines.
   * @returns {string[]} The array of headings.
   */
  private generateHeadings(): string[] {
    return this.outlines.map(
      (outline, idx) =>
        outline.title ||
        (['introduction', 'conclusion'].includes(outline.id)
          ? this.capitalize(outline.id)
          : `Chapter ${idx}`),
    );
  }

  /**
   * Generates the HTML for the table of contents.
   * @param {string[]} headings - The array of headings.
   * @returns {string} The HTML string for the table of contents.
   */
  private generateTableOfContents(headings: string[]): string {
    return `
      <h1>Table of Contents</h1>
      ${headings.map((title) => `<h2>${title}</h2>`).join('\n')}
    `;
  }

  /**
   * Generates the HTML for the chapters.
   * @returns {string} The HTML string for the chapters.
   */
  private generateChaptersHtml(): string {
    const parser = new DOMParser();

    return this.chapters
      .map((chapter, i) => {
        const dom = parser.parseFromString(chapter.content, 'text/html');
        const titleText =
          this.outlines[i].title ||
          (['introduction', 'conclusion'].includes(this.outlines[i].id)
            ? this.capitalize(this.outlines[i].id)
            : `Chapter ${i}`);

        this.removeExistingTitles(dom, titleText);

        const title = dom.createElement('h1');
        title.textContent = titleText;
        dom.body.insertBefore(title, dom.body.firstChild);

        return `<div style="page-break-before: always;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>${dom.documentElement.innerHTML}`;
      })
      .join('');
  }

  /**
   * Removes existing titles from the chapter DOM that match the given title text.
   * @param {Document} dom - The DOM of the chapter.
   * @param {string} titleText - The title text to remove.
   */
  private removeExistingTitles(dom: Document, titleText: string): void {
    const existingTitles = dom.body.querySelectorAll('h1');
    existingTitles.forEach((title) => {
      if (title.textContent?.includes(titleText)) {
        title.remove();
      }
    });
  }

  /**
   * Capitalizes the first letter of a string.
   * @param {string} text - The string to capitalize.
   * @returns {string} The capitalized string.
   */
  private capitalize(text: string): string {
    return text.charAt(0).toUpperCase() + text.slice(1);
  }
}

export const bookPrinterService = (
  book: Book,
  outlines: ChapterOutline[],
  chapters: Chapter[],
) => new BookPrinter(book, outlines, chapters);
