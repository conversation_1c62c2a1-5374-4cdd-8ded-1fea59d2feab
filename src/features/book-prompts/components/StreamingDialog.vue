<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide" persistent>
    <q-card
      style="
        width: 900px;
        max-width: 90vw;
        max-height: 90vh;
        display: flex;
        flex-direction: column;
      "
      class="streaming-dialog"
    >
      <q-card-section class="dialog-header">
        <div class="header-content">
          <div class="robot-avatar">
            <q-img src="robot.png" width="48px" />
            <div class="pulse-ring" v-if="isTyping"></div>
          </div>
          <div class="header-text">
            <div class="title">{{ headerTitle }}</div>
            <div class="subtitle" v-if="isTyping && !hasError">
              <span class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
              </span>
              Generating response...
            </div>
            <div
              class="subtitle success"
              v-else-if="isComplete && !isTyping && !hasError"
            >
              <q-icon name="check_circle" size="16px" class="q-mr-xs" />
              Response generated successfully
            </div>
            <div class="subtitle error" v-else-if="hasError">
              <q-icon name="error" size="16px" class="q-mr-xs" />
              Something went wrong
            </div>
          </div>
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section class="content-section">
        <div class="content-container" ref="contentRef">
          <div class="content-text">
            <div v-html="displayedContent"></div>
            <span
              class="modern-cursor"
              v-if="(!isComplete || isTyping) && displayedContent"
            ></span>
          </div>
          <div
            v-if="!displayedContent && !isComplete"
            class="loading-placeholder"
          >
            <div class="loading-line"></div>
            <div class="loading-line short"></div>
            <div class="loading-line medium"></div>
          </div>
        </div>
      </q-card-section>

      <!-- Additional Instructions Section -->
      <q-separator v-if="showAdditionalInstructions" />

      <q-card-section
        v-if="showAdditionalInstructions"
        class="additional-instructions"
      >
        <div class="instructions-header">
          <q-icon name="tune" size="18px" class="q-mr-xs" />
          <span class="instructions-title">Additional Instructions</span>
        </div>
        <q-input
          v-model="additionalInstructions"
          type="textarea"
          placeholder="Add specific instructions to modify the response..."
          outlined
          dense
          rows="3"
          class="instructions-input"
          :readonly="isStreaming"
        />
        <div class="instructions-actions">
          <q-btn
            flat
            size="sm"
            label="Apply & Regenerate"
            color="primary"
            @click="onApplyInstructions"
            :disabled="!additionalInstructions.trim() || isStreaming"
            icon="refresh"
          />
        </div>
      </q-card-section>

      <q-separator v-if="isComplete || isStreaming" />

      <!-- Action Buttons -->
      <q-card-actions class="action-buttons">
        <!-- Instructions toggle button (only on desktop or when needed) -->
        <q-btn
          flat
          size="sm"
          :label="
            showAdditionalInstructions
              ? 'Hide Instructions'
              : 'Add Instructions'
          "
          color="grey-7"
          @click="toggleAdditionalInstructions"
          :icon="showAdditionalInstructions ? 'expand_less' : 'tune'"
          class="instructions-toggle-btn"
          :class="$q.screen.xs ? 'full-width' : ''"
        />

        <!-- Spacer for desktop -->
        <q-space v-if="isComplete && !hasError" />

        <!-- Main action buttons -->
        <div class="main-actions">
          <q-btn
            v-if="isStreaming || isTyping"
            flat
            label="Stop"
            color="red-6"
            @click="onStop"
            class="stop-btn"
            icon="stop"
            :class="$q.screen.xs ? 'full-width' : ''"
          />
          <q-btn
            v-else
            flat
            :label="hasError ? 'Close' : 'Cancel'"
            color="grey-7"
            @click="onCancel"
            class="cancel-btn"
            :class="$q.screen.xs ? 'full-width' : ''"
            :disable="isTyping"
          />
          <q-btn
            v-if="!hasError && isComplete && !isTyping"
            unelevated
            label="Use Response"
            color="primary"
            @click="onUseResponse"
            class="use-btn"
            :class="$q.screen.xs ? 'full-width' : ''"
            icon="check"
            :loading="isTyping"
          />
          <q-btn
            v-else-if="hasError"
            unelevated
            label="Try Again"
            color="orange"
            @click="onTryAgain"
            class="retry-btn"
            :class="$q.screen.xs ? 'full-width' : ''"
            icon="refresh"
          />
        </div>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { useDialogPluginComponent, useQuasar } from 'quasar';
import { ref, watch, computed, nextTick, onMounted } from 'vue';
import { pollApiWithProgress, type PromptId } from '../lib/utils';
import type { PromptId as PromptIdType } from '../model';

const props = defineProps({
  promptId: {
    type: String as () => PromptIdType,
    required: true,
  },
  prompt: {
    type: String,
    required: true,
  },
  additionalContext: {
    type: String,
    default: '',
  },
  isComplete: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits([
  ...useDialogPluginComponent.emits,
  'response-confirmed',
  'response-cancelled',
]);

const { dialogRef, onDialogCancel, onDialogHide, onDialogOK } =
  useDialogPluginComponent();

const $q = useQuasar();

const contentStream = ref('');
const displayedContent = ref('');
const isComplete = ref(false);
const contentRef = ref<HTMLElement>();
const isStreaming = ref(false);
const hasError = ref(false);
const errorMessage = ref('');
const showAdditionalInstructions = ref(props.additionalContext ? true : false);
const additionalInstructions = ref(props.additionalContext);

// For stopping the stream
let shouldStop = ref(false);

const headerTitle = computed(() => {
  if (hasError.value) return 'Error Occurred';
  if (isStreaming.value || isTyping.value) return 'Manny is Writing';
  if (isComplete.value && !isTyping.value) return 'Response Complete';
  return 'Preparing Response';
});

// Real-time typing effect variables
let typingQueue: string[] = [];
let isTyping = ref(false);
let typingTimeout: NodeJS.Timeout | null = null;

const processTypingQueue = () => {
  if (typingQueue.length === 0 || isTyping.value) {
    return;
  }

  isTyping.value = true;
  const targetContent = typingQueue[typingQueue.length - 1]; // Get the latest content
  typingQueue = []; // Clear the queue

  const currentLength = displayedContent.value.length;
  const targetLength = targetContent.length;

  if (targetLength <= currentLength) {
    displayedContent.value = targetContent;
    isTyping.value = false;
    return;
  }

  // Type new characters one by one
  let currentIndex = currentLength;

  const typeNextChar = () => {
    if (currentIndex < targetLength) {
      displayedContent.value = targetContent.substring(0, currentIndex + 1);
      currentIndex++;

      // Auto-scroll to bottom
      nextTick(() => {
        if (contentRef.value) {
          contentRef.value.scrollTop = contentRef.value.scrollHeight;
        }
      });

      // Continue typing with a small delay
      typingTimeout = setTimeout(typeNextChar, 15); // Faster typing for real-time feel
    } else {
      isTyping.value = false;
      // Process any new content that arrived while typing
      if (typingQueue.length > 0) {
        setTimeout(processTypingQueue, 10);
      }
    }
  };

  typeNextChar();
};

const addToTypingQueue = (content: string) => {
  typingQueue.push(content);
  if (!isTyping.value) {
    processTypingQueue();
  }
};

const startStreamingWithPrompt = async (promptText: string) => {
  try {
    isStreaming.value = true;
    shouldStop.value = false;

    await pollApiWithProgress(
      props.promptId as PromptIdType,
      promptText,
      (content) => {
        if (shouldStop.value) return;
        contentStream.value = content;
        // Add to typing queue for real-time typing effect
        addToTypingQueue(content);
      },
    );

    if (!shouldStop.value) {
      isComplete.value = true;
      isStreaming.value = false;
    }
  } catch (error) {
    if (shouldStop.value) {
      // Stream was stopped by user
      return;
    }

    console.error('Streaming error:', error);
    hasError.value = true;
    isComplete.value = true;
    isStreaming.value = false;
    errorMessage.value =
      error instanceof Error ? error.message : 'An unexpected error occurred';
    displayedContent.value = `<div style="color: #ef4444; padding: 16px; background: #fef2f2; border-radius: 8px; border: 1px solid #fecaca;">
      <strong>Error:</strong> ${errorMessage.value}
      <br><br>
      Please try again or contact support if the issue persists.
    </div>`;
  }
};

const startStreaming = async () => {
  await startStreamingWithPrompt(props.prompt);
};

const onUseResponse = () => {
  emit('response-confirmed', contentStream.value);
  onDialogOK(contentStream.value);
};

const onCancel = () => {
  emit('response-cancelled');
  onDialogCancel();
};

const onTryAgain = () => {
  // Reset state and try again
  cleanup(); // Clear any ongoing typing
  hasError.value = false;
  isComplete.value = false;
  isStreaming.value = false;
  shouldStop.value = false;
  contentStream.value = '';
  displayedContent.value = '';
  errorMessage.value = '';
  startStreaming();
};

const onStop = () => {
  shouldStop.value = true;
  isStreaming.value = false;
  isComplete.value = true;
  cleanup();
};

const toggleAdditionalInstructions = () => {
  showAdditionalInstructions.value = !showAdditionalInstructions.value;
};

const onApplyInstructions = () => {
  if (!additionalInstructions.value.trim()) return;

  // Reset state and regenerate with additional instructions
  cleanup();
  hasError.value = false;
  isComplete.value = false;
  isStreaming.value = false;
  shouldStop.value = false;
  contentStream.value = '';
  displayedContent.value = '';
  errorMessage.value = '';

  // Append additional instructions to the original prompt
  const enhancedPrompt = `${props.prompt}\n\nAdditional Instructions: ${additionalInstructions.value}`;

  // Start streaming with enhanced prompt
  startStreamingWithPrompt(enhancedPrompt);
};

// Start streaming when component mounts
onMounted(() => {
  startStreaming();
});

// Cleanup on unmount
const cleanup = () => {
  if (typingTimeout) {
    clearTimeout(typingTimeout);
    typingTimeout = null;
  }
  shouldStop.value = true;
  isTyping.value = false;
  typingQueue = [];
};

// Watch for dialog hide to cleanup
watch(
  () => dialogRef.value,
  (newVal) => {
    if (!newVal) {
      cleanup();
    }
  },
);
</script>

<style scoped lang="scss">
$onboarding-primary: $primary;
$onboarding-secondary: #667eea;

.streaming-dialog {
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  height: 100%;
}

.dialog-header {
  /** background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); **/
  background: linear-gradient(
    135deg,
    $onboarding-primary 0%,
    $onboarding-secondary 100%
  );
  color: white;
  padding: 20px 24px;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.robot-avatar {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pulse-ring {
  position: absolute;
  width: 60px;
  height: 60px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

.header-text {
  flex: 1;
}

.title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
}

.subtitle {
  font-size: 14px;
  opacity: 0.9;
  display: flex;
  align-items: center;
}

.subtitle.success {
  color: #4ade80;
}

.subtitle.error {
  color: #ef4444;
}

.typing-dots {
  display: inline-flex;
  gap: 2px;
  margin-right: 8px;
}

.typing-dots span {
  width: 4px;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}
.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.content-section {
  padding: 0;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.content-container {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  background: #fafafa;
  min-height: 200px;
}

.content-text {
  font-family:
    'Inter',
    -apple-system,
    BlinkMacSystemFont,
    sans-serif;
  font-size: 15px;
  line-height: 1.6;
  color: #374151;
  // white-space: pre-wrap;
  word-wrap: break-word;
}

.modern-cursor {
  display: inline-block;
  width: 2px;
  height: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin-left: 2px;
  animation: modernBlink 1s infinite;
  vertical-align: text-bottom;
}

@keyframes modernBlink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

.loading-placeholder {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 20px 0;
}

.loading-line {
  height: 16px;
  background: linear-gradient(90deg, #e5e7eb 25%, #f3f4f6 50%, #e5e7eb 75%);
  background-size: 200% 100%;
  border-radius: 8px;
  animation: shimmer 2s infinite;
}

.loading-line.short {
  width: 60%;
}
.loading-line.medium {
  width: 80%;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.additional-instructions {
  padding: 16px 24px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  flex-shrink: 0;
}

.instructions-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  color: #475569;
  font-weight: 500;
}

.instructions-title {
  font-size: 14px;
}

.instructions-input {
  margin-bottom: 12px;
}

.instructions-actions {
  display: flex;
  justify-content: flex-end;
}

.action-buttons {
  padding: 16px 24px;
  background: white;
  display: flex;
  align-items: center;
  flex-shrink: 0;
  border-top: 1px solid #e2e8f0;
  gap: 12px;
}

.main-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.instructions-toggle-btn.mobile-only {
  min-width: 40px;
  padding: 8px;
}

/* Mobile responsive adjustments */
@media (max-width: 599px) {
  .action-buttons {
    padding: 12px 16px;
    flex-direction: column;
    gap: 8px;
  }

  .main-actions {
    width: 100%;
    justify-content: center;
    gap: 8px;
  }

  .main-actions .q-btn {
    flex: 1;
    min-width: 0;
  }

  .use-btn,
  .retry-btn {
    order: -1; /* Primary actions first on mobile */
  }
}

.cancel-btn {
  padding: 8px 20px;
  border-radius: 8px;
  font-weight: 500;
}

.use-btn {
  padding: 8px 20px;
  border-radius: 8px;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.2s ease;
}

.use-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.retry-btn {
  padding: 8px 20px;
  border-radius: 8px;
  font-weight: 600;
  background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
  box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);
  transition: all 0.2s ease;
}

.retry-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(249, 115, 22, 0.4);
}

.stop-btn {
  padding: 8px 20px;
  border-radius: 8px;
  font-weight: 600;
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  color: white !important;
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
  transition: all 0.2s ease;
}

.stop-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(220, 38, 38, 0.4);
}

.instructions-toggle-btn {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.instructions-toggle-btn:hover {
  background-color: #f1f5f9;
}

/* Scrollbar styling */
.content-container::-webkit-scrollbar {
  width: 6px;
}

.content-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.content-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.content-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
