<template>
  <q-dialog
    ref="dialogRef"
    persistent
    transition-show="scale"
    transition-hide="scale"
  >
    <q-card style="width: 700px; max-width: 80vw">
      <q-card-section>
        <div class="text-h6">
          <q-icon name="img:robot.png" size="26px" /> &nbsp;Add a Story
        </div>
      </q-card-section>
      <q-card-section class="q-py-none">
        <q-input
          label="Ask <PERSON> to write a story using the selected text"
          autogrow
          required
          outlined
          dense
          v-model="inputPrompt"
          autofocus
          :rules="[
            (val) => {
              isInputValid =
                val.length <= 200 && val.length >= 20 ? true : false;
              if (val.length > 200) return 'Must not exceed 200 characters.';
              else if (val.length < 20)
                return 'Please enter a minimum of 20 characters.';
              else return isInputValid;
            },
          ]"
        >
          <template v-slot:append>
            <q-btn
              flat
              icon="img:robot.png"
              :loading="visible"
              :disable="visible"
              @click="onGenerateClick"
              v-if="inputPrompt && isInputValid"
            >
              <q-tooltip>Generate using AI</q-tooltip>
            </q-btn>
          </template>
        </q-input>
      </q-card-section>
      <div v-if="expandedGeneratedResult || visible" class="q-pa-none">
        <q-card-section v-if="visible" class="q-py-none">
          <q-skeleton type="text" class="text-subtitle1" />
          <q-skeleton type="text" width="90%" class="text-caption" />
          <q-skeleton type="text" width="85%" class="text-caption" />
          <q-skeleton type="text" class="text-caption" />
          <q-skeleton type="text" width="80%" class="text-caption" />
          <q-skeleton type="text" class="text-caption" />
          <q-skeleton type="text" class="text-caption q-mt-sm" />
          <q-skeleton type="text" class="text-subtitle2" />
          <q-skeleton type="text" width="85%" class="text-caption" />
          <q-skeleton type="text" width="95%" class="text-subtitle2" />
          <q-skeleton type="text" class="text-caption" />
          <q-skeleton type="text" class="text-caption" />
        </q-card-section>
        <q-card-section
          style="height: 350px"
          v-else-if="!visible"
          class="q-py-none"
        >
          <q-scroll-area class="fit">
            <transition
              appear
              enter-active-class="animated fadeIn"
              leave-active-class="animated fadeOut"
            >
              <div v-show="showSimulatedReturnData" class="column no-wrap">
                <div v-html="generatedResult"></div>
              </div>
            </transition>
          </q-scroll-area>
        </q-card-section>
      </div>

      <q-card-actions align="right">
        <q-btn flat label="Cancel" color="danger" v-close-popup />
        <q-btn
          v-if="generatedResult"
          flat
          label="Insert text"
          color="primary"
          @click="onAddStoryClick"
        />
      </q-card-actions>

      <q-inner-loading
        :showing="visible"
        color="primary"
        label="Hang tight, Manny is writing!"
        label-class="text-primary"
        label-style="font-size: 1.1em"
        style="z-index: 1"
      >
        <q-circular-progress
          indeterminate
          rounded
          size="50px"
          color="primary"
          class="q-ma-md"
        />
        <div class="align-center">
          <q-icon name="img:robot.png" class="q-ml-xs" size="xl" />
          Manny is writing, hang tight!
        </div>
      </q-inner-loading>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { useDialogPluginComponent } from 'quasar';
import { pollApiWithProgress } from 'src/features/book-prompts';
import { ref, watch } from 'vue';

const props = defineProps<{
  selection: string;
  selectionHTML: string;
}>();

// dialog
const { dialogRef, onDialogOK } = useDialogPluginComponent();

const inputPrompt = ref('');
const generatedResult = ref('');

// loader
const visible = ref(false);
const expandedGeneratedResult = ref(false);
const showSimulatedReturnData = ref(false);

// validation
const isInputValid = ref(false);

function onAddStoryClick() {
  onDialogOK(generatedResult.value);
}
async function onGenerateClick() {
  expandedGeneratedResult.value = true;
  const prompt = `Please ensure that the answer is inserted within HTML tags and formatted correctly using paragraph elements. Write a story that illustrates the point that is being made. Use relevant and recognized literature, historical and current events, sports, and related materical to help write the story. The story should be non-fictional, and not exceed 2-3 paragraphs, and 300 words. Use the selected text provided.
      ${props.selection}. The story primarily focuses on the following context. ${inputPrompt.value} `;
  visible.value = true;
  showSimulatedReturnData.value = false;
  try {
    generatedResult.value = await pollApiWithProgress('addStory', prompt);
    if (!generatedResult.value || generatedResult.value == 'false')
      generatedResult.value = 'An error has occured. Please try again.';
  } catch (error) {
    generatedResult.value = 'An error has occured. Please try again.';
  }
  expandedGeneratedResult.value = true;
  visible.value = false;
  showSimulatedReturnData.value = true;
}
</script>
<style scoped>
.modal-slide {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  .column {
    padding-top: 16px !important;
    padding-bottom: 16px !important;
  }
}
</style>
