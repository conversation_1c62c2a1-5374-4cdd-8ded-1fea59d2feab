import { Prompt, PromptDetails, type PromptId } from '../model';
import { createBookVersion } from 'src/features/book-versioning';
import { requestOpenAI, streamOpenAI } from 'src/shared/api/openai';
import { ACTIONS, loggingService, PAGES } from 'src/entities/log';

/**
 * get today's formatted date time to be used in book version description
 */
export function getTodayFormattedDateTime() {
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
  };
  return new Intl.DateTimeFormat('zh-CN', options).format(Date.now());
}

/**
 * Get the prompt for OpenAI by promptId
 */
function getPromptById(promptId: PromptId, selectedContent: string): Prompt {
  let prompt = '';
  let promptTitle = '';
  const chapterTitle = window.selectedChapter;

  switch (promptId) {
    case 'outline': {
      promptTitle = 'Outline for ';
      prompt = selectedContent;
      selectedContent = `Writing the outline for ${chapterTitle}`;
      break;
    }
    case 'main': {
      promptTitle = 'Main Content for ';
      prompt = selectedContent;
      selectedContent = `Writing the main content for ${chapterTitle}`;
      break;
    }
    case 'addContent': {
      promptTitle = 'Add Content for ';
      prompt =
        selectedContent +
        '. Please provide a new piece of content that retains the core message and ideas of the original text, uses different words and phrases, and adds new insights or perspectives where appropriate.';
      break;
    }
    case 'instruct': {
      promptTitle = 'Open Text Input for ';
      prompt = selectedContent;
      break;
    }
    case 'addStory': {
      promptTitle = 'Add a Story for ';
      prompt =
        'Write a story that illustrates the point that is being made. Use relevant and recognized literature, historical and current events, sports, and related materical to help write the story. The story should be non-fictional, and not exceed 2-3 paragraphs, and 300 words. Use the selected text provided. ' +
        selectedContent;
      break;
    }
    case 'rewrite': {
      promptTitle = 'Rewrite the Content for ';
      prompt = 'Rewrite and enhance the following text: ' + selectedContent;
      break;
    }
    case 'grammar': {
      promptTitle = 'Edit Grammar, Spelling and Punctuation for ';
      prompt =
        'Edit the content for grammar, spelling, and punctuation without changing the following content.' +
        selectedContent;
      break;
    }
    default: {
      promptTitle = 'Simplify the Content for ';
      prompt = 'Please simplify the following text: ' + selectedContent;
      break;
    }
  }
  prompt +=
    '.\nPlease ensure that the answer is inserted within HTML tags and formatted correctly using paragraph elements. Do not wrap the answer in code snippets.';
  promptTitle += chapterTitle;

  return {
    title: promptTitle,
    promptId: promptId,
    prompt: prompt,
    selectedChapter: window.selectedChapter,
    selectedContent: selectedContent,
  };
}

/**
 * Save book version on Open AI call
 */
async function saveBookVersionOnPrompt(bookId: string, prompt: Prompt) {
  const formattedDateTime = getTodayFormattedDateTime();
  const versionNote = {
    Date: formattedDateTime,
    Prompt: prompt.title,
  };
  const description = Object.entries(versionNote)
    .map(([key, value]) => `${key}: ${value}`)
    .join('\n\n');
  await createBookVersion(bookId, formattedDateTime, description);

  await loggingService.logAction(
    PAGES.BOOKPROMPTS,
    ACTIONS.CLICK,
    description,
    'book',
    bookId,
  );
}

function getPrompt(promptId: PromptId, prompt: Prompt) {
  return new Promise<PromptDetails>((resolve) =>
    resolve({
      prompt: prompt.prompt,
      promptId: prompt.promptId,
      promptRequest: prompt.selectedContent,
    }),
  );
}

/**
 * Make open AI call
 */
export function pollApiWithProgress(
  promptId: PromptId,
  prompt: string,
  onData: (data: string) => void,
): Promise<string> {
  return new Promise((resolve, reject) => {
    let fullContent = '';
    try {
      streamOpenAI(
        {
          type: 'compose',
          prompt: prompt as string,
        },
        (contentChunk) => {
          fullContent += contentChunk;
          onData(fullContent);
        },
      ).then(() => {
        resolve(fullContent);
      });
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * Get prompt to be used for Open AI call
 */
export async function onOpenAIButtonsApiCall(
  promptId: PromptId,
  content: string,
  bookId: string,
) {
  const prompt = getPromptById(promptId, content);
  saveBookVersionOnPrompt(bookId, prompt);
  return getPrompt(promptId, prompt);
}
