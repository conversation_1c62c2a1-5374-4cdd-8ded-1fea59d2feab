import { ref, unref, type Ref, type WatchStopHandle } from 'vue';
import { BookVersion } from '../model';
import { firestore } from 'src/firebase';
import {
  collection,
  doc,
  getDoc,
  getDocs,
  onSnapshot,
  orderBy,
  query,
  setDoc,
  updateDoc,
  writeBatch,
} from 'firebase/firestore';
import { includeIdAndFixTimestamp } from 'src/shared/api/firestore-converters';
import { debounceFilter, watchDebounced, watchIgnorable } from '@vueuse/core';
import {
  Chapter,
  ChapterOutline,
  BookOutlines,
  getBook,
  getChapter,
  listOutlines,
  listFormattedBookOutline,
  getChapterTranscriptions,
  ChapterMedia,
} from 'src/entities/book';

export async function listBookVersions(bookId: string) {
  const booksCollection = collection(firestore, 'books', bookId, 'versions');
  const booksQuery = query(
    booksCollection,
    orderBy('createdAt', 'desc'),
  ).withConverter(includeIdAndFixTimestamp<BookVersion>());

  const bookVersions: Ref<Ref<BookVersion>[]> = ref([]);
  // Warning: possible memory leak
  let stopHandles: WatchStopHandle[] = [];
  onSnapshot(booksQuery, (snap) => {
    // Stop watching the old versions
    for (const stop of stopHandles) {
      stop();
    }
    stopHandles = [];
    bookVersions.value = snap.docs.map((doc) => {
      const bookVersion = ref(doc.data());
      const unsub = watchDebounced(
        bookVersion,
        ({ name, description }) => {
          updateDoc(doc.ref, { name, description });
        },
        { deep: true, debounce: 1000 },
      );
      stopHandles.push(unsub);
      return bookVersion;
    });
  });
  return bookVersions;
}

/**
 * Returns a 2-way synchronized snapshot of the book version object from Firestore.
 * Changes from Firestore update it automatically, and changes to the return object are
 *  synchronized back to Firestore.
 * @param id Book ID (of the Firestore document)
 * @param versionId ID of the version to load
 */
export async function getBookVersion(
  bookId: string,
  versionId: string,
): Promise<Ref<BookVersion>> {
  const docRef = doc(
    firestore,
    'books',
    bookId,
    'versions',
    versionId,
  ).withConverter(includeIdAndFixTimestamp<BookVersion>());
  const snapshot = await getDoc(docRef);
  const bookVersion = ref(snapshot.data()!);

  // Automatically save any changes to Firestore
  const { ignoreUpdates, stop } = watchIgnorable(
    bookVersion,
    (newBook) => {
      if (newBook.id !== bookId) {
        // The book id has changed. Avoid a race condition
        stop();
        return;
      }
      setDoc(docRef, newBook);
    },
    { eventFilter: debounceFilter(1000, { maxWait: 5000 }), deep: true },
  );

  onSnapshot(docRef, (snapshot) => {
    ignoreUpdates(() => {
      bookVersion.value = snapshot.data()!;
    });
  });
  return bookVersion;
}

/**
 * Copies all the contents of the given book, returning the id of the new version.
 * @param bookId The id of the book for which to create a new copy
 * @param name The name of the book for which to create a new copy
 * @param description The description of the book for which to create a new copy
 */
export async function createBookVersion(
  bookId: string,
  name?: string,
  description?: string,
): Promise<string> {
  const book = unref(await getBook(bookId));
  const bookOutlines = unref(await listOutlines(bookId));

  const chaptersContents = await Promise.all(
    listFormattedBookOutline(bookOutlines).map((chapter) =>
      getChapter(bookId, chapter.id),
    ),
  );

  const batch = writeBatch(firestore);
  const versionRef = doc(collection(firestore, 'books', bookId, 'versions'));
  batch.set(versionRef, {
    ...book,
    name: name ?? '',
    description: description ?? '',
    createdAt: new Date(),
  });
  for (const chapter of chaptersContents) {
    batch.set(doc(versionRef, 'chapters', chapter.value.id), chapter.value);
    const bookChapterTranscriptions = await getChapterTranscriptions(
      bookId,
      chapter.value.id,
    );
    for (const chapterTranscription of bookChapterTranscriptions) {
      batch.set(
        doc(
          versionRef,
          'chapters',
          chapter.value.id,
          'transcriptions',
          chapterTranscription.id,
        ),
        chapterTranscription,
      );
    }
  }

  if (bookOutlines.introduction) {
    batch.set(
      doc(versionRef, 'outlines', bookOutlines.introduction!.id),
      bookOutlines.introduction,
    );
  }
  if (bookOutlines.conclusion) {
    batch.set(
      doc(versionRef, 'outlines', bookOutlines.conclusion!.id),
      bookOutlines.conclusion,
    );
  }
  for (const outline of bookOutlines.outlines!) {
    batch.set(doc(versionRef, 'outlines', outline.id), outline);
  }

  await batch.commit();
  return versionRef.id;
}

export async function updateBookVersion(
  bookId: string,
  versionId: string,
  bookVersion: Partial<BookVersion>,
) {
  return updateDoc(
    doc(firestore, 'books', bookId, 'versions', versionId),
    bookVersion,
  );
}

export async function deleteBookVersion(bookId: string, versionId: string) {
  const bookVersionDoc = doc(firestore, 'books', bookId, 'versions', versionId);
  const bookOutlines = unref(await listOutlines(bookId));
  const batch = writeBatch(firestore);
  for (const chapter of listFormattedBookOutline(bookOutlines)) {
    batch.delete(
      doc(
        firestore,
        'books',
        bookId,
        'versions',
        versionId,
        'chapters',
        chapter.id,
      ),
    );
    const bookChapterTranscriptions = await listVersionChapterTranscriptions(
      versionId,
      bookId,
      chapter.id,
    );
    for (const chapterTranscription of bookChapterTranscriptions) {
      batch.delete(
        doc(
          firestore,
          'books',
          bookId,
          'versions',
          versionId,
          'chapters',
          chapter.id,
          'transcriptions',
          chapterTranscription.id as string,
        ),
      );
    }
    batch.delete(
      doc(
        firestore,
        'books',
        bookId,
        'versions',
        versionId,
        'outlines',
        chapter.id,
      ),
    );
  }
  batch.delete(bookVersionDoc);
  await batch.commit();
}

/**
 * Returns a single snapshot (not automatically updated) of the book object
 *  from Firestore, and synchronizes changes to it back to Firestore.
 * @param bookId Firestore document of the containing book
 * @param chapterId Chapter ID
 */
export async function getVersionChapter(
  bookId: string,
  versionId: string,
  chapterId: string,
): Promise<Readonly<Chapter>> {
  const docRef = doc(
    firestore,
    'books',
    bookId,
    'versions',
    versionId,
    'chapters',
    chapterId,
  ).withConverter(includeIdAndFixTimestamp<Chapter>());
  const snapshot = await getDoc(docRef);
  // Old versions are read-only
  const chapter = snapshot.data()!;

  return chapter;
}

/**
 * Returns a single snapshot (not automatically updated) of the book object
 *  from Firestore, and synchronizes changes to it back to Firestore.
 * @param bookId Firestore document of the containing book
 * @param versionId Version ID
 * @param chapterId Chapter ID
 */
export async function getVersionOutline(
  bookId: string,
  versionId: string,
  chapterId: string,
): Promise<Readonly<ChapterOutline>> {
  const docRef = doc(
    firestore,
    'books',
    bookId,
    'versions',
    versionId,
    'outlines',
    chapterId,
  ).withConverter(includeIdAndFixTimestamp<ChapterOutline>());
  const snapshot = await getDoc(docRef);
  // Old versions are read-only
  const outline = snapshot.data()!;

  return outline;
}

/**
 * Returns a single snapshot (not automatically updated) of the book object
 *  from Firestore, and synchronizes changes to it back to Firestore.
 * @param bookId Firestore document of the containing book
 * @param versionId Version ID
 */
export async function listVersionOutlines(bookId: string, versionId: string) {
  const versionsQuery = query(
    collection(firestore, 'books', bookId, 'versions', versionId, 'outlines'),
    orderBy('number', 'asc'),
  ).withConverter(includeIdAndFixTimestamp<ChapterOutline>());

  const chapterOutline: Ref<BookOutlines> | undefined = ref<BookOutlines>({});
  const outlines: ChapterOutline[] = [];
  const outlineDocs = await getDocs(versionsQuery);
  outlineDocs.docs.forEach((snap) => {
    const outline = snap.data()!;
    if (!outline.id) outline.id = snap.ref.id;
    if (outline.id == 'introduction') {
      if (!outline.title) {
        outline.title = 'Introduction';
      }
      chapterOutline.value.introduction = outline;
    } else if (outline.id == 'conclusion') {
      if (!outline.title) {
        outline.title = 'Conclusion';
      }
      chapterOutline.value.conclusion = outline;
    } else {
      outlines.push(outline);
    }
  });
  chapterOutline.value.outlines = outlines;
  return chapterOutline;
}

export async function listVersionChapterTranscriptions(
  versionId: string,
  bookId: string,
  chapterId: string,
): Promise<ChapterMedia[]> {
  const versionsQuery = query(
    collection(
      firestore,
      'books',
      bookId,
      'versions',
      versionId,
      'chapters',
      chapterId,
      'transcriptions',
    ),
    // orderBy('createdAt', 'desc'),
  );
  const transcripts: ChapterMedia[] = [] as ChapterMedia[];

  const transcriptDocs = await getDocs(versionsQuery);

  transcriptDocs.docs.forEach((doc) => {
    const transcript = doc.data()!;
    transcripts.push(transcript as ChapterMedia);
  });

  return transcripts;
}
