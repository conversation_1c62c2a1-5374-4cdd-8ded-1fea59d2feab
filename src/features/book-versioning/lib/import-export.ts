import type { BookVersion } from '../model';
import {
  getVersionChapter,
  listVersionChapterTranscriptions,
  listVersionOutlines,
} from '../api';
import { doc, getDoc, writeBatch } from 'firebase/firestore';
import { firestore } from 'src/firebase';
import {
  ChapterMedia,
  ChapterOutline,
  listFormattedBookOutline,
} from 'src/entities/book';
import { unref } from 'vue';

export interface Backup extends BookVersion {
  /** The chapter content used for export */
  contents?: BackupContent[] | undefined;
}

export interface BackupContent extends ChapterOutline {
  /** The chapter content used for export */
  content?: string | undefined;
  chapterId?: string | undefined;
  transcriptions?: ChapterMedia[];
}

export async function exportBookVersion(
  bookId: string,
  version: BookVersion,
): Promise<Backup> {
  const bookOutlines = unref(await listVersionOutlines(bookId, version.id));
  const contents: BackupContent[] | undefined = await Promise.all(
    listFormattedBookOutline(bookOutlines).map(async (chapter) => {
      const content = await getVersionChapter(bookId, version.id, chapter?.id!);
      if (chapter) {
        const bookChapterTranscriptions =
          await listVersionChapterTranscriptions(
            version.id,
            bookId,
            chapter.id,
          );
        chapter.transcriptions = [];
        if (bookChapterTranscriptions) {
          for (const chapterTranscription of bookChapterTranscriptions) {
            chapter.transcriptions.push(chapterTranscription);
          }
        }
        chapter.content = content?.content! ?? '';
        chapter.chapterId = content?.id! ?? '';
      }
      return chapter;
    }),
  );

  return { ...version, id: version.id, contents };
}

export async function importBookVersion(
  bookId: string,
  backup: Backup,
): Promise<BookVersion> {
  const { contents, ...version } = backup;
  const { introduction, chapters, conclusion } = version;

  // Make sure the book version doesn't already exist
  const docRef = doc(firestore, 'books', bookId, 'versions', backup.id);
  const snapshot = await getDoc(docRef);
  if (snapshot.exists()) {
    throw new Error('Book version already exists. Import cancelled.');
  }

  const batch = writeBatch(firestore);
  const versionRef = doc(firestore, 'books', bookId, 'versions', version.id);
  // @ts-expect-error id should not be in the Firestore document
  delete version.id;
  batch.set(versionRef, {
    ...version,
    createdAt: new Date(),
  });

  for (const chapter of contents as BackupContent[]) {
    const chapterId = chapter.chapterId;
    if (chapterId) {
      batch.set(doc(versionRef, 'chapters', chapterId), {
        content: chapter.content,
      });
      if (chapter?.transcriptions) {
        for (const chapterTranscription of chapter?.transcriptions) {
          batch.set(
            doc(
              versionRef,
              'chapters',
              chapterId,
              'transcriptions',
              chapterTranscription.id as string,
            ),
            chapterTranscription,
          );
        }
        delete chapter.transcriptions;
      }
      delete chapter.chapterId;
      delete chapter.content;

      chapter.id = chapterId;
      batch.set(doc(versionRef, 'outlines', chapterId), chapter);
    }
  }
  await batch.commit();
  return version;
}
