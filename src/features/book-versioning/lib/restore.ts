import { listFormattedBookOutline } from 'src/entities/book';
import { BookVersion } from '../model';
import { toValue, unref } from 'vue';
import { doc, writeBatch } from 'firebase/firestore';
import { firestore } from 'src/firebase';
import {
  getVersionChapter,
  listVersionChapterTranscriptions,
  listVersionOutlines,
} from '../api';

export async function restoreBookVersion(bookId: string, version: BookVersion) {
  const bookOutlines = unref(await listVersionOutlines(bookId, version.id));

  const batch = writeBatch(firestore);
  for await (const outline of listFormattedBookOutline(bookOutlines)) {
    const chapter = toValue(
      await getVersionChapter(bookId, version.id, outline.id),
    );
    batch.set(doc(firestore, 'books', bookId, 'chapters', outline.id), chapter);
    const bookChapterTranscriptions = await listVersionChapterTranscriptions(
      version.id,
      bookId,
      outline.id,
    );
    for (const chapterTranscription of bookChapterTranscriptions) {
      batch.set(
        doc(
          firestore,
          'books',
          bookId,
          'chapters',
          outline.id,
          'transcriptions',
          chapterTranscription.id,
        ),
        chapterTranscription,
      );
    }
    batch.set(doc(firestore, 'books', bookId, 'outlines', outline.id), outline);
  }
  const { name, description, ...newBook } = version;
  batch.set(doc(firestore, 'books', bookId), newBook);
  await batch.commit();
}
