import { Book, Chapter, ChapterOutline } from 'src/entities/book';

export function bookHtmlToDocx(
  book: Book,
  outlines: ChapterOutline[],
  chapters: Chapter[],
) {
  const headings = outlines.map(
    (outline, idx) =>
      outline.title ||
      (['introduction', 'conclusion'].includes(outline.id)
        ? outline.id.charAt(0).toUpperCase() + outline.id.slice(1)
        : `Chapter ${idx}`),
  );

  const previewTOC =
    '<h1>Table of Contents</h1>\n' +
    headings.map((title) => `<h2>${title}</h2>`).join('\n');

  const parser = new DOMParser();
  const chaptersDoms = chapters.map((chapter, i) => {
    const dom = parser.parseFromString(chapter.content, 'text/html');
    const outline = outlines[i];
    const titleText =
      outline.title ||
      (['introduction', 'conclusion'].includes(outline.id)
        ? outline.id.charAt(0).toUpperCase() + outline.id.slice(1)
        : `Chapter ${i}`);

    // Get all existing <h1> elements
    const existingTitles = dom.body.querySelectorAll('h1');

    // Filter and remove <h1> elements that contain the specified titleText
    existingTitles.forEach((title) => {
      if (title.textContent?.includes(titleText)) {
        title.remove();
      }
    });

    // Create a new <h1> element and insert it at the beginning of the chapter content
    const title = dom.createElement('h1');
    title.textContent = titleText;

    dom.body.insertBefore(title, dom.body.firstChild);

    return dom;
  });
  const bodyStyle = book.pageSetup.style ?? '';
  // Construct the Word document HTML content with page breaks
  const word = `
<html xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:w='urn:schemas-microsoft-com:office:word' xmlns='http://www.w3.org/TR/REC-html40'>
  <head>
    <meta charset='utf-8'>
    <title>${book.title}</title>
  </head>
  <body style="${bodyStyle}">
    ${previewTOC} 
    
    ${chaptersDoms
      .map(
        (dom, index) => `
      <div style="page-break-before: always;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>${dom.documentElement.innerHTML}
    `,
      )
      .join('')}</body>
</html>`;

  // Create a data URI for the Word document
  const source =
    'data:application/vnd.ms-word;charset=utf-8,' + encodeURIComponent(word);

  // Create a temporary link element to trigger the download
  const filename = book.title.replace(/[/\\?%*:|"<>]/g, '') || 'Untitled Book';
  const fileDownload = document.createElement('a');
  document.body.appendChild(fileDownload);
  fileDownload.href = source;
  fileDownload.download = `${filename}.doc`;
  fileDownload.click();

  // Clean up the temporary link element
  document.body.removeChild(fileDownload);
}

export function bookOutlineHtmlToDocx(book: Book, outlines: ChapterOutline[]) {
  const parser = new DOMParser();
  const chaptersDoms = outlines.map((chapter, i) => {
    const dom = parser.parseFromString(chapter.outline, 'text/html');

    const titleText =
      chapter.title ||
      (['introduction', 'conclusion'].includes(chapter.id)
        ? chapter.id.charAt(0).toUpperCase() + chapter.id.slice(1)
        : `Chapter ${i}`);

    // Get all existing <h1> elements
    const existingTitles = dom.body.querySelectorAll('h1');

    // Filter and remove <h1> elements that contain the specified titleText
    existingTitles.forEach((title) => {
      if (title.textContent?.includes(titleText)) {
        title.remove();
      }
    });

    // Create a new <h1> element and insert it at the beginning of the chapter content
    const title = dom.createElement('h1');
    title.textContent = titleText;
    dom.body.insertBefore(title, dom.body.firstChild);
    return dom;
  });

  // Construct the Word document HTML content with page breaks
  const word = `
  <html xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:w='urn:schemas-microsoft-com:office:word' xmlns='http://www.w3.org/TR/REC-html40'>
    <head>
      <meta charset='utf-8'>
      <title>${book.title}</title>

    </head>
    <body> ${chaptersDoms
      .map(
        (
          dom,
        ) => `${dom.documentElement.innerHTML}<div style="page-break-before: always;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
     `,
      )
      .join('')}</body>
  </html>`;

  // Create a data URI for the Word document
  const source =
    'data:application/vnd.ms-word;charset=utf-8,' + encodeURIComponent(word);

  // Create a temporary link element to trigger the download
  const filename = book.title.replace(/[/\\?%*:|"<>]/g, '') || 'Untitled Book';
  const fileDownload = document.createElement('a');
  document.body.appendChild(fileDownload);
  fileDownload.href = source;
  fileDownload.download = `${filename}.doc`;
  fileDownload.click();

  // Clean up the temporary link element
  document.body.removeChild(fileDownload);
}
