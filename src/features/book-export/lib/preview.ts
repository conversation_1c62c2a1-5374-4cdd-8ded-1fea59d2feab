import { ChapterOutline, Book, type Chapter } from 'src/entities/book';

export function generateBookPreview(
  book: Book,
  outlines: ChapterOutline[],
  chapters: Chapter[],
) {
  const headings = outlines.map(
    (outline, idx) =>
      outline.title ||
      (['introduction', 'conclusion'].includes(outline.id)
        ? outline.id.charAt(0).toUpperCase() + outline.id.slice(1)
        : `Chapter ${idx}`),
  );

  const previewTOC =
    '<h1>Table of Contents</h1>\n' +
    headings.map((title) => `<h2>${title}</h2>`).join('\n');

  const previewChapters = chapters
    .map((chapter, idx) => {
      const chapterDOM = new DOMParser().parseFromString(
        chapter.content,
        'text/html',
      );
      const existingH1Elements = chapterDOM.querySelectorAll('h1');

      // Remove <h1> elements that contain the titleText
      existingH1Elements.forEach((h1Element) => {
        if (h1Element.textContent?.includes(headings[idx])) {
          h1Element.remove();
        }
      });

      return `<div class="preview-chapter" >
        <div style="page-break-before: always;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
        <h1>${headings[idx]}</h1>
        ${chapterDOM.body.innerHTML}
      </div>`;
    })
    .join('\n');
  const bodyStyle = book.pageSetup.style ?? '';

  const previewHTML = `<div style="${bodyStyle}" ><div class="preview-toc">${previewTOC}</div>${previewChapters}</div>`;

  return previewHTML;
}
