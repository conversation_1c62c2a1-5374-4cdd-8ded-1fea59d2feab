import JSZip from 'jszip';
// @ts-expect-error - no types (or @types/jszip-utils) are available yet
import JSZipUtils from 'jszip-utils';
import { saveAs } from 'file-saver';
import { Book, BookOutlines, ChapterOutline } from 'src/entities/book';

export async function exportBookImages(book: Book, bookOutlines: BookOutlines) {
  const zip = new JSZip();
  const outlines: ChapterOutline[] = [
    bookOutlines.introduction as ChapterOutline,
    ...(bookOutlines.outlines?.filter(
      (chapter) => !chapter.isSection,
    ) as ChapterOutline[]),
    bookOutlines.conclusion as ChapterOutline,
  ];

  const images = book.images.filter((img) => !img.isThumb);

  const imgsData: ArrayBuffer[] = await Promise.all(
    images.map(async (image) => {
      try {
        return await JSZipUtils.getBinaryContent(image.url);
      } catch (error) {
        return null; // or handle the error as appropriate
      }
    }),
  );

  if (imgsData.length) {
    imgsData.forEach((data, i) => {
      if (data) {
        const chapter = outlines.find(
          (outline) => outline.id === images[i].assignment,
        );
        if (chapter) {
          zip.file(
            `${book.title}/${chapter.title}/${images[i].filename}`,
            data,
            { binary: true },
          );
        }
      }
    });

    const content = await zip.generateAsync({ type: 'blob' });
    saveAs(content, `${book.title} (images).zip`);
  }
}
