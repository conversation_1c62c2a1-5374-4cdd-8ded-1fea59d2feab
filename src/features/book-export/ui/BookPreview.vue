<template>
  <div v-if="!needsReload" v-html="content" class="preview" />
  <q-inner-loading
    :showing="!content || needsReload"
    color="primary"
    label="Hang tight, Saving...."
    label-class="text-primary"
    label-style="font-size: 1.1em"
    style="z-index: 1"
  >
    <q-btn
      label="Error has occured. Click here to reload"
      icon="error"
      color="danger"
      @click="reloadPage"
      class="q-mb-md"
      v-if="needsReload"
      flat
      rounded
    >
      <q-tooltip>An error has occured. Please reload the page.</q-tooltip>
    </q-btn>
    <q-spinner-dots v-if="showLoader" color="primary" size="2em" />
    <div class="align-center" v-if="showLoader">
      <q-icon name="img:robot.png" class="q-ml-xs" size="xl" />
      Hang tight, Loading....
    </div>
  </q-inner-loading>
</template>

<script setup lang="ts">
import { computed, unref } from 'vue';
import { getChapter, type Book, listOutlines } from 'src/entities/book';
import { generateBookPreview } from '../lib';
import { computedAsync } from '@vueuse/core';
import { useRouter } from 'vue-router';

const props = defineProps<{ book: Book }>();

const router = useRouter();

const bookOutlines = unref(await listOutlines(props.book.id as string));
const outlines = computed(() => [
  bookOutlines.introduction,
  ...bookOutlines.outlines?.filter((chapter) => !chapter.isSection),
  bookOutlines.conclusion,
]);

const content = computedAsync(async () => {
  try {
    const chapters = await Promise.all(
      outlines.value.map((outline) => getChapter(props.book.id, outline.id)),
    );
    return generateBookPreview(props.book, outlines.value, chapters.map(unref));
  } catch (err) {
    return '1';
  }
}, '');

const needsReload = computed(() => content.value === '1');
const showLoader = computed(() => !content.value && !needsReload.value);
const reloadPage = () => {
  router.replace({ path: router.currentRoute.value.fullPath }).then(() => {
    // Optionally, force a reload if needed
    router.go(0);
  });
};
</script>

<style lang="scss">
.preview {
  background: rgba(0, 86, 155, 0.09);
  width: 100%;
  border: 1px solid rgba(0, 0, 0, 0.12);
}
</style>
