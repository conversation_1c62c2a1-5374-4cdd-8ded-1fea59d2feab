<template>
  <div class="category-details">
    <q-card class="category-header">
      <q-card-section>
        <div class="row items-center">
          <q-icon :name="activeCategory.icon" size="2em" />
          <h2 class="q-ml-md q-mb-none">{{ activeCategory.title }}</h2>
        </div>
      </q-card-section>
    </q-card>

    <q-card class="questions-container">
      <q-card-section>
        <div class="row justify-between items-center">
          <div class="text-h6">Questions</div>
          <q-btn color="primary" icon="add" @click="showQuestionForm" />
        </div>
      </q-card-section>

      <q-list bordered separator>
        <q-item v-for="question in questions" :key="question.id">
          <q-item-section>
            <q-item-label>{{ question.question }}</q-item-label>
            <q-item-label caption>{{ question.description }}</q-item-label>
          </q-item-section>
          <q-item-section side>
            <q-chip :color="answerElementColor[question.answerElement]">
              {{ question.answerElement }}
            </q-chip>
          </q-item-section>
        </q-item>
      </q-list>
    </q-card>

    <QuestionFormDialog
      v-model:show="showForm"
      :model-value="emptyQuestion"
      :available-variables="availableVariables"
      @submit="addQuestion"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useCategoryStore } from 'src/entities/book-category';
import { useQuestionStore } from 'src/entities/question/store/useQuestionStore';
import { Question } from 'src/entities/question';
import QuestionFormDialog from 'src/features/question-management/ui/QuestionFormDialog.vue';

const categoryStore = useCategoryStore();
const questionStore = useQuestionStore();
const { activeCategory, questions } = storeToRefs(categoryStore);

const showForm = ref(false);
const emptyQuestion = {
  id: '',
  categoryId: activeCategory.value?.id || '',
  question: '',
  description: '',
  hasAI: false,
  aiPrompt: '',
  aiOptions: undefined,
  answerElement: 'text' as AnswerElement,
  options: [],
  createdAt: new Date(),
  updatedAt: new Date(),
};

const availableVariables = ['name', 'email', 'age', 'preferences'];

function showQuestionForm() {
  showForm.value = true;
}

function addQuestion(question: Question) {
  questionStore.create(question);
  showForm.value = false;
}
</script>
