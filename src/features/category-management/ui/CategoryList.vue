<template>
  <q-list bordered separator>
    <q-item
      v-for="category in categories"
      :key="category.id"
      clickable
      @click="selectCategory(category)"
    >
      <q-item-section avatar>
        <q-icon :name="category.icon" />
      </q-item-section>
      <q-item-section>
        <q-item-label>{{ category.title }}</q-item-label>
        <q-item-label caption>
          {{ category.questions?.length || 0 }} questions
        </q-item-label>
      </q-item-section>
    </q-item>
  </q-list>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
import { BookCategory } from 'src/entities/book-category';

defineProps<{
  categories: BookCategory[];
}>();

const emit = defineEmits<{
  (e: 'select', category: BookCategory): void;
}>();

function selectCategory(category: BookCategory) {
  emit('select', category);
}
</script>
