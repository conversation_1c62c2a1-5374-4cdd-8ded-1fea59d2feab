export function formatCategoryTitle(title: string): string {
  return title.trim().charAt(0).toUpperCase() + title.slice(1);
}

export function validateCategory(category: any): boolean {
  return !!category.title && !!category.icon;
}

export function getCategoryStats(questions: any[]): {
  total: number;
  aiCount: number;
} {
  return {
    total: questions.length,
    aiCount: questions.filter((q) => q.hasAI).length,
  };
}
