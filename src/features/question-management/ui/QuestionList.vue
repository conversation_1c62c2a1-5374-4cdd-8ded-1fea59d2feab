<template>
  <q-list bordered separator>
    <q-item v-for="question in questions" :key="question.id">
      <q-item-section>
        <q-item-label>{{ question.question }}</q-item-label>
        <q-item-label caption>{{ question.description }}</q-item-label>
      </q-item-section>
      <q-item-section side>
        <q-chip :color="answerElementColor[question.answerElement]">
          {{ question.answerElement }}
        </q-chip>
      </q-item-section>
    </q-item>
  </q-list>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
import { AnswerElement, Question } from 'src/entities/question';

defineProps<{
  questions: Question[];
}>();

const answerElementColor: Record<AnswerElement, string> = {
  text: 'blue',
  textarea: 'indigo',
  select: 'green',
  options: 'orange',
};
</script>
