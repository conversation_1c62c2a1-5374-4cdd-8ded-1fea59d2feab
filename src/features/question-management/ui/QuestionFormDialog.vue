<template>
  <q-dialog v-model="showDialog" persistent>
    <q-card style="min-width: 700px">
      <q-card-section>
        <div class="text-h6">
          {{ isEdit ? 'Edit Question' : 'Create New Question' }}
        </div>
      </q-card-section>

      <q-card-section>
        <q-form @submit.prevent="submit">
          <q-input
            v-model="question.question"
            label="Question"
            :rules="[required]"
            class="q-mb-md"
          />

          <q-input
            v-model="question.description"
            label="Description"
            type="textarea"
            class="q-mb-md"
          />

          <q-toggle v-model="question.hasAI" label="Use AI" class="q-mb-md" />

          <div v-if="question.hasAI" class="ai-section">
            <AIPromptEditor
              v-model="question.aiPrompt"
              :variables="availableVariables"
              class="q-mb-md"
            />

            <div class="ai-options q-mt-md">
              <div class="option-label">
                Temperature: {{ question.aiOptions.temperature }}
              </div>
              <q-slider
                v-model="question.aiOptions.temperature"
                :min="0"
                :max="1"
                label
                :step="0.01"
                color="primary"
                class="q-mb-md"
              />

              <div class="option-label">
                Max Tokens: {{ question.aiOptions.maxTokens }}
              </div>
              <q-slider
                v-model="question.aiOptions.maxTokens"
                :min="50"
                :max="4096"
                label
                :step="1"
                color="secondary"
                class="q-mb-md"
              />
            </div>
          </div>

          <q-select
            v-model="question.answerElement"
            :options="answerElementOptions"
            label="Answer Element"
            :rules="[required]"
            class="q-mb-md"
          />

          <div
            v-if="
              question.answerElement === 'select' ||
              question.answerElement === 'options'
            "
            class="options-section"
          >
            <q-input
              v-model="option"
              @keyup.enter="addOption"
              placeholder="Add option"
              class="q-mb-md"
            />

            <div class="options-list q-mt-sm">
              <q-chip
                v-for="(opt, index) in question.options"
                :key="index"
                removable
                @remove="removeOption(index)"
                color="info"
                text-color="white"
              >
                {{ opt }}
              </q-chip>
            </div>
          </div>

          <AnswerPreview
            :answer-element="question.answerElement"
            :answer-value="previewAnswer"
            :options="question.options || []"
            class="q-mb-md"
          />

          <q-card-actions align="right">
            <q-btn flat label="Cancel" @click="closeDialog" />
            <q-btn type="submit" color="primary" label="Save" />
          </q-card-actions>
        </q-form>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { AIPromptEditor, AnswerPreview, Question } from 'src/entities/question';
import { defaultAIOptions } from 'src/features/question-management/lib/ai-options';

const props = defineProps<{
  modelValue: Question;
  availableVariables: string[];
  show: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:show', value: boolean): void;
  (e: 'submit', question: Question): void;
}>();

const showDialog = ref(props.show);
const question = ref<Question>({
  ...props.modelValue,
  aiOptions: props.modelValue.aiOptions || { ...defaultAIOptions },
  options: props.modelValue.options || [],
});
const option = ref('');
const previewAnswer = ref('');

const isEdit = computed(() => !!question.value.id);

const answerElementOptions = [
  'text',
  'textarea',
  'select',
  'options',
] as AnswerElement[];

function required(value: string) {
  return !!value || 'Field is required';
}

function addOption() {
  if (option.value && question.value.options) {
    question.value.options.push(option.value);
    option.value = '';
  }
}

function removeOption(index: number) {
  question.value.options?.splice(index, 1);
}

function submit() {
  emit('submit', question.value);
  closeDialog();
}

function closeDialog() {
  emit('update:show', false);
}

watch(
  () => props.show,
  (newVal) => {
    showDialog.value = newVal;
  },
);
</script>
