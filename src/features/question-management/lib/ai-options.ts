import { AIOptions } from 'src/entities/question';

export const defaultAIOptions: AIOptions = {
  temperature: 0.7,
  maxTokens: 1024,
  topP: 1,
  frequencyPenalty: 0,
  presencePenalty: 0,
};

export function resetAIOptions(): AIOptions {
  return { ...defaultAIOptions };
}

export function validateAIOptions(options: AIOptions): boolean {
  return (
    options.temperature >= 0 &&
    options.temperature <= 1 &&
    options.maxTokens > 0 &&
    options.maxTokens <= 4096
  );
}
