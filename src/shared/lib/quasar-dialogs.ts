import type { QVueGlobals } from 'quasar';

type Params = {
  title?: string;
  message?: string;
};

export function confirmOverrideText(
  $q: QVueGlobals,
  {
    title = 'Are you sure?',
    message = 'This action will override any content you already have in the textbox.',
  }: Params = {},
) {
  return new Promise<boolean>((resolve) => {
    $q.dialog({
      title,
      message,
      ok: {
        color: 'primary',
      },
      cancel: {
        outline: true,
      },
    })
      .onOk(() => resolve(true))
      .onCancel(() => resolve(false))
      .onDismiss(() => resolve(false));
  });
}

export function warn($q: QVueGlobals, { title = 'Warning', message }: Params) {
  return new Promise<void>((resolve) => {
    $q.dialog({
      title,
      message,
      color: 'negative',
      ok: {
        color: 'secondary',
      },
    })
      .onOk(() => resolve())
      .onDismiss(() => resolve());
  });
}

// TODO: add consistency in resolve value (true/false or resolve/reject)

export function confirmDeletion($q: QVueGlobals) {
  return new Promise<void>((resolve, reject) => {
    $q.dialog({
      title: 'Are you sure?',
      message: 'This action is permanent.',
      ok: {
        color: 'primary',
      },
      cancel: {
        outline: true,
      },
      persistent: true,
    })
      .onOk(() => resolve())
      .onCancel(() => reject());
  });
}

export function showError($q: QVueGlobals, message: string) {
  return new Promise<void>((resolve, reject) => {
    $q.dialog({
      title: 'Error',
      message,
      ok: {
        color: 'primary',
      },
    })
      .onOk(() => resolve())
      .onDismiss(() => reject())
      .onCancel(() => reject());
  });
}

// TODO: expose just one function `useQuasarDialogs` that returns all of these functions after obtaining the $q object from the context
