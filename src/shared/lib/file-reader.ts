function readAs<T>(file: Blob, readerMethod: (file: Blob) => void) {
  return new Promise<T>((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as T);
    reader.onerror = () => reject(reader.error);
    readerMethod.apply(reader, [file]);
  });
}

export function readAsDataURL(file: Blob) {
  return readAs<string>(file, FileReader.prototype.readAsDataURL);
}

export function readAsText(file: Blob) {
  return readAs<string>(file, FileReader.prototype.readAsText);
}

export function readAsArrayBuffer(file: Blob) {
  return readAs<ArrayBuffer>(file, FileReader.prototype.readAsArrayBuffer);
}
