import { createBookVersion } from 'src/features/book-versioning';
import { Dialog, DialogChainObject } from 'quasar';
import { requestOpenAI } from 'src/shared/api/openai';
import { canEditPrompt } from 'src/entities/setting';

interface PromptDetails {
  prompt: string;
  promptId: string;
  promptRequest: string;
  dialog?: DialogChainObject;
}

const dialogOptions = {
  message:
    '     <div class="instruct-dialog-title align-center">' +
    '        <img src="robot.png" width="42px" class="q-mr-sm" />' +
    '        <PERSON> is writing, hang tight!' +
    '      </div>',
  progress: true,
  persistent: true,
  html: true,
  ok: false,
};
async function onOpenAIButtonsApiCall(
  promptId: string,
  content: string,
  bookId: string,
) {
  let prompt = '';
  let promptTitle = '';
  const chapterTitle = window.selectedChapter;

  switch (promptId) {
    case 'outline': {
      promptTitle = 'Outline for ';
      prompt = content;
      content = `Writing the outline for ${chapterTitle}`;
      break;
    }
    case 'main': {
      promptTitle = 'Main Content for ';
      prompt = content;
      content = `Writing the main content for ${chapterTitle}`;
      break;
    }
    case 'rewrite': {
      promptTitle = 'Rewrite the Content for ';
      prompt = 'Rewrite and enhance the following text: ' + content;
      break;
    }
    case 'addContent': {
      promptTitle = 'Add Content for ';
      prompt = content;
      break;
    }
    case 'instruct': {
      promptTitle = 'Open Text Input for ';
      prompt = content;
      break;
    }
    case 'addStory': {
      promptTitle = 'Add a Story for ';
      prompt =
        'Write a story that illustrates the point that is being made. Use relevant and recognized literature, historical and current events, sports, and related materical to help write the story. The story should be non-fictional, and not exceed 2-3 paragraphs, and 300 words. Use the selected text provided. ' +
        content;
      break;
    }
    case 'grammar': {
      promptTitle = 'Edit Grammar, Spelling and Punctuation for ';
      prompt =
        'Edit the content for grammar, spelling, and punctuation without changing the following content.' +
        content;
      break;
    }
    default: {
      promptTitle = 'Simplify the Content for ';
      prompt = 'Please simplify the following text: ' + content;
      break;
    }
  }
  promptTitle += chapterTitle;

  const formattedDateTime = getTodayFormattedDateTime();
  const versionNote = {
    Date: formattedDateTime,
    Prompt: promptTitle,
  };
  const description = Object.entries(versionNote)
    .map(([key, value]) => `${key}: ${value}`)
    .join('\n\n');
  await createBookVersion(bookId, formattedDateTime, description);

  if (canEditPrompt.value && promptId != 'main') {
    return new Promise<PromptDetails>((resolve) => {
      const editPromptTitle = 'Edit Prompt';
      Dialog.create({
        title: `<div class="instruct-dialog-title">
                  <img src="robot.png" width="42px" class="q-mr-sm" />
                  ${editPromptTitle}
          </div>`,
        message: 'Prompt Manny to write based on the highlighted text:',
        color: 'negative',
        class: 'tox-dialog',
        html: true,
        ok: {
          color: 'primary',
        },
        prompt: {
          model: prompt,
          type: 'textarea',
        },
        cancel: true,
        persistent: true,
      })
        .onOk((data: string) => {
          const dialogLoader: DialogChainObject = Dialog.create(dialogOptions);
          return resolve({
            prompt: data,
            promptId: promptTitle,
            promptRequest: content,
            dialog: dialogLoader,
          });
        })
        .onDismiss(() => {
          return resolve({
            prompt: '',
            promptId: promptTitle,
            promptRequest: content,
          });
        });
    });
  } else {
    return new Promise<PromptDetails>((resolve) =>
      resolve({
        prompt: prompt,
        promptId: promptTitle,
        promptRequest: content,
      }),
    );
  }
}

export function getTodayFormattedDateTime() {
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
  };
  return new Intl.DateTimeFormat('zh-CN', options).format(Date.now());
}

type Params = {
  message?: string;
};

export async function onOpenAIButtons(
  promptId: string,
  content: string,
  bookId: string,
) {
  let dialog: DialogChainObject;
  const prompt = await onOpenAIButtonsApiCall(promptId, content, bookId);
  if (prompt.dialog) dialog = prompt.dialog as DialogChainObject;
  if (prompt.prompt as string) {
    return pollApiWithProgress(
      prompt.promptId as string,
      prompt.prompt as string,
    )
      .then((responseData) => {
        if (responseData) {
          if (promptId == 'main') {
            dialog.update({
              message:
                '     <div class="instruct-dialog-title align-center">' +
                '        <img src="robot.png" width="42px" class="q-mr-sm" />' +
                '        Manny has completed your request!' +
                '      </div>',
              ok: true,
              progress: false,
            });
          } else {
            dialog.hide();
          }
          return responseData;
        }
        dialog.update({
          message: `An error has occured. Please try again`,
          ok: true,
          progress: false,
        });
      })
      .catch((error) => {
        dialog.update({
          message: `An error has occured. Please try again`,
          ok: true,
          progress: false,
        });
        console.error('Error while polling API:', error);
        return false;
      });
  }
}
function pollApiWithProgress(promptId: string, prompt: string): Promise<any> {
  const checkApiResponse = async (): Promise<any> => {
    try {
      // Make the API call
      const responseData = await requestOpenAI({
        type: 'compose',
        prompt: prompt as string,
      });
      return Promise.resolve(responseData);
    } catch (error) {
      return Promise.resolve(false);
    }
  };
  return checkApiResponse();
}
