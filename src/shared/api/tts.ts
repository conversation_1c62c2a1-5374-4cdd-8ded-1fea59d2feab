import {
  requestConvertTextToSpeech,
  requestConvertTextToSpeechLongAudio,
  requestFetchVoicesFirebase,
  type TTSParams,
  type TTSParamsLongAudio,
} from './firebase-functions';

export function fetchVoices() {
  return requestFetchVoicesFirebase();
}

export async function convertTextToSpeech(
  text: string,
  voiceName: string,
  languageCode: string,
) {
  const params: TTSParams = {
    text: text,
    voiceName: voiceName,
    languageCode: languageCode,
  };
  const { data } = await requestConvertTextToSpeech(params);
  return data;
}

export async function convertTextToSpeechLongAudio(
  text: string,
  voiceName: string,
  languageCode: string,
  storageLocation: string,
  filename: string,
  projectId: string,
) {
  const params: TTSParamsLongAudio = {
    text: text,
    voiceName: voiceName,
    languageCode: languageCode,
    storageLocation: storageLocation,
    filename: filename,
    projectId: projectId,
  };
  const { data } = await requestConvertTextToSpeechLongAudio(params);
  return data;
}
