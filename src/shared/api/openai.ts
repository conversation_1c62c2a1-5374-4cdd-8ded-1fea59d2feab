import { type OpenAIParams, requestOpenAIFirebase } from './firebase-functions';
import { auth } from 'src/firebase';

export async function requestOpenAI(params: OpenAIParams) {
  const { data } = await requestOpenAIFirebase(params);
  return data;
}

export async function streamOpenAI(
  params: OpenAIParams,
  onData: (data: string) => void,
) {
  const user = auth.currentUser;
  if (!user) {
    throw new Error('User not authenticated');
  }

  const token = await user.getIdToken();

  const response = await fetch(
    'https://us-central1-bspub-3085b.cloudfunctions.net/streamOpenAI',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(params),
    },
  );

  if (!response.body) {
    return;
  }

  const reader = response.body.getReader();
  const decoder = new TextDecoder();

  const read = async () => {
    const { done, value } = await reader.read();
    if (done) {
      return;
    }

    const chunk = decoder.decode(value, { stream: true });
    const lines = chunk.split('\n\n');
    for (const line of lines) {
      if (line.startsWith('data: ')) {
        const data = line.substring(6);
        if (data) {
          try {
            const parsed = JSON.parse(data);
            onData(parsed.content);
          } catch (e) {
            console.error('Error parsing stream data', e);
          }
        }
      }
    }

    await read();
  };

  await read();
}

export function composeText(prompt: string) {
  return requestOpenAI({
    type: 'compose',
    prompt,
  });
}

export function simplifyText(prompt: string) {
  return requestOpenAI({
    type: 'simplify',
    prompt,
  });
}
