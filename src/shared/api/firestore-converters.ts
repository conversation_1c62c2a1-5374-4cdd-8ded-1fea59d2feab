import type { FirestoreDataConverter } from 'firebase/firestore';
import { Timestamp, serverTimestamp } from 'firebase/firestore';

type WithId<T> = T & {
  /** The unique identifier of the document on Firestore */
  id: string;
};

export const includeIdAndFixTimestamp = <
  T extends {},
>(): FirestoreDataConverter<WithId<T>> => ({
  fromFirestore(snapshot, options) {
    const data = snapshot.data(options);
    Object.keys(data).forEach((key) => {
      if (data[key] instanceof Timestamp) {
        data[key] = (data[key] as Timestamp).toDate();
      }
    });
    Object.defineProperty(data, 'id', {
      value: snapshot.id,
      enumerable: false,
      writable: false,
    });
    return data as WithId<T>;
  },
  toFirestore(object) {
    const { id: _, ...rest } = object;
    const data = rest as any;
    Object.keys(data).forEach((key) => {
      if (data[key] instanceof Date) {
        data[key] = Timestamp.fromDate(data[key]);
      }
    });
    return data;
  },
});

export const lastUpdated = <T extends {}>(): FirestoreDataConverter<T> => ({
  fromFirestore(snapshot, options) {
    return snapshot.data(options) as T;
  },
  toFirestore(object) {
    return {
      updatedAt: serverTimestamp(),
      ...object,
    };
  },
});
