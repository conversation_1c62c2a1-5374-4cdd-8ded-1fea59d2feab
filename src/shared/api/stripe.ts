import { firebase } from 'src/firebase';
import { getStripePayments } from '@stripe/firestore-stripe-payments';
import {
  cancelStripeSubscriptionFirebase,
  createStripePortalLinkFirebase,
  getSubscriptionInvoicesPaidFirebase,
} from './firebase-functions';

export const payments = getStripePayments(firebase, {
  customersCollection: 'users',
  productsCollection: 'products',
});

export async function getSubscriptionInvoicesPaid(subscriptionId: string) {
  const { data } = await getSubscriptionInvoicesPaidFirebase({
    subscription: subscriptionId,
  });
  return data;
}

export async function createStripePortalLink(customer: string) {
  const url = new URL(window.location.href);
  const { data } = await createStripePortalLinkFirebase({
    returnUrl: `${url.origin}/profile`,
    customer: customer,
  });
  return data;
}

export async function cancelStripeSubscription(subscriptionId: string) {
  const { data } = await cancelStripeSubscriptionFirebase({
    subscription: subscriptionId,
  });
  return data;
}
