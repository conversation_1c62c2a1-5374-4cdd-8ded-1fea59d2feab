import { debounceFilter, watchIgnorable } from '@vueuse/core';
import {
  type DocumentReference,
  type Unsubscribe,
  onSnapshot,
  getDoc,
  setDoc,
  doc,
} from 'firebase/firestore';
import { type Ref, ref } from 'vue';

/**
 * Fetches a document (given its reference) from Firestore and returns it in a
 *  Vue ref that handles automatic updates both ways.
 * Update to the ref will be written back to Firestore, and updates from
 * Firestore will be reflected in the ref.
 * Updates are debounced to avoid excessive writes.
 */
export async function getDocSync<T extends {}>(
  docRef: DocumentReference<T>,
): Promise<Ref<T>> {
  // Get the initial value to not return an empty ref
  const snap = await getDoc(docRef);
  const data = snap.data() as T;
  const dataRef = ref(data) as Ref<T>;

  let unsub: Unsubscribe;
  // Propagate updates in the ref back to Firestore
  const { ignoreUpdates, stop, ignorePrevAsyncUpdates } = watchIgnorable(
    dataRef,
    (newData, oldData) => {
      // Can also check `newData !== oldData` (referential equality)
      if (
        oldData &&
        newData &&
        'id' in newData &&
        'id' in oldData &&
        newData.id !== oldData.id
      ) {
        // The entire doc (not just a property in it) is being replaced.
        // Prevent replacing the document with a new one with a different id.

        // Update the document reference
        docRef = doc(
          docRef.parent,
          newData.id as string,
        ) as DocumentReference<T>;
        // Remove the old listener and add a fresh one
        unsub?.();
        ignorePrevAsyncUpdates();
        unsub = onSnapshot(docRef, (snapshot) => {
          ignoreUpdates(() => {
            dataRef.value = snapshot.data()! as T;
          });
        });
        return;
      } else {
        if ('id' in newData) {
          docRef = doc(
            docRef.parent,
            newData.id as string,
          ) as DocumentReference<T>;
        }

        // Same object, just update the properties
        setDoc(docRef, newData as T, { merge: true });
      }
    },
    { eventFilter: debounceFilter(1000, { maxWait: 5000 }), deep: true },
  );

  // Propagate updates from Firestore to the ref
  unsub = onSnapshot(docRef, (snapshot) => {
    ignoreUpdates(() => {
      dataRef.value = snapshot.data()! as T;
    });
  });

  return dataRef;
}
