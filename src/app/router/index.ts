import {
  createRouter,
  createMemoryHistory,
  createWebHistory,
} from 'vue-router';
import type { Store } from 'vuex';
import routes from './routes';
import {
  isAdmin,
  isAuthor,
  loadingState,
  isReviewer,
  getCurrentUser,
} from 'src/entities/user';
import { isMaintenanceMode } from 'src/entities/setting';

/*
 * If not building with SSR mode, you can
 * directly export the Router instantiation;
 *
 * The function below can be async too; either use
 * async/await or return a Promise which resolves
 * with the Router instance.
 */

export default function ({ store }: { store: Store<any> }) {
  const createHistory = process.env.SERVER
    ? createMemoryHistory
    : createWebHistory;
  const Router = createRouter({
    scrollBehavior: () => ({ left: 0, top: 0 }),
    routes,

    // Leave these as they are and change in quasar.conf.js instead!
    // quasar.conf.js -> build -> vueRouterMode
    // quasar.conf.js -> build -> publicPath
    // mode: process.env.VUE_ROUTER_MODE,
    // base: process.env.VUE_ROUTER_BASE,
    history: createHistory(process.env.VUE_ROUTER_BASE),
  });

  Router.beforeEach(async (to, from, next) => {
    const requiresAuth = to.matched.some((record) => record.meta.requiresAuth);
    const requiresAdmin = to.matched.some(
      (record) => record.meta.requiresAdmin,
    );
    const requiresAuthor = to.matched.some(
      (record) => record.meta.requiresAuthor,
    );
    const maintenanceMode = to.matched.some(
      (record) => record.meta.maintenanceMode,
    );
    const isLoggedIn = await getCurrentUser();
    const canAuthor = isAdmin.value && isAuthor.value && isReviewer.value;
    const isMaintenance =
      isMaintenanceMode.value && maintenanceMode && !isAdmin.value;

    const isForbidden = requiresAuth && !isLoggedIn;
    const isAuthForbidden =
      requiresAuth && isLoggedIn && requiresAuthor && canAuthor;

    const isShouldNotOnPublic = !requiresAuth && isLoggedIn;
    const isNotFound = to.matched.length === 0;

    loadingState.value = true;
    await new Promise((resolve) => setTimeout(resolve, 500));

    if (isNotFound) {
      next({ name: 'error-404' });
    } else if (isMaintenance) {
      next({ path: '/maintenance' });
    } else if (isShouldNotOnPublic) {
      next({ path: '/' });
    } else if (isForbidden) {
      next({ path: '/login' });
    } else if (isAuthForbidden) {
      next({ path: '/' });
    } else {
      next();
    }
  });
  Router.afterEach((to, from) => {
    loadingState.value = false;
  });
  return Router;
}
