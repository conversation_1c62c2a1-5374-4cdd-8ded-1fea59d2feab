// Quasar SCSS (& Sass) Variables
// --------------------------------------------------
// To customize the look and feel of this app, you can override
// the Sass/SCSS variables found in Quasar's source Sass/SCSS files.

// Check documentation for full list of Quasar variables

// Your own variables (that are declared here) and Quasar's own
// ones will be available out of the box in your .vue/.scss/.sass files

// It's highly recommended to change the default colors
// to match your app's branding.
// Tip: Use the "Theme Builder" on Quasar's documentation website.

$primary: #00569b;
.body--dark {
  --q-dark: #121212;
  background: #121212 !important;
}
$secondary: #00569b;
$accent: #00569b;

$dark: #717171;

$positive: #21ba45;
$negative: #c10015;
$info: #00569b;
$yellow: #ffd940;
$gray-500: #adb5bd;

// Theme Colors
$primary-color: #3b82f6; // Tailwind Blue 500
$secondary-color: #10b981; // Emerald 500
$accent-color: #8b5cf6; // Violet 500
$neutral-color: #6b7280; // Gray 500
$error-color: #ef4444; // Red 500
$success-color: #10b981; // Green 500

// Font Families
$font-primary: 'Quicksand', 'Helvetica Neue', sans-serif;
$font-secondary: 'Cabin', 'Arial', sans-serif;

// Typography
$font-family: $font-secondary, 'Inter', 'Roboto', sans-serif;
$header-font: $font-primary, 'Poppins', $font-family;

// Spacing System
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// Shadows
$card-shadow: 0 4px 18px rgba(0, 0, 0, 0.08);
$input-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
$button-hover-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

// Border Radius
$border-radius-sm: 8px;
$border-radius-md: 12px;
$border-radius-lg: 16px;

// stylelint-disable
$gray-100: #f8f9fa;
$gray-200: #e9ecef;
$gray-300: #dee2e6;
$gray-400: #ced4da;
$gray-500: #adb5bd;
$gray-600: #6c757d;
$gray-700: #495057;
$gray-800: #343a40;
$gray-900: #212529;

$gray-dark-100: #242c34;
$gray-dark-200: #283039;
$gray-dark-300: #303841;
$gray-dark-400: #3d454e;
$gray-dark-500: #63686f;
$gray-dark-600: #767b81;
$gray-dark-700: #ced1d2;
$gray-dark-800: #cfd1d2;
$gray-dark-900: #d4d5d7;

// ===================================
// COLOR SYSTEM
// ===================================

// Primary Colors
$primary: #02369c;
$primary-light: #1154ed;
$primary-accent: #465ff1;
$primary-input-icon: #1565d8;
$primary-lightest: #dfeaff;

// Secondary Colors
$secondary-purple: #3f3ee9;
$secondary-purple-light: #f2f2ff;
$secondary-purple-lighter: #dedefc;

// Neutral Colors
$neutral-50: #f3f4f8;
$neutral-100: #f5f5f5;
$neutral-200: #eaeaea;
$neutral-300: #dae1e9;
$neutral-400: #a6a6ba;
$neutral-500: #8692a6;
$neutral-600: #777a8d;
$neutral-700: #74787c;
$neutral-800: #4b5767;
$neutral-900: #131416;

// Semantic Colors
$background-primary: #ffffff;
$background-body: $neutral-50;
$text-primary: $neutral-900;
$text-secondary: $neutral-800;
$text-muted: $neutral-600;
$text-label: $neutral-500;
$border-light: $neutral-200;
$border-default: $neutral-300;

// Special Colors
$tooltip-title: #040445;

// ===================================
// TYPOGRAPHY SYSTEM
// ===================================

// Font Weights
$font-light: 300;
$font-regular: 400;
$font-medium: 500;
$font-semibold: 600;
$font-bold: 700;

// Font Sizes (Mobile First)
$font-size-h1: 32px;
$font-size-h1-desktop: 50px;
$font-size-modal-title: 24px;
$font-size-modal-title-desktop: 32px;
$font-size-text-bold: 18px;
$font-size-text-bold-desktop: 24px;
$font-size-text-regular: 16px;
$font-size-text-regular-desktop: 18px;
$font-size-button: 16px;
$font-size-input-label: 16px;
$font-size-tooltip: 16px;

// Line Heights
$line-height-tight: 100%;
$line-height-normal: 120%;

// ===================================
// SPACING SYSTEM
// ===================================

$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 40px;

// Component Specific Spacing
$input-padding: 4px;
$input-gap: 10px;
$button-gap: 8px;
$modal-gap: 24px;
$modal-padding: 40px;
$tooltip-gap: 10px;

// ===================================
// RESPONSIVE BREAKPOINTS
// ===================================

$breakpoint-sm: 600px;
$breakpoint-md: 1024px;
$breakpoint-lg: 1440px;

@mixin mobile {
  @media (max-width: #{$breakpoint-sm - 1px}) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: #{$breakpoint-sm}) and (max-width: #{$breakpoint-md - 1px}) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: #{$breakpoint-md}) {
    @content;
  }
}

// ===================================
// COMPONENT SIZING
// ===================================

// Input Dimensions
$input-width: 452px;
$input-width-mobile: 100%;
$input-height: 44px;
$input-border-radius: 8px;
$input-border-width: 1.5px;
$input-icon-size: 20px;
$input-icon-container: 36px;
$input-icon-border-radius: 6px;
$input-icon-padding: 8px;

// Button Dimensions
$button-width: 210px;
$button-width-mobile: 100%;
$button-height: 48px;
$button-border-radius: 8px;
$button-padding-vertical: 10px;
$button-padding-horizontal: 20px;

// Modal Dimensions
$modal-width: 452px;
$modal-width-mobile: 90vw;
$modal-min-height: 412px;
$modal-border-radius: 12px;
$modal-actions-height: 80px;

// Tooltip Dimensions
$tooltip-width: 207px;
$tooltip-body-width: 199px;
$tooltip-body-height: 142px;
$tooltip-close-size: 18px;
