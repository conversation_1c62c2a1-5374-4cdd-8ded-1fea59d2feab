// ===================================
// MANUSCRIPTR DESIGN SYSTEM
// ===================================

// Google Fonts Import
@import url('https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&family=Cabin:wght@400;500;600;700&display=swap');

// Import Quasar variables first
@import '~quasar/src/css/variables.sass';
@import 'src/css/theme/style.scss';

// ===================================
// CSS CUSTOM PROPERTIES FOR THEMING
// ===================================

:root {
  // Gray scale - will be overridden by theme config
  --theme-gray-100: #f8f9fa;
  --theme-gray-200: #e9ecef;
  --theme-gray-300: #dee2e6;
  --theme-gray-400: #ced4da;
  --theme-gray-500: #adb5bd;
  --theme-gray-600: #6c757d;
  --theme-gray-700: #495057;
  --theme-gray-800: #343a40;
  --theme-gray-900: #212529;

  // Primary Colors
  --theme-primary: #02369c;
  --theme-primary-light: #1154ed;
  --theme-primary-accent: #465ff1;
  --theme-primary-input-icon: #1565d8;
  --theme-primary-lightest: #dfeaff;

  // Secondary Colors
  --theme-secondary-purple: #3f3ee9;
  --theme-secondary-purple-light: #f2f2ff;
  --theme-secondary-purple-lighter: #dedefc;

  // Neutral Colors
  --theme-neutral-50: #f3f4f8;
  --theme-neutral-100: #f5f5f5;
  --theme-neutral-200: #eaeaea;
  --theme-neutral-300: #dae1e9;
  --theme-neutral-400: #a6a6ba;
  --theme-neutral-500: #8692a6;
  --theme-neutral-600: #777a8d;
  --theme-neutral-700: #74787c;
  --theme-neutral-800: #4b5767;
  --theme-neutral-900: #131416;

  // Semantic Colors
  --theme-background-primary: #ffffff;
  --theme-background-body: #f3f4f8;
  --theme-text-primary: #131416;
  --theme-text-secondary: #4b5767;
  --theme-text-muted: #777a8d;
  --theme-text-label: #8692a6;
  --theme-border-light: #eaeaea;
  --theme-border-default: #dae1e9;

  // Special Colors
  --theme-tooltip-title: #040445;
}

// ===================================
// LEGACY SCSS VARIABLES (for backward compatibility)
// ===================================

// Primary Colors
$primary: var(--theme-primary);
$primary-light: var(--theme-primary-light);
$primary-accent: var(--theme-primary-accent);
$primary-input-icon: var(--theme-primary-input-icon);
$primary-lightest: var(--theme-primary-lightest);

// Secondary Colors
$secondary-purple: var(--theme-secondary-purple);
$secondary-purple-light: var(--theme-secondary-purple-light);
$secondary-purple-lighter: var(--theme-secondary-purple-lighter);

// Neutral Colors
$neutral-50: var(--theme-neutral-50);
$neutral-100: var(--theme-neutral-100);
$neutral-200: var(--theme-neutral-200);
$neutral-300: var(--theme-neutral-300);
$neutral-400: var(--theme-neutral-400);
$neutral-500: var(--theme-neutral-500);
$neutral-600: var(--theme-neutral-600);
$neutral-700: var(--theme-neutral-700);
$neutral-800: var(--theme-neutral-800);
$neutral-900: var(--theme-neutral-900);

// Semantic Colors
$background-primary: var(--theme-background-primary);
$background-body: var(--theme-background-body);
$text-primary: var(--theme-text-primary);
$text-secondary: var(--theme-text-secondary);
$text-muted: var(--theme-text-muted);
$text-label: var(--theme-text-label);
$border-light: var(--theme-border-light);
$border-default: var(--theme-border-default);

// Special Colors
$tooltip-title: var(--theme-tooltip-title);

// ===================================
// TYPOGRAPHY SYSTEM
// ===================================

// Font Weights
$font-light: 300;
$font-regular: 400;
$font-medium: 500;
$font-semibold: 600;
$font-bold: 700;

// Font Sizes (Mobile First)
$font-size-h1: 32px;
$font-size-h1-desktop: 50px;
$font-size-modal-title: 24px;
$font-size-modal-title-desktop: 32px;
$font-size-text-bold: 18px;
$font-size-text-bold-desktop: 24px;
$font-size-text-regular: 16px;
$font-size-text-regular-desktop: 18px;
$font-size-button: 16px;
$font-size-input-label: 16px;
$font-size-tooltip: 16px;

// Line Heights
$line-height-tight: 100%;
$line-height-normal: 120%;

// ===================================
// SPACING SYSTEM
// ===================================

$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 40px;

// Component Specific Spacing
$input-padding: 4px;
$input-gap: 10px;
$button-gap: 8px;
$modal-gap: 24px;
$modal-padding: 40px;
$tooltip-gap: 10px;

// ===================================
// RESPONSIVE BREAKPOINTS
// ===================================

$breakpoint-sm: 600px;
$breakpoint-md: 1024px;
$breakpoint-lg: 1440px;

@mixin mobile {
  @media (max-width: #{$breakpoint-sm - 1px}) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: #{$breakpoint-sm}) and (max-width: #{$breakpoint-md - 1px}) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: #{$breakpoint-md}) {
    @content;
  }
}

// ===================================
// COMPONENT SIZING
// ===================================

// Input Dimensions
$input-width: 452px;
$input-width-mobile: 100%;
$input-height: 44px;
$input-border-radius: 8px;
$input-border-width: 1.5px;
$input-icon-size: 20px;
$input-icon-container: 25px;
$input-icon-border-radius: 6px;
$input-icon-padding: 8px;

// Button Dimensions
$button-width: 210px;
$button-width-mobile: 100%;
$button-height: 48px;
$button-border-radius: 8px;
$button-padding-vertical: 10px;
$button-padding-horizontal: 20px;

// Modal Dimensions
$modal-width: 452px;
$modal-width-mobile: 90vw;
$modal-min-height: 412px;
$modal-border-radius: 12px;
$modal-actions-height: 80px;

// Tooltip Dimensions
$tooltip-width: 207px;
$tooltip-body-width: 199px;
$tooltip-body-height: 142px;
$tooltip-close-size: 18px;

// ===================================
// GLOBAL STYLES
// ===================================

body.body--dark {
  color: #fff;
  background: var(--q-dark-page) !important;
}

.q-icon {
  cursor: pointer;
}

::selection {
  background: color-mix(in srgb, var(--theme-primary) 25%, white);
}

body {
  font-family: $font-secondary;
  font-size: $font-size-text-regular;
  font-weight: $font-regular;
  line-height: $line-height-normal;
  color: var(--theme-text-primary);
  background-color: var(--theme-background-body);
  background-size: cover;
  background-repeat: no-repeat;
  background-attachment: fixed;

  @include desktop {
    font-size: $font-size-text-regular-desktop;
  }
}

// ===================================
// TYPOGRAPHY CLASSES
// ===================================

#title {
  font-size: 20px;
  line-height: 34px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  line-height: normal;
  font-weight: bold;
  color: var(--theme-text-primary);
}

h1 {
  font-family: $font-primary;
  font-weight: $font-medium;
  font-size: $font-size-h1;
  line-height: $line-height-normal;
  margin-bottom: 0.67em;

  @include desktop {
    font-size: $font-size-h1-desktop;
  }
}

h2 {
  font-size: 1.5em;
  margin-bottom: 0.83em;
}

h3 {
  font-size: 1.17em;
  margin-bottom: 1em;
}

h4 {
  font-size: 1em;
  margin-bottom: 1.33em;
}

h5 {
  font-size: 0.83em;
  margin-bottom: 1.67em;
}

h6 {
  font-size: 0.67em;
  margin-bottom: 2.33em;
}

.text-h1 {
  font-family: $font-primary;
  font-weight: $font-medium;
  font-size: $font-size-h1;
  line-height: $line-height-normal;
  text-align: center;
  color: var(--theme-text-primary);

  @include desktop {
    font-size: $font-size-h1-desktop;
  }
}

.text-bold {
  font-family: $font-secondary;
  font-weight: $font-bold;
  font-size: $font-size-text-bold;
  line-height: $line-height-normal;
  text-align: center;
  color: var(--theme-text-primary);

  @include desktop {
    font-size: $font-size-text-bold-desktop;
  }
}

.text-regular {
  font-family: $font-secondary;
  font-weight: $font-regular;
  font-size: $font-size-text-regular;
  line-height: $line-height-normal;
  text-align: center;
  color: var(--theme-text-primary);

  @include desktop {
    font-size: $font-size-text-regular-desktop;
  }
}

// ===================================
// THEME-AWARE PRESERVED EXISTING STYLES
// ===================================

.book-chapters-nav {
  .q-item.disabled {
    background-color: var(--theme-background-primary) !important;
    opacity: 1 !important;
  }
  .selected-chapter-nav .q-item--clickable {
    background-color: var(--theme-primary);
    color: var(--theme-background-primary);
    border-radius: 3px;
    .q-item__label,
    .q-expansion-item__toggle-icon,
    .q-item__section--side {
      color: var(--theme-background-primary);
    }
  }
  .q-expansion-item__toggle-icon {
    min-height: 1.4em;
    color: var(--theme-text-primary);
  }
}

// ===================================
// THEME-ADMIRAL CUSTOM STYLES
// ===================================
.palette-default {
  background: transparent url('/images/ui/body-bg.jpg') !important;
  background-size: cover !important;
  background-repeat: no-repeat !important;
  background-attachment: fixed !important;
  &:has(.q-layout.bookdetails) {
    background: linear-gradient(
      135deg,
      #e0f7fa 0%,
      #b2ebf2 50%,
      #80deea 100%
    ) !important;
    background-size: cover !important;
    background-repeat: no-repeat !important;
    background-attachment: fixed !important;
  }
  .onboarding-card {
    background: url('/images/ui/body-bg.jpg') !important;
    background-size: cover !important;
    background-repeat: no-repeat !important;
    background-attachment: fixed !important;
  }
  .q-inner-loading {
    background: none !important;
  }
  .page-layout {
    .page-header {
      .page-title {
        color: var(--q-text-color) !important;
      }
    }
  }
  .text-color-custom {
    color: var(--q-text-color) !important;
  }
}
.body--dark.palette-default {
  .q-inner-loading {
    background: none !important;
  }
}
.body--dark {
  .q-inner-loading {
    background: none !important;
  }
}
.body--dark.palette-default {
  .q-item__label,
  .q-expansion-item__toggle-icon,
  .q-item__section--side {
    color: var(--q-text-color) !important;
  }
  .drawer-icon {
    opacity: 0.8;
    color: var(--q-text-color) !important;
  }
}

.review-mode .comment-highlight {
  background: rgba($yellow, 0.33) !important;
}

.review-mode .comment-highlight.selected {
  background: $yellow !important;
}

.review-mode .comment-highlight.resolved {
  background: none !important;
}

.fixed-drawer-header {
  position: absolute;
  top: 50px;
  left: 0;
  width: 100%;
  padding: 24px;
  height: 104px;
  align-items: center;
  background-color: var(--theme-background-primary);
}

.footnote-mode .footnote {
  border-bottom: 2px solid var(--theme-primary);
}

.footnote {
  pointer-events: none;
}

.footnote:after {
  content: ' x';
  color: var(--theme-primary) !important;
  position: relative;
  top: -6px;
}

.footnote sup {
  color: var(--theme-primary) !important;
}

.chapters-list {
  .q-item__label {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--theme-text-primary);
  }
}

.rounded-borders {
  overflow: hidden;
}

.q-popup-edit__buttons {
  justify-content: flex-end;
}

.q-popup-edit__buttons .q-btn {
  background: var(--theme-neutral-100);
}

thead tr th {
  position: sticky;
  z-index: 1;
  background: var(--theme-background-primary);
}

thead tr:first-child th {
  top: 0;
  background: var(--theme-background-primary);
}

// ===================================
// QUASAR COMPONENT CUSTOMIZATION
// ===================================

// Q-INPUT Styling
.q-input {
  .q-field__control {
    border-radius: $input-border-radius;
    border: $input-border-width solid var(--theme-border-light);
    background-color: var(--theme-background-primary);
  }

  .q-field__label {
    font-family: $font-secondary;
    font-weight: $font-regular;
    font-size: $font-size-input-label;
    line-height: $line-height-tight;
    color: var(--theme-text-label);
  }

  .q-field__native {
    color: var(--theme-text-primary);
  }

  .q-field__prepend {
    .q-icon {
      width: $input-icon-container;
      height: $input-icon-container;
      border-radius: $input-icon-border-radius;
      padding: $input-icon-padding;
      background: var(--theme-primary-input-icon);
      color: white !important;
      font-size: $input-icon-size;
      margin-left: -2px;
      margin-top: -1px;
    }
  }

  .q-field__append {
    .q-icon {
      font-size: 24px;
      color: var(--theme-secondary-purple);
    }
  }
}

// Q-BTN Styling
.q-card__actions {
  .q-btn {
    border-radius: $button-border-radius;
  }
}

.q-btn {
  font-family: $font-primary;
  font-weight: $font-bold;
  font-size: $font-size-button;
  line-height: $line-height-tight;
  text-align: center;

  &.q-btn--standard {
    background: var(--theme-primary-accent);
    color: white;

    &:hover {
      background: color-mix(in srgb, var(--theme-primary-accent) 85%, black);
    }
  }

  &.q-btn--outline {
    border: 1px solid var(--theme-border-default);
    background: transparent;
    color: var(--theme-text-muted);

    &:hover {
      background: var(--theme-neutral-50);
    }
  }
}

// Q-DIALOG/MODAL Styling
.q-dialog__inner {
  .q-card {
    border-radius: $modal-border-radius;

    color: var(--theme-text-primary);

    @include desktop {
      width: $modal-width;
    }

    .q-card__section {
      gap: $modal-gap;
    }
  }
}

.q-dialog__title {
  font-family: $font-primary;
  font-weight: $font-medium;
  font-size: $font-size-modal-title;
  line-height: $line-height-normal;
  text-align: center;
  color: var(--theme-text-secondary);

  @include desktop {
    font-size: $font-size-modal-title-desktop;
  }
}

// Q-TOOLTIP Styling
.q-tooltip {
  background: var(--theme-background-primary);
  color: var(--theme-text-primary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: $spacing-sm;

  .body--dark & {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  }

  .tooltip-body {
    padding: $spacing-sm;
    gap: $tooltip-gap;
  }

  .tooltip-title {
    font-family: $font-primary;
    font-weight: $font-medium;
    font-size: $font-size-tooltip;
    line-height: 115%;
    letter-spacing: -0.5%;
    padding-bottom: $spacing-sm;
    border-bottom: 1px solid var(--theme-border-light);
    color: var(--theme-tooltip-title);
  }

  .tooltip-close {
    width: $tooltip-close-size;
    height: $tooltip-close-size;
    color: var(--theme-neutral-400);
    cursor: pointer;
    position: absolute;
    top: $spacing-sm;
    right: $spacing-sm;

    &:hover {
      color: var(--theme-neutral-600);
    }
  }
}

// ===================================
// SCROLLBAR STYLING
// ===================================

/* For WebKit browsers */
::-webkit-scrollbar {
  height: 12px;
  width: 14px;
  background: var(--theme-primary);
  opacity: 0.2;
  z-index: 12;
  overflow: visible;
}

::-webkit-scrollbar-thumb {
  width: 10px;
  background-color: var(--theme-primary);
  border-radius: 10px;
  z-index: 12;
  border: 4px solid rgba(0, 0, 0, 0);
  background-clip: padding-box;
  -webkit-transition: background-color 0.28s ease-in-out;
  transition: background-color 0.28s ease-in-out;
  margin: 4px;
  min-height: 32px;
  min-width: 32px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--theme-primary-light);
}

/* For Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--theme-primary) transparent;
}

// ===================================
// INPUT AUTOFILL STYLING
// ===================================

input:-webkit-autofill,
input:-webkit-autofill:focus {
  background-color: var(--theme-background-primary) !important;
  -webkit-box-shadow: 0 0 0 1000px var(--theme-background-primary) inset !important;
  -webkit-text-fill-color: var(--theme-text-primary) !important;
}

// ===================================
// UTILITY CLASSES
// ===================================

.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}

.full-width {
  width: 100%;
}
.full-height {
  height: 100%;
}

// Spacing utilities
.q-mt-xs {
  margin-top: $spacing-xs;
}
.q-mt-sm {
  margin-top: $spacing-sm;
}
.q-mt-md {
  margin-top: $spacing-md;
}
.q-mt-lg {
  margin-top: $spacing-lg;
}
.q-mt-xl {
  margin-top: $spacing-xl;
}

.q-mb-xs {
  margin-bottom: $spacing-xs;
}
.q-mb-sm {
  margin-bottom: $spacing-sm;
}
.q-mb-md {
  margin-bottom: $spacing-md;
}
.q-mb-lg {
  margin-bottom: $spacing-lg;
}
.q-mb-xl {
  margin-bottom: $spacing-xl;
}

.q-pa-xs {
  padding: $spacing-xs;
}
.q-pa-sm {
  padding: $spacing-sm;
}
.q-pa-md {
  padding: $spacing-md;
}
.q-pa-lg {
  padding: $spacing-lg;
}
.q-pa-xl {
  padding: $spacing-xl;
}

// ===================================
// QUASAR THEME CUSTOMIZATION
// ===================================

:root {
  --q-primary: var(--theme-primary);
  --q-secondary: var(--theme-secondary-purple);
  --q-accent: var(--theme-primary-accent);
  --q-positive: #21ba45;
  --q-negative: #c10015;
  --q-info: var(--theme-primary-light);
  --q-warning: #f2c037;
  --q-dark: var(--theme-neutral-900);
  --q-dark-page: var(--theme-neutral-800);
}
