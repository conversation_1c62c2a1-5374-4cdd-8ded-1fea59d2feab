// Global premium styles for the application

@mixin mobile {
  @media (max-width: #{$breakpoint-sm - 1px}) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: #{$breakpoint-sm}) and (max-width: #{$breakpoint-md - 1px}) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: #{$breakpoint-md}) {
    @content;
  }
}

.bg-custom {
  background: linear-gradient(
    135deg,
    $primary,
    darken($primary, 15%)
  ) !important;
}

// Premium buttons

.premium-btn-danger {
  background: linear-gradient(135deg, $negative, darken($negative, 15%));
  color: white;
  border-radius: 12px;
  height: 48px;
  font-weight: 500;
  box-shadow: 0 4px 15px rgba($negative, 0.25);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba($negative, 0.35);
  }

  .q-icon {
    margin-right: 8px;
  }
}

.premium-btn-warning {
  background: linear-gradient(135deg, $warning, darken($warning, 15%));
  color: white;
  border-radius: 12px;
  height: 48px;
  font-weight: 500;
  box-shadow: 0 4px 15px rgba($warning, 0.25);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba($warning, 0.35);
  }

  .q-icon {
    margin-right: 8px;
  }
}

.premium-btn-secondary {
  background: linear-gradient(135deg, #8060cf, darken(#8060cf, 15%));
  color: white;
  border-radius: 12px;
  height: 48px;
  font-weight: 500;
  box-shadow: 0 4px 15px rgba(#8060cf, 0.25);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(#8060cf, 0.35);
  }

  .q-icon {
    margin-right: 8px;
  }
}

.premium-btn-primary {
  background: linear-gradient(135deg, $primary, darken($primary, 15%));
  color: white;
  border-radius: 12px;
  height: 48px;
  font-weight: 500;
  box-shadow: 0 4px 15px rgba($primary, 0.25);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba($primary, 0.35);
  }

  .q-icon {
    margin-right: 8px;
  }
}

.premium-btn-success {
  background: linear-gradient(135deg, $positive, darken($positive, 15%));
  color: white;
  border-radius: 10px;
  padding: 0 20px;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba($positive, 0.25);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  height: 42px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba($positive, 0.35);
  }
}

// Action buttons
.input-action-btn {
  color: $primary;
  background-color: rgba($primary, 0.12);
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

  &:hover {
    background-color: rgba($primary, 0.2);
    transform: scale(1.05);
  }
}

// Custom scrollbar
.custom-scroll-area {
  &::-webkit-scrollbar {
    width: 10px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.03);
    border-radius: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba($primary, 0.25);
    border-radius: 6px;
    border: 2px solid transparent;
    background-clip: padding-box;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba($primary, 0.4);
    border: 2px solid transparent;
    background-clip: padding-box;
  }
}

.premium-card {
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.07);
  border: none;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

  &:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.09);
    // transform: translateY(-2px);
  }

  .card-header {
    padding: 20px 24px;
    background: linear-gradient(
      135deg,
      rgba($primary, 0.08),
      rgba($primary, 0.15)
    );
    border-bottom: 1px solid rgba($primary, 0.1);

    .card-title {
      font-weight: 600;
      color: $primary;
      font-size: 1.2rem;
      letter-spacing: -0.01em;
    }
  }

  .card-content {
    padding: 24px;
  }
}

.modern-input {
  .q-field__control {
    border-radius: 10px;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  }
}

.ai-action-btn {
  color: $primary;
  background-color: rgba($primary, 0.12);
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

  &:hover {
    background-color: rgba($primary, 0.2);
    transform: scale(1.05);
  }

  .q-spinner {
    color: $primary;
  }
}

.premium-btn {
  background: linear-gradient(135deg, $primary, darken($primary, 15%));
  color: white;
  border-radius: 10px;
  padding: 0 20px;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba($primary, 0.25);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  height: 42px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba($primary, 0.35);
  }
}
