//
// variables.scss
//

// fonts
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@200;300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700;900&display=swap');

//color
$success: #14c5a2;
$info: #49c6e5;
$warning: #ffcd00;
$danger: #c71f1f;
$white: #ffffff;
$dark: #1c2f50;
$light: #fbfbff;
$muted: #979797;
$black: #000000;
$purple: #8060cf;
$cyan: #00e6e6;
$blue: #2558ab;
$pink: #b6317a;

// stylelint-disable
$gray-100: #f8f9fa;
$gray-200: #e9ecef;
$gray-300: #dee2e6;
$gray-400: #ced4da;
$gray-500: #adb5bd;
$gray-600: #6c757d;
$gray-700: #495057;
$gray-800: #343a40;
$gray-900: #212529;

$gray-dark-100: #242c34;
$gray-dark-200: #283039;
$gray-dark-300: #303841;
$gray-dark-400: #3d454e;
$gray-dark-500: #63686f;
$gray-dark-600: #767b81;
$gray-dark-700: #ced1d2;
$gray-dark-800: #cfd1d2;
$gray-dark-900: #d4d5d7;

$colors: (
  'primary': $primary,
  'secondary': $secondary,
  'success': $success,
  'info': $info,
  'warning': $warning,
  'danger': $danger,
  'dark': $dark,
  'cyan': $cyan,
  'blue': $blue,
  'muted': $muted,
  'purple': $purple,
  'light': $light,
  'white': $white,
  'pink': $pink,
);

$theme-colors: (
  'primary': $primary,
  'secondary': $secondary,
  'success': $success,
  'info': $info,
  'warning': $warning,
  'danger': $danger,
  'light': $light,
  'dark': $dark,
  'muted': $muted,
  'white': $white,
);

//box-shadow
$box-shadow: 0px 3px 10px 0px rgba($dark, 0.08);

$box-shadow-md: 0 0 3px rgba(60, 72, 88, 0.15);

$box-shadow-lg: 0 5px 13px rgb(60 72 88 / 20%);

$body-dark-bg: #303841;
$body-dark-bg-light: #343f4b;

// font weight

$fw-semibold: 500;
$fw-bold: 700;

// font-family
$font-family-base: 'Poppins', sans-serif;
$font-family-secondary: 'Roboto', sans-serif;

$muted-light: rgba($blue, 0.05);

$footer: $dark;
