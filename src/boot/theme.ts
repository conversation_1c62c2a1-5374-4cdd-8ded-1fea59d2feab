// src/boot/theme.ts
import { setCssVar } from 'quasar';
import { boot } from 'quasar/wrappers';
import {
  type ThemeMode,
  type PaletteName,
  type ThemeConfig,
  themePalettes,
  getThemeConfig,
} from 'src/config/theme';

// Theme management state
let currentThemeConfig: ThemeConfig | null = null;

// Enhanced theme application using Quasar's setCssVar
export const applyQuasarTheme = (config: ThemeConfig): void => {
  const { colors } = config;
  const selectedPalette = themePalettes[config.palette];
  // Apply background with palette-specific backgrounds
  const backgroundImage =
    config.mode === 'dark'
      ? selectedPalette.backgroundImage?.dark || colors.bodyBackground
      : selectedPalette.backgroundImage?.light || colors.bodyBackground;

  // Apply core Quasar color variables
  setCssVar('primary', colors.primary);
  setCssVar('secondary', colors.secondary);
  setCssVar('accent', colors.accent);
  setCssVar('dark', colors.dark);
  setCssVar('dark-page', colors['dark-page']);
  setCssVar('positive', colors.positive);
  setCssVar('negative', colors.negative);
  setCssVar('info', colors.info);
  setCssVar('warning', colors.warning);
  setCssVar('defined-bg', backgroundImage);
  if (colors.textColor) setCssVar('text-color', colors.textColor);
  else
    setCssVar('text-color', config.mode === 'dark' ? '#fff' : colors.primary);
  // Apply additional theme-specific CSS custom properties
  const root = document.documentElement;
  Object.entries(colors).forEach(([key, value]) => {
    const cssVar = key.replace(/([A-Z])/g, '-$1').toLowerCase();
    root.style.setProperty(`--theme-${cssVar}`, value);
  });

  if (backgroundImage.includes('url') || backgroundImage.includes('gradient')) {
    document.body.style.backgroundImage = backgroundImage;
    document.body.style.backgroundColor = 'transparent';
    document.body.style.backgroundAttachment = 'fixed';
    document.body.style.backgroundSize = 'cover';
    document.body.style.backgroundRepeat = 'no-repeat';
    document.body.style.backgroundPosition = 'center center';
  } else {
    document.body.style.backgroundImage = 'none';
    document.body.style.background = backgroundImage;
  }

  // Add theme and palette classes to body for CSS targeting
  document.body.classList.remove('theme-light', 'theme-dark');
  document.body.classList.add(`theme-${config.mode}`);

  // Add palette class
  document.body.classList.remove(
    ...Object.keys(themePalettes).map((p) => `palette-${p}`),
  );
  document.body.classList.add(`palette-${config.palette}`);

  // Add smooth transition class
  document.body.classList.add('theme-transition');

  // Update current config
  currentThemeConfig = config;
};

// Initialize theme system
export const initializeTheme = (
  mode: ThemeMode = 'light',
  palette: PaletteName = 'default',
): ThemeConfig => {
  const config = getThemeConfig(mode, palette);
  applyQuasarTheme(config);
  return config;
};

// Toggle theme mode
export const toggleThemeMode = (): ThemeConfig => {
  if (!currentThemeConfig) {
    return initializeTheme();
  }

  // Add transitioning class to prevent animations during theme switch
  document.body.classList.add('theme-transitioning');

  const newMode: ThemeMode =
    currentThemeConfig.mode === 'dark' ? 'light' : 'dark';
  const newConfig = getThemeConfig(newMode, currentThemeConfig.palette);

  applyQuasarTheme(newConfig);

  // Store preferences
  localStorage.setItem('theme-preference', newMode);
  localStorage.setItem('palette-preference', currentThemeConfig.palette);

  // Remove transitioning class after theme is applied
  setTimeout(() => {
    document.body.classList.remove('theme-transitioning');
  }, 50);

  return newConfig;
};

// Change color palette
export const changeThemePalette = (paletteName: PaletteName): ThemeConfig => {
  if (!currentThemeConfig) {
    return initializeTheme('dark', paletteName);
  }

  document.body.classList.add('theme-transitioning');

  const newConfig = getThemeConfig(currentThemeConfig.mode, paletteName);

  applyQuasarTheme(newConfig);

  // Store preference
  localStorage.setItem('palette-preference', paletteName);

  // Remove transitioning class
  setTimeout(() => {
    document.body.classList.remove('theme-transitioning');
  }, 50);

  return newConfig;
};

// Get current theme configuration
export const getCurrentThemeConfig = (): ThemeConfig | null => {
  return currentThemeConfig;
};

// Watch for system theme changes
export const watchSystemTheme = (
  callback?: (isDark: boolean) => void,
): (() => void) => {
  if (typeof window === 'undefined') return () => {};

  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

  const handleChange = (e: MediaQueryListEvent) => {
    if (currentThemeConfig?.mode === 'auto') {
      const newMode: ThemeMode = e.matches ? 'dark' : 'light';
      const newConfig = getThemeConfig(newMode, currentThemeConfig.palette);
      applyQuasarTheme(newConfig);
    }
    callback?.(e.matches);
  };

  mediaQuery.addEventListener('change', handleChange);

  // Return cleanup function
  return () => {
    mediaQuery.removeEventListener('change', handleChange);
  };
};

export default boot(({ app }) => {
  // Get stored preferences or defaults
  const storedTheme =
    (localStorage.getItem('theme-preference') as ThemeMode) || 'light';
  const storedPalette =
    (localStorage.getItem('palette-preference') as PaletteName) || 'default';

  // Initialize theme system
  const config = initializeTheme(storedTheme, storedPalette);

  // Set Quasar dark mode
  app.config.globalProperties.$q.dark.set(storedTheme === 'dark');

  // Watch for system theme changes if in auto mode
  if (storedTheme === 'auto') {
    watchSystemTheme((isDark) => {
      app.config.globalProperties.$q.dark.set(isDark);
    });
  }

  // Make theme functions globally available
  app.config.globalProperties.$theme = {
    apply: applyQuasarTheme,
    initialize: initializeTheme,
    toggleMode: toggleThemeMode,
    changePalette: changeThemePalette,
    getCurrent: getCurrentThemeConfig,
    watchSystem: watchSystemTheme,
  };

  console.log('Theme system initialized with:', config);
});

// Type declaration for global properties
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $theme: {
      apply: typeof applyQuasarTheme;
      initialize: typeof initializeTheme;
      toggleMode: typeof toggleThemeMode;
      changePalette: typeof changeThemePalette;
      getCurrent: typeof getCurrentThemeConfig;
      watchSystem: typeof watchSystemTheme;
    };
  }
}
