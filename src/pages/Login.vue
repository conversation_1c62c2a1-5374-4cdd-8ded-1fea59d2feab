<template>
  <q-page
    :padding="!$q.screen.lt.sm"
    class="flex content-center"
    :class="{
      'bg-white': $q.screen.lt.sm, // Full width on xs and sm
    }"
  >
    <q-card
      class="q-mx-auto"
      :class="{
        'full-width': $q.screen.lt.sm, // Full width on xs and sm
      }"
    >
      <q-card-section horizontal class="q-pa-xl q-pa-sm q-pa-xs q-pb-none">
        <q-img src="logo.png" min-width="320px" />
      </q-card-section>
      <q-card-section
        v-if="signUpTitle !== 'login'"
        class="bg-primary text-white text-center q-pa-sm"
      >
        <div class="text-overline title">{{ signUpTitle }}</div>
      </q-card-section>

      <q-separator />
      <q-card-section v-if="messageLogin !== ''" class="q-pa-none">
        <q-banner
          inline-actions
          dense
          rounded
          class="text-red text-center absolute-top"
        >
          <q-icon name="error" /> {{ messageLogin }}
        </q-banner>
      </q-card-section>
      <div v-if="view == 'login'">
        <form @submit.prevent="handleLogIn">
          <q-card-section class="q-pa-xl q-pa-sm q-pa-xs">
            <q-input
              v-model="email"
              label="Email"
              type="email"
              class="q-mb-md"
              :rules="[validateRequired, validateEmail]"
              @keyup.enter="handleLogIn"
            />
            <q-input
              v-model="password"
              label="Password"
              :type="showPass ? 'password' : 'text'"
              class="q-mb-md"
              :rules="[validateRequired]"
              @keyup.enter="handleLogIn"
            >
              <template v-slot:append>
                <q-btn
                  :icon="showPass ? 'visibility_off' : 'visibility'"
                  flat
                  rounded
                  color="black"
                  v-if="password.length"
                  @click="showPass = !showPass"
                  dense
                />
              </template>
            </q-input>

            <q-toggle v-model="rememberMe" label="Remember Me" />

            <q-card-actions
              align="center"
              class="bg-primary q-mt-sm sm-hide md-hide lg-hide xl-hide"
            >
              <q-btn
                type="submit"
                :disabled="!canLogin"
                :loading="isLoading"
                text-color="white"
                label="Log In"
                flat
                icon="key"
                class="full-width"
              />
            </q-card-actions>

            <!-- Centered OR -->
            <div class="separator-container">
              <div class="line"></div>
              <span class="or-text">OR</span>
              <div class="line"></div>
            </div>

            <q-card-actions align="center" class="bg-primary">
              <q-btn
                @click="signInWithGoogle"
                text-color="white"
                label="Sign in with Google"
                class="full-width"
                flat
                icon="fa-brands fa-google"
              />
            </q-card-actions>
          </q-card-section>
          <q-separator />
          <q-card-section class="flex justify-evenly q-pa-none">
            <div class="bg-primary q-pa-sm submit-btn xs-hide">
              <q-btn
                type="submit"
                :disabled="!canLogin"
                :loading="isLoading"
                text-color="white"
                label="Log In"
                flat
                icon="key"
                class="full-width"
              />
            </div>
            <div class="q-pa-sm">
              <q-btn
                @click="view = 'signup'"
                flat
                label="Sign Up"
                icon="app_registration"
                :disabled="isLoading"
              />
              <q-btn
                @click="view = 'reset'"
                flat
                label="Reset Password"
                icon="password"
                :disabled="isLoading"
              />
            </div>
          </q-card-section>
        </form>
      </div>
      <div v-if="view == 'signup'" id="signup-form">
        <form @submit.prevent="createUser">
          <q-card-section class="q-pa-xl q-pa-sm q-pa-xs">
            <q-input
              v-model="email"
              label="Email"
              type="email"
              class="q-mb-md"
              :rules="[validateRequired, validateEmail]"
            />
            <q-input
              v-model="name"
              label="Name"
              type="text"
              class="q-mb-md"
              :rules="[validateRequired]"
            />

            <q-card-actions
              align="center"
              class="bg-primary sm-hide md-hide lg-hide xl-hide"
            >
              <q-btn
                text-color="white"
                label="Sign Up"
                icon="app_registration"
                type="submit"
                flat
                :disabled="!canCreateUser"
                :loading="isLoading"
                class="full-width"
              />
            </q-card-actions>

            <!-- Centered OR -->
            <div class="separator-container">
              <div class="line"></div>
              <span class="or-text">OR</span>
              <div class="line"></div>
            </div>

            <q-card-actions align="center" class="bg-primary">
              <q-btn
                @click="signInWithGoogle"
                text-color="white"
                label="Sign up with Google"
                class="full-width"
                flat
                icon="fa-brands fa-google"
              />
            </q-card-actions>
          </q-card-section>

          <q-separator />
          <q-card-section class="flex justify-evenly q-pa-none">
            <div class="bg-primary q-pa-sm submit-btn xs-hide">
              <q-btn
                type="submit"
                flat
                :disabled="!canCreateUser"
                :loading="isLoading"
                text-color="white"
                label="Sign Up"
                icon="app_registration"
                class="full-width"
              />
            </div>
            <div class="q-pa-sm">
              <q-btn
                @click="view = 'login'"
                flat
                label="Log In"
                icon="key"
                :disabled="isLoading"
              />
              <q-btn
                @click="view = 'reset'"
                flat
                label="Reset Password"
                icon="password"
                :disabled="isLoading"
              />
            </div>
          </q-card-section>
        </form>
      </div>
      <div v-if="view == 'reset'" id="reset-form">
        <form @submit.prevent="resetPassword">
          <q-card-section class="q-pa-xl q-pa-sm q-pa-xs">
            <q-input
              v-model="email"
              label="Email"
              type="email"
              :rules="[validateRequired, validateEmail]"
            />

            <q-card-actions
              align="center"
              class="bg-primary sm-hide md-hide lg-hide xl-hide"
            >
              <q-btn
                type="submit"
                :disabled="!canResetPassword"
                :loading="isLoading"
                text-color="white"
                label="Reset Password"
                icon="password"
                flat
                class="full-width"
              />
            </q-card-actions>
          </q-card-section>
          <q-separator />
          <q-card-section class="flex justify-evenly q-pa-none">
            <div class="bg-primary q-pa-sm submit-btn xs-hide">
              <q-btn
                type="submit"
                :disabled="!canResetPassword"
                :loading="isLoading"
                text-color="white"
                label="Reset Password"
                icon="password"
                flat
                class="full-width"
              />
            </div>
            <div class="q-pa-sm">
              <q-btn
                @click="view = 'login'"
                flat
                label="Log In"
                icon="key"
                :disabled="isLoading"
              />
              <q-btn
                @click="view = 'signup'"
                flat
                label="Sign Up"
                icon="app_registration"
                :disabled="isLoading"
              />
            </div>
          </q-card-section>
        </form>
      </div>
    </q-card>
  </q-page>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { auth, functions, siteURL } from 'src/firebase';
import {
  signInWithEmailAndPassword,
  sendPasswordResetEmail,
  signInWithPopup,
  GoogleAuthProvider,
} from 'firebase/auth';
import { httpsCallable } from 'firebase/functions';
import { isAdmin, user } from 'src/entities/user';
import { Cookies, useQuasar } from 'quasar';
import { UserCredential } from '@firebase/auth';

// Reactive state
const view = ref('login');
const messageLogin = ref('');
const name = ref('');
const userRole = ref('');
const showPass = ref(true);
const isLoading = ref(false);
const email = ref('');
const password = ref('');
const rememberMe = ref(false);
const $q = useQuasar();

const validateRequired = (val: string) =>
  val.length === 0 ? 'This field is required.' : true;
const validateEmail = (val: string) =>
  /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val) ? true : 'Please enter a valid email.';

// Computed properties
const signUpTitle = computed(() => {
  messageLogin.value = '';
  return view.value;
});
const canLogin = computed(() => !invalidLogin());
const canCreateUser = computed(() => !invalidSignUp());
const canResetPassword = computed(() => !invalidResetPassword());

// route, and router
const route = useRoute();
const router = useRouter();

// Methods
const invalidLogin = () => {
  return (
    validateRequired(password.value) !== true ||
    validateRequired(email.value) !== true ||
    validateEmail(email.value) !== true
  );
};
const invalidSignUp = () => {
  return (
    validateRequired(name.value) !== true ||
    validateRequired(email.value) !== true ||
    validateEmail(email.value) !== true
  );
};
const invalidResetPassword = () => {
  return (
    validateRequired(email.value) !== true ||
    validateEmail(email.value) !== true
  );
};

const handleLogIn = () => {
  if (!canLogin.value) return;
  messageLogin.value = '';
  isLoading.value = true;
  signInWithEmailAndPassword(auth, email.value, password.value)
    .then(async (result) => {
      if (rememberMe.value) {
        Cookies.set('email', email.value, { expires: 30 });
        Cookies.set('password', password.value, { expires: 30 });
        Cookies.set('rememberMe', true, { expires: 30 });
      } else {
        Cookies.remove('email');
        Cookies.remove('password');
        Cookies.remove('rememberMe');
      }
      messageLogin.value = '';
      await checkIfNewUser(result);
    })
    .catch((error) => {
      isLoading.value = false;
      let message = '';
      if (
        [
          'auth/wrong-password',
          'auth/user-not-found',
          'auth/invalid-email',
        ].includes(error.code)
      ) {
        message = 'You have entered an invalid email or password.';
      } else if (error.code === 'auth/too-many-requests') {
        message =
          'Too many failed login attempts. You can immediately restore it by resetting your password or you can try again later.';
      } else {
        message = 'An error has occurred. Please try again.';
      }
      messageLogin.value = message;
    });
};

const signInWithGoogle = () => {
  messageLogin.value = '';
  const provider = new GoogleAuthProvider();
  signInWithPopup(auth, provider)
    .then(async (result) => {
      const credential = GoogleAuthProvider.credentialFromResult(result);
      const token = credential?.accessToken;
      const user = result.user;
      messageLogin.value = '';
      await checkIfNewUser(result);
    })
    .catch((error) => {
      const errorCode = error.code;
      const errorMessage = error.message;
      const email = error.email;
      const credential = GoogleAuthProvider.credentialFromError(error);
      if (user.value) {
        messageLogin.value = '';
        router.push('/');
      }
      messageLogin.value = 'An error has occurred. Please try again.';
    });
};

const createUser = () => {
  if (!canCreateUser.value) return;
  messageLogin.value = '';
  isLoading.value = true;
  const defaultUser = {
    name: name.value,
    email: email.value,
    password: 'P@ssw0rd',
    role: isAdmin.value ? userRole.value : '',
    isPaused: false,
    features: [],
    settings: {},
    assignedAdminId: [],
    messages: [],
    templateId: '043rMnzq5pYLqTdbAYcX',
  };
  const createUser = httpsCallable(functions, 'createUser');
  createUser(defaultUser)
    .then((response) => {
      if (response.data.errorInfo) {
        console.error('Error: ', response.data.errorInfo.message);
        isLoading.value = false;
        messageLogin.value = response.data.errorInfo.message;
        return;
      }
      $q.dialog({
        title: 'Sign-Up Successful!',
        html: true,
        message: `<p>Thank you for signing up! Your account has been successfully created.</p>

        <p><strong>Next Steps:</strong></p>
        <ol class="steps">
            <li>Check your inbox for a confirmation email.</li>
            <li>Follow the instructions in the email to verify your account and get started.</li>
        </ol>

        <p class="text-bold">If you don’t see the email, please check your spam or promotions folder.</p>`,
        ok: {
          color: 'primary',
        },
      });
    })
    .catch((error) => {
      const message = error.message;
      messageLogin.value = message;
      isLoading.value = false;
    })
    .finally(() => {
      isLoading.value = false;
    });
};

const resetPassword = () => {
  if (!canResetPassword.value) return;
  messageLogin.value = '';
  isLoading.value = true;
  const actionCodeSettings = {
    url: siteURL,
    handleCodeInApp: false,
  };
  sendPasswordResetEmail(auth, email.value, actionCodeSettings)
    .then(() => {
      $q.dialog({
        title: 'Email sent.',
        message: 'Follow the link in the email to reset your password.',
        ok: {
          color: 'primary',
        },
      });
      messageLogin.value = '';
      isLoading.value = false;
    })
    .catch((error) => {
      console.error(error);
      let message = '';
      if (['auth/user-not-found', 'auth/invalid-email'].includes(error.code)) {
        message = 'You have entered an invalid email. Please try again.';
      } else {
        message = error.message;
      }
      messageLogin.value = message;
      isLoading.value = false;
    });
};

const checkIfNewUser = async (result: UserCredential) => {
  await router.push('/');
};

// Lifecycle hook
onMounted(() => {
  if (!user.value) view.value = 'login';
  else router.push('/');
  const savedEmail = Cookies.get('email');
  const savedPassword = Cookies.get('password');
  const savedRememberMe = Cookies.get('rememberMe') === 'true';

  if (savedRememberMe) {
    email.value = savedEmail || '';
    password.value = savedPassword || '';
    rememberMe.value = savedRememberMe;
  }
});
</script>

<style lang="scss" scoped>
.title {
  text-transform: uppercase;
}
.separator-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
}

.line {
  flex-grow: 1;
  height: 1px;
  background-color: $gray-500; /* Change the color to your desired line color */
  margin: 0 8px;
}

.or-text {
  margin: 0 8px;
  font-weight: bold;
}

.submit-btn {
  min-width: calc(30vw - 350px);
}
</style>
