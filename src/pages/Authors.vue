<template>
  <div id="authors" class="container q-pa-lg q-mx-auto">
    <div id="title" class="q-mb-lg">Authors</div>
    <q-table
      :rows="users"
      :columns="columns"
      row-key="name"
      :pagination="pagination"
      class="q-mb-lg"
      :filter="filter"
    >
      <template v-slot:top-left>
        <q-input
          borderless
          dense
          debounce="300"
          v-model="filter"
          placeholder="Search"
        >
          <template v-slot:prepend>
            <q-icon name="search" />
          </template>
        </q-input>
      </template>
      <template v-slot:header-cell-unresolvedComments="props">
        <q-th :props="props" class="table-label">{{ props.col.label }}</q-th>
      </template>
      <template v-slot:body="props">
        <q-tr
          :props="props"
          @click="reviewModeCheck(props.row)"
          :class="{ 'cursor-pointer': !inReviewMode && props.row.id != userId }"
        >
          <q-td key="name" :props="props">{{ props.row.name }}</q-td>
          <q-td key="email" :props="props">{{ props.row.email }}</q-td>
          <q-td key="paypal" :props="props">
            <q-btn
              @click.stop="markPaypalPayment(props.row.id, props.row.email)"
              icon="img:https://cdn-icons-png.flaticon.com/512/174/174861.png"
            />
          </q-td>
          <q-td key="role" :props="props" @click.stop>
            <q-select
              :modelValue="props.row.role"
              :options="roleOptions"
              emit-value
              map-options
              borderless
              @click.native="currentRole = props.row.role"
              @update:model-value="changeRole(props.row, $event)"
            ></q-select>
          </q-td>
          <q-td key="features" :props="props" @click.stop>
            <q-select
              v-model="props.row.features"
              :options="featureOptions"
              emit-value
              map-options
              borderless
              multiple
              @update:model-value="
                updateUserProp('features', props.row.features, props.row)
              "
            ></q-select>
          </q-td>
          <q-td key="messages" :props="props" @click.stop>
            <q-btn
              round
              flat
              icon="comment"
              @click.stop="reviewModeCheck(props.row, true)"
            >
              <q-badge v-if="unreadMessages.length" color="negative" floating>{{
                unreadMessages.length
              }}</q-badge>
            </q-btn>
          </q-td>
          <q-td key="assignedAdminId" :props="props" @click.stop>
            <q-select
              clearable
              v-model="props.row.assignedAdminId"
              :options="adminOptions"
              use-input
              input-debounce="0"
              label="Select Reviewer"
              @filter="filterAdmins"
              @popup-hide="getAllUsers"
              emit-value
              map-options
              borderless
              @update:model-value="
                updateUserProp(
                  'assignedAdminId',
                  props.row.assignedAdminId,
                  props.row,
                )
              "
            ></q-select>
          </q-td>
          <q-td key="password" :props="props" @click.stop>
            <q-icon name="password" size="24px">
              <q-popup-edit ref="passEdit" v-model="newPass">
                <q-input v-model="newPass" bottom-slots dense autofocus>
                  <template v-slot:hint> Minimum 6 characters </template>
                </q-input>
                <q-btn
                  label="Save"
                  color="primary"
                  @click="updateUserPass(props.row)"
                  class="q-mt-md"
                  :disabled="newPass.length < 6"
                />
              </q-popup-edit>
            </q-icon>
          </q-td>
          <q-td key="paused" :props="props" @click.stop>
            <q-checkbox
              v-model="props.row.isPaused"
              @input="pauseUser(props.row)"
              :disable="props.row.role == 'admin'"
            />
          </q-td>
          <q-td key="delete" :props="props" @click.stop>
            <q-icon
              class="delete-author"
              name="delete"
              color="negative"
              @click="deleteAuthor(props.row)"
            />
          </q-td>
        </q-tr>
      </template>
    </q-table>
    <q-btn
      v-if="!inReviewMode"
      label="Add Author"
      icon="person_add"
      color="primary"
      to="/login"
    />
  </div>
</template>

<script>
import { defineComponent, ref } from 'vue';
import _ from 'lodash';
import mixins from 'src/app/mixins/index.js';
import { auth, functions } from 'src/firebase';
import { signInWithEmailAndPassword } from 'firebase/auth';
import { httpsCallable } from 'firebase/functions';
import { confirmDeletion, showError } from 'src/shared/lib/quasar-dialogs';
import { isAdmin } from 'src/entities/user';
import {
  setPaypalPaymentStatus,
  setUserAdminStatus,
} from 'src/shared/api/firebase-functions';

import draggable from 'vuedraggable';

export default defineComponent({
  name: 'Authors',
  components: {
    draggable,
  },
  mixins: mixins,
  setup() {
    return {
      adminOptions: ref([]),
    };
  },
  data() {
    return {
      currentRole: 'author',
      newPass: '',
      columns: [
        {
          name: 'name',
          required: true,
          label: 'Name',
          align: 'left',
          field: (row) => row.name,
          sortable: true,
        },
        {
          name: 'email',
          required: true,
          label: 'Email',
          align: 'left',
          field: (row) => row.email,
          sortable: true,
        },
        {
          name: 'paypal',
          label: 'Mark as paid via Paypal',
          sortable: false,
        },
        {
          name: 'role',
          required: true,
          label: 'Role',
          align: 'left',
          field: (row) => {
            return row.role;
          },
          sortable: true,
        },
        {
          name: 'features',
          required: true,
          label: 'Features',
          align: 'left',
          field: (row) => {
            return row.features;
          },
          sortable: false,
        },
        {
          name: 'messages',
          required: true,
          label: 'Messages',
          align: 'left',
          sortable: true,
        },
        {
          name: 'assignedAdminId',
          required: true,
          label: 'Reviewer',
          align: 'left',
          field: (row) => {
            return row.assignedAdminId;
          },
          sortable: true,
        },
        {
          name: 'password',
          required: true,
          label: 'Password',
          align: 'left',
          sortable: true,
        },
        {
          name: 'paused',
          required: true,
          label: 'Paused',
          align: 'left',
          sortable: true,
        },
        {
          name: 'delete',
          required: true,
          label: 'Delete',
          align: 'left',
          sortable: false,
        },
      ],
      pagination: {
        sortBy: 'name',
        descending: false,
        page: 1,
        rowsPerPage: 20,
      },
      filter: '',
    };
  },
  computed: {
    templateOptions: function () {
      let options = _.map(this.templates, function (template) {
        return { value: template.id, label: template.name };
      });
      return _.sortBy(options, ['label']);
    },
  },
  methods: {
    getAllUsers: function () {
      let adminOptions = [{ label: '', value: '' }];

      _.each(this.users, (user) => {
        if (user.email) {
          adminOptions.push({
            label: user.email,
            value: user.id,
          });
        }
      });
      this.adminOptions = adminOptions;
    },
    filterAdmins(val, update) {
      if (val === '') {
        update(() => {
          this.getAllUsers();

          // here you have access to "ref" which
          // is the Vue reference of the QSelect
        });
        return;
      }

      update(() => {
        const needle = val.toLowerCase();
        this.adminOptions = this.adminOptions.filter((v) => {
          return v.label.toLowerCase().indexOf(needle) > -1;
        });
        console.log(this.adminOptions);
      });
    },
    getUnresolved: function (comments) {
      comments = _.filter(comments, { isResolved: false });
      return comments.length;
    },
    getNameFromId: function (id) {
      let user = _.find(this.users, ['id', id]);
      return user && user.name ? user.name : '';
    },
    reviewModeCheck: function (author, navToMessages = false) {
      if (navToMessages && author.id == this.userId)
        this.$router.push('/messages');

      if (this.inReviewMode || author.id == this.userId) return;

      this.$q
        .dialog({
          title: 'Review Mode',
          message: 'Click OK to enter review mode for ' + author.name + '.',
          ok: {
            color: 'primary',
          },
          cancel: {
            color: 'negative',
          },
          persistent: true,
        })
        .onOk(() => {
          this.$store.commit('user/toggleReviewMode', {
            boolean: true,
            author: author,
          });
          this.$nextTick(() => {
            EventBus.$emit('enter-review');
            if (navToMessages) this.$router.push('/messages');
          });
        })
        .onCancel(() => {});
    },
    changeRole: function (author, role) {
      if (!isAdmin.value) return;
      this.$q
        .dialog({
          title: 'Are you sure?',
          message:
            'Click OK to change role for ' + author.name + ' to ' + role + '.',
          ok: {
            color: 'primary',
          },
          cancel: {
            color: 'negative',
          },
          persistent: true,
        })
        .onOk(() => {
          setUserAdminStatus({ uid: author.id, isAdmin: role === 'admin' })
            .then(() => {
              author.role = role;
            })
            .catch((error) => {
              console.log('Error setting user role: ', error);
              showError(this.$q, JSON.stringify(error));
            });
        })
        .onCancel(() => {
          let index = _.findIndex(this.users, ['id', author.id]);
          this.users[index].role = this.currentRole;
          this.currentRole = 'author';
        });
    },
    markPaypalPayment: async function (id, email) {
      this.$q
        .dialog({
          title: 'Mark as paid via Paypal',
          message: `Are you sure you want to mark the user "${email}" as having paid for Manuscriptr using Paypal?`,
          ok: {
            color: 'primary',
          },
          cancel: {
            color: 'negative',
          },
        })
        .onOk(async () => {
          try {
            await setPaypalPaymentStatus({
              uid: id,
              paidViaPaypal: true,
            });
            this.$q.notify('Marked successfully!');
          } catch (e) {
            alert('An error occured: ' + JSON.stringify(e));
          }
        });
    },
    updateUserPass: function (author) {
      if (!isAdmin.value) return;
      this.$q
        .dialog({
          title: 'Are you sure?',
          message: 'Click OK to change password for ' + author.name + '.',
          ok: {
            color: 'primary',
          },
          cancel: {
            color: 'negative',
          },
          persistent: true,
        })
        .onOk(() => {
          let updateUserPass = httpsCallable(functions, 'updateUserPass');

          let data = { id: author.id, password: this.newPass };

          updateUserPass(data)
            .then((response) => {
              console.log('Update user pass: ', response);

              this.$refs.passEdit.hide();

              this.$q
                .dialog({
                  title: 'Success',
                  message: 'Password successfully changed.',
                  ok: {
                    color: 'primary',
                  },
                })
                .onOk(() => {});

              if (author.id == this.userId) {
                signInWithEmailAndPassword(auth, author.email, this.newPass);
              }
            })
            .catch((error) => {
              console.log('Error: ', error.code, error.message);
              showError(this.$q, error.message);
            });
        })
        .onCancel(() => {
          this.$refs.passEdit.hide();
        });
    },
    pauseUser: function (user) {
      this.$q
        .dialog({
          title: 'Are you sure?',
          message:
            'Pausing a user will immediately sign them out and prevent further access.',
          ok: {
            color: 'primary',
          },
          cancel: {
            color: 'negative',
          },
        })
        .onOk(() => {
          this.updateUserProp('isPaused', user.isPaused, user);
        })
        .onCancel(() => {
          let index = _.findIndex(this.users, ['id', user.id]);
          this.users[index].isPaused = !user.isPaused;
        });
    },
    deleteAuthor: function (author) {
      if (!isAdmin.value) return;
      confirmDeletion(this.$q)
        .then(() => {
          let deleteUser = httpsCallable(functions, 'deleteUser');
          deleteUser(author).then((response) => {
            console.log('Delete user: ', response);
          });
        })
        .catch(() => {});
    },
  },
  mounted: function () {
    if (!isAdmin.value || this.inReviewMode) this.$router.replace('/books');
    this.getAllUsers();
  },
});
</script>

<style lang="scss">
#authors {
  width: 1080px;
  max-width: 100%;
}

#authors .q-table__container {
  max-height: 600px;
}

.fields-title {
  font-size: 18px;
}

.input-icon {
  width: 100%;
  height: 100%;
}

.delete-author {
  font-size: 24px;
}

#authors th.table-label {
  max-width: 100px;
  white-space: pre-wrap;
  line-height: 1.2em;
}

.q-badge {
  top: 4px !important;
  right: 0px !important;
  font-size: 10px;
}
</style>
