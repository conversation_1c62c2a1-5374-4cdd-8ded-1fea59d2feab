<template>
  <PageLayout
    title="Users Management"
    subtitle="Manage user accounts, roles, and permissions"
    icon="group"
    :loading="!users.length || loading"
    loading-label="Loading users data..."
  >
    <PageCard elevated>
      <!-- Filters Section -->
      <template #header>
        <div class="row items-center justify-between full-width">
          <div>
            <div class="text-h5 text-weight-medium">Users Management</div>
            <div class="text-subtitle2 opacity-80">
              Manage user accounts, roles, and permissions
            </div>
          </div>
          <q-btn
            v-if="!isUsersListLoaded"
            label="Load Users"
            icon="person_search"
            color="primary"
            @click="loadInitData"
            unelevated
          >
            <q-tooltip
              >Click to load users (initial loading may be slow)</q-tooltip
            >
          </q-btn>
        </div>
      </template>

      <div class="filters-container q-mb-md">
        <div class="row q-col-gutter-md items-center">
          <div class="col-12 col-sm-4">
            <q-input
              v-model="filter.search"
              class="modern-input"
              debounce="300"
              placeholder="Search users..."
              outlined
              dense
            >
              <template v-slot:prepend>
                <q-icon name="search" color="primary" />
              </template>
              <template v-slot:append v-if="filter.search">
                <q-icon
                  name="close"
                  class="cursor-pointer"
                  @click="filter.search = ''"
                  color="grey-6"
                />
              </template>
            </q-input>
          </div>

          <div class="col-12 col-sm-4">
            <q-select
              v-model="filter.role"
              :options="roleOptions"
              class="modern-input"
              debounce="300"
              outlined
              dense
              map-options
              emit-value
              placeholder="Filter by role"
            >
              <template v-slot:prepend>
                <q-icon name="admin_panel_settings" color="primary" />
              </template>
              <template v-slot:append v-if="filter.role">
                <q-icon
                  name="close"
                  class="cursor-pointer"
                  @click="filter.role = ''"
                  color="grey-6"
                />
              </template>
            </q-select>
          </div>

          <div class="col-12 col-sm-4">
            <q-select
              v-model="filter.status"
              :options="statusOptions"
              class="modern-input"
              debounce="300"
              outlined
              dense
              map-options
              emit-value
              placeholder="Filter by status"
            >
              <template v-slot:prepend>
                <q-icon name="person" color="primary" />
              </template>
              <template v-slot:append v-if="filter.status">
                <q-icon
                  name="close"
                  class="cursor-pointer"
                  @click="filter.status = ''"
                  color="grey-6"
                />
              </template>
            </q-select>
          </div>
        </div>
      </div>

      <!-- Table Section -->
      <q-table
        :dense="$q.screen.lt.md"
        class="users-table sticky-header-table"
        flat
        bordered
        ref="tableRef"
        :rows="users"
        :columns="columns"
        row-key="uid"
        v-model:pagination="pagination"
        :filter="filter"
        binary-state-sort
        @request="onRequest"
      >
        <!-- Admin Toggle Column -->
        <template v-slot:body-cell-admin="props">
          <q-td auto-width :props="props">
            <q-toggle
              v-model="props.row.isAdmin"
              @update:model-value="toggleAdmin(props.row)"
              :disable="loading"
              color="primary"
              icon="admin_panel_settings"
            />
          </q-td>
        </template>

        <!-- Reviewer Toggle Column -->
        <template v-slot:body-cell-reviewer="props">
          <q-td auto-width :props="props">
            <q-toggle
              v-model="props.row.isReviewer"
              @update:model-value="toggleReviewer(props.row)"
              :disable="loading"
              color="purple"
              icon="rate_review"
            />
          </q-td>
        </template>

        <!-- PayPal Toggle Column -->
        <template v-slot:body-cell-paypal="props">
          <q-td auto-width :props="props">
            <q-toggle
              v-model="props.row.paidViaPaypal"
              @update:model-value="togglePaypal(props.row)"
              :disable="loading"
              color="info"
              icon="payments"
            />
          </q-td>
        </template>

        <!-- Actions Column -->
        <template v-slot:body-cell-more="props">
          <q-td auto-width :props="props">
            <q-btn round flat color="primary" icon="more_vert">
              <q-menu cover auto-close class="actions-menu">
                <q-list style="min-width: 200px">
                  <q-item
                    clickable
                    v-ripple
                    @click="moreActions(props.row)"
                    v-if="
                      props.row?.isAdmin === true ||
                      props.row?.isReviewer === true ||
                      props.row?.paidViaPaypal === true ||
                      props.row?.isOldAuthor === true ||
                      props.row?.stripeRole === 'basic'
                    "
                  >
                    <q-item-section avatar>
                      <q-icon name="settings" color="primary" />
                    </q-item-section>
                    <q-item-section>User Settings</q-item-section>
                  </q-item>

                  <q-item clickable v-ripple @click="reviewActions(props.row)">
                    <q-item-section avatar>
                      <q-icon name="login" color="purple" />
                    </q-item-section>
                    <q-item-section>Login As User</q-item-section>
                  </q-item>

                  <q-item
                    clickable
                    v-ripple
                    @click="generateResetPasswordLink(props.row)"
                  >
                    <q-item-section avatar>
                      <q-icon name="lock_reset" color="orange" />
                    </q-item-section>
                    <q-item-section>Reset Password</q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </q-btn>
          </q-td>
        </template>

        <!-- No Data Message -->
        <template v-slot:no-data>
          <div class="full-width row flex-center q-pa-lg">
            <div class="column items-center">
              <q-icon
                name="sentiment_dissatisfied"
                size="3rem"
                color="grey-6"
              />
              <span class="q-mt-sm text-grey-6 text-h6">No users found</span>
              <span class="text-caption text-grey-7 q-mt-xs">
                Try adjusting your search or filter criteria
              </span>
            </div>
          </div>
        </template>
      </q-table>
    </PageCard>

    <!-- User Settings Dialog -->
    <q-dialog v-model="userMoreSettingsDialog" maximized>
      <PageCard
        :title="selectedRow.user.displayName || selectedRow.user.email"
        :subtitle="selectedRow.user.email"
        icon="person"
        class="settings-dialog-card"
      >
        <template #actions>
          <q-btn flat round icon="close" v-close-popup>
            <q-tooltip>Close</q-tooltip>
          </q-btn>
        </template>

        <div class="row q-col-gutter-lg q-pa-lg">
          <!-- Left Column -->
          <div class="col-12 col-md-6">
            <div class="settings-section" v-if="selectedRow.isActive">
              <h5 class="section-title">Book Allowance</h5>
              <q-input
                v-model="selectedRow.allowed_books"
                class="modern-input"
                clearable
                clear-icon="close"
                type="number"
                color="primary"
                label="Number of books allowed"
                outlined
                hint="Set the maximum number of books this user can create"
              />
            </div>

            <div class="settings-section" v-if="selectedRow.isActive">
              <h5 class="section-title">Reviewer Assignment</h5>
              <div class="row items-center q-gutter-md">
                <q-select
                  v-model="selectedRow.assignedAdminId"
                  :options="adminOptions"
                  option-value="value"
                  option-label="label"
                  class="modern-input col"
                  debounce="300"
                  label="Select Reviewer"
                  @filter="filterAdmins"
                  @popup-hide="getAllUsers"
                  outlined
                  map-options
                  v-if="reviewers.length"
                  @update:model-value="isReviewerChanged = true"
                  hint="Assign a reviewer to this user"
                >
                  <template v-slot:prepend>
                    <q-icon name="person" color="primary" />
                  </template>
                  <template v-slot:append>
                    <q-icon
                      name="close"
                      @click.stop.prevent="
                        unAssignReviewerFromAuthor(selectedRow.author)
                      "
                      class="cursor-pointer"
                      v-if="selectedRow.assignedAdminId"
                    />
                  </template>
                </q-select>

                <q-btn
                  icon="mail"
                  class="notification-btn"
                  color="primary"
                  @click="sendEmailNotificationToReviewer"
                  :disable="
                    !selectedRow.assignedAdminId ||
                    (typeof selectedRow.assignedAdminId === 'object' &&
                      !selectedRow.assignedAdminId.value)
                  "
                  :loading="isSending"
                >
                  <q-tooltip>Send notification to reviewer</q-tooltip>
                </q-btn>
              </div>
            </div>
          </div>

          <!-- Right Column -->
          <div class="col-12 col-md-6">
            <div
              class="settings-section"
              v-if="selectedRow.toReview && selectedRow.toReview.length"
            >
              <h5 class="section-title">Assigned Authors</h5>
              <q-separator class="q-mb-md" />
              <div class="assigned-authors q-gutter-sm">
                <q-chip
                  v-for="authorToReview in selectedRow.toReview"
                  :key="authorToReview.id"
                  removable
                  v-model="authorToReview.id"
                  @remove="unAssignReviewerFromAuthor(authorToReview)"
                  color="primary"
                  text-color="white"
                  icon="person"
                  class="author-chip"
                >
                  {{ authorToReview.name || authorToReview.email }}
                </q-chip>
              </div>
            </div>
          </div>
        </div>

        <template #footer>
          <q-card-actions align="right" class="q-pa-md">
            <q-btn
              icon="save"
              label="Save Changes"
              @click="saveAuthorData"
              :disable="isSending || isReviewerChanged"
              v-if="selectedRow.isActive"
              color="primary"
              unelevated
            />
            <q-btn
              flat
              label="Cancel"
              icon="cancel"
              color="grey-7"
              v-close-popup
              :disable="isSending"
            />
          </q-card-actions>
        </template>
      </PageCard>
    </q-dialog>
  </PageLayout>
</template>

<script setup lang="ts">
import {
  listUsers,
  setPaypalPaymentStatus,
  setUserAdminStatus,
  setUserReviewerStatus,
  type UserWithClaims,
} from 'src/shared/api/firebase-functions';
import { onMounted, ref } from 'vue';
import { type QTableProps, type QTable, useQuasar } from 'quasar';
import { confirmOverrideText } from 'src/shared/lib/quasar-dialogs';
import {
  isReviewMode,
  isUsersListLoaded,
  loginAs,
  user,
  useAuthorStore,
} from 'src/entities/user';
import { useRouter } from 'vue-router';
import { sendEmail, fetchResetPasswordLink } from 'src/entities/setting';
import { useNotificationStore } from 'src/entities/notification';
import PageLayout from 'src/layouts/PageLayout.vue';
import PageCard from 'src/layouts/PageCard.vue';

const router = useRouter();

// State to determine if the current mode is review
isReviewMode.value = '';
// State to store the current login role
loginAs.value = '';
// Reference to the QTable component
const tableRef = ref<QTable>();
// State to indicate if data is being sent
const isSending = ref(false);
// State to indicate if select reviewer has changed
const isReviewerChanged = ref(false);
// Reactive state for storing users
const users = ref<UserWithClaims[]>([]);
// Reactive state for storing reviewers
const reviewers = ref<UserWithClaims[]>([]);
// Reactive state for filters applied on the table
const filter = ref({ search: '', role: '', status: '' });
// Options for role filtering
const roleOptions = ref([
  { label: 'All', value: '' },
  { label: 'Admin', value: 'isAdmin' },
  { label: 'Reviewers', value: 'isReviewer' },
]);
// Options for status filtering
const statusOptions = ref([
  { label: 'All', value: '' },
  { label: 'Active', value: 'active' },
  { label: 'Inactive', value: 'inactive' },
]);
// State to indicate if data is loading
const loading = ref(true);
// Pagination state
const pagination = ref({
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
});

// Columns configuration for the table
type Columns = NonNullable<QTableProps['columns']>;
const columns: Columns = [
  {
    name: 'displayName',
    field: 'displayName',
    label: 'Name',
    align: 'left',
  },
  {
    name: 'email',
    field: 'email',
    label: 'Email',
    align: 'left',
  },
  {
    name: 'creationTime',
    field: 'creationTime',
    label: 'Created',
    align: 'left',
  },
  {
    name: 'lastSignInTime',
    field: 'lastSignInTime',
    label: 'Signed-In',
    align: 'left',
  },
  {
    name: 'stripe',
    field: 'stripeRole',
    label: 'Stripe Role',
    align: 'left',
  },
  {
    name: 'admin',
    field: 'isAdmin',
    label: 'Is admin?',
    align: 'center',
  },
  {
    name: 'paypal',
    field: 'paidViaPaypal',
    label: 'Paid via Paypal',
    align: 'center',
  },
  {
    name: 'reviewer',
    field: 'reviewer',
    label: 'Reviewer',
    align: 'center',
  },
  {
    name: 'more',
    field: 'more',
    label: '',
    align: 'center',
  },
];

// Author Store
const authorStore = useAuthorStore();
const notificationStore = useNotificationStore();

// Mapping of pagination tokens for Firebase integration
const pageTokens: Record<number, string | undefined> = {};
type QTableRequestParams = Parameters<NonNullable<QTableProps['onRequest']>>[0];
// Admin options for dropdowns or selections
const adminOptions = ref([]);
// Feature options derived from author features
const featureOptions = ref(authorStore.listAuthorFeatures());
// State for the currently selected row in the table
const selectedRow = ref({
  id: '',
  user: '',
  author: '',
  toReview: '',
  assignedAdminId: '',
  assignedAdminEmail: '',
  features: [],
  allowed_books: 4,
  isActive: true,
});
// State to control visibility of the 'more settings' dialog
const userMoreSettingsDialog = ref(false);

/**
 * Handles table request to load users based on pagination and filters.
 * @param props - The table request parameters including pagination and filters.
 */
const onRequest = async (props: QTableRequestParams) => {
  if (!isUsersListLoaded.value) return;

  const { page, rowsPerPage } = props.pagination;
  const filter: any = props.filter;
  loading.value = true;

  // Need the "next page" token of the previous page, and Quasar's `page` is 1-indexed
  const token = pageTokens[page - 1 - 1];
  const {
    users: newUsers,
    totalUsers,
    nextPageToken,
  } = await listUsers({
    filter: filter.search,
    filterByRole: filter.role,
    filterByStatus: filter.status,
    page: page,
    limit: rowsPerPage,
    ...(token && { pageToken: token }),
  });

  pageTokens[page - 1] = nextPageToken;

  // update rowsCount with appropriate value
  pagination.value.rowsNumber = totalUsers;

  users.value = newUsers;

  // don't forget to update local pagination object
  pagination.value.page = page;
  pagination.value.rowsPerPage = rowsPerPage;

  // ...and turn off loading indicator
  loading.value = false;
};

onMounted(async () => {
  // get initial data from server (1st page)
  if (isUsersListLoaded.value === true) await loadInitData();

  getAllUsers();
});

/**
 * Loads initial data for the component.
 */
const loadInitData = async () => {
  isUsersListLoaded.value = true;

  const { users: newReviewers } = await listUsers({
    filterByRole: 'isReviewer',
    limit: 50,
    page: 1,
  });
  reviewers.value = newReviewers;

  tableRef.value!.requestServerInteraction();
};
const $q = useQuasar();

/**
 * Toggles the admin status of a user.
 * @param user - The user object whose admin status is to be toggled.
 */
const toggleAdmin = async (user: UserWithClaims) => {
  const confirmed = await confirmOverrideText($q, {
    title: 'Set admin status',
    message: `Are you sure you want to ${
      user.isAdmin ? 'set' : 'remove'
    } the user ${user.displayName} (${user.email}) as an admin?`,
  });
  if (!confirmed) {
    user.isAdmin = !user.isAdmin;
    return;
  }
  loading.value = true;
  try {
    await setUserAdminStatus({
      uid: user.uid,
      isAdmin: user.isAdmin,
    });
    user.isAdmin = user.isAdmin;
  } catch (e) {
    $q.notify({
      message: `Failed to modify user admin status: ${JSON.stringify(
        e,
      )}. Please try again.`,
      color: 'red',
      timeout: 3000,
      icon: 'error',
    });
  } finally {
    loading.value = false;
  }
};

/**
 * Toggles the reviewer status of a user.
 * @param userSelected - The user object whose reviewer status is to be toggled.
 */
const toggleReviewer = async (userSelected: UserWithClaims) => {
  const confirmed = await confirmOverrideText($q, {
    title: 'Set reviewer status',
    message: `Are you sure you want to ${
      userSelected.isReviewer ? 'set' : 'remove'
    } the user ${userSelected.displayName} (${
      userSelected.email
    }) as a reviewer?`,
  });
  if (!confirmed) {
    userSelected.isReviewer = !userSelected.isReviewer;
    return;
  }
  loading.value = true;
  try {
    await setUserReviewerStatus({
      uid: userSelected.uid,
      isReviewer: userSelected.isReviewer,
    });
    userSelected.isReviewer = userSelected.isReviewer;
    if (!userSelected.isReviewer) {
      const userToRemove = reviewers.value.findIndex(
        (reviewer) => reviewer.uid === userSelected.uid,
      );
      delete reviewers.value[userToRemove];
    } else {
      if (user.value.uid !== userSelected.uid)
        reviewers.value.push(userSelected);
    }
  } catch (e) {
    $q.notify({
      message: `Failed to modify user reviewer status: ${JSON.stringify(
        e,
      )}. Please try again.`,
      color: 'red',
      timeout: 3000,
      icon: 'error',
    });
  } finally {
    loading.value = false;
  }
};

/**
 * Toggles the PayPal payment status of a user.
 * @param user - The user object whose PayPal payment status is to be toggled.
 */
const togglePaypal = async (user: UserWithClaims) => {
  const confirmed = await confirmOverrideText($q, {
    title: 'Set PayPal status',
    message: `Are you sure you want to ${
      user.paidViaPaypal ? 'mark' : 'unmark'
    } the user ${user.displayName} (${
      user.email
    }) as having paid through PayPal?`,
  });
  if (!confirmed) {
    user.paidViaPaypal = !user.paidViaPaypal;
    return;
  }
  loading.value = true;
  try {
    await setPaypalPaymentStatus({
      uid: user.uid,
      paidViaPaypal: user.paidViaPaypal,
    });
    user.paidViaPaypal = user.paidViaPaypal;
  } catch (e) {
    $q.notify({
      message: `Failed to modify user PayPal status: ${JSON.stringify(
        e,
      )}. Please try again.`,
      color: 'red',
      timeout: 3000,
      icon: 'error',
    });
  } finally {
    loading.value = false;
  }
};

/**
 * Opens the more actions dialog for a user.
 * @param user - The user object for which more actions are to be taken.
 */
const moreActions = async (user: UserWithClaims) => {
  const author = await authorStore.getAuthor(user.uid);
  selectedRow.value.user = user;
  selectedRow.value.author = author;
  selectedRow.value.id = user.uid;
  const assignedAdminId = author?.assignedAdminId || '';
  const assignedAdminEmail = author?.assignedAdminEmail || '';
  selectedRow.value.assignedAdminId = {
    value: assignedAdminId,
    label: assignedAdminEmail,
  };
  selectedRow.value.features = author?.features;
  selectedRow.value.allowed_books = author.allowed_books;
  const isActive =
    user?.isAdmin === true ||
    user?.paidViaPaypal === true ||
    user?.isOldAuthor === true ||
    user?.stripeRole === 'basic';
  selectedRow.value.isActive = isActive;
  if (user.isReviewer) {
    authorStore.listAuthorsForReview(user.uid);
    selectedRow.value.toReview = authorStore.authorsForReview;
  } else {
    selectedRow.value.toReview = null;
  }
  userMoreSettingsDialog.value = true;
};

/**
 * Initiates review actions for a user.
 * @param user - The user object for which review actions are to be initiated.
 */
const reviewActions = async (user: UserWithClaims) => {
  const confirmed = await confirmOverrideText($q, {
    title: 'Enter Review Mode',
    message: `Are you sure you want to review ${user.displayName}?`,
  });
  if (!confirmed) return;

  loginAs.value = user.uid;
  await router.push({ path: 'books' });
};

/**
 * Saves the author data after modifications.
 */
const saveAuthorData = async () => {
  userMoreSettingsDialog.value = false;
  let features = selectedRow.value.features;
  if (selectedRow.value.assignedAdminId.value) {
    features.push('review');
  } else {
    features = features.filter((feature) => feature !== 'review');
  }

  const newData = {
    id: selectedRow.value.id,
    assignedAdminId: selectedRow.value.assignedAdminId.value ?? '',
    assignedAdminEmail: selectedRow.value.assignedAdminId.label ?? '',
    features: features ?? [],
  };
  if (newData.assignedAdminId) {
    await authorStore.updateAuthor(selectedRow.value.id, newData);
    await authorStore.updateReviewerAuthorsToReview(
      newData.assignedAdminId,
      {
        email: selectedRow.value.user.email,
        id: selectedRow.value.id,
      },
      true,
    );

    await authorStore.updateAuthorAllowedBooksMeta(
      selectedRow.value.id,
      selectedRow.value.allowed_books || 4,
    );
  }
};

/**
 * Filters admins based on the input value.
 * @param val - The input value to filter admins.
 * @param update - The function to update the filtered results.
 */
const filterAdmins = (val: string, update: (callback: () => void) => void) => {
  if (val === '') {
    update(() => {
      getAllUsers();
    });
    return;
  }

  update(() => {
    const needle = val.toLowerCase();
    adminOptions.value = adminOptions.value.filter((v) => {
      return v.label.toLowerCase().indexOf(needle) > -1;
    });
  });
};

const generateResetPasswordLink = async (user: UserWithClaims) => {
  loading.value = true;

  try {
    loading.value = true;

    const resetPasswordLink = await fetchResetPasswordLink(user.uid);

    const message = resetPasswordLink
      ? `You can use the reset password link below: <br /> <a target="_blank" href="${resetPasswordLink}">Reset Password Link</a>. <br /><br />Please note that the link will expire in one hour.`
      : 'An error has occurred. Please try again!';

    $q.dialog({
      style: { width: '50vw' },
      title: 'Generate Reset Password Link',
      message: message,
      html: true,
      ok: {
        color: 'primary',
      },
    });
  } catch (error) {
    $q.dialog({
      style: { width: '50vw' },
      title: 'Error',
      message:
        'An error has occurred while generating the reset password link. Please try again!',
      ok: {
        color: 'primary',
      },
    });
  } finally {
    loading.value = false;
  }
};

/**
 * Retrieves all users and updates admin options.
 */
const getAllUsers = () => {
  let options = [];
  reviewers.value.forEach((reviewer) => {
    options.push({
      label: reviewer.email,
      value: reviewer.uid,
    });
  });
  adminOptions.value = options;
};

/**
 * Sends an email notification to the reviewer.
 */
const sendEmailNotificationToReviewer = () => {
  let subject =
    '[Manuscriptr] You Have Been Assigned as a Reviewer to ' +
      selectedRow.value?.user?.displayName || selectedRow.value?.user?.email;
  let message = `<p>Dear ${selectedRow.value?.assignedAdminId?.label},</p>
                     <p>We are pleased to inform you that you have been assigned the role of a <strong>Reviewer</strong> in Manuscriptr, associated with the author ${
                       selectedRow.value?.user?.displayName ||
                       selectedRow.value?.user?.email
                     }.</p>
            <p>Once logged in, you can view any available author books that require your review. If you've been assigned to multiple authors, you can select a specific author's book by using the <strong>Select Author</strong> dropdown menu in your dashboard.</p>
            <p>Thank you for your participation and valuable contributions to the Manuscriptr platform. We look forward to your insightful reviews.</p>
            <p>The Manuscriptr Team</p>`;

  if (!selectedRow.value?.assignedAdminId?.label) return;

  notificationStore.createUserNotification(
    selectedRow.value?.assignedAdminId?.value as string,
    {
      title: subject,
      notification: message,
    },
  );
  try {
    isSending.value = true;
    sendEmail({
      to: selectedRow.value.assignedAdminId.label,
      from: user?.value?.email,
      subject: subject,
      html: message,
    }).then(async (response) => {
      if (selectedRow.value.user.email) {
        subject =
          '[Manuscriptr] A Reviewer Has Been Assigned to Your Books: ' +
          selectedRow.value.assignedAdminId.label;
        message = `<p>Dear ${
          selectedRow.value.user.displayName || selectedRow.value.user.email
        },</p>
            <p>We are pleased to inform you that a <strong>Reviewer</strong> has been assigned to review your books on the Manuscriptr platform.</p>
            <p>The assigned reviewer is associated with the email address: ${
              selectedRow.value.assignedAdminId.label
            }.</p>
            <p>This step ensures that your work will receive the valuable attention it deserves. You can log in to your account to track the progress and updates from the assigned reviewer.</p>
            <p>If you have any questions or need assistance, please don't hesitate to reach out to our support team.</p>
            <p>The Manuscriptr Team</p>`;
        notificationStore.createUserNotification(
          selectedRow.value.user.uid as string,
          {
            title: subject,
            notification: message,
          },
        );
        await sendEmail({
          to: selectedRow.value.user.email,
          from: user?.value?.email,
          subject: subject,
          html: message,
        });
      }
      isSending.value = false;
      $q.notify({
        message: 'Message sent.',
        color: 'primary',
        timeout: 3000,
        icon: 'email',
      });
      await saveAuthorData();
    });
  } catch (err) {
    isSending.value = false;
    $q.notify({
      message: 'An error has occurred. Please try again.',
      color: 'red',
      timeout: 3000,
      icon: 'error',
    });
  }
};

/**
 * Unassigns a reviewer from an author.
 * @param user - The user object from which the reviewer is to be unassigned.
 */
const unAssignReviewerFromAuthor = async (user: any) => {
  const confirmed = await confirmOverrideText($q, {
    title: 'Unassigned Reviewer',
    message: `Are you sure you want to un-assign ${
      user.assignedAdminEmail
    } from the role of Reviewer for the user ${user.name || user.email}?`,
  });
  if (!confirmed) {
    return;
  }

  let subject =
    '[Manuscriptr] You Have Been Unassigned as a Reviewer from ' + user.name ||
    user.email;
  let message = `<p>Dear ${user.assignedAdminEmail},</p>
                   <p>We regret to inform you that you have been unassigned from the role of <strong>Reviewer</strong> in Manuscriptr for the author ${
                     user.name || user.email
                   }.</p>
          <p>Thank you for your past contributions to the Manuscriptr platform. If you have any questions or need further clarification, feel free to reach out to our support team.</p>
          <p>The Manuscriptr Team</p>`;

  if (!user.assignedAdminEmail) return;

  try {
    notificationStore.createUserNotification(user.assignedAdminId as string, {
      title: subject,
      notification: message,
    });
    await authorStore.updateReviewerAuthorsToReview(
      user.assignedAdminId,
      {
        email: selectedRow.value.user.email,
        id: selectedRow.value.id,
      },
      false,
    );

    isSending.value = true;
    sendEmail({
      to: user.assignedAdminEmail,
      from: user?.value?.email,
      subject: subject,
      html: message,
    })
      .then(async (response) => {
        if (user.email) {
          subject =
            '[Manuscriptr] A Reviewer Has Been Unassigned from Your Books: ' +
            user.assignedAdminEmail;
          message = `<p>Dear ${user.name || user.email},</p>
        <p>We would like to inform you that a <strong>Reviewer</strong> has been unassigned from reviewing your books on the Manuscriptr platform.</p>
        <p>The reviewer associated with the email address ${
          user.assignedAdminEmail
        } will no longer be reviewing your work.</p>
        <p>If you have any concerns or require further assistance, please don't hesitate to contact our support team.</p>
        <p>The Manuscriptr Team</p>`;

          notificationStore.createUserNotification(user.id as string, {
            title: subject,
            notification: message,
          });
          await sendEmail({
            to: user.email,
            from: user?.value?.email,
            subject: subject,
            html: message,
          });
        }
        isSending.value = false;
        $q.notify({
          message: 'Message sent.',
          color: 'primary',
          timeout: 3000,
          icon: 'email',
        });
        await authorStore.updateAuthor(user.id, {
          assignedAdminEmail: '',
          assignedAdminId: '',
        });
      })
      .finally(() => {
        if (!selectedRow.value.toReview) selectedRow.value.assignedAdminId = '';
      });
  } catch (err) {
    isSending.value = false;
    $q.notify({
      message: 'An error has occurred. Please try again.',
      color: 'red',
      timeout: 3000,
      icon: 'error',
    });
  }
};
</script>

<style lang="scss" scoped>
.sticky-header-table {
  height: calc(100vh - 350px);

  :deep(.q-table__top),
  :deep(.q-table__bottom),
  :deep(thead tr:first-child th) {
    background-color: var(--q-card-background, white);

    body.body--dark & {
      background-color: var(--q-dark);
    }
  }

  :deep(thead tr th) {
    position: sticky;
    z-index: 1;
  }

  :deep(thead tr:first-child th) {
    top: 0;
  }

  &.q-table--loading :deep(thead tr:last-child th) {
    top: 48px;
  }

  :deep(tbody) {
    scroll-margin-top: 48px;
  }
}

.filters-container {
  .modern-input {
    :deep(.q-field__control) {
      border-radius: 8px;
    }
  }
}

.settings-section {
  margin-bottom: 2rem;

  .section-title {
    color: var(--q-primary);
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid rgba(var(--q-primary-rgb), 0.2);
  }
}

.author-chip {
  margin: 0.25rem;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(var(--q-primary-rgb), 0.3);
  }
}

.notification-btn {
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.05);
  }
}

// Enhanced actions menu
:deep(.actions-menu) {
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);

  body.body--dark & {
    background: var(--q-dark);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
  }

  .q-item {
    border-radius: 8px;
    margin: 4px;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(var(--q-primary-rgb), 0.1);
      transform: translateX(4px);
    }

    .q-icon {
      transition: all 0.2s ease;
    }

    &:hover .q-icon {
      transform: scale(1.1);
    }
  }
}
</style>
