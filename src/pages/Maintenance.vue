<template>
  <q-layout view="hHh lpR fFf">
    <q-page-container>
      <div class="row" style="height: calc(100vh - 50px)">
        <div
          class="col-8 col-lg-8 col-sm-12 col-xs-12 q-pa-xl full-height flex flex-center xs-hide sm-hide md-hide"
        >
          <div style="font-size: 20vh" class="text-primary">Maintenance</div>
        </div>
        <div class="col-4 col-lg-4 col-sm-12 col-xs-12 bg-white full-height">
          <q-card
            flat
            class="text-center text-primary full-height flex flex-center"
          >
            <q-card-section>
              <img src="logo.png" style="width: 350px" />
              <div style="font-size: 3vh" class="q-mt-md">
                Sorry, System maintenance in progress.
              </div>
              <br />
              <div style="font-size: 3vh">
                {{ maintenanceMessage }}
              </div>

              <br />
              <br />
              <br />
              <q-btn-group spread v-if="!isMaintenanceMode">
                <q-btn size="md" padding="md" color="primary" to="/">
                  <q-icon name="img:robot.png" /> &nbsp;&nbsp;Return to main
                  page
                </q-btn>
              </q-btn-group>
            </q-card-section>
          </q-card>
        </div>
      </div>
      <q-page-sticky position="bottom-left" :offset="[5, 18]">
        <q-fab
          label="Contact Us"
          label-position="left"
          color="primary"
          icon="contact_support"
          :direction="$q.screen.lt.sm ? 'up' : 'right'"
          v-model="helpDialogForm"
          class="help-icon-popup"
        >
          <q-fab-action class="help-popup" disable label-position="left">
            <HelpCard
              @minimizeToggle="helpDialogForm = false"
              @closeToggle="helpDialogForm = false"
            />
          </q-fab-action>
        </q-fab>
      </q-page-sticky>
    </q-page-container>

    <q-footer class="bg-primary text-white no-box-shadow">
      <q-toolbar>
        <q-toolbar-title clickable @click="redirect" class="cursor-pointer">
          <q-avatar>
            <img src="robot.png" />
          </q-avatar>
          <span style="font-size: 1rem" class="q-ml-sm">Manuscriptr</span>
        </q-toolbar-title>
        <q-space />
        © 2024 Best Seller Publishing, All rights reserved
      </q-toolbar>
    </q-footer>
  </q-layout>
</template>

<script setup lang="ts">
import { isMaintenanceMode, maintenanceMessage } from 'src/entities/setting';
import { user } from 'src/entities/user';
import { getAuth } from 'firebase/auth';
import { ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { HelpCard } from 'src/entities/user';
import { useQuasar } from 'quasar';

const router = useRouter();
const helpDialogForm = ref(false);
const $q = useQuasar();
const redirect = () => {
  router.push('/');
};

// signs out the user
if (user.value) {
  getAuth().signOut();
}

// if maintenance is complete redirect to login
watch(
  isMaintenanceMode,
  (maintenance, oldMaintenance) => {
    if (!isMaintenanceMode.value) {
      window.location.href = '/';
    }
  },
  { immediate: true },
);
</script>
<style lang="scss">
.help-icon-popup {
  .q-fab__actions--up {
    left: calc(100vw - 30px) !important;
  }
}
.help-popup {
  align-items: self-end;
  justify-content: center;
  align-self: self-end;
  border-radius: 0;
  padding: 0;

  &.q-btn.disabled {
    opacity: 1 !important;
    cursor: default !important;
    * {
      cursor: default !important;
    }
  }
}
</style>
