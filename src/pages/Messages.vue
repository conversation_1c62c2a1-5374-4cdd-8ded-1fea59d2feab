<template>
  <div id="messages" class="container q-pa-lg q-mx-auto">
    <div id="title" class="q-mb-lg">Messages</div>

    <q-list bordered class="rounded-borders q-mb-lg" separator>
      <q-item v-for="(message, index) in messages" :key="message.id">
        <q-item-section class="column">
          <div>{{ message.sender }} | {{ formatDate(message.createdAt) }}</div>
          <div v-html="message.content"></div>
        </q-item-section>
        <q-item-section side v-if="message.sender == user.name">
          <q-icon name="more_vert" color="dark">
            <q-menu>
              <q-list style="min-width: 100px">
                <q-item clickable v-close-popup @click="deleteMessage(index)">
                  <q-item-section>Delete</q-item-section>
                </q-item>
              </q-list>
            </q-menu>
          </q-icon>
        </q-item-section>
      </q-item>
    </q-list>

    <q-editor
      v-model="editor"
      min-height="5rem"
      class="q-mb-md"
      :toolbar="toolbar"
    />

    <!--<editor v-model="editor" api-key="yk2zcr92ljrjqpxrxbfo8p5914oyhcka9eowu2gohwtzuoro" :init="editorInit" class="message-editor mce-content-body" />-->

    <q-btn
      label="Send"
      icon="send"
      color="primary"
      @click="addMessage()"
      class="q-mr-md"
    />
  </div>
</template>

<script>
import _ from 'lodash';
import mixins from 'src/app/mixins/index.js';

import Editor from '@tinymce/tinymce-vue';

import { date } from 'quasar';

export default {
  name: 'Messages',
  mixins: mixins,
  components: {
    editor: Editor,
  },
  data() {
    return {
      editor: '',
      toolbar: [['bold', 'italic', 'underline']],
      editorInit: {
        selector: '.message-editor',
        height: '200px',
        menubar: false,
        plugins: [
          'advlist',
          'autolink',
          'lists',
          'link',
          'searchreplace',
          'nonbreaking',
          'quickbars',
          'custom-comment',
          'custom-footnote',
        ],
        toolbar:
          'undo redo | block | fontsize | bold italic underline forecolor | bullist numlist | link image-insert | removeformat | searchreplace | alignleft aligncenter alignright alignjustify',
        quickbars_selection_toolbar: false,
        quickbars_insert_toolbar: false,
        inline: false,
        fontsize_formats: '12px 14px 16px 18px 24px 36px 48px',
        content_style: '',
        nonbreaking_force_tab: true,
        statusbar: false,
        contextmenu: false,
        paste_as_text: false,
        paste_block_drop: true,
        browser_spellcheck: true,
        setup: (editor) => {},
      },
    };
  },
  computed: {},
  methods: {
    addMessage: function () {
      let text = this.editor;
      text = text.trim();

      if (text == '') return;

      let user = this.inReviewMode ? this.user : this.user;

      let message = {
        sender: this.user.name,
        createdAt: Date.now(),
        content: text,
        readBy: [this.userId],
      };

      let messages = this.messages;
      messages.push(message);

      this.updateUserProp('messages', messages);

      this.editor = '';
    },
    formatDate: function (timestamp) {
      let formattedDate = date.formatDate(timestamp, 'M-D-YY h:mm A');
      return formattedDate;
    },
    deleteMessage: function (index) {
      let messages = this.messages;
      messages.splice(index, 1);

      this.updateUserProp('messages', messages);
    },
    markAllAsRead: function () {
      let messages = this.messages;

      _.forEach(messages, (message, index) => {
        if (message.readBy == null) message.readBy = [];
        message.readBy.push(this.userId);
        message.readBy = _.uniq(message.readBy);
      });

      this.updateUserProp('messages', messages);
    },
  },
  mounted: function () {
    let user = this.inReviewMode ? this.user : this.user;

    if (user.messages == null) this.updateUserProp('messages', []);

    this.markAllAsRead();
  },
};
</script>

<style lang="scss">
#messages .q-list {
  max-height: 500px;
  overflow-y: auto;
}
</style>
