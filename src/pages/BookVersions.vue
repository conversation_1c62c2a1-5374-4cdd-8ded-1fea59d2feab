<template>
  <PageLayout
    title="Version Manager"
    subtitle="Manage different versions of your book"
    icon="history"
    :loading="isLoading"
    loading-label="Managing your book versions..."
  >
    <PageCard elevated>
      <q-scroll-area
        :thumb-style="thumbStyle"
        :bar-style="barStyle"
        style="height: calc(100vh - 300px)"
      >
        <q-list class="rounded-borders q-mb-lg">
          <!-- NOTE: refs inside an array do not get automatically unwrapped -->
          <div v-for="(version, index) in versions" :key="version.value.id">
            <q-expansion-item group="versions" hide-expand-icon>
              <template v-slot:header>
                <q-item-section>
                  <q-item-label>{{
                    version.value.name ||
                    'Version ' + formatDate(version.value.createdAt)
                  }}</q-item-label>
                  <q-item-label caption>{{
                    formatDate(version.value.createdAt)
                  }}</q-item-label>
                </q-item-section>

                <q-item-section class="items-end">
                  <q-btn
                    flat
                    round
                    dense
                    color="grey-7"
                    icon="more_vert"
                    @click.stop
                    class="more-button"
                  >
                    <q-menu>
                      <q-list style="min-width: 180px">
                        <q-item
                          clickable
                          v-close-popup
                          :to="`/books/${bookId}?version=${version.value.id}`"
                        >
                          <q-item-section avatar>
                            <q-icon name="preview" color="primary" />
                          </q-item-section>
                          <q-item-section>Preview</q-item-section>
                        </q-item>
                        <q-item
                          clickable
                          v-close-popup
                          @click="setAsCurrent(version.value)"
                        >
                          <q-item-section avatar>
                            <q-icon name="restore" color="orange" />
                          </q-item-section>
                          <q-item-section
                            >Rollback to this version</q-item-section
                          >
                        </q-item>
                        <q-item
                          clickable
                          v-close-popup
                          @click="downloadBackup(version.value)"
                        >
                          <q-item-section avatar>
                            <q-icon name="download" color="green" />
                          </q-item-section>
                          <q-item-section>Download Backup</q-item-section>
                        </q-item>
                        <q-item
                          clickable
                          v-close-popup
                          @click="deleteVersion(version.value)"
                        >
                          <q-item-section avatar>
                            <q-icon name="delete" color="negative" />
                          </q-item-section>
                          <q-item-section>Delete</q-item-section>
                        </q-item>
                      </q-list>
                    </q-menu>
                  </q-btn>
                </q-item-section>
              </template>
              <q-separator />
              <div class="q-pa-md version-details">
                <div class="book-spine"></div>
                <div class="col-12">
                  <q-input
                    v-model="version.value.name"
                    label="Version Name"
                    outlined
                    class="q-mb-md modern-input"
                    dense
                    bg-color="white"
                  >
                    <template v-slot:append>
                      <q-icon
                        name="edit"
                        size="sm"
                        color="primary"
                        class="cursor-pointer"
                      />
                    </template>
                  </q-input>
                  <q-input
                    v-model="version.value.description"
                    type="textarea"
                    label="Version Notes"
                    class="modern-input"
                    outlined
                    dense
                  >
                    <template v-slot:append>
                      <q-icon
                        name="notes"
                        size="sm"
                        color="primary"
                        class="cursor-pointer"
                      />
                    </template>
                  </q-input>
                </div>
              </div>
              <q-separator />
            </q-expansion-item>
          </div>
        </q-list>
      </q-scroll-area>

      <template #footer>
        <q-card-actions align="left" class="q-gutter-sm">
          <q-btn
            unelevated
            label="Back to Books"
            icon="arrow_back"
            to="/books"
            class="premium-btn-secondary"
          />
          <q-btn
            class="premium-btn-primary"
            unelevated
            label="New Version"
            icon="add_circle"
            color="primary"
            :loading="addingVersion"
            :disable="addingVersion"
            @click="addVersion"
          />
          <q-btn
            class="premium-btn-primary"
            unelevated
            label="Import Backup"
            icon="upload_file"
            color="secondary"
            :disable="importingBackup"
            @click="showFilePicker"
          />
          <q-file
            v-show="false"
            ref="fileInput"
            v-model="file"
            accept=".json"
            @update:model-value="importBackup"
          />
        </q-card-actions>
      </template>
    </PageCard>
  </PageLayout>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';
import { type QFile, date, useQuasar } from 'quasar';
import {
  confirmDeletion,
  confirmOverrideText,
  showError,
} from 'src/shared/lib/quasar-dialogs';
import { saveAs } from 'file-saver';
import {
  listBookVersions,
  createBookVersion,
  deleteBookVersion,
  restoreBookVersion,
  importBookVersion,
  exportBookVersion,
  type BookVersion,
  type Backup,
} from 'src/features/book-versioning';
import { readAsText } from 'src/shared/lib/file-reader';
import { barStyle, thumbStyle } from 'src/entities/setting';
import PageLayout from 'src/layouts/PageLayout.vue';
import PageCard from 'src/layouts/PageCard.vue';

const route = useRoute();
const bookId = route.params.id as string;
const versions = await listBookVersions(bookId);
const isLoading = computed(
  () =>
    versions.value?.length === 0 ||
    addingVersion.value ||
    importingBackup.value,
);

const addingVersion = ref(false);
async function addVersion() {
  addingVersion.value = true;
  try {
    await createBookVersion(route.params.id as string);
  } catch (e) {
    console.error(e);
    alert('Error creating version');
  } finally {
    addingVersion.value = false;
  }
}

const $q = useQuasar();
async function setAsCurrent(version: BookVersion) {
  const confirmed = await confirmOverrideText($q, {
    title: 'Are you sure?',
    message:
      'This will save a backup copy of the current working version and begin using the selected version.',
  });
  if (!confirmed) return;
  addingVersion.value = true;
  try {
    await addVersion();
    await restoreBookVersion(bookId, version);
  } catch (e) {
    console.error(e);
    alert('Error restoring version');
  }
  addingVersion.value = false;
}

async function deleteVersion(version: BookVersion) {
  try {
    await deleteBookVersion(bookId, version.id);
  } catch (error) {
    console.error(error);
    alert('Error deleting version');
  }
  addingVersion.value = false;
}

const fileInput = ref<QFile>();
function showFilePicker() {
  fileInput.value?.pickFiles();
}

const importingBackup = ref(false);
const file = ref<File>();
async function importBackup() {
  if (!file.value) return;
  importingBackup.value = true;
  const fileContent = await readAsText(file.value!);
  file.value = undefined;

  try {
    const backup: Backup = JSON.parse(fileContent);
    await importBookVersion(bookId, backup);
  } catch (error) {
    showError($q, (error as Error).message);
  } finally {
    importingBackup.value = false;
  }
}

async function downloadBackup(version: BookVersion) {
  importingBackup.value = true;
  const backup = await exportBookVersion(bookId, version);
  const jsonString = JSON.stringify(JSON.parse(JSON.stringify(backup)));

  const blob = new Blob([jsonString], { type: 'application/json' });
  let filename = version.title + (version.name ? ' - ' + version.name : '');
  filename = filename?.replace(/[/\\?%*:|"<>]/g, '');
  saveAs(blob, filename + ' (backup).json');
  importingBackup.value = false;
}

function formatDate(timestamp: Date) {
  return date.formatDate(timestamp, 'M-D-YY h:mm A');
}
</script>

<style lang="scss" scoped>
.version-details {
  position: relative;
  overflow: hidden;
  background: linear-gradient(
    135deg,
    rgba(var(--q-primary-rgb), 0.02) 0%,
    rgba(var(--q-primary-rgb), 0.08) 100%
  );
  border-radius: 8px;

  body.body--dark & {
    background: linear-gradient(
      135deg,
      rgba(var(--q-primary-rgb), 0.1) 0%,
      rgba(var(--q-primary-rgb), 0.2) 100%
    );
  }
}

.book-spine {
  width: 6px;
  height: 100%;
  background: linear-gradient(135deg, var(--q-primary), var(--q-primary-dark));
  position: absolute;
  left: 0;
  top: 0;
  border-radius: 0 3px 3px 0;

  &:after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 1px;
    height: 100%;
    background: rgba(255, 255, 255, 0.3);
  }
}

.modern-input {
  :deep(.q-field__control) {
    border-radius: 8px;
  }

  :deep(.q-field__append) {
    .q-icon {
      transition: all 0.2s ease;

      &:hover {
        color: var(--q-primary-dark) !important;
        transform: scale(1.1);
      }
    }
  }
}

.more-button {
  transition: all 0.2s ease;

  &:hover {
    background: rgba(var(--q-primary-rgb), 0.1);
    color: var(--q-primary);
  }
}

// Enhanced menu styling
:deep(.q-menu) {
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);

  body.body--dark & {
    background: var(--q-dark);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
  }

  .q-item {
    border-radius: 4px;
    margin: 2px 4px;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(var(--q-primary-rgb), 0.1);
    }

    .q-icon {
      transition: all 0.2s ease;
    }

    &:hover .q-icon {
      transform: scale(1.1);
    }
  }
}
</style>
