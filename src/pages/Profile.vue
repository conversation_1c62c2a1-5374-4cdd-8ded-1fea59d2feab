<template>
  <PageLayout
    title="My Profile"
    icon="person"
    subtitle="Manage your account settings and subscriptions"
    :loading="loading"
    loading-label="Loading your profile..."
  >
    <div class="row q-col-gutter-lg">
      <!-- User Info Column -->
      <div class="col-12 col-md-4">
        <PageCard title="Profile Information" icon="account_circle" elevated>
          <UserInfoCard :user="onBoardingUser" />

          <template #footer>
            <div class="column q-gutter-md">
              <q-btn
                v-if="
                  !userLoggedIn?.accountDeletionRequestedAt &&
                  !userLoggedIn?.accountDeletionScheduledDate
                "
                class="premium-btn-danger full-width"
                icon="delete"
                label="Delete Account"
                @click="requestAccountDeletion"
                :loading="loading"
                unelevated
              />
              <q-btn
                v-else
                class="premium-btn-warning full-width"
                icon="cancel"
                label="Cancel Account Deletion"
                @click="cancelAccountDeletion"
                :loading="loading"
                unelevated
              />
              <q-btn
                class="premium-btn-warning full-width"
                icon="person"
                color="green"
                label="Edit Profile"
                @click="showOnboarding = true"
                unelevated
              />

              <div class="row q-col-gutter-md q-mt-sm">
                <div class="col-6">
                  <q-btn
                    class="premium-btn-secondary full-width"
                    icon="help"
                    label="Help"
                    to="/help"
                    unelevated
                  />
                </div>
                <div class="col-6">
                  <q-btn
                    class="premium-btn-primary full-width"
                    icon="home"
                    label="Books"
                    to="/books"
                    unelevated
                  />
                </div>
              </div>
            </div>
          </template>
        </PageCard>
      </div>

      <!-- Subscriptions Column -->
      <div class="col-12 col-md-8">
        <PageCard
          title="Subscriptions"
          subtitle="Manage your active subscriptions and payment methods"
          icon="verified"
          :loading="isLoadingSubs"
          loading-label="Loading subscriptions..."
        >
          <!-- Loading State -->
          <div v-if="isLoadingSubs" class="loading-skeleton">
            <q-skeleton type="text" class="q-mb-md" />
            <q-skeleton type="rect" height="100px" class="q-mb-md" />
            <q-skeleton type="rect" height="100px" class="q-mb-md" />
            <q-skeleton type="text" width="60%" class="q-mb-sm" />
            <q-skeleton type="text" width="40%" />
          </div>

          <!-- Error State -->
          <div v-else-if="errorSubs" class="error-state text-center q-pa-lg">
            <q-icon name="error" color="negative" size="48px" />
            <p class="text-body1 q-mt-md">
              An error has occurred: {{ errorSubs }}
            </p>
            <q-btn
              color="primary"
              label="Try Again"
              @click="refreshSubscriptions"
              unelevated
            />
          </div>

          <!-- Subscriptions List -->
          <div v-else-if="subs.length" class="subscriptions-list">
            <div v-for="sub in subs" :key="sub.id" class="q-mb-md">
              <SubscriptionCard :subscription="sub" @cancelled="cancelled" />
            </div>
          </div>

          <!-- No Subscriptions -->
          <div v-else class="no-subscriptions">
            <UnsubscribedNotice
              message="You no longer have an active subscription"
            />
          </div>
        </PageCard>
      </div>
    </div>
  </PageLayout>

  <UserOnboarding
    v-model:show="showOnboarding"
    @complete="completeOnboarding"
    :user="onBoardingUser as object"
  />
</template>

<script setup lang="ts">
import { useAsyncState, watchDebounced } from '@vueuse/core';
import { getMyProfileSubscriptions } from 'src/entities/package';
import SubscriptionCard from 'src/entities/package/ui/SubscriptionCard.vue';
import UnsubscribedNotice from 'src/entities/package/ui/UnsubscribedNotice.vue';
import {
  UserInfoCard,
  user,
  userLoggedIn,
  useAuthorStore,
  useSubscriptionStore,
  UserOnboarding,
  getUserDetailsForOnboarding,
} from 'src/entities/user';
import { confirmOverrideText } from 'src/shared/lib/quasar-dialogs';
import { ref } from 'vue';
import { useQuasar } from 'quasar';
import { sendEmail } from 'src/entities/setting';
import { useNotificationStore } from 'src/entities/notification';
import { ACTIONS, loggingService, PAGES } from 'src/entities/log';
import PageLayout from 'src/layouts/PageLayout.vue';
import PageCard from 'src/layouts/PageCard.vue';

// State management for subscriptions
const {
  state: subs,
  isLoading: isLoadingSubs,
  error: errorSubs,
  execute: refreshSubscriptions,
} = useAsyncState(getMyProfileSubscriptions(), []);

// Quasar framework hook
const $q = useQuasar();
const loading = ref(false);
const subscriptionStore = useSubscriptionStore();
const authorStore = useAuthorStore();
const notificationStore = useNotificationStore();
const showOnboarding = ref(false);
const onBoardingUser = ref<null | object>(
  getUserDetailsForOnboarding(userLoggedIn.value),
);

// Watch for changes in newSubscription and update subscriptions list debounced
watchDebounced(
  subscriptionStore,
  async (newVal) => {
    if (newVal.isSubscribe === false || newVal.newSubscription) {
      isLoadingSubs.value = true;
      subs.value = await getMyProfileSubscriptions();
      isLoadingSubs.value = false;
    }
  },
  { debounce: 3500, maxWait: 10000 },
);

/**
 * Request the deletion of the user account.
 */
const requestAccountDeletion = async (): Promise<void> => {
  const confirmed = await confirmOverrideText($q, {
    title: 'Confirm Account Deletion Request',
    message:
      'Are you sure you want to request account deletion? This action cannot be undone once processed.',
  });
  if (!confirmed) {
    return;
  }
  loading.value = true;

  try {
    await authorStore.updateAuthor(user.value?.uid as string, {
      accountDeletionRequestedAt: Date.now(),
    });
    await loggingService.logAction(
      PAGES.PROFILE,
      ACTIONS.DELETE,
      `Requested Account Deletion`,
      'profile',
      user.value?.uid,
    );
    $q.notify({
      message:
        'Your account deletion request has been submitted successfully. You will be notified once it is processed.',
      color: 'primary',
      timeout: 3000,
      icon: 'check',
    });
  } catch (e) {
    $q.notify({
      message:
        'There was an issue submitting your request. Please try again later.',
      color: 'red',
      timeout: 3000,
      icon: 'error',
    });
  } finally {
    loading.value = false;

    notificationStore.createUserNotification(user?.value?.uid as string, {
      title: `Account Deletion Request `,
      notification: `<p>We have received your request to delete your Manuscriptr account. We are processing your request in accordance with our Deletion Policy.</p>`,
      url: `/profile`,
    });
  }
};

/**
 * Cancel the account deletion request.
 */
const cancelAccountDeletion = async (): Promise<void> => {
  const confirmed = await confirmOverrideText($q, {
    title: 'Cancel Account Deletion Request',
    message:
      'Are you sure you want to cancel your account deletion request? Your account will remain active, and the deletion process will be halted.',
  });
  if (!confirmed) {
    return;
  }
  loading.value = true;

  try {
    await authorStore.updateAuthor(user.value?.uid as string, {
      accountDeletionRequestedAt: null,
      accountDeletionScheduledDate: null,
    });
    await loggingService.logAction(
      PAGES.PROFILE,
      ACTIONS.DELETE,
      `Account Deletion Cancelled`,
      'profile',
      user.value?.uid,
    );
    const subject = 'Account Deletion Request Cancellation';
    const message = `<p>Dear ${user.value?.displayName || user.value.email},</p>
          <p>
          We wanted to inform you that your request to cancel the account deletion has been successfully processed. Your account remains active, and no further action will be taken regarding the deletion.
          </p>
          <p>
          If you did not initiate this cancellation, please contact our support team immediately.
          </p>
          <p>The Manuscriptr Team</p>`;

    if (!user.value?.email) return;

    await sendEmail({
      to: user.value?.email,
      from: user?.value?.email,
      cc: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      subject: subject,
      html: message,
    });
    $q.notify({
      message: 'Your account deletion request has been successfully canceled.',
      color: 'primary',
      timeout: 3000,
      icon: 'check',
    });
  } catch (e) {
    console.log(e);
    $q.notify({
      message:
        'There was an issue submitting your request. Please try again later.',
      color: 'red',
      timeout: 3000,
      icon: 'error',
    });
  } finally {
    loading.value = false;

    notificationStore.createUserNotification(user?.value?.uid as string, {
      title: `Cancel Account Deletion Request `,
      notification: `<p>We wanted to inform you that your request to cancel the account deletion has been successfully processed. Your account remains active, and no further action will be taken regarding the deletion.</p>`,
      url: `/profile`,
    });
  }
};

/** Handle onboarding completion */
const completeOnboarding = async (userInfoData: any): Promise<void> => {
  try {
    // Update the user profile with the collected information
    await authorStore.updateAuthor(user.value?.uid as string, {
      ...userInfoData,
      onboardingCompleted: true,
    });

    // Show welcome notification
    $q.notify({
      color: 'positive',
      message: `Welcome, ${userInfoData.firstName}! Your profile is now set up.`,
      icon: 'check_circle',
      timeout: 5000,
    });
  } catch (error) {
    console.error('Error updating user profile:', error);
    $q.notify({
      color: 'negative',
      message: 'There was an error saving your profile. Please try again.',
      icon: 'error',
    });
  }
};

const cancelled = () => {
  isLoadingSubs.value = true;
  subs.value = [];
  isLoadingSubs.value = false;
};
</script>

<style lang="scss" scoped>
.loading-skeleton {
  padding: 16px 0;
}

.error-state {
  padding: 40px 0;

  .q-btn {
    margin-top: 16px;
  }
}

.no-subscriptions {
  padding: 16px 0;
}

.subscriptions-list {
  .q-mb-md:last-child {
    margin-bottom: 0;
  }
}
</style>
