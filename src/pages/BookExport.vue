<template>
  <PageLayout
    title="Export Book"
    subtitle="Export your book in various formats"
    icon="download"
    max-width="1200px"
  >
    <PageCard elevated>
      <q-scroll-area
        :thumb-style="thumbStyle"
        :bar-style="barStyle"
        style="height: calc(100vh - 350px)"
      >
        <BookPreview :book="book" />
      </q-scroll-area>

      <template #footer>
        <q-card-actions align="left" class="q-gutter-sm">
          <q-btn
            unelevated
            label="Back"
            icon="subdirectory_arrow_left"
            to="/books"
            class="premium-btn-secondary"
          />
          <q-btn
            class="premium-btn-primary"
            unelevated
            label="Export book"
            icon="note_add"
            color="primary"
            @click="exportBook"
          />
          <q-btn
            class="premium-btn-primary"
            unelevated
            label="Export outline"
            icon="note_add"
            color="primary"
            @click="exportOutline"
          />
          <q-btn
            class="premium-btn-primary"
            unelevated
            label="Export Images"
            color="primary"
            icon="photo_library"
            @click="exportImages"
          />
        </q-card-actions>
      </template>
    </PageCard>
  </PageLayout>
</template>

<script setup lang="ts">
import { computed, unref } from 'vue';
import { saveAs } from 'file-saver';
import { useRoute } from 'vue-router';
import { getBook, getChapter, listOutlines } from 'src/entities/book';
import {
  BookPreview,
  bookHtmlToDocx,
  bookOutlineHtmlToDocx,
  exportBookImages,
} from 'src/features/book-export';
import { barStyle, thumbStyle } from 'src/entities/setting';
import PageLayout from 'src/layouts/PageLayout.vue';
import PageCard from 'src/layouts/PageCard.vue';

const route = useRoute();

const book = await getBook(route.params.id as string);
const bookOutlines = unref(await listOutlines(route.params.id as string));
const outlines = computed(() => [
  bookOutlines.introduction,
  ...bookOutlines.outlines?.filter((chapter) => !chapter.isSection),
  bookOutlines.conclusion,
]);

async function exportBook() {
  const chapters = await Promise.all(
    outlines.value.map((outline) => getChapter(book.value.id, outline.id)),
  );
  bookHtmlToDocx(book.value, outlines.value, chapters.map(unref));
}

function exportOutline() {
  bookOutlineHtmlToDocx(book.value, outlines.value);
}

async function exportImages() {
  await exportBookImages(book.value, bookOutlines);
}
</script>
