<template>
  <Teleport to="#new-book-create">
    <q-btn-dropdown
      :class="isTopNavDefault ? '' : 'q-ml-xs'"
      :dense="isTopNavDefault"
      :stretch="isTopNavDefault"
      :color="!isTopNavDefault ? 'primary' : ''"
      :flat="isTopNavDefault"
      :rounded="!isTopNavDefault"
      :padding="!isTopNavDefault ? '13px' : ''"
      :loading="addingBook"
      icon="add"
      :disable="!isAuthor && !isAdmin && !canAddBook"
      label="New"
      no-wrap
    >
      <q-list>
        <q-item
          clickable
          v-close-popup
          @click="
            selectedBookCategory = 'non-fiction';
            newBookHelpPrompt = true;
          "
        >
          <q-item-section avatar>
            <q-icon name="collections_bookmark" color="primary" />
          </q-item-section>
          <q-item-section>
            <q-item-label>Non-Fiction</q-item-label>
          </q-item-section>
        </q-item>

        <q-item
          clickable
          v-close-popup
          @click="
            selectedBookCategory = 'autobiography';
            newBookHelpPrompt = true;
          "
        >
          <q-item-section avatar>
            <q-icon name="local_library" color="primary" />
          </q-item-section>
          <q-item-section>
            <q-item-label>Autobiography</q-item-label>
          </q-item-section>
        </q-item>

        <q-item
          clickable
          v-close-popup
          @click="
            selectedBookCategory = 'faithstory';
            newBookHelpPrompt = true;
          "
        >
          <q-item-section avatar>
            <q-icon name="diversity_1" color="primary" />
          </q-item-section>
          <q-item-section style="width: 120px">
            <q-item-label>Faith Story</q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </q-btn-dropdown>
  </Teleport>

  <PageLayout
    title="Let's start writing!"
    icon="book"
    subtitle="Unlock our tools and resources to transform your thoughts into a published story."
    :loading="loading"
    loading-label="Loading your profile..."
  >
    <div class="books-page q-pa-md q-pt-xl">
      <div class="books-header q-px-lg q-mb-lg">
        <div class="row justify-between items-center">
          <div
            class="col-xs-12 col-md-6 row justify-end items-center q-gutter-md"
          >
            <!-- Author selector for reviewers -->
            <div
              v-if="isReviewer && usersForReview.length"
              class="row items-center q-gutter-md"
            >
              <q-select
                outlined
                dense
                label="View"
                v-model="booksView"
                :options="viewOptions"
                class="books-view-select modern-input"
                bg-color="transparent"
              />

              <q-select
                outlined
                dense
                label="Select Author"
                v-model="toReview"
                :options="authorsOptions"
                class="author-select modern-input"
                emit-value
                map-options
                bg-color="transparent"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Notices -->
      <div v-if="!isAdmin && !isAuthor" class="q-px-lg q-mb-lg">
        <PageCard class="notice-card">
          <UnsubscribedNotice
            message="To access Manuscriptr, you need to have an active subscription. Follow the subscribe option to continue!"
          />
        </PageCard>
      </div>

      <div v-if="isReviewer && usersForReview.length" class="q-px-lg q-mb-lg">
        <PageCard class="notice-card">
          <ReviewerNotice
            message="You have been assigned the role of Reviewer. Please choose an author whose books you would like to review."
          />
        </PageCard>
      </div>

      <div class="books-container q-mt-md">
        <!-- Reviewer's books section -->
        <div
          v-if="
            isReviewer &&
            usersForReview.length &&
            ['Reviews', 'All'].includes(booksView)
          "
          class="q-mb-xl"
        >
          <div class="row items-stretch books-grid-container">
            <template v-for="(book, index) in booksByAuthor" :key="book.id">
              <!-- Shelf image for new rows on mobile/tablet only -->
              <div
                v-if="shouldShowShelfImageMyBooks(index)"
                class="col-12 shelf-image-container"
              >
                <q-img
                  src="images/ui/shelf.png"
                  class="shelf-image"
                  fit="fill"
                  no-spinner
                />
              </div>

              <section class="col-xs-12 col-sm-6 col-md-4 q-pa-md">
                <BookCardCover
                  :book="book"
                  @edit="setEditBook(book)"
                  @delete="deleteSelectedBook(book)"
                />
              </section>
            </template>
          </div>
        </div>

        <!-- User's books section -->
        <div v-if="['My Books', 'All'].includes(booksView)">
          <!-- Empty state -->
          <div
            v-if="books.length === 0"
            class="empty-state q-pa-xl text-center"
          >
            <PageCard class="empty-state-card">
              <q-icon
                name="auto_stories"
                size="80px"
                class="text-color-custom"
                :color="$q.dark.isActive ? 'white' : 'primary'"
              />
              <h4
                class="text-h5 q-mt-md text-color-custom"
                :class="$q.dark.isActive ? 'text-white' : 'text-primary'"
              >
                You don't have any books yet
              </h4>
              <p
                :class="$q.dark.isActive ? 'text-white' : 'text-primary'"
                class="text-color-custom"
              >
                Click the
                <q-btn
                  icon="add"
                  outline
                  flat
                  dense
                  label="New"
                  @click="newBookHelpPrompt = true"
                />
                button to create your first book
              </p>
            </PageCard>
          </div>

          <!-- Grid view -->
          <div
            v-else
            class="row items-stretch books-grid-container flex justify-center"
          >
            <template v-for="(book, index) in books" :key="book.id">
              <!-- Shelf image for new rows on mobile/tablet only -->
              <div
                v-if="shouldShowShelfImageMyBooks(index)"
                class="col-12 shelf-image-container"
              >
                <q-img
                  src="images/ui/shelf.png"
                  class="shelf-image"
                  fit="fill"
                  no-spinner
                />
              </div>

              <section
                class="col-xs-6 col-sm-6 col-md-4 col-lg-3 q-pa-md flex justify-center"
              >
                {{ book.id }}
                <BookCardCover
                  :is-create-book="index === 0"
                  @create-new-book="newBookHelpPrompt = true"
                  :book="book"
                  :default-cover-color="getBookCoverColor(index)"
                  :font-color="getBookFontColor(index)"
                  @edit="setEditBook(book)"
                  @delete="deleteSelectedBook(book)"
                />
                <BookCard
                  v-show="false"
                  :book="book"
                  @edit="setEditBook(book)"
                  @delete="deleteSelectedBook(book)"
                />
              </section>

              <div
                v-if="books.length - 1 === index"
                class="col-12 shelf-image-container"
              >
                <q-img
                  src="images/ui/shelf.png"
                  class="shelf-image"
                  fit="fill"
                  no-spinner
                />
              </div>
            </template>
          </div>
        </div>
      </div>
      <!-- Loading indicator -->
      <q-inner-loading
        :showing="showing"
        :color="$q.dark.isActive ? 'white' : 'primary'"
        label="Loading your books..."
        label-class="text-primary"
        label-style="font-size: 1.1em"
      >
        <div class="column items-center">
          <q-spinner-dots
            :color="$q.dark.isActive ? 'white' : 'primary'"
            size="3em"
          />
          <div class="q-mt-sm row items-center">
            <q-icon name="img:robot.png" class="q-mr-sm" size="md" />
            <span>Loading your books...</span>
          </div>
        </div>
      </q-inner-loading>
    </div>
  </PageLayout>

  <!-- New book dialog -->
  <q-dialog v-model="newBookHelpPrompt" persistent>
    <q-card>
      <q-card-section class="q-pa-none">
        <q-bar class="bg-primary text-white">
          <q-icon name="img:robot.png" />

          <div>Create a new book!</div>

          <q-space />

          <q-btn
            dense
            flat
            icon="close"
            @click="newBookHelpPrompt = false"
            v-if="!addingBook"
          >
            <q-tooltip class="bg-white text-primary">Close</q-tooltip>
          </q-btn>
        </q-bar>
      </q-card-section>
      <q-card-section>
        <p class="text-h4 text-primary">
          Welcome to Manuscriptr!
          <q-img src="robot.png" class="q-ma-sm float-right" width="100px" />
        </p>
        <p class="text-body1">
          {{ newBookDescription }}
        </p>
      </q-card-section>
      <q-separator />
      <q-card-actions>
        <q-btn
          flat
          :disable="addingBook"
          @click="
            addBook({});
            newBookHelpPrompt = false;
          "
        >
          Skip Manny!
        </q-btn>
        <q-btn color="primary" :loading="addingBook" @click="setEmptyBook()">
          Use Manny!
        </q-btn>
      </q-card-actions>
    </q-card>
  </q-dialog>

  <!-- Onboarding dialog -->
  <NewBookDialog
    v-if="selectedBookCategory === 'non-fiction'"
    v-model:initial-values="initialValues"
    v-model:initial-outlines="initialOutlines"
    :modal="onboardingActive"
    @completed="addBook"
    @updated="updateBookNow"
    @cancel="onboardingActive = false"
  />
  <NewFaithStoryFlow
    v-else-if="selectedBookCategory === 'faithstory'"
    v-model:initial-values="initialValues"
    v-model:initial-outlines="initialOutlines"
    @completed="addBook"
    :modal="onboardingActive"
    @updated="updateBookNow"
    @cancel="onboardingActive = false"
  />
  <NewAutoBioFlow
    v-else-if="selectedBookCategory === 'autobiography'"
    v-model:initial-values="initialValues"
    v-model:initial-outlines="initialOutlines"
    @completed="addBook"
    :modal="onboardingActive"
    @updated="updateBookNow"
    @cancel="onboardingActive = false"
  />
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted, onUnmounted, toRef } from 'vue';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import NewBookDialog from 'src/entities/book/ui/components/onboarding/nonfiction/NewBookFlow.vue';
import UnsubscribedNotice from 'src/entities/package/ui/UnsubscribedNotice.vue';
import {
  confirmDeletion,
  confirmOverrideText,
} from 'src/shared/lib/quasar-dialogs';

import {
  isAdmin,
  isAuthor,
  isReviewer,
  isReviewMode,
  loginAs,
  useAuthorStore,
  user,
} from 'src/entities/user';

import {
  type Book,
  BookCard,
  BookCardCover,
  createBook,
  createEmptyBook,
  listBooks,
  deleteBook,
  updateBook,
  listOutlines,
  deleteBookById,
  setNewBook,
  ChapterOutline,
} from 'src/entities/book';
import ReviewerNotice from 'src/entities/package/ui/ReviewerNotice.vue';
import { computedAsync } from '@vueuse/core/index';
import { ACTIONS, loggingService, PAGES } from 'src/entities/log';
import { isTopNavDefault } from 'src/entities/setting';
import {
  NewFaithStoryFlow,
  NewAutoBioFlow,
} from 'src/entities/book/ui/components/onboarding';
import PageLayout from 'src/layouts/PageLayout.vue';
import PageCard from 'src/layouts/PageCard.vue';

// Add view mode state
const viewMode = ref('grid');

// Local state variables
const addingBook = ref(false);
const onboardingActive = ref(false);
const newBookHelpPrompt = ref(false);
const initialValues = ref<Book | undefined>(undefined);
const initialOutlines = ref<ChapterOutline[] | undefined>(undefined);
const authorStore = useAuthorStore();
const selectedBookCategory = ref('non-fiction');

// Fetching the list of books for the current user
const userGetBooks = computed(() => loginAs.value || user.value!.uid);
const bookList = ref(await listBooks(userGetBooks.value, true));

// Create a computed property to filter books
const books = computed(() => {
  const booksList = bookList.value.filter((book) => !book?.deleted);
  // prepend a new book object with mission: 'new-book' on bookList

  return booksList;
});

// Router instance for navigation
const router = useRouter();

// Quasar instance for notification and dialog
const $q = useQuasar();

// Get the adding of new book description
const newBookDescription = computed(() => {
  switch (selectedBookCategory.value) {
    case 'autobiography':
      return 'Use Manny, "our AI" to write your Autobiography Book\'s  title, subtitle, and chapter outline or start writing!';
    case 'faithstory':
      return 'Use Manny, "our AI" to write your Faith Story Book\'s  title, subtitle, and chapter outline or start writing!';
    default:
      return `Use Manny, "our AI" to write your Non-Fiction Book's mission statement, title, subtitle,
          and chapter outline or start writing!`;
  }
});

// Add color arrays for cycling through the color combinations
const coverColors = ['#D66F37', '#F5E2AC', '#CBCBE6'];
const fontColors = ['#F6E1D5', '#D6703B', '#026897'];

// Functions to get colors based on index (cycles through the color arrays)
const getBookCoverColor = (index: number) => {
  return coverColors[index % coverColors.length];
};

const getBookFontColor = (index: number) => {
  return fontColors[index % fontColors.length];
};

/**
 * Determines if a shelf image should be shown before a book card
 * Only shows on mobile (every book) and tablet (every 2nd book)
 * @param index - The index of the current book
 */
const shouldShowShelfImageMyBooks = computed(() => {
  return (index: number) => {
    let position = 4;
    // assign the position based on the quasar grid
    if ($q.screen.xl) {
      position = 4;
    } else if ($q.screen.lg) {
      position = 4;
    } else if ($q.screen.md) {
      position = 3;
    } else if ($q.screen.sm) {
      position = 2;
    } else {
      position = 2;
    }
    if (index < position) return false;
    const res = index % position === 0;

    return res;
  };
});
// Fetching the list of books for the current user
const userId = user.value!.uid; // Get the user ID from the current user's value
const isInReview = computed(() => isReviewMode.value.length > 0); // Check if the review mode is active
const allowed_no_of_books = computedAsync(async () => {
  if (user.value?.uid) {
    const author = await authorStore.getAuthorWithoutSync(
      user.value?.uid as string,
    ); // Get the author's data using the user ID
    return author.allowed_books; // Return the number of allowed books for the author
  }
}, 4); // Default value is 4

// List of authors available for review
const usersForReview = isAdmin.value
  ? authorStore.listAllAuthorsForReview() // List all authors if the user is an admin
  : authorStore.listAuthorsForReview(user.value!.uid); // List authors for review based on the current user's ID

const toReview = ref(''); // Selected author for review
let booksByAuthor = ref([]); // List of books by the selected author
const showing = ref(false); // Indicates if the loading indicator should be shown
const booksView = ref(isReviewer.value ? 'Reviews' : 'My Books'); // Default view based on the user role
const viewOptions = ['All', 'My Books', 'Reviews']; // View options for the books
const bookFoundationAutosave = ref(true);

// Options for selecting authors in the dropdown
const authorsOptions = computed(() => {
  const userOptions = usersForReview.value.map((user) => ({
    label: user.name || user.email, // Use the user's name or email as the label
    value: user.id, // Use the user's ID as the value
  }));
  if (!toReview.value && userOptions.length) {
    toReview.value = userOptions[0].value as string; // Default to the second user if no review is selected
    booksByAuthor = toReview.value ? listBooks(toReview.value) : ref([]); // Fetch the books for the selected author
  }
  return userOptions; // Return the default option followed by the user options
});

if (isReviewMode.value) {
  toReview.value = isReviewMode.value; // Set the review mode value to the selected review
  booksByAuthor = toReview.value ? listBooks(toReview.value) : ref([]); // Fetch the books for the selected author
}

// Watch for changes in the selected author for review
watch(toReview, (authorSelected) => {
  showing.value = true; // Show the loading indicator
  setTimeout(() => {
    booksByAuthor = authorSelected ? listBooks(authorSelected) : ref([]); // Fetch the books for the selected author
    showing.value = false; // Hide the loading indicator
  }, 500);

  if (authorSelected) {
    isReviewMode.value = toReview.value; // Set the review mode value
  } else {
    isReviewMode.value = ''; // Clear the review mode value
  }
});

// Watch for changes in the view of the books
watch(booksView, (bView) => {
  showing.value = true; // Show the loading indicator
  setTimeout(() => {
    showing.value = false; // Hide the loading indicator
  }, 500);
});

// Define the module or component name
const MODULE_NAME = 'book';

/**
 * Function to check if the user can add more books
 */
const canAddBook = () => {
  return !(
    isAdmin.value ||
    (isAuthor.value && books.value.length < allowed_no_of_books.value)
  );
};

/**
 * Function to set empty book once book foundation is clicked
 */
const setEmptyBook = async () => {
  onboardingActive.value = false;
  bookFoundationAutosave.value = true;
  addingBook.value = true;
  const newBook = await setNewBook(selectedBookCategory.value);
  initialValues.value = newBook;
  newBookHelpPrompt.value = false;
  initialOutlines.value = [];
  onboardingActive.value = true;
  addingBook.value = false;
};

const setEditBook = async (book: Book) => {
  onboardingActive.value = false;
  selectedBookCategory.value = book.category;
  bookFoundationAutosave.value = true;
  initialValues.value = book;
  const editBookOutlines = await getBookOutlines(book.id);
  initialOutlines.value = editBookOutlines ? editBookOutlines : [];

  onboardingActive.value = true;
};

/**
 * Function to get outlines for a specific book
 * @param bookId - The ID of the book to get outlines for
 */
const getBookOutlines = async (bookId: string) => {
  const outlines = await listOutlines(bookId);
  return outlines.value.outlines || [];
};

/**
 * Function to add a new book
 * @param prefilled - Optional prefilled data for the book
 */
const addBook = async (prefilled = {}) => {
  addingBook.value = true;
  onboardingActive.value = false;

  try {
    const book = await createBook({
      ...createEmptyBook(selectedBookCategory.value),
      authorId: user.value!.uid,
      ...prefilled,
    });
    await loggingService.logAction(
      PAGES.BOOKLIST,
      ACTIONS.CREATE,
      `Created a new book: ${book.title || 'Untitled book'}`,
      MODULE_NAME,
      book.id,
    );
    if (book?.id) await router.push(`/books/${book.id}`);
  } finally {
    addingBook.value = false;
  }
};

/**
 * Function to update an existing book
 * @param id - The ID of the book to update
 * @param newData - The new data to update the book with
 */
const updateBookConfirm = async (id: string, newData: Partial<Book>) => {
  if (initialValues.value && initialValues.value.id) {
    const shouldSave = await confirmOverrideText($q, {
      message: `Are you sure you want to save the changes?`,
    });
    if (!shouldSave) {
      return;
    }
    await updateBookNow(id, newData);
  }
};
const updateBookNow = async (id: string, newData: Partial<Book>) => {
  await updateBook(id, newData);
  initialValues.value = undefined;
  initialOutlines.value = [];
  onboardingActive.value = false;
  $q.notify('Book updated successfully!');
};

/**
 * Function to delete a book
 * @param book - The book to delete
 */
const deleteSelectedBook = (book: Book) => {
  showing.value = true;
  confirmDeletion($q)
    .then(() => {
      deleteBookById(book.id);
    })
    .then(async () => {
      console.log('Delete Book: ', book.id);
      deleteBook(book.id);
      await loggingService.logAction(
        PAGES.BOOKLIST,
        ACTIONS.DELETE,
        `Book is deleted: ${book.title || 'Untitled book'}`,
        MODULE_NAME,
        book.id,
      );
    })
    .catch(() => {})
    .finally(() => (showing.value = false));
};
</script>

<style lang="scss" scoped>
.books-page {
  .books-grid-container {
    position: relative;

    .shelf-image-container {
      padding: 0 16px;
      margin-bottom: 16px; // Increased from 8px

      // On mobile, show before every book
      @media (max-width: 599px) {
        .shelf-image {
          margin-bottom: 8px;
        }
      }

      // On tablet, show before every 2nd book
      @media (min-width: 600px) and (max-width: 1023px) {
        .shelf-image {
          margin-bottom: 12px; // Increased spacing
        }
      }
    }

    .shelf-image {
      width: 145%; // Default for lg and xl
      object-fit: contain;
      opacity: 0.7;
      position: relative;
      left: -22.5%; // Default positioning for 150% width

      // Smaller screens (xs, sm, md) - 125% width
      @media (max-width: 1123px) {
        width: 125%;
        left: -12.5%; // Adjusted positioning for 125% width
      }

      // Ensure image doesn't stretch
      img {
        width: 100%;
        height: auto;
        max-height: 100px; // Kept at 100px for bigger shelf
        object-fit: contain;
      }

      // Dark mode adjustments
      body.body--dark & {
        opacity: 0.5;
        filter: brightness(0.8);
      }
    }
  }

  // ... rest of the styles remain the same
  .books-header {
    margin-bottom: 2rem;
  }

  .section-title {
    border-left: 4px solid var(--q-primary);
    padding-left: 12px;
  }

  .empty-state {
    background: transparent;
    border-radius: 16px;
    margin: 0 16px;

    .empty-state-card {
      background: rgba(var(--q-primary-rgb), 0.03);

      body.body--dark & {
        background: rgba(var(--q-primary-rgb), 0.1);
      }
    }
  }

  .notice-card {
    background: rgba(var(--q-warning-rgb), 0.1);
    border: 1px solid rgba(var(--q-warning-rgb), 0.3);

    body.body--dark & {
      background: rgba(var(--q-warning-rgb), 0.15);
      border-color: rgba(var(--q-warning-rgb), 0.4);
    }
  }

  .view-toggle {
    .q-btn-toggle {
      border-radius: 12px;
      overflow: hidden;

      .q-btn {
        padding: 8px 16px;
        min-height: 40px;
      }
    }
  }

  .modern-input {
    :deep(.q-field__control) {
      border-radius: 8px;
      height: 40px;

      body.body--dark & {
        background: rgba(255, 255, 255, 0.05);
        border-color: rgba(255, 255, 255, 0.2);
      }
    }
  }

  .books-view-select,
  .author-select {
    border-radius: 8px;

    .q-field__control {
      height: 40px;
    }
  }
}

// List view styling
.q-list.rounded-borders {
  border-radius: 16px;
  overflow: hidden;
}

// Grid layout improvements
.row.items-stretch {
  margin: 0 -8px;

  section {
    padding: 12px;
    padding-bottom: 0px;
    // Ensure sections are properly centered on the shelf
    position: relative;
    // z-index: 1; // Place book cards above the shelf
  }
}

.premium-btn {
  border-radius: 8px;
  padding: 8px 24px;
  font-weight: 600;
  text-transform: none;
  font-size: 14px;
}

#books .q-item__section--main ~ .q-item__section--side {
  display: none;
}

#books .book-heading {
  display: flex;
  flex-direction: row !important;
  justify-content: flex-start !important;
  align-items: center;

  .write-button {
    margin-left: 16px;
  }
}

@media (min-width: 600px) and (max-width: 1023px) {
  .books-grid-container {
    .shelf-image-container:nth-of-type(4n-3) {
      // Show shelf before 1st and 3rd books in tablet layout (every new row)
      display: block;
    }
  }
}

// List view styling
.q-list.rounded-borders {
  border-radius: 16px;
  overflow: hidden;
}

// Grid layout improvements
.row.items-stretch {
  margin: 0 -8px;

  section {
    padding: 12px;
    // Ensure sections are properly centered on the shelf
    position: relative;
    // z-index: 1; // Place book cards above the shelf
  }
}

.premium-btn {
  border-radius: 8px;
  padding: 8px 24px;
  font-weight: 600;
  text-transform: none;
  font-size: 14px;
}

#books .q-item__section--main ~ .q-item__section--side {
  display: none;
}

#books .book-heading {
  display: flex;
  flex-direction: row !important;
  justify-content: flex-start !important;
  align-items: center;

  .write-button {
    margin-left: 16px;
  }
}

@media (min-width: 600px) and (max-width: 1023px) {
  .books-grid-container {
    .shelf-image-container:nth-of-type(4n-3) {
      // Show shelf before 1st and 3rd books in tablet layout (every new row)
      display: block;
    }
  }
}

// List view styling
.q-list.rounded-borders {
  border-radius: 16px;
  overflow: hidden;
}

// Grid layout improvements
.row.items-stretch {
  margin: 0 -8px;

  section {
    padding: 12px;
    // Ensure sections are properly centered on the shelf
    position: relative;
    // z-index: 1; // Place book cards above the shelf
  }
}

.premium-btn {
  border-radius: 8px;
  padding: 8px 24px;
  font-weight: 600;
  text-transform: none;
  font-size: 14px;
}

#books .q-item__section--main ~ .q-item__section--side {
  display: none;
}

#books .book-heading {
  display: flex;
  flex-direction: row !important;
  justify-content: flex-start !important;
  align-items: center;

  .write-button {
    margin-left: 16px;
  }
}

@media (min-width: 600px) and (max-width: 1023px) {
  .books-grid-container {
    .shelf-image-container:nth-of-type(4n-3) {
      // Show shelf before 1st and 3rd books in tablet layout (every new row)
      display: block;
    }
  }
}
</style>
