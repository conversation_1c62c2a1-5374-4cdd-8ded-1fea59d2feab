<template>
  <PageLayout
    title="Book Category Manager"
    subtitle="Organize your book categories and questions"
    icon="category"
    :loading="loadingCategories || loading || loadingSubcategories"
    loading-label="Loading categories..."
  >
    <PageCard elevated>
      <div class="row">
        <!-- Category List Column -->
        <div class="col-12 col-md-4 category-sidebar">
          <div class="q-pa-md">
            <div class="row justify-between items-center q-mb-md">
              <div class="text-h6">Categories</div>
              <q-btn
                color="primary"
                icon="add"
                round
                dense
                @click="createCategory"
                :loading="savingCategory"
              >
                <q-tooltip>Add Category</q-tooltip>
              </q-btn>
            </div>

            <q-list bordered separator class="category-list rounded-borders">
              <template v-if="categories.length > 0">
                <q-item
                  v-for="category in categories"
                  :key="category.id"
                  clickable
                  :active="activeCategory?.id === category.id"
                  @click="setActiveCategory(category.id)"
                  v-ripple
                >
                  <q-item-section avatar>
                    <q-avatar
                      :color="$q.dark.isActive ? 'primary' : 'primary'"
                      text-color="white"
                    >
                      <q-icon :name="category.icon || 'category'" />
                    </q-avatar>
                  </q-item-section>
                  <q-item-section>
                    <q-item-label class="text-weight-medium">{{
                      category.title
                    }}</q-item-label>
                    <q-item-label caption>
                      <q-icon name="help_outline" size="xs" class="q-mr-xs" />
                      {{ questions?.length || 0 }} questions
                    </q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <div class="row items-center">
                      <q-btn
                        flat
                        round
                        dense
                        icon="edit"
                        color="primary"
                        @click.stop="editCategory(category)"
                        :loading="savingCategory"
                      >
                        <q-tooltip>Edit Category</q-tooltip>
                      </q-btn>
                      <q-btn
                        flat
                        round
                        dense
                        icon="delete"
                        color="negative"
                        @click.stop="deleteCategory(category)"
                        :loading="deletingCategory"
                        class="q-ml-xs"
                      >
                        <q-tooltip>Delete Category</q-tooltip>
                      </q-btn>
                    </div>
                  </q-item-section>
                </q-item>
              </template>
              <template v-else>
                <q-item class="text-center">
                  <q-item-section>
                    <div class="empty-list-message q-py-md">
                      <q-icon
                        name="category_off"
                        size="48px"
                        :color="$q.dark.isActive ? 'grey-6' : 'grey-5'"
                      />
                      <div
                        class="text-subtitle1 q-mt-sm"
                        :class="
                          $q.dark.isActive ? 'text-grey-4' : 'text-grey-8'
                        "
                      >
                        No categories yet
                      </div>
                      <div
                        class="text-caption q-mb-sm"
                        :class="
                          $q.dark.isActive ? 'text-grey-5' : 'text-grey-7'
                        "
                      >
                        Create your first category to get started
                      </div>
                    </div>
                  </q-item-section>
                </q-item>
              </template>

              <q-inner-loading
                :showing="loadingCategories || loading || loadingSubcategories"
                :color="$q.dark.isActive ? 'white' : 'primary'"
                label-class="text-primary"
                label-style="font-size: 1.1em"
                style="z-index: 1"
              >
                <q-spinner-dots
                  :color="$q.dark.isActive ? 'white' : 'primary'"
                  size="2em"
                />
                <div class="align-center">
                  <q-icon name="img:robot.png" class="q-ml-xs" size="xl" />
                  {{
                    loadingCategories
                      ? 'Loading Categories...'
                      : loadingSubcategories
                      ? 'Loading Subcategories...'
                      : savingCategory || savingSubcategory || savingQuestion
                      ? 'Saving...'
                      : deletingCategory || deletingSubcategory
                      ? 'Deleting...'
                      : loadingQuestions
                      ? 'Loading Questions...'
                      : 'Loading...'
                  }}
                </div>
              </q-inner-loading>
            </q-list>
          </div>
        </div>

        <!-- Category Details Column -->
        <div class="col-12 col-md-8 category-details">
          <div v-if="activeCategory" class="q-pa-md">
            <div class="row items-center q-mb-md">
              <q-avatar
                color="primary"
                text-color="white"
                size="42px"
                class="q-mr-md"
              >
                <q-icon :name="activeCategory.icon || 'category'" size="28px" />
              </q-avatar>
              <div>
                <div class="text-h5 text-weight-medium">
                  {{ activeCategory.title }}
                </div>
                <div
                  class="text-caption"
                  :class="$q.dark.isActive ? 'text-grey-4' : 'text-grey-7'"
                >
                  <q-icon name="help_outline" size="xs" class="q-mr-xs" />
                  {{ questions.length }} questions in this category
                </div>
              </div>
            </div>
            <q-separator class="q-mb-md" />

            <!-- Subcategories Section -->
            <div class="row justify-between items-center q-mb-md">
              <div class="text-subtitle1 text-weight-medium">
                <q-icon name="folder" class="q-mr-xs" />
                Subcategories
              </div>
              <q-btn
                color="amber"
                label="Add Subcategory"
                icon="add"
                unelevated
                @click="showSubcategoryInForm"
                :loading="savingSubcategory"
              />
            </div>

            <div v-if="subcategories.length > 0">
              <q-list
                bordered
                separator
                class="rounded-borders subcategory-list"
              >
                <q-item
                  v-for="subcategory in subcategories"
                  :key="subcategory.id"
                  class="subcategory-item"
                  clickable
                  :active="selectedSubcategory?.id === subcategory.id"
                  @click="selectSubcategory(subcategory)"
                  v-ripple
                >
                  <q-item-section avatar>
                    <q-avatar color="amber" text-color="white">
                      <q-icon :name="subcategory.icon || 'folder'" />
                    </q-avatar>
                  </q-item-section>
                  <q-item-section>
                    <q-item-label class="text-weight-medium">
                      {{ subcategory.name }}
                    </q-item-label>
                    <q-item-label caption>
                      <q-icon name="help_outline" size="xs" class="q-mr-xs" />
                      {{ getQuestionsCountBySubcategory(subcategory.id) }}
                      questions
                    </q-item-label>
                  </q-item-section>
                  <q-item-section
                    side
                    v-if="
                      subcategory.name !== 'Uncategorized' &&
                      subcategory.name !== 'Default'
                    "
                  >
                    <div class="row items-center">
                      <q-btn
                        flat
                        round
                        dense
                        icon="edit"
                        color="amber"
                        @click.stop="editSubcategory(subcategory)"
                        :loading="savingSubcategory"
                      >
                        <q-tooltip>Edit Subcategory</q-tooltip>
                      </q-btn>
                      <q-btn
                        flat
                        round
                        dense
                        icon="delete"
                        color="negative"
                        @click.stop="deleteSubcategory(subcategory.id)"
                        :loading="deletingSubcategory"
                        class="q-ml-xs"
                      >
                        <q-tooltip>Delete Subcategory</q-tooltip>
                      </q-btn>
                    </div>
                  </q-item-section>
                </q-item>
              </q-list>
            </div>
            <div v-else class="text-center q-pa-lg empty-state">
              <q-icon
                name="folder_off"
                size="48px"
                :color="$q.dark.isActive ? 'grey-6' : 'grey-5'"
              />
              <div
                class="text-h6 q-mt-md"
                :class="$q.dark.isActive ? 'text-grey-4' : 'text-grey-8'"
              >
                No subcategories yet
              </div>
              <div
                class="text-caption q-mb-md"
                :class="$q.dark.isActive ? 'text-grey-5' : 'text-grey-7'"
              >
                Create subcategories to organize your questions
              </div>
              <q-btn
                color="amber"
                label="Add Subcategory"
                icon="add"
                unelevated
                @click="showSubcategoryInForm"
                :loading="savingSubcategory"
              />
            </div>

            <!-- Questions Section -->
            <div>
              <div class="row justify-between items-center q-mb-md q-mt-lg">
                <div class="text-subtitle1 text-weight-medium">
                  <q-icon name="quiz" class="q-mr-xs" />
                  Questions
                  <span v-if="selectedSubcategory" class="text-caption q-ml-sm">
                    in
                    <q-chip
                      size="sm"
                      class="chip-padding"
                      color="amber"
                      text-color="white"
                      dense
                    >
                      <q-icon
                        :name="selectedSubcategory.icon"
                        size="xs"
                        class="q-mr-xs"
                      />
                      {{ selectedSubcategory.name }}
                    </q-chip>
                  </span>
                </div>
                <q-btn
                  color="primary"
                  label="Add Question"
                  icon="add"
                  unelevated
                  v-if="
                    selectedSubcategory &&
                    selectedSubcategory.name !== 'Default'
                  "
                  @click="showQuestionForm"
                  :disable="!selectedSubcategory"
                  :loading="savingQuestion"
                >
                  <q-tooltip v-if="!selectedSubcategory">
                    Select a subcategory first
                  </q-tooltip>
                </q-btn>
              </div>

              <div
                v-if="!selectedSubcategory"
                class="text-center q-pa-lg empty-state"
              >
                <q-icon
                  name="subdirectory_arrow_right"
                  size="48px"
                  color="amber"
                />
                <div
                  class="text-h6 q-mt-md"
                  :class="$q.dark.isActive ? 'text-grey-4' : 'text-grey-8'"
                >
                  Select a subcategory
                </div>
                <div
                  class="text-caption q-mb-md"
                  :class="$q.dark.isActive ? 'text-grey-5' : 'text-grey-7'"
                >
                  Choose a subcategory to view or add questions
                </div>
              </div>

              <div v-else-if="filteredQuestions.length > 0">
                <q-list
                  bordered
                  separator
                  class="rounded-borders question-list"
                >
                  <q-item
                    v-for="question in filteredQuestions"
                    :key="question.id"
                    class="question-item q-py-md"
                  >
                    <q-item-section avatar>
                      <q-avatar
                        :color="answerElementColor[question.answerElement]"
                        text-color="white"
                      >
                        <q-icon name="help" />
                      </q-avatar>
                    </q-item-section>
                    <q-item-section>
                      <q-item-label class="text-weight-medium">{{
                        question.question
                      }}</q-item-label>
                      <q-item-label
                        caption
                        v-if="question.description"
                        class="q-mt-xs"
                      >
                        {{ question.description }}
                      </q-item-label>
                      <div class="row q-mt-sm">
                        <q-chip
                          size="sm"
                          :color="answerElementColor[question.answerElement]"
                          text-color="white"
                          dense
                          class="q-mr-sm chip-padding"
                        >
                          <q-icon
                            left
                            :name="getAnswerElementIcon(question.answerElement)"
                            size="xs"
                          />
                          {{ question.answerElement }}
                        </q-chip>
                        <q-chip
                          v-if="question.hasAI"
                          size="sm"
                          color="primary"
                          text-color="white"
                          dense
                          class="chip-padding"
                        >
                          <q-icon left name="img:robot.png" size="xs" />
                          Manny
                        </q-chip>
                      </div>
                    </q-item-section>
                    <q-item-section side>
                      <div class="row items-center">
                        <q-btn
                          flat
                          round
                          dense
                          icon="edit"
                          color="primary"
                          @click="editQuestion(question)"
                        >
                          <q-tooltip>Edit Question</q-tooltip>
                        </q-btn>
                        <q-btn
                          flat
                          round
                          dense
                          icon="delete"
                          color="negative"
                          v-if="selectedSubcategory.name !== 'Default'"
                          @click="deleteQuestion(question)"
                          class="q-ml-xs"
                        >
                          <q-tooltip>Delete Question</q-tooltip>
                        </q-btn>
                      </div>
                    </q-item-section>
                  </q-item>
                </q-list>
              </div>
              <div v-else class="text-center q-pa-lg empty-state">
                <q-icon
                  name="quiz_off"
                  size="64px"
                  :color="$q.dark.isActive ? 'grey-6' : 'grey-5'"
                />
                <div
                  class="text-h6 q-mt-md"
                  :class="$q.dark.isActive ? 'text-grey-4' : 'text-grey-8'"
                >
                  No questions in this subcategory
                </div>
                <div
                  class="text-caption q-mb-md"
                  :class="$q.dark.isActive ? 'text-grey-5' : 'text-grey-7'"
                >
                  Add questions to help guide book creation
                </div>
                <q-btn
                  color="primary"
                  label="Add Question"
                  icon="add"
                  unelevated
                  @click="showQuestionForm"
                />
              </div>
            </div>
          </div>
          <div v-else class="no-category q-pa-xl">
            <q-card flat class="no-category-card text-center">
              <q-card-section>
                <q-icon
                  name="category"
                  size="64px"
                  :color="$q.dark.isActive ? 'grey-6' : 'grey-5'"
                />
                <div
                  class="text-h6 q-mt-md"
                  :class="$q.dark.isActive ? 'text-grey-4' : 'text-grey-8'"
                >
                  {{
                    categories.length > 0
                      ? 'Select a category'
                      : 'No categories yet'
                  }}
                </div>
                <div
                  class="text-caption q-mb-md"
                  :class="$q.dark.isActive ? 'text-grey-5' : 'text-grey-7'"
                >
                  {{
                    categories.length > 0
                      ? 'to view or manage questions'
                      : 'Create your first category to get started'
                  }}
                </div>
                <q-btn
                  v-if="categories.length === 0"
                  color="primary"
                  label="Add Category"
                  icon="add"
                  unelevated
                  @click="createCategory"
                  class="q-mt-sm"
                />
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
    </PageCard>

    <!-- Category Form Dialog -->
    <q-dialog v-model="showCategoryForm" persistent>
      <PageCard style="min-width: 400px" class="category-form-dialog">
        <template #header>
          <div class="text-h6 text-white">
            {{ editingCategory ? 'Edit' : 'Add' }} Category
          </div>
        </template>

        <q-form @submit="saveCategory" class="q-gutter-md q-pa-md">
          <q-input
            v-model="categoryForm.title"
            label="Title"
            :rules="[(val) => !!val || 'Title is required']"
            class="modern-input"
            outlined
            autofocus
          />
          <q-select
            v-model="categoryForm.icon"
            :options="iconOptions"
            label="Icon"
            :rules="[(val) => !!val || 'Icon is required']"
            class="modern-input"
            outlined
            emit-value
            map-options
          >
            <template v-slot:option="scope">
              <q-item v-bind="scope.itemProps">
                <q-item-section avatar>
                  <q-icon :name="scope.opt.value" />
                </q-item-section>
                <q-item-section>{{ scope.opt.label }}</q-item-section>
              </q-item>
            </template>
            <template v-slot:selected>
              <div class="row items-center">
                <q-icon :name="categoryForm.icon" class="q-mr-sm" />
                {{ categoryForm.icon }}
              </div>
            </template>
          </q-select>
        </q-form>

        <template #footer>
          <q-card-actions align="right" class="q-pa-md">
            <q-btn
              flat
              label="Cancel"
              :disable="savingCategory"
              color="grey-7"
              v-close-popup
            />
            <q-btn
              unelevated
              label="Save"
              color="primary"
              @click="saveCategory"
              :loading="savingCategory"
            />
          </q-card-actions>
        </template>
      </PageCard>
    </q-dialog>

    <!-- Subcategory Form Dialog -->
    <q-dialog v-model="showSubcategoryForm" persistent>
      <PageCard style="min-width: 400px" class="subcategory-form-dialog">
        <template #header>
          <div class="text-h6 text-white">
            {{ editingSubcategory ? 'Edit' : 'Add' }} Subcategory
          </div>
        </template>

        <q-form @submit="saveSubcategory" class="q-gutter-md q-pa-md">
          <q-input
            v-model="subcategoryForm.name"
            label="Name"
            :rules="[(val) => !!val || 'Name is required']"
            class="modern-input"
            outlined
            autofocus
          >
            <template v-slot:prepend>
              <q-icon name="folder" color="amber" />
            </template>
          </q-input>
          <q-select
            v-model="subcategoryForm.icon"
            :options="folderIconOptions"
            label="Icon"
            :rules="[(val) => !!val || 'Icon is required']"
            class="modern-input"
            outlined
            emit-value
            map-options
          >
            <template v-slot:option="scope">
              <q-item v-bind="scope.itemProps">
                <q-item-section avatar>
                  <q-icon :name="scope.opt.value" color="amber" />
                </q-item-section>
                <q-item-section>{{ scope.opt.label }}</q-item-section>
              </q-item>
            </template>
            <template v-slot:selected>
              <div class="row items-center">
                <q-icon
                  :name="subcategoryForm.icon"
                  color="amber"
                  class="q-mr-sm"
                />
                {{ subcategoryForm.icon }}
              </div>
            </template>
          </q-select>
        </q-form>

        <template #footer>
          <q-card-actions align="right" class="q-pa-md">
            <q-btn flat label="Cancel" color="grey-7" v-close-popup />
            <q-btn
              unelevated
              label="Save"
              color="amber"
              text-color="white"
              @click="saveSubcategory"
            />
          </q-card-actions>
        </template>
      </PageCard>
    </q-dialog>

    <!-- Question Form Dialog -->
    <q-dialog v-model="showQuestionDialog" persistent>
      <PageCard
        style="
          min-width: 700px;
          max-height: 90vh;
          display: flex;
          flex-direction: column;
        "
        class="premium-card"
      >
        <template #header>
          <div class="text-h6 text-white">
            {{ editingQuestion ? 'Edit Question' : 'Create New Question' }}
            <span v-if="selectedSubcategory" class="text-caption q-ml-sm">
              in {{ selectedSubcategory.name }}
            </span>
          </div>
        </template>

        <q-scroll-area style="flex: 1; overflow-y: auto; padding-bottom: 70px">
          <QuestionForm
            v-model="questionForm"
            :available-variables="categoryQuestionVariables"
            :subcategory-options="subcategoryOptions"
            @submit="saveQuestion"
          />
        </q-scroll-area>

        <template #footer>
          <q-card-actions align="right" class="q-pa-md">
            <q-btn
              flat
              label="Cancel"
              color="grey-7"
              @click="closeQuestionDialog"
            />
            <q-btn
              unelevated
              label="Save"
              color="primary"
              @click="saveQuestion"
            />
          </q-card-actions>
        </template>
      </PageCard>
    </q-dialog>
  </PageLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import { storeToRefs } from 'pinia';
import { useCategoryStore } from 'src/entities/book-category/store/useCategoryStore';
import { useQuestionStore } from 'src/entities/question/store/useQuestionStore';
import {
  Question,
  AIOptions,
  AnswerElement,
} from 'src/entities/question/model';
import { QuestionForm } from 'src/entities/question';
import { BookCategory, Subcategory } from 'src/entities/book-category';
import PageLayout from 'src/layouts/PageLayout.vue';
import PageCard from 'src/layouts/PageCard.vue';

const $q = useQuasar();
const categoryStore = useCategoryStore();
const questionStore = useQuestionStore();
const { categories, activeCategory, loading } = storeToRefs(categoryStore);
const { questions, subcategories } = storeToRefs(questionStore);

// Form state for categories
const showCategoryForm = ref(false);
const editingCategory = ref<string | null>(null);
const categoryForm = ref({
  title: '',
  icon: 'category',
});

// Form state for subcategories
const showSubcategoryForm = ref(false);
const editingSubcategory = ref<Subcategory | null>(null);
const subcategoryForm = ref({
  name: '',
  icon: 'folder',
});

// Form state for questions
const showQuestionDialog = ref(false);
const editingQuestion = ref<string | null>(null);
const questionForm = ref<Question>({} as Question);

// Default AI options
const defaultAIOptions = ref<AIOptions>({
  temperature: 0.7,
  maxTokens: 500,
  topP: 1,
  frequencyPenalty: 0,
  presencePenalty: 0,
});

// Selected subcategory state
const selectedSubcategory = ref<Subcategory | null>(null);

// Add loading state for different actions
const loadingCategories = ref(false);
const loadingSubcategories = ref(false);
const loadingQuestions = ref(false);
const savingCategory = ref(false);
const savingSubcategory = ref(false);
const savingQuestion = ref(false);
const deletingCategory = ref(false);
const deletingSubcategory = ref(false);
const deletingQuestion = ref(false);

// Rest of the script would include all the methods from the original file
// with the same logic but adapted for dark mode and the new layout structure

// Select a subcategory
function selectSubcategory(subcategory: Subcategory) {
  selectedSubcategory.value = subcategory;
}

// Computed property for filtered questions based on selected subcategory
const filteredQuestions = computed(() => {
  if (!selectedSubcategory.value) return [];
  loadingQuestions.value = true;
  const filtered = questions.value.filter(
    (q) => q.subcategoryId === selectedSubcategory.value?.id,
  );
  loadingQuestions.value = false;
  return filtered;
});

// Get questions count by subcategory
function getQuestionsCountBySubcategory(subcategoryId: string): number {
  return questions.value.filter((q) => q.subcategoryId === subcategoryId)
    .length;
}

// Icon options for categories
const iconOptions = [
  { label: 'Category', value: 'category' },
  { label: 'Book', value: 'book' },
  { label: 'Library', value: 'local_library' },
  { label: 'School', value: 'school' },
  { label: 'Science', value: 'science' },
  { label: 'History', value: 'history' },
  { label: 'Business', value: 'business' },
  { label: 'Psychology', value: 'psychology' },
  { label: 'Health', value: 'health_and_safety' },
  { label: 'Travel', value: 'travel_explore' },
  { label: 'Food', value: 'restaurant' },
  { label: 'Sports', value: 'sports' },
  { label: 'Art', value: 'palette' },
  { label: 'Music', value: 'music_note' },
  { label: 'Technology', value: 'devices' },
  { label: 'Nature', value: 'nature' },
];

// Icon options for folders/subcategories
const folderIconOptions = [
  { label: 'folder', value: 'folder' },
  { label: 'folder_open', value: 'folder_open' },
  { label: 'folder_shared', value: 'folder_shared' },
  { label: 'folder_special', value: 'folder_special' },
  { label: 'folder_zip', value: 'folder_zip' },
  { label: 'folder_off', value: 'folder_off' },
  ...iconOptions,
];

// Colors for answer elements
const answerElementColor = ref<Record<AnswerElement, string>>({
  text: 'blue',
  textarea: 'indigo',
  select: 'green',
  options: 'orange',
  texteditor: 'purple',
  button: 'red',
});

// Helper function to get icon for answer element
function getAnswerElementIcon(answerElement: string): string {
  const icons = {
    text: 'short_text',
    textarea: 'notes',
    select: 'arrow_drop_down_circle',
    options: 'radio_button_checked',
    texteditor: 'text_fields',
    button: 'img:robot.png',
  };
  return icons[answerElement] || 'help';
}

// Get unique subcategories for dropdown
const subcategoryOptions = computed(() => {
  const subcategories = new Set<string>();
  questions.value.forEach((question) => {
    if (question.subcategory) {
      subcategories.add(question.subcategory);
    }
  });
  return Array.from(subcategories);
});

// Variables available for AI prompts in questions
const categoryQuestionVariables = computed(() => {
  // Return filtered questions that can be used as variables
  return questions.value.filter((q) => q.inBook === false);
});

// Placeholder functions - you would need to implement these based on your existing logic
const createCategory = () => {
  editingCategory.value = null;
  categoryForm.value = { title: '', icon: 'category' };
  showCategoryForm.value = true;
};

const editCategory = (category: BookCategory) => {
  editingCategory.value = category.id;
  categoryForm.value = {
    title: category.title,
    icon: category.icon || 'category',
  };
  showCategoryForm.value = true;
};

const deleteCategory = async (category: BookCategory) => {
  // Implementation for deleting category
};

const saveCategory = async () => {
  // Implementation for saving category
};

const setActiveCategory = async (categoryId: string) => {
  await categoryStore.setActiveCategory(categoryId);
  // Additional logic for setting active category
};

const showSubcategoryInForm = () => {
  editingSubcategory.value = null;
  subcategoryForm.value = { name: '', icon: 'folder' };
  showSubcategoryForm.value = true;
};

const editSubcategory = (subcategory: Subcategory) => {
  editingSubcategory.value = subcategory;
  subcategoryForm.value = {
    name: subcategory.name,
    icon: subcategory.icon || 'folder',
  };
  showSubcategoryForm.value = true;
};

const deleteSubcategory = async (subcategoryId: string) => {
  // Implementation for deleting subcategory
};

const saveSubcategory = async () => {
  // Implementation for saving subcategory
};

const showQuestionForm = () => {
  editingQuestion.value = null;
  questionForm.value = {} as Question;
  showQuestionDialog.value = true;
};

const editQuestion = (question: Question) => {
  editingQuestion.value = question.id;
  questionForm.value = question;
  showQuestionDialog.value = true;
};

const deleteQuestion = async (question: Question) => {
  // Implementation for deleting question
};

const saveQuestion = async () => {
  // Implementation for saving question
};

const closeQuestionDialog = () => {
  showQuestionDialog.value = false;
};

onMounted(async () => {
  loadingCategories.value = true;
  try {
    await categoryStore.fetchCategories();
    if (categories.value.length > 0 && !activeCategory.value) {
      await setActiveCategory(categories.value[0].id);
    }
  } catch (error) {
    console.error('Error loading categories:', error);
    $q.notify({
      color: 'negative',
      message: 'Failed to load categories',
      icon: 'error',
      position: 'top',
    });
  } finally {
    loadingCategories.value = false;
  }
});
</script>

<style scoped lang="scss">
.category-sidebar {
  border-right: 1px solid rgba(var(--q-primary-rgb), 0.1);
  background: rgba(var(--q-primary-rgb), 0.02);
  padding-right: 0;

  body.body--dark & {
    background: rgba(var(--q-primary-rgb), 0.05);
    border-right-color: rgba(var(--q-primary-rgb), 0.2);
  }
}

.category-details {
  background: var(--q-card-background, white);

  body.body--dark & {
    background: var(--q-dark);
  }
}

.category-list,
.subcategory-list,
.question-list {
  border-radius: 12px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  background-color: var(--q-card-background, white);

  body.body--dark & {
    background-color: var(--q-dark);
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
  }

  .q-item {
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
    margin: 4px 0;

    &.q-item--active {
      background-color: rgba(var(--q-primary-rgb), 0.08);
      border-left: 3px solid var(--q-primary);

      body.body--dark & {
        background-color: rgba(var(--q-primary-rgb), 0.15);
      }
    }

    &:hover:not(.q-item--active) {
      background-color: rgba(var(--q-primary-rgb), 0.04);

      body.body--dark & {
        background-color: rgba(var(--q-primary-rgb), 0.08);
      }
    }

    .q-item-section {
      padding: 12px 0;
    }

    .q-avatar {
      box-shadow: 0 2px 8px rgba(var(--q-primary-rgb), 0.2);
    }
  }
}

.chip-padding {
  height: 24px;
  padding: 0 10px;
  font-size: 0.8rem;
  font-weight: 500;
}

.empty-list-message,
.empty-state {
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(var(--q-primary-rgb), 0.02);
  border-radius: 12px;

  body.body--dark & {
    background-color: rgba(var(--q-primary-rgb), 0.05);
  }

  .q-icon {
    opacity: 0.5;
    margin-bottom: 16px;
  }

  .text-h6 {
    font-weight: 500;
    margin-bottom: 8px;
  }

  .text-caption {
    max-width: 240px;
    line-height: 1.4;
  }
}

.modern-input {
  :deep(.q-field__control) {
    border-radius: 10px;
    height: 56px;

    body.body--dark & {
      background: var(--q-dark);
      color: var(--q-text-color);
    }

    .q-field__marginal {
      height: 56px;
    }
  }
}

.category-form-dialog,
.subcategory-form-dialog {
  border-radius: 16px;
  overflow: hidden;

  .q-card-section {
    padding: 20px 24px;

    &.bg-primary,
    &.bg-amber {
      .text-h6 {
        font-weight: 600;
        letter-spacing: -0.01em;
      }
    }
  }
}

@media (min-width: 768px) {
  .category-details {
    height: calc(100vh - 200px);
    overflow-y: auto;
  }
}
</style>
