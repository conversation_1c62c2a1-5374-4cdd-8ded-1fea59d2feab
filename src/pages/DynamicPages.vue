<template>
  <PageLayout
    title="Page Settings"
    subtitle="Manage your dynamic page content"
    icon="web"
    :loading="isLoading"
    loading-label="Loading page settings..."
  >
    <PageCard elevated>
      <q-scroll-area
        :thumb-style="thumbStyle"
        :bar-style="barStyle"
        style="height: calc(100vh - 300px)"
      >
        <q-splitter v-model="splitterModel">
          <template v-slot:before>
            <q-tabs v-model="innerTab" vertical>
              <q-tab
                name="innertos"
                icon="fact_check"
                label="Terms of Service"
              />
              <q-tab name="innerprivacy" icon="shield" label="Privacy Policy" />
            </q-tabs>
          </template>

          <template v-slot:after>
            <q-tab-panels
              v-model="innerTab"
              animated
              transition-prev="slide-down"
              transition-next="slide-up"
            >
              <q-tab-panel name="innertos">
                <div class="q-pa-md">
                  <div class="text-h6 q-mb-md">Terms of Service</div>
                  <q-editor
                    v-model="tosContent"
                    :disable="isLoading === true"
                    :toolbar="toolbarItems"
                    min-height="15em"
                    paragraph-tag="p"
                    height="calc(100vh - 400px)"
                    @update:model-value="savePage('tos', tosContent)"
                  />
                </div>
              </q-tab-panel>

              <q-tab-panel name="innerprivacy">
                <div class="q-pa-md">
                  <div class="text-h6 q-mb-md">Privacy Policy</div>
                  <q-editor
                    v-model="privacyContent"
                    :disable="isLoading === true"
                    :toolbar="toolbarItems"
                    min-height="15em"
                    paragraph-tag="p"
                    height="calc(100vh - 400px)"
                    @update:model-value="savePage('privacy', privacyContent)"
                  />
                </div>
              </q-tab-panel>
            </q-tab-panels>
          </template>
        </q-splitter>
      </q-scroll-area>
    </PageCard>
  </PageLayout>
</template>

<script setup lang="ts">
import {
  barStyle,
  privacyPage,
  thumbStyle,
  tosPage,
  useDynamicPageStore,
} from 'src/entities/setting';
import { ref } from 'vue';
import { useQuasar } from 'quasar';
import PageLayout from 'src/layouts/PageLayout.vue';
import PageCard from 'src/layouts/PageCard.vue';

// Refs for reactive variables
const isLoading = ref(false);
const innerTab = ref('innertos');
const splitterModel = ref(20);
const $q = useQuasar();
const dynamicPageStore = useDynamicPageStore();

// Async refs for loading page content
const tosContent = ref(await tosPage());
const privacyContent = ref(await privacyPage());

/**
 * Saves the content of a page.
 * @param {string} pageName - The name of the page to save.
 * @param {string} content - The content to save on the page.
 */
const savePage = async (pageName: string, content: string): Promise<void> => {
  try {
    await dynamicPageStore.savePageByPageName(pageName, content);
  } catch (err) {
    console.error('Error saving dynamic page:', err);
  }
};

// Toolbar items configuration for a text editor
const toolbarItems = [
  ['undo', 'redo'],
  [
    'bold',
    'italic',
    'strike',
    'underline',
    'subscript',
    'superscript',
    {
      label: $q.lang.editor.align,
      icon: $q.iconSet.editor.align,
      fixedLabel: true,
      list: 'only-icons',
      options: ['left', 'center', 'right', 'justify'],
    },
  ],
  ['token', 'hr', 'link', 'custom_btn'],
  ['print', 'fullscreen'],
  [
    {
      label: $q.lang.editor.formatting,
      icon: $q.iconSet.editor.formatting,
      list: 'no-icons',
      options: ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'code'],
    },
    {
      label: $q.lang.editor.fontSize,
      icon: $q.iconSet.editor.fontSize,
      fixedLabel: true,
      fixedIcon: true,
      list: 'no-icons',
      options: [
        'size-1',
        'size-2',
        'size-3',
        'size-4',
        'size-5',
        'size-6',
        'size-7',
      ],
    },
    'removeFormat',
  ],
  ['quote', 'unordered', 'ordered', 'outdent', 'indent'],
  ['viewsource'],
];
</script>
