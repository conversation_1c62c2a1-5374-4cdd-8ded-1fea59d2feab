<template>
  <q-layout view="hHh lpR fFf">
    <q-page-container>
      <div class="row" style="height: calc(100vh - 50px)">
        <div
          class="col-8 col-lg-8 col-sm-12 col-xs-12 q-pa-xl full-height flex flex-center xs-hide sm-hide md-hide"
        >
          <img
            src="logo.png"
            clickable
            v-ripple
            @click="redirect"
            class="q-pa-xl cursor-pointer"
            style="min-width: 350px"
          />
        </div>
        <div class="col-4 col-lg-4 col-sm-12 col-xs-12 bg-white full-height">
          <q-card
            flat
            class="text-center text-primary full-height flex flex-center"
          >
            <q-card-section>
              <div style="font-size: 30vh">404</div>
              <div style="font-size: 3vh">Sorry, the page not found</div>
              <br />
              <div style="font-size: 3vh">
                The link you followed is probably broken or the page has been
                removed.
              </div>

              <br />
              <br />
              <br />
              <q-btn-group spread>
                <q-btn size="md" padding="md" color="primary" to="/">
                  <q-icon name="img:robot.png" /> &nbsp;&nbsp;Return to main
                  page
                </q-btn>
              </q-btn-group>
            </q-card-section>
          </q-card>
        </div>
      </div>
      <q-page-sticky position="bottom-left" :offset="[5, 18]">
        <q-fab
          label="Contact Us"
          label-position="left"
          color="primary"
          icon="contact_support"
          :direction="$q.screen.lt.sm ? 'up' : 'right'"
          v-model="helpDialogForm"
          class="help-icon-popup"
        >
          <q-fab-action class="help-popup" disable>
            <HelpCard
              @minimizeToggle="helpDialogForm = false"
              @closeToggle="helpDialogForm = false"
            />
          </q-fab-action>
        </q-fab>
      </q-page-sticky>
    </q-page-container>

    <q-footer class="bg-primary text-white no-box-shadow">
      <q-toolbar>
        <q-toolbar-title clickable @click="redirect" class="cursor-pointer">
          <q-avatar>
            <img src="robot.png" />
          </q-avatar>
          <span style="font-size: 1rem" class="q-ml-sm">Manuscriptr</span>
        </q-toolbar-title>
        <q-space />
        © 2024 Best Seller Publishing, All rights reserved
      </q-toolbar>
    </q-footer>
  </q-layout>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { HelpCard } from 'src/entities/user';
import { ref } from 'vue';

const router = useRouter();
const helpDialogForm = ref(false);

const redirect = () => {
  router.push('/');
};
</script>

<style lang="scss">
.help-icon-popup {
  .q-fab__actions--up {
    left: calc(100vw - 30px) !important;
  }
}
.help-popup {
  align-items: self-end;
  justify-content: center;
  align-self: self-end;
  border-radius: 0;
  padding: 0;

  &.q-btn.disabled {
    opacity: 1 !important;
    cursor: default !important;
    * {
      cursor: default !important;
    }
  }
}
</style>
