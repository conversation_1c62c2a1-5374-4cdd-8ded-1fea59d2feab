# Pages

> https://feature-sliced.design/docs/reference/layers#pages

Complete pages for a page-based application (like a website) or screens/activities for screen-based applications (like mobile apps).

This layer is similar to Widgets in its compositional nature, albeit on a larger scale. Each slice in this layer contains UI components that are ready to be plugged into a router and sometimes data-fetching logic and error handling.

Slice examples:

- News feed
- Community page
- User's public profile
- Repository page
- User's repositories
