<template>
  <section
    id="export"
    class="q-mx-auto"
    :class="{
      'q-pa-lg': !$q.screen.lt.sm, // Full width on xs and sm
    }"
    style="max-width: 1200px"
  >
    <q-card
      :class="{
        'full-width': $q.screen.lt.sm, // Full width on xs and sm
      }"
    >
      <q-card-section class="flex justify-between">
        <h2 class="col-sm-12">Notifications</h2>
        <div class="q-pa-md">
          <div class="q-gutter-md row">
            <q-input
              dense
              debounce="1500"
              v-model="filter.search"
              :rules="[
                (val) =>
                  val.length >= minSearchValue ||
                  !val ||
                  'Minimum 7 characters required',
              ]"
              placeholder="Search"
              :class="{
                'full-width': $q.screen.lt.sm, // Full width on xs and sm
              }"
            >
              <template v-slot:append>
                <q-icon name="search" />
              </template>
            </q-input>

            <q-select
              v-model="filter.isRead"
              :options="readUnreadOptions"
              debounce="300"
              dense
              style="min-width: 200px"
              map-options
              emit-value
              :class="{
                'full-width': $q.screen.lt.sm, // Full width on xs and sm
              }"
            >
              <template v-slot:prepend>
                <q-icon name="visibility" @click.stop.prevent />
              </template>
              <template v-slot:append>
                <q-icon
                  name="close"
                  @click.stop.prevent="filter.isRead = ''"
                  class="cursor-pointer"
                />
              </template>
            </q-select>
          </div>
        </div>
      </q-card-section>
      <q-separator />
      <q-card-section class="q-pa-none">
        <q-table
          :dense="$q.screen.lt.md"
          class="my-sticky-header-table"
          flat
          bordered
          ref="tableRef"
          :rows="notifications"
          :columns="columns"
          row-key="id"
          v-model:pagination="pagination"
          :filter="filter"
          binary-state-sort
          @request="fetchNotifications"
        >
          <template v-slot:body-cell-notification="props">
            <q-td :props="props">
              <q-item-label
                :caption="!props.row.isRead"
                lines="1"
                v-html="trimText(props.row.notification, 50)"
              ></q-item-label>
            </q-td>
          </template>

          <template v-slot:body-cell-url="props">
            <q-td auto-width :props="props">
              <q-btn
                v-if="props.row.url"
                rounded
                size="sm"
                color="primary"
                @click="notificationStore.goToNotificationUrl(props.row)"
                >Click here</q-btn
              >
            </q-td>
          </template>
          <template v-slot:body-cell-createdat="props">
            <q-td auto-width :props="props">
              {{ getShortHumanReadableDate(props.row.createdAt) }}
            </q-td>
          </template>

          <template v-slot:body-cell-view="props">
            <q-td auto-width :props="props">
              <q-btn
                flat
                round
                color="primary"
                icon="touch_app"
                @click="onShowNotification(props.row)"
              />
            </q-td>
          </template>

          <template v-slot:body-cell-readunread="props">
            <q-td auto-width :props="props">
              <q-toggle
                v-model="props.row.isRead"
                @update:model-value="toggleReadUnread(props.row)"
                :disable="loading"
              />
            </q-td>
          </template>
        </q-table>

        <q-inner-loading
          :showing="loading"
          color="primary"
          label="Hang tight, Loading...."
          label-class="text-primary"
          label-style="font-size: 1.1em"
          style="z-index: 1"
        >
          <q-spinner-dots v-if="loading" color="primary" size="2em" />
          <div class="align-center">
            <q-icon name="img:robot.png" class="q-ml-xs" size="xl" />
            Hang tight, Loading....
          </div>
        </q-inner-loading>
      </q-card-section>
    </q-card>
  </section>

  <q-dialog
    v-model="notificationDialog"
    transition-show="scale"
    transition-hide="scale"
  >
    <q-card style="min-width: 40vw">
      <q-card-section class="q-pa-none">
        <q-bar class="bg-primary text-white">
          <q-icon name="img:robot.png" />

          <div>{{ selectedNotification.title }}</div>

          <q-space />

          <q-btn
            dense
            flat
            icon="close"
            @click="notificationDialog = false"
            v-close-popup
          >
            <q-tooltip class="bg-white text-primary">Close</q-tooltip>
          </q-btn>
        </q-bar>
      </q-card-section>

      <q-card-section class="q-py-none">
        <div class="q-pa-md splitter-container">
          <div class="text-h6 sticky-header">
            <q-img src="robot.png" width="30px" /> Notification
            <q-chip
              outline
              color="primary"
              text-color="white"
              icon="event"
              class="absolute-right q-mt-sm"
            >
              {{ getHumanReadableDate(selectedNotification.createdAt) }}
            </q-chip>
          </div>
          <q-separator />

          <div
            class="q-my-sm"
            v-html="selectedNotification?.notification"
          ></div>
        </div>
      </q-card-section>
      <q-card-actions align="right">
        <q-btn
          flat
          label="Close"
          color="danger"
          v-close-popup
          @click="notificationDialog = false"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue';
import { type QTableProps, type QTable, useQuasar } from 'quasar';
import { confirmOverrideText } from 'src/shared/lib/quasar-dialogs';
import {
  useNotificationStore,
  UserNotification,
} from 'src/entities/notification';
import { user, userLoggedIn } from 'src/entities/user';
import {
  barStyle,
  getHumanReadableDate,
  getShortHumanReadableDate,
  thumbStyle,
  trimText,
} from 'src/entities/setting';
import { onInsertContent } from 'src/features/book-transcribe';

const notificationStore = useNotificationStore();
const tableRef = ref<QTable>();

// Columns configuration for the table
type Columns = NonNullable<QTableProps['columns']>;
const columns: Columns = [
  {
    name: 'createdat',
    field: 'createdat',
    label: 'Date',
    align: 'left',
  },
  {
    name: 'notification',
    field: 'notification',
    label: 'Notification',
    align: 'left',
  },
  {
    name: 'readunread',
    field: 'readunread',
    label: 'Unread/Read',
    align: 'left',
  },
  {
    name: 'view',
    field: 'view',
    label: 'View',
    align: 'left',
  },
];

const notifications = ref<UserNotification[]>([]);
const selectedNotification = ref<UserNotification>();
const filter = ref({ search: '', isRead: '' });
const readUnreadOptions = ref([
  { label: 'All', value: '' },
  { label: 'Read', value: true },
  { label: 'Unread', value: false },
]);
const notificationDialog = ref(false);
const loading = ref(true);
const minSearchValue = ref(10);
const pagination = ref({
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
});
const lastVisibleDoc = ref<any>(null);
const $q = useQuasar();
type QTableRequestParams = Parameters<NonNullable<QTableProps['onRequest']>>[0];
onMounted(() => {
  tableRef.value!.requestServerInteraction();
});

const fetchNotifications = async (props: QTableRequestParams) => {
  loading.value = true;
  try {
    const { page, rowsPerPage } = props.pagination;
    const filter: any = props.filter;

    // Ensure search filter has at least 7 characters
    if (filter.search && filter.search.length < minSearchValue.value) {
      notifications.value = []; // Clear results or handle appropriately
      loading.value = false;
      return;
    }

    // Reset lastVisibleDoc if rowsPerPage changes or navigating back to page 1
    if (pagination.value.rowsPerPage !== rowsPerPage || page === 1) {
      lastVisibleDoc.value = null;
    }

    pagination.value.rowsPerPage = rowsPerPage;

    // Determine lastVisibleDoc based on pagination
    const useLastVisible = page > pagination.value.page && lastVisibleDoc.value;
    const querySnapshot = await notificationStore.buildNotificationsQuery(
      rowsPerPage,
      useLastVisible ? lastVisibleDoc.value : null,
      filter,
    );

    // Map notifications
    notifications.value = querySnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as UserNotification[];

    // Update lastVisibleDoc only if new data exists
    lastVisibleDoc.value =
      querySnapshot.docs.length > 0
        ? querySnapshot.docs[querySnapshot.docs.length - 1]
        : lastVisibleDoc.value;

    // Update pagination info
    if (filter.search) {
      await notificationStore.getNotificationsQueryCountBySearch(filter);
      pagination.value.rowsNumber = notificationStore.countSearch;
    } else {
      pagination.value.rowsNumber = userLoggedIn?.value?.notifications ?? 0;
      if (filter.isRead === false) {
        pagination.value.rowsNumber =
          pagination.value.rowsNumber -
          (userLoggedIn?.value?.notifications_read ?? 0);
      } else if (filter.isRead === true) {
        pagination.value.rowsNumber =
          userLoggedIn?.value?.notifications_read ?? 0;
      }
    }

    pagination.value.page = page;
  } catch (error) {
    console.error('Error fetching notifications:', error);
  } finally {
    loading.value = false;
  }
};

const toggleReadUnread = async (notification: UserNotification) => {
  loading.value = true;
  try {
    await notificationStore.updateUserNotification(
      user?.value?.uid as string,
      notification.id,
      {
        isRead: !!notification.isRead,
      },
    );
    notification.isRead = !!notification.isRead;
  } catch (error) {
    $q.notify({
      message: 'Failed to update notification status.',
      color: 'red',
      timeout: 3000,
      icon: 'error',
    });
  } finally {
    loading.value = false;
  }
};
const onShowNotification = async (notification: UserNotification) => {
  loading.value = true;
  try {
    await notificationStore.updateUserNotification(
      user?.value?.uid as string,
      notification.id,
      {
        isRead: true,
      },
    );
    selectedNotification.value = notification;
    notificationDialog.value = true;
    notification.isRead = true;
  } catch (error) {
    $q.notify({
      message: 'Failed to show notification dialog.',
      color: 'red',
      timeout: 3000,
      icon: 'error',
    });
  } finally {
    loading.value = false;
  }
};
</script>
