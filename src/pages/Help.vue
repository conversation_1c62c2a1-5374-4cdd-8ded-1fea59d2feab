<template>
  <PageLayout
    title="Help Center"
    subtitle="Find answers, guides, and support for Manuscriptr"
    icon="support"
    max-width="1000px"
  >
    <div class="row justify-center">
      <div class="col-12">
        <PageCard
          title="Frequently Asked Questions"
          subtitle="Get quick answers to common questions"
          icon="quiz"
          class="q-mb-lg"
        >
          <HelpCard />
        </PageCard>

        <PageCard
          title="Need More Help?"
          subtitle="Our support team is ready to assist you"
          icon="contact_support"
          class="text-center"
        >
          <div class="q-pa-lg">
            <q-icon name="email" size="4rem" color="primary" class="q-mb-md" />
            <h3 class="text-h5 text-weight-medium q-mb-md">Contact Support</h3>
            <p class="text-body1 q-mb-lg">
              Our support team is ready to assist you with any questions or
              issues you may have.
            </p>
            <q-btn
              color="primary"
              label="Contact Support"
              icon="email"
              class="premium-btn q-px-lg"
              padding="sm lg"
              to="mailto:<EMAIL>"
              unelevated
              size="lg"
            />
          </div>
        </PageCard>
      </div>
    </div>
  </PageLayout>
</template>

<script setup lang="ts">
import { HelpCard } from 'src/entities/user';
import PageLayout from 'src/layouts/PageLayout.vue';
import PageCard from 'src/layouts/PageCard.vue';
</script>
