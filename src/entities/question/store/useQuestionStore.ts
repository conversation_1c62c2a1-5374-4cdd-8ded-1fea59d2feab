import { defineStore } from 'pinia';
import { ref, Ref } from 'vue';
import { Question } from '../model';
import { questionApi } from '../api/firestore';
import { categoryApi } from 'src/entities/book-category/api/firestore';
import { Subcategory } from 'src/entities/book-category';

export const useQuestionStore = defineStore('question', () => {
  // State
  const questions: Ref<Question[]> = ref([]);
  const subcategories: Ref<Subcategory[]> = ref([]);
  const loading: Ref<boolean> = ref(false);
  const error: Ref<Error | null> = ref(null);

  // Actions
  /**
   * Fetch all questions for a specific category, organized by subcategories
   */
  async function fetchAllBySubcategory(categoryId: string) {
    loading.value = true;
    try {
      // Fetch subcategories first
      subcategories.value = await categoryApi.getSubcategories(categoryId);

      // Then fetch all questions
      questions.value = await questionApi.getAllByCategory(categoryId);

      // Map subcategoryId to subcategory name for display purposes
      questions.value = questions.value.map((question) => {
        if (question.subcategoryId) {
          const subcategory = subcategories.value.find(
            (s) => s.id === question.subcategoryId,
          );
          if (subcategory) {
            question.subcategory = subcategory.name;
          }
        }
        return question;
      });
    } catch (err) {
      error.value = err as Error;
      console.error('Error fetching questions:', err);
    } finally {
      loading.value = false;
    }
  }

  /**
   * Create a new question
   */
  async function create(question: Question): Promise<string> {
    loading.value = true;
    try {
      // If question has a subcategory name but no ID, find or create the subcategory
      if (question.subcategory && !question.subcategoryId) {
        // Find existing subcategory with this name
        const existingSubcategory = subcategories.value.find(
          (s) => s.name === question.subcategory,
        );

        if (existingSubcategory) {
          question.subcategoryId = existingSubcategory.id;
        } else {
          // Create new subcategory
          const subcategoryId = await categoryApi.createSubcategory(
            question.categoryId,
            question.subcategory,
          );
          question.subcategoryId = subcategoryId;

          // Add to local subcategories list
          subcategories.value.push({
            id: subcategoryId,
            name: question.subcategory,
          });
        }
      }

      const id = await questionApi.create(question);

      // Add to local questions list
      questions.value.push({
        ...question,
        id,
      });

      return id;
    } catch (err) {
      error.value = err as Error;
      console.error('Error creating question:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Update an existing question
   */
  async function update(id: string, updates: Partial<Question>): Promise<void> {
    loading.value = true;
    try {
      const questionIndex = questions.value.findIndex((q) => q.id === id);
      if (questionIndex === -1) return;

      const question = questions.value[questionIndex];

      await questionApi.update(id, updates);

      // Update local questions list
      questions.value[questionIndex] = {
        ...question,
        ...updates,
      };
    } catch (err) {
      error.value = err as Error;
      console.error('Error updating question:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Delete a question
   */
  async function deleteQuestion(id: string): Promise<void> {
    loading.value = true;
    try {
      const question = questions.value.find((q) => q.id === id);
      if (!question) return;

      await questionApi.delete(id, question.categoryId, question.subcategoryId);

      // Remove from local questions list
      questions.value = questions.value.filter((q) => q.id !== id);
    } catch (err) {
      error.value = err as Error;
      console.error('Error deleting question:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Fetch subcategories for a specific category
   */
  async function fetchSubcategories(
    categoryId: string,
  ): Promise<Subcategory[]> {
    loading.value = true;
    try {
      subcategories.value = await categoryApi.getSubcategories(categoryId);
      return subcategories.value;
    } catch (err) {
      error.value = err as Error;
      console.error('Error fetching subcategories:', err);
      return [];
    } finally {
      loading.value = false;
    }
  }

  /**
   * Create a new subcategory
   */
  async function createSubcategory(subcategory: {
    categoryId: string;
    name: string;
    icon?: string;
    isDefault?: boolean;
  }): Promise<string> {
    loading.value = true;
    try {
      const id = await categoryApi.createSubcategory(
        subcategory.categoryId,
        subcategory.name,
        subcategory.icon,
        subcategory.isDefault,
      );

      // Add to local subcategories list
      subcategories.value.push({
        id,
        name: subcategory.name,
        icon: subcategory.icon || 'folder',
        isDefault: subcategory.isDefault || false,
      });

      return id;
    } catch (err) {
      error.value = err as Error;
      console.error('Error creating subcategory:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Update an existing subcategory
   */
  async function updateSubcategory(
    categoryId: string,
    subcategoryId: string,
    updates: Partial<Omit<Subcategory, 'id' | 'createdAt' | 'updatedAt'>>,
  ): Promise<boolean> {
    loading.value = true;
    try {
      await categoryApi.updateSubcategory(categoryId, subcategoryId, updates);

      // Update local subcategories list
      const index = subcategories.value.findIndex(
        (s) => s.id === subcategoryId,
      );
      if (index !== -1) {
        subcategories.value[index] = {
          ...subcategories.value[index],
          ...updates,
        };
      }

      return true;
    } catch (err) {
      error.value = err as Error;
      console.error('Error updating subcategory:', err);
      return false;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Delete a subcategory
   */
  async function deleteSubcategory(
    categoryId: string,
    subcategoryId: string,
  ): Promise<boolean> {
    loading.value = true;
    try {
      await categoryApi.deleteSubcategory(categoryId, subcategoryId);

      if (subcategories.value && Array.isArray(subcategories.value)) {
        subcategories.value = subcategories.value.filter(
          (s) => s.id !== subcategoryId,
        );
      }

      return true;
    } catch (err) {
      error.value = err as Error;
      console.error('Error deleting subcategory:', err);
      return false;
    } finally {
      loading.value = false;
    }
  }

  return {
    questions,
    subcategories,
    loading,
    error,

    fetchAllBySubcategory,
    fetchSubcategories,
    createSubcategory,
    updateSubcategory,
    deleteSubcategory,
    create,
    update,
    delete: deleteQuestion,
  };
});
