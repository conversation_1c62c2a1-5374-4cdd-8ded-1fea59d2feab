import { Subcategory } from 'src/entities/book-category';

export type AnswerElement =
  | 'text'
  | 'textarea'
  | 'select'
  | 'options'
  | 'texteditor'
  | 'button';

export interface AIOptions {
  temperature: number;
  maxTokens: number;
  topP: number;
  frequencyPenalty: number;
  presencePenalty: number;
}

export interface Question {
  id: string;
  categoryId: string;
  subcategoryId?: string; // Reference to the subcategory document ID
  subcategoryObject?: Subcategory; // Reference to the subcategory object
  subcategory?: string; // Human-readable subcategory name (for backward compatibility)
  originalSubcategoryId?: string; // Used for tracking moves between subcategories
  question: string;
  description?: string;
  hasAI: boolean;
  aiPrompt?: string;
  aiOptions?: AIOptions;
  questionElement: AnswerElement;
  answerElement: AnswerElement;
  options?: string[];
  inBook?: boolean;
  createdAt: Date;
  updatedAt: Date;
}
