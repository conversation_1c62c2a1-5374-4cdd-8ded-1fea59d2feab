import {
  collection,
  doc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  serverTimestamp,
  Timestamp,
  collectionGroup,
  setDoc,
} from 'firebase/firestore';

import { firestore } from 'src/firebase';
import { Question } from '../model';

export const questionApi = {
  async getAllByCategory(categoryId: string): Promise<Question[]> {
    // Create an array to hold all questions
    let allQuestions: Question[] = [];

    // Next, get all subcategories for this category
    const subcategoriesCollection = collection(
      firestore,
      'bookCategories',
      categoryId,
      'subcategories',
    );

    const subcategoriesSnapshot = await getDocs(subcategoriesCollection);

    // For each subcategory, get its questions
    for (const subcategoryDoc of subcategoriesSnapshot.docs) {
      const subcategoryId = subcategoryDoc.id;
      const subcategoryName = subcategoryDoc.data().name;

      const subcategoryQuestionsCollection = collection(
        firestore,
        'bookCategories',
        categoryId,
        'subcategories',
        subcategoryId,
        'questions',
      );

      const subcategoryQuestionsSnapshot = await getDocs(
        subcategoryQuestionsCollection,
      );

      // Add subcategory questions to our results
      const subcategoryQuestions = subcategoryQuestionsSnapshot.docs.map(
        (doc) => {
          const data = doc.data();
          return {
            id: doc.ref.id,
            ...data,
            categoryId, // Ensure categoryId is set
            subcategoryId, // Set the subcategoryId
            subcategory: subcategoryName, // Set the subcategory name
            subcategoryObject: data.subcategoryObject, // Set the subcategory object
            createdAt: data.createdAt?.toDate() || new Date(),
            updatedAt: data.updatedAt?.toDate() || new Date(),
          } as Question;
        },
      );

      allQuestions = [...allQuestions, ...subcategoryQuestions];
    }

    return allQuestions;
  },

  async getAllBySubcategory(
    categoryId: string,
    subcategoryId: string,
  ): Promise<Question[]> {
    const questionsCollection = collection(
      firestore,
      'bookCategories',
      categoryId,
      'subcategories',
      subcategoryId,
      'questions',
    );

    const snapshot = await getDocs(questionsCollection);

    return snapshot.docs.map((doc) => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
      } as Question;
    });
  },

  async create(question: Question): Promise<string> {
    let questionsCollection;

    if (!question.subcategoryId) {
      throw new Error('Cannot create question without subcategory');
    }
    // If question has a subcategory, add it to that subcategory's questions collection
    questionsCollection = collection(
      firestore,
      'bookCategories',
      question.categoryId,
      'subcategories',
      question.subcategoryId,
      'questions',
    );

    const docRef = await addDoc(questionsCollection, {
      ...question,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });
    await setDoc(docRef, { id: docRef.id }, { merge: true });
    return docRef.id;
  },

  async update(id: string, updates: Partial<Question>): Promise<void> {
    // If not moving, just update in place
    // We need to know the path, which should be stored in the question
    const { categoryId, subcategoryId } = updates;

    let questionRef;
    if (!categoryId) {
      throw new Error('Cannot update question without categoryId');
    }
    if (!subcategoryId) {
      throw new Error('Cannot update question without subcategory');
    }
    if (subcategoryId) {
      questionRef = doc(
        firestore,
        'bookCategories',
        categoryId!,
        'subcategories',
        subcategoryId,
        'questions',
        id,
      );

      await updateDoc(questionRef, {
        ...updates,
        updatedAt: serverTimestamp(),
      });
    }
  },

  async delete(
    id: string,
    categoryId?: string,
    subcategoryId?: string,
  ): Promise<void> {
    if (!id) {
      throw new Error('Cannot delete question without id');
    }
    // If we know the path (categoryId and optionally subcategoryId)
    if (!categoryId) {
      throw new Error('Cannot delete question without categoryId');
    }
    if (!subcategoryId) {
      throw new Error('Cannot delete question without subcategoryId');
    }
    if (categoryId) {
      let questionRef;

      if (subcategoryId) {
        questionRef = doc(
          firestore,
          'bookCategories',
          categoryId,
          'subcategories',
          subcategoryId,
          'questions',
          id,
        );

        await deleteDoc(questionRef);

        return;
      }
    }

    // If we don't know the path, we need to find the question first
    // This is more complex and would require a collectionGroup query
    // For simplicity, we'll require the path information
    throw new Error(
      'Cannot delete question without path information (categoryId and subcategoryId)',
    );
  },

  async getById(id: string): Promise<Question | null> {
    // Use collectionGroup to find the question by ID across all locations
    const questionsQuery = query(
      collectionGroup(firestore, 'questions'),
      where('__name__', '==', id),
    );

    const docSnap = await getDocs(questionsQuery);

    if (docSnap.empty) {
      return null;
    }

    const data = docSnap.docs[0].data();
    return {
      id: docSnap.docs[0].id,
      ...data,
      createdAt: data.createdAt?.toDate() || new Date(),
      updatedAt: data.updatedAt?.toDate() || new Date(),
    } as Question;
  },
};
