<template>
  <q-form @submit.prevent="submit" class="q-gutter-md">
    <!-- Basic Question Information -->
    <div class="form-section">
      <q-input
        v-model="localQuestion.question"
        label="Question *"
        :rules="[required]"
        stack-label
        class="modern-input"
        outlined
      >
        <template v-slot:prepend>
          <q-icon name="help" color="primary" />
        </template>
      </q-input>

      <q-input
        v-model="localQuestion.description"
        label="Description"
        type="textarea"
        class="modern-input"
        outlined
        rows="3"
        hint="Provide additional context or instructions for this question"
        stack-label
      >
        <template v-slot:prepend>
          <q-icon name="description" color="primary" />
        </template>
      </q-input>
    </div>

    <!-- Subcategory Selection -->
    <div class="form-section">
      <q-input
        v-model="localQuestion.subcategoryObject.name"
        label="Subcategory"
        class="modern-input"
        outlined
        readonly
        stack-label
      >
        <template v-slot:prepend>
          <q-icon
            :name="localQuestion.subcategoryObject?.icon || 'folder'"
            color="amber"
          />
        </template>
      </q-input>
    </div>

    <!-- Question Type Selection -->
    <div class="form-section">
      <div class="text-subtitle1 q-mb-sm">Question Type</div>
      <q-select
        v-model="localQuestion.questionElement"
        :options="answerElementOptions"
        label="Question Element *"
        :rules="[required]"
        emit-value
        map-options
        class="modern-input"
        outlined
        stack-label
        :readonly="localQuestion.subcategoryObject.name === 'Default'"
      >
        <template v-slot:prepend>
          <q-icon name="format_list_bulleted" color="primary" />
        </template>
        <template v-slot:option="scope">
          <q-item v-bind="scope.itemProps">
            <q-item-section avatar>
              <q-icon
                :name="answerElementIcons[scope.opt.value]"
                :color="answerElementColors[scope.opt.value]"
              />
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ scope.opt.label }}</q-item-label>
              <q-item-label caption>
                {{ answerElementDescriptions[scope.opt.value] }}
              </q-item-label>
            </q-item-section>
          </q-item>
        </template>
      </q-select>
    </div>

    <!-- Answer Type Selection -->
    <div class="form-section">
      <div class="text-subtitle1 q-mb-sm">Answer Type</div>
      <q-select
        v-model="localQuestion.answerElement"
        :options="answerElementOptions"
        label="Answer Element *"
        :rules="[required]"
        emit-value
        map-options
        class="modern-input"
        outlined
        stack-label
        :readonly="localQuestion.subcategoryObject.name === 'Default'"
      >
        <template v-slot:prepend>
          <q-icon name="format_list_bulleted" color="primary" />
        </template>
        <template v-slot:option="scope">
          <q-item v-bind="scope.itemProps">
            <q-item-section avatar>
              <q-icon
                :name="answerElementIcons[scope.opt.value]"
                :color="answerElementColors[scope.opt.value]"
              />
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ scope.opt.label }}</q-item-label>
              <q-item-label caption>
                {{ answerElementDescriptions[scope.opt.value] }}
              </q-item-label>
            </q-item-section>
          </q-item>
        </template>
      </q-select>

      <!-- Options for select/options answer types -->
      <div
        v-if="
          localQuestion.answerElement === 'select' ||
          localQuestion.answerElement === 'options'
        "
        class="options-section q-mb-md q-pa-md bg-grey-2 rounded-borders"
      >
        <div class="text-subtitle2 q-mb-sm">Options</div>
        <div class="row">
          <q-input
            v-model="option"
            @keyup.enter="addOption"
            placeholder="Add option"
            class="modern-input col-grow"
            outlined
            dense
          >
            <template v-slot:append>
              <q-btn
                round
                dense
                flat
                icon="add"
                color="primary"
                @click="addOption"
                :disable="!option"
              />
            </template>
          </q-input>
        </div>

        <div class="options-list q-mt-sm">
          <q-chip
            v-for="(opt, index) in localQuestion.options"
            :key="index"
            removable
            @remove="removeOption(index)"
            color="primary"
            text-color="white"
            class="q-ma-xs"
          >
            {{ opt }}
          </q-chip>
          <div
            v-if="!localQuestion.options?.length"
            class="text-grey-7 q-pa-sm text-center"
          >
            No options added yet. Add at least one option.
          </div>
        </div>
      </div>
    </div>

    <!-- AI Integration -->
    <div class="form-section q-mt-lg">
      <q-separator class="q-my-md" />

      <div class="row items-center q-mb-md">
        <div class="text-subtitle1">AI Integration</div>
        <q-space />
        <q-toggle
          :disable="localQuestion.subcategoryObject.name === 'Default'"
          v-model="localQuestion.hasAI"
          label="Enable AI"
          color="primary"
          left-label
        />
      </div>

      <div
        v-if="localQuestion.hasAI"
        class="ai-section q-pa-md bg-blue-1 rounded-borders"
      >
        <AIPromptEditor
          v-model="localQuestion.aiPrompt"
          :inBook="localQuestion.inBook"
          :variables="availableVariables"
          class="q-mb-md"
        />

        <div class="ai-options q-mt-lg">
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6">
              <div class="text-subtitle2 q-mb-xs">Temperature</div>
              <q-tooltip>
                Controls randomness: Lower values make responses more focused
                and deterministic
              </q-tooltip>
              <q-slider
                v-model="localQuestion.aiOptions.temperature"
                :min="0"
                :max="1"
                :step="0.1"
                label
                label-always
                color="primary"
                class="q-mb-lg"
              />
            </div>

            <div class="col-12 col-md-6">
              <div class="text-subtitle2 q-mb-xs">Max Tokens</div>
              <q-tooltip> Maximum length of the generated response </q-tooltip>
              <q-slider
                v-model="localQuestion.aiOptions.maxTokens"
                :min="50"
                :max="4096"
                :step="50"
                label
                label-always
                color="primary"
                class="q-mb-lg"
              />
            </div>

            <div class="col-12 col-md-6">
              <div class="text-subtitle2 q-mb-xs">Top P</div>
              <q-tooltip>
                Top P controls the randomness of the response. Lower values make
                responses more focused and deterministic.
              </q-tooltip>
              <q-slider
                v-model="localQuestion.aiOptions.topP"
                :min="0"
                :max="1"
                :step="0.01"
                label
                label-always
                color="primary"
                class="q-mb-lg"
              />
            </div>

            <div class="col-12 col-md-6">
              <div class="text-subtitle2 q-mb-xs">Frequency Penalty</div>
              <q-tooltip>
                Frequency penalty controls how often the model should repeat the
                same token sequence in its response.
              </q-tooltip>
              <q-slider
                v-model="localQuestion.aiOptions.frequencyPenalty"
                :min="0"
                :max="1"
                :step="0.01"
                label
                label-always
                color="primary"
                class="q-mb-lg"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Answer Preview -->
    <div class="form-section q-mt-lg" v-if="1 != 1">
      <q-separator class="q-my-md" />
      <div class="text-subtitle1 q-mb-md">Answer Preview</div>

      <AnswerPreview
        :answer-element="localQuestion.answerElement"
        :options="localQuestion.options"
        :hasAI="localQuestion.hasAI"
        :answer-value="previewAnswer"
        class="q-pa-md bg-grey-2 rounded-borders"
      />
    </div>

    <!-- Submit Button -->
    <div class="form-actions q-mt-lg">
      <q-btn
        type="submit"
        color="primary"
        label="Save Question"
        :loading="isSaving"
        :disable="isSaving"
        class="full-width"
        v-if="1 != 1"
      >
        <template v-slot:loading>
          <q-spinner-dots color="white" />
          Saving...
        </template>
      </q-btn>
    </div>
  </q-form>

  <q-inner-loading :showing="isSaving">
    <q-spinner-dots color="primary" size="2em" />
    <div class="align-center">
      <q-icon name="img:robot.png" class="q-ml-xs" size="xl" />
      Hang tight, Saving....
    </div>
  </q-inner-loading>

  <q-inner-loading
    :showing="isSaving"
    color="primary"
    label-class="text-primary"
    label-style="font-size: 1.1em"
    style="z-index: 1"
  >
    <q-spinner-dots color="primary" size="2em" />
    <div class="align-center">
      <q-icon name="img:robot.png" class="q-ml-xs" size="xl" />
      {{ isSaving ? 'Saving...' : 'Hang tight, Loading....' }}
    </div>
  </q-inner-loading>
</template>

<script setup lang="ts">
import { ref, watch, computed, reactive, toRaw } from 'vue';
import { Question, AnswerPreview, AIPromptEditor } from 'src/entities/question';
import { Subcategory } from 'src/entities/book-category';

const props = defineProps<{
  modelValue: Question;
  availableVariables: Question[] | string[] | undefined;
  subcategories?: Subcategory[]; // Updated to use Subcategory type
}>();

const emit = defineEmits<{
  (e: 'submit', question: Question): void;
  (e: 'update:modelValue', question: Question): void;
  (e: 'saving', isSaving: boolean): void;
}>();

// Create default AI options
const defaultAIOptions = {
  temperature: 0.7,
  maxTokens: 1024,
  topP: 1,
  frequencyPenalty: 0,
  presencePenalty: 0,
};

// Create a local copy of the question to avoid direct mutation
const localQuestion = reactive<Question>(props.modelValue);

const option = ref('');
const previewAnswer = ref('Sample answer text');
const isSaving = ref(false);

// Answer element options with labels
const answerElementOptions = [
  { label: 'Short Text', value: 'text' },
  { label: 'Long Text', value: 'textarea' },
  { label: 'Dropdown Select', value: 'select' },
  { label: 'Multiple Choice', value: 'options' },
  { label: 'Text Editor', value: 'texteditor' },
  { label: 'Button', value: 'button' },
];

// Icons for answer elements
const answerElementIcons = {
  text: 'short_text',
  textarea: 'notes',
  select: 'arrow_drop_down_circle',
  options: 'radio_button_checked',
  texteditor: 'text_fields',
  button: 'img:robot.png',
};

// Colors for answer elements
const answerElementColors = {
  text: 'blue',
  textarea: 'indigo',
  select: 'green',
  options: 'orange',
  texteditor: 'purple',
  button: 'red',
};

// Descriptions for answer elements
const answerElementDescriptions = {
  text: 'Single line text input for short answers',
  textarea: 'Multi-line text area for longer responses',
  select: 'Dropdown selection from predefined options',
  options: 'Multiple choice radio button selection',
  texteditor: 'Text Editor',
  button: 'Button',
};

// Computed property for subcategory options
const subcategoryOptions = computed(() => {
  if (props.subcategories) {
    return props.subcategories.map((subcategory) => subcategory.name);
  }
  return [];
});

// Validation rules
function required(value: string) {
  return !!value || 'Field is required';
}

// Add option to the list
function addOption() {
  if (option.value && localQuestion.options) {
    // Check if option already exists
    if (!localQuestion.options.includes(option.value)) {
      localQuestion.options.push(option.value);
      option.value = '';
      emitUpdate();
    } else {
      // Show error or notification that option already exists
      console.warn('Option already exists');
    }
  }
}

// Remove option from the list
function removeOption(index: number) {
  if (localQuestion.options) {
    localQuestion.options.splice(index, 1);
    emitUpdate();
  }
}

// Submit the form
async function submit() {
  // Validate required fields
  if (!localQuestion.question) {
    return;
  }

  // Validate options if select/options is selected
  if (
    (localQuestion.answerElement === 'select' ||
      localQuestion.answerElement === 'options') &&
    (!localQuestion.options || localQuestion.options.length === 0)
  ) {
    return;
  }

  try {
    isSaving.value = true;
    emit('saving', true);

    // Simulate network delay (remove this in production)
    // This is just to demonstrate the loading state
    // In a real app, this would be replaced by your actual API call
    await new Promise((resolve) => setTimeout(resolve, 1000));

    emit('submit', toRaw(localQuestion));
  } catch (error) {
    console.error('Error saving question:', error);
  } finally {
    isSaving.value = false;
    emit('saving', false);
  }
}

// Emit update with a debounce to prevent recursive updates
let updateTimeout: number | null = null;
function emitUpdate() {
  if (updateTimeout) {
    clearTimeout(updateTimeout);
  }

  updateTimeout = window.setTimeout(() => {
    emit('update:modelValue', toRaw(localQuestion));
    updateTimeout = null;
  }, 100);
}

// Watch for changes from parent
watch(
  () => props.modelValue,
  (newVal) => {
    // Only update if there are actual differences to avoid recursive updates
    if (JSON.stringify(toRaw(localQuestion)) !== JSON.stringify(newVal)) {
      Object.assign(localQuestion, {
        ...newVal,
        options: [...(newVal.options || [])],
        aiOptions: {
          ...defaultAIOptions,
          ...(newVal.aiOptions || {}),
        },
      });
    }
  },
  { deep: true },
);

// Watch for changes to update the parent component
watch(
  localQuestion,
  () => {
    emitUpdate();
  },
  { deep: true },
);

// Add subcategory filtering
const subcategoryFilter = ref('');
const filteredSubcategoryOptions = ref<string[]>([]);

function filterSubcategories(val: string, update: (fn: () => void) => void) {
  if (val === '') {
    filteredSubcategoryOptions.value = subcategoryOptions.value;
  } else {
    const needle = val.toLowerCase();
    filteredSubcategoryOptions.value = subcategoryOptions.value.filter(
      (v) => v.toLowerCase().indexOf(needle) > -1,
    );
  }

  update(() => {
    subcategoryFilter.value = val;
  });
}
</script>

<style scoped>
.question-form {
  max-width: 100%;
  position: relative;
}

.form-section {
  margin-bottom: 1.5rem;
}

.options-section,
.ai-section {
  border: 1px solid rgba(0, 0, 0, 0.12);
  transition: all 0.3s ease;
}

.options-section:hover,
.ai-section:hover {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.options-list {
  display: flex;
  flex-wrap: wrap;
  min-height: 40px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .q-card {
    padding: 12px !important;
  }
}
</style>
