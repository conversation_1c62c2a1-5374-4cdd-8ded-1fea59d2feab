<template>
  <q-card-section>
    <div class="text-h6">AI Prompt Editor</div>
    <div class="text-caption q-mt-sm">
      Drag or click variables to insert them into your prompt
    </div>
  </q-card-section>

  <q-card-section class="variables">
    <div class="label">Available Variables:</div>
    <div class="variable-list">
      <q-chip
        v-for="variable in localVariables"
        :key="typeof variable === 'object' ? variable.id : variable"
        :draggable="true"
        @dragstart="
          onDragStart(
            typeof variable === 'object' ? variable.question : variable,
          )
        "
        @click="
          insertVariableAtCursor(
            typeof variable === 'object' ? variable.question : variable,
          )
        "
        color="primary"
        text-color="white"
        icon="input"
        class="variable-chip"
      >
        {{ typeof variable === 'object' ? variable.question : variable }}
      </q-chip>
    </div>
  </q-card-section>

  <q-card-section class="editor">
    <q-editor
      ref="editorRef"
      v-model="localPrompt"
      :toolbar="toolbarOptions"
      min-height="150px"
      class="rich-text-editor"
      @paste="handlePaste"
    >
      <template v-slot:definitions>
        <button
          type="button"
          class="ql-custom-variable"
          aria-label="Insert Variable"
          title="Insert Variable"
        >
          <q-icon name="code" />
        </button>
      </template>
    </q-editor>
  </q-card-section>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, nextTick } from 'vue';
import { Question } from 'src/entities/question';
import { useQuasar } from 'quasar';

const $q = useQuasar();

const props = defineProps<{
  modelValue: string;
  inBook: boolean;
  variables: string[] | Question[] | undefined;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
}>();

// Use a local copy of the prompt to avoid recursive updates
const localPrompt = ref('');
const localVariables = ref('');

// Initialize the local prompt once on mount
onMounted(() => {
  localPrompt.value = props.modelValue || '';
  localVariables.value = props.variables || undefined;
  if (props?.inBook === true) {
    if (!localVariables.value.some((item) => item.id === 'selectedContent')) {
      localVariables.value.push({
        id: 'selectedContent',
        question: 'selectedContent',
      });
    }
    if (!localVariables.value.some((item) => item.id === 'chapterTitle')) {
      localVariables.value.push({
        id: 'chapterTitle',
        question: 'chapterTitle',
      });
    }
    if (!localVariables.value.some((item) => item.id === 'nextChapterTitle')) {
      localVariables.value.push({
        id: 'nextChapterTitle',
        question: 'nextChapterTitle',
      });
    }
  }

  initCustomButtons();
});

const editorRef = ref();
const toolbarOptions = [
  ['bold', 'italic', 'underline', 'strike'],
  ['h1', 'h2', 'h3', 'p', 'blockquote'],
  ['link'],
  ['custom-variable'],
  ['clean'],
];

function initCustomButtons() {
  nextTick(() => {
    if (!editorRef.value) return;

    // Get the custom variable button
    const customVarBtn = editorRef.value.$el.querySelector(
      '.ql-custom-variable',
    );
    if (customVarBtn) {
      customVarBtn.addEventListener('click', showVariableMenu);
    }
  });
}

function showVariableMenu() {
  // Create a list of variables for the menu
  const variableItems = Array.isArray(props.variables)
    ? props.variables.map((v) => {
        const varName = typeof v === 'object' ? v.question : v;
        const varId = typeof v === 'object' ? v.id : v;
        return {
          label: varName,
          id: varId,
          handler: () => insertVariableAtCursor(varName),
        };
      })
    : [];

  // Show a Quasar menu with the variables
  $q.dialog({
    title: 'Insert Variable',
    message: 'Select a variable to insert:',
    options: {
      type: 'radio',
      model: '',
      items: variableItems.map((item) => ({
        label: item.label,
        value: item.id,
      })),
    },
    cancel: true,
    persistent: true,
  }).onOk((selectedVarId) => {
    const selectedVar = variableItems.find((item) => item.id === selectedVarId);
    if (selectedVar) {
      selectedVar.handler();
    }
  });
}

function onDragStart(variable: string) {
  if (editorRef.value && editorRef.value.$el) {
    const editor = editorRef.value.$el.querySelector('.ql-editor');
    if (editor) {
      editor.dataset.draggedVariable = variable;
    }
  }
}

function handlePaste(e: ClipboardEvent) {
  // Handle special paste operations if needed
  // For now, we'll let the default paste behavior work
}

function insertVariableAtCursor(variable: string) {
  // Focus the editor first
  if (editorRef.value) {
    const editorElement = editorRef.value.$el.querySelector('.ql-editor');
    if (editorElement) {
      editorElement.focus();
      insertVariable(variable);
    }
  }
}

function insertVariable(variable: string) {
  // Get the Quill editor instance
  const quill = editorRef.value.quill;
  if (!quill) return;

  // Create a custom blot for the variable
  const variableHTML = `<span class="ai-variable" data-variable="${variable}" contenteditable="false">{{${variable}}}</span>&nbsp;`;

  // Get the current selection
  const range = quill.getSelection(true);

  // Insert the variable HTML at the current cursor position
  quill.clipboard.dangerouslyPasteHTML(range.index, variableHTML);

  // Move the cursor after the inserted variable
  quill.setSelection(range.index + variableHTML.length, 0);

  // Update the model value
  setTimeout(() => {
    emit('update:modelValue', localPrompt.value);
  }, 0);
}

// Watch for changes in the local prompt and emit updates to parent
watch(localPrompt, (newVal) => {
  // Prevent recursive updates by checking if the value has actually changed
  if (newVal !== props.modelValue) {
    emit('update:modelValue', newVal);
  }
});

// Watch for changes from parent
watch(
  () => props.modelValue,
  (newVal) => {
    // Prevent recursive updates by checking if the value has actually changed
    if (newVal !== localPrompt.value) {
      localPrompt.value = newVal || '';
      localVariables.value = props.variables || [];
    }
  },
);

// Add event listener to handle variable deletion properly
onMounted(() => {
  if (editorRef.value && editorRef.value.quill) {
    const quill = editorRef.value.quill;

    // Register a custom format for variables
    const Inline = quill.import('blots/inline');

    class VariableBlot extends Inline {
      static create(value: string) {
        const node = super.create();
        node.setAttribute('data-variable', value);
        node.setAttribute('contenteditable', 'false');
        node.classList.add('ai-variable');
        node.textContent = `{{${value}}}`;
        return node;
      }

      static formats(node: HTMLElement) {
        return node.getAttribute('data-variable');
      }
    }

    VariableBlot.blotName = 'variable';
    VariableBlot.tagName = 'span';

    quill.register(VariableBlot);

    // Handle keyboard events for deleting variables
    quill.keyboard.addBinding(
      {
        key: 'backspace',
      },
      (range, context) => {
        // Check if we're at the edge of a variable node
        const [line, offset] = quill.getLine(range.index);
        const formats = quill.getFormat(range.index - 1, 1);

        if (formats.variable) {
          // Delete the entire variable
          quill.deleteText(
            range.index - formats.variable.length - 4,
            formats.variable.length + 4,
          );
          return false;
        }
        return true;
      },
    );

    quill.keyboard.addBinding(
      {
        key: 'delete',
      },
      (range, context) => {
        // Check if we're at the edge of a variable node
        const formats = quill.getFormat(range.index, 1);

        if (formats.variable) {
          // Delete the entire variable
          quill.deleteText(range.index, formats.variable.length + 4);
          return false;
        }
        return true;
      },
    );
  }
});
</script>

<style scoped>
.variable-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.variable-chip {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.variable-chip:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.rich-text-editor {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.label {
  font-weight: 500;
  margin-bottom: 4px;
}

/* Add styles for the editor to handle variables better */
:deep(.ai-variable) {
  background-color: #e3f2fd;
  padding: 2px 4px;
  border-radius: 4px;
  color: #1976d2;
  font-weight: bold;
  display: inline-block;
  margin: 0 2px;
  user-select: all;
  cursor: default;
  pointer-events: all;
}

:deep(.ql-custom-variable) {
  &:after {
    content: 'VAR';
    font-size: 12px;
    font-weight: bold;
  }
}
</style>
