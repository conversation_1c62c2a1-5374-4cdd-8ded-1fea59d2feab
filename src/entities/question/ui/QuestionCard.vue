<template>
  <q-card class="question-card">
    <q-card-section>
      <div class="row justify-between items-center">
        <h5 class="q-ma-none">{{ question.question }}</h5>
        <q-chip v-if="question.hasAI" color="secondary">AI</q-chip>
      </div>
      <p class="q-ma-sm text-caption">{{ question.description }}</p>
      <q-chip small :color="answerElementColor[question.answerElement]">
        {{ question.answerElement }}
      </q-chip>
    </q-card-section>

    <q-card-actions>
      <q-btn flat @click="$emit('edit')">Edit</q-btn>
      <q-btn flat @click="$emit('delete')">Delete</q-btn>
    </q-card-actions>
  </q-card>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
import { AnswerElement, Question } from 'src/entities/question/model';

defineProps<{
  question: Question;
}>();

defineEmits<{
  (e: 'edit'): void;
  (e: 'delete'): void;
}>();

const answerElementColor: Record<AnswerElement, string> = {
  text: 'blue',
  textarea: 'indigo',
  select: 'green',
  options: 'orange',
};
</script>
