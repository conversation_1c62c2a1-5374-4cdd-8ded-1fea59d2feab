<template>
  <div class="answer-preview q-pa-md">
    <div class="text-subtitle2 q-mb-md text-primary">
      <q-icon name="visibility" class="q-mr-xs" />
      Preview: User Response
    </div>

    <!-- Text input preview -->
    <q-input
      v-if="answerElement === 'text'"
      v-model="localValue"
      label="Your answer"
      filled
      readonly
      class="preview-element q-mb-sm"
      bottom-slots
      hint="Short text response"
      bg-color="white"
    >
      <template v-slot:prepend>
        <q-icon name="edit" color="primary" />
      </template>
      <template v-slot:append v-if="hasAI">
        <q-btn flat icon="img:robot.png" :disable="true">
          <q-tooltip>Ask Manny</q-tooltip>
        </q-btn>
      </template>
    </q-input>

    <!-- Textarea preview -->
    <q-input
      v-if="answerElement === 'textarea'"
      v-model="localValue"
      label="Your answer"
      type="textarea"
      filled
      readonly
      rows="3"
      class="preview-element q-mb-sm"
      bottom-slots
      hint="Long text response"
      bg-color="white"
    >
      <template v-slot:prepend>
        <q-icon name="subject" color="primary" />
      </template>
      <template v-slot:append v-if="hasAI">
        <q-btn flat icon="img:robot.png" :disable="true">
          <q-tooltip>Ask Manny</q-tooltip>
        </q-btn>
      </template>
    </q-input>

    <!-- Select dropdown preview -->
    <q-select
      v-if="answerElement === 'select'"
      v-model="localValue"
      :options="options || []"
      label="Select an option"
      filled
      readonly
      class="preview-element q-mb-sm"
      :disable="!options || options.length === 0"
      bottom-slots
      hint="Dropdown selection"
      bg-color="white"
    >
      <template v-slot:prepend>
        <q-icon name="arrow_drop_down_circle" color="primary" />
      </template>
      <template v-slot:append v-if="hasAI">
        <q-btn flat icon="img:robot.png" :disable="true">
          <q-tooltip>Ask Manny</q-tooltip>
        </q-btn>
      </template>
    </q-select>

    <!-- Radio options preview -->
    <div v-if="answerElement === 'options'" class="preview-element q-mb-sm">
      <q-card flat bordered class="option-card">
        <q-card-section>
          <div class="text-body2 q-mb-md text-weight-medium">
            <q-icon
              name="radio_button_checked"
              color="primary"
              class="q-mr-xs"
            />
            Select one option:
          </div>
          <q-option-group
            v-model="localValue"
            :options="optionsFormatted"
            type="radio"
            readonly
            :disable="!options || options.length === 0"
            class="option-group"
          />
        </q-card-section>
      </q-card>
    </div>

    <!-- Empty state when no options are provided -->
    <div
      v-if="
        (answerElement === 'select' || answerElement === 'options') &&
        (!options || options.length === 0)
      "
      class="empty-state q-pa-md text-center"
    >
      <q-icon name="add_circle" size="2rem" color="grey-7" />
      <div class="q-mt-sm text-grey-7">Add options to see the preview</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';

const props = defineProps<{
  answerElement: string;
  answerValue?: string;
  options?: string[];
  hasAI?: boolean;
}>();

const localValue = ref(props.answerValue || '');

// Format options for radio buttons
const optionsFormatted = computed(() => {
  return (props.options || []).map((opt) => ({
    label: opt,
    value: opt,
  }));
});

// Update local value when props change
watch(
  () => props.answerValue,
  (newVal) => {
    if (newVal !== undefined) {
      localValue.value = newVal;
    }
  },
);

// Set default value for select/options when options change
watch(
  () => props.options,
  (newOptions) => {
    if (props.answerElement === 'select' || props.answerElement === 'options') {
      if (newOptions && newOptions.length > 0 && !localValue.value) {
        localValue.value = newOptions[0];
      }
    }
  },
  { immediate: true },
);
</script>

<style scoped>
.answer-preview {
  background-color: #f8f9fa;
  border-radius: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.preview-element {
  max-width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.option-card {
  border-radius: 8px;
  background-color: white;
  transition: all 0.2s ease;
}

.option-group {
  padding: 0;
}

.option-group :deep(.q-radio) {
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 8px;
  transition: all 0.2s ease;
  background-color: white;
}

.option-group :deep(.q-radio:hover) {
  background-color: #f5f5f5;
  border-color: #00569b;
}

.option-group :deep(.q-radio__inner) {
  font-size: 15px;
}

.option-group :deep(.q-radio__label) {
  font-size: 15px;
  line-height: 1.4;
}

.option-group :deep(.q-radio__inner--truthy) {
  color: #00569b;
}

.empty-state {
  background-color: white;
  border-radius: 8px;
  border: 1px dashed #e0e0e0;
  padding: 24px;
  margin-top: 8px;
}

/* Add a subtle hover effect to the preview container */
.answer-preview:hover {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

/* Make the inputs look more like they would in a real form */
:deep(.q-field__control) {
  background-color: white !important;
}

:deep(.q-field--readonly) {
  opacity: 0.9;
}

:deep(.q-field__label) {
  color: #00569b;
  font-weight: 500;
}

:deep(.q-field__bottom) {
  padding: 4px 12px;
  font-size: 12px;
  color: #6c757d;
}
</style>
