import { httpsCallable } from 'firebase/functions';
import { functions } from 'src/firebase';

/**
 * Sends an email using Firebase cloud functions.
 * @param {any} emailData The data to be sent via email.
 * @returns {Promise<boolean>} A promise that resolves to true if the email was sent successfully.
 */
export const sendEmail = (emailData: any): Promise<boolean> => {
  const sendEmailFunction = httpsCallable(functions, 'sendEmail');
  return sendEmailFunction(emailData).then((response) => {
    console.log('Send email: ', response);
    return true;
  });
};

export const fetchResetPasswordLink = async (
  userId: string,
): Promise<string | undefined> => {
  const getResetPasswordLink = httpsCallable(functions, 'getResetPasswordLink');
  const response = await getResetPasswordLink({ userId });
  return response?.data as string | undefined;
};

/**
 * Formats a timestamp into a human-readable relative time string.
 * @param {string | number | Date} timestamp The timestamp to format.
 * @returns {string} A formatted string representing the relative time.
 */
export const formatDate = (timestamp: string | number | Date): string => {
  const now = new Date();
  const date = new Date(timestamp);
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'Just now';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} hour${hours > 1 ? 's' : ''} ago`;
  } else if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} day${days > 1 ? 's' : ''} ago`;
  } else {
    // Show the full date if the difference is more than a week
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }
};

/**
 * Converts a timestamp into a human-readable date string.
 * @param {number} timestamp The timestamp to convert.
 * @returns {string} A formatted date string.
 */
export const getHumanReadableDate = (timestamp: number): string => {
  const now = new Date(timestamp); // Get the current date from Date.now()
  return now.toLocaleString('en-US', {
    // Example format for US
    weekday: 'long',
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: true, // Use 12-hour format with AM/PM
  });
};
export const getShortHumanReadableDate = (timestamp: number): string => {
  const now = new Date(timestamp); // Get the current date from Date.now()
  return now.toLocaleString('en-US', {
    // Example format for US
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true, // Use 12-hour format with AM/PM
  });
};
