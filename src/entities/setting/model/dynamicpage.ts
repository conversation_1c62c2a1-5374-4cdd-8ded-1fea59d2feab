import { useDynamicPageStore } from 'src/entities/setting/store';

const dynamicPageStore = useDynamicPageStore();

/**
 * Fetches the Terms of Service page content.
 * @returns A promise that resolves to the content of the Terms of Service page.
 */
export const tosPage: () => Promise<any> = async () => {
  return await dynamicPageStore.getDynamicPage('tos');
};

/**
 * Fetches the Privacy Policy page content.
 * @returns A promise that resolves to the content of the Privacy Policy page.
 */
export const privacyPage: () => Promise<any> = async () => {
  return await dynamicPageStore.getDynamicPage('privacy');
};
