import { computed, Ref } from 'vue';
import { isAdmin } from 'src/entities/user';
import { useBookSettingsStore } from 'src/entities/setting/store';

const bookSettingStore = useBookSettingsStore();
await bookSettingStore.listBookSettings();

// Fetching book settings asynchronously
const bookSettings = computed(() => bookSettingStore.bookSetting);

/**
 * Determines if prompt editing is allowed.
 * @returns {boolean} True if prompt editing is allowed, otherwise false.
 */
export const canEditPrompt = computed(
  () => bookSettings.value?.canEditPrompt === true && isAdmin,
);

/**
 * Determines if printing a book is allowed.
 * @returns {boolean} True if printing is allowed, otherwise false.
 */
export const canPrintBook = computed<boolean>(
  () => bookSettings.value?.can_print_book === true,
);

/**
 * Determines if uploading chapter content is allowed.
 * @returns {boolean} True if uploading is allowed, otherwise false.
 */
export const allowUploadChapterContent = computed<boolean>(
  () => bookSettings.value?.allow_upload_chapter === true,
);

/**
 * Gets the maximum file size limit for media uploads in chapters.
 * @returns {number} The file size limit in MB.
 */
export const maxChapterMediaFileSizeUpload = computed<number>(
  () => bookSettings.value?.upload_media_filesize_limit ?? 50,
);

/**
 * Gets the maximum media duration limit for chapters.
 * @returns {number} The duration limit in seconds.
 */
export const maxChapterMediaDurationLimit = computed<number>(
  () => bookSettings.value?.max_duration_limit ?? 600,
);

/**
 * Gets the allowed number of transcriptions per chapter.
 * @returns {number} The number of allowed transcriptions.
 */
export const maxChapterMediaTranscripts = computed<number>(
  () => bookSettings.value?.allowed_transcriptions_every_chapter ?? 100,
);

/**
 * Gets the allowed number of chats per chapter.
 * @returns {number} The number of allowed chats.
 */
export const maxChapterAllowedChats = computed<number>(
  () => bookSettings.value?.allowed_chats_every_chapter ?? 3,
);

/**
 * Gets the allowed number of chats per chapter.
 * @returns {number} The number of allowed chats.
 */
export const maxChatsPerDay = computed<number>(
  () => bookSettings.value?.allowed_chat_messages_per_day ?? 30,
);
