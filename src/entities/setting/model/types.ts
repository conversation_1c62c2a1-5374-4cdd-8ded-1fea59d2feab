// Interface for global settings configuration
export interface GlobalSetting {
  maintenance: boolean; // Indicates if the system is under maintenance
  maintenance_message: string; // Message to display during maintenance
}

// Interface for subscription settings configuration
export interface SubscriptionSetting {
  show_paypal_details: boolean; // Flag to show or hide PayPal details
}

// Interface for book settings configuration
export interface BookSetting {
  canEditPrompt: true; // Allows editing of prompts
  allow_upload_chapter: boolean; // Allows uploading of new chapters
  can_print_book: boolean; // Allows printing of the book
  upload_media_filesize_limit: number; // Maximum file size limit for media uploads
  max_duration_limit: number; // Maximum duration limit for media
  allowed_transcriptions_every_chapter: number; // Number of allowed transcriptions per chapter
  allowed_chat_messages_per_day: number; // Number of allowed chat messages a day
  allowed_chats_every_chapter: number; // Number of allowed chats per chapter
}

// Interface for a dynamic page structure
export interface DynamicPage {
  page: string; // Identifier for the page
  html_content: string; // HTML content of the page
}

// Interface for dynamic page settings configuration
export interface DynamicPageSetting {
  pages: DynamicPage[]; // Array of dynamic pages
}

// Interface for prompt settings configuration
export interface PromptSetting {
  mission: any; // Mission statement
  transformations: any; // Transformations
  problems: any; // Problems
  need: any; // Need
  chapter: any; // Chapter
  title: any; // Title
  subtitle: any; // Subtitle
}
