import { computed, ref } from 'vue';
import { getCssVar, Screen } from 'quasar';
import { useGlobalSettingsStore } from 'src/entities/setting/store';

// Fetch global settings from the server
const globalSettingsStore = useGlobalSettingsStore();
await globalSettingsStore.listGlobalSettings();

// Computed property to check if the system is in maintenance mode
export const isMaintenanceMode = computed(
  () => globalSettingsStore.globalSetting?.maintenance === true,
);

// Computed property for maintenance message
export const maintenanceMessage = computed(
  () =>
    globalSettingsStore.globalSetting?.maintenance_message ??
    'Our system is currently undergoing maintenance to enhance performance and stability. Please expect temporary disruptions. We appreciate your understanding.',
);

// Fetch primary color from CSS variables
export const primaryColor = getCssVar('primary');

// Style for the scrollbar thumb
export const thumbStyle = ref({
  right: '4px',
  borderRadius: '5px',
  backgroundColor: primaryColor,
  width: '5px',
  opacity: 1,
  zIndex: 12,
});

// Style for the scrollbar track
export const barStyle = ref({
  right: '2px',
  borderRadius: '9px',
  backgroundColor: primaryColor,
  width: '9px',
  opacity: 0.2,
  zIndex: 12,
});

/**
 * Trims text to a specified maximum length, adding ellipsis if necessary.
 * @param text {string} - The text to trim.
 * @param maxLength {number} - The maximum length of the text.
 * @returns {string} - The trimmed text.
 */
export const trimText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) {
    return text;
  }
  return text.slice(0, maxLength) + '...';
};

// Computed property to determine if the top navigation should be transparent based on screen size
export const isTopNavDefault = computed(() => Screen.lt.sm);

/**
 * Determines if the current browser is Safari.
 * @returns {boolean} - True if the browser is Safari, false otherwise.
 */
export const isSafari = (): boolean => {
  const userAgent = navigator.userAgent.toLowerCase();
  const vendor = navigator.vendor?.toLowerCase() || '';

  // Check for Safari or Safari-like browsers
  const isSafari =
    /safari/.test(userAgent) && !/chrome|chromium|edge|opera/.test(userAgent);

  return isSafari;
};
