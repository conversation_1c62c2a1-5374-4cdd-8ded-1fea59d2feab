// dynamicPageStore.ts
import { defineStore } from 'pinia';
import { ref, Ref } from 'vue';
import { doc, getDoc, onSnapshot, updateDoc } from 'firebase/firestore';
import { firestore } from 'src/firebase';
import { includeIdAndFixTimestamp } from 'src/shared/api/firestore-converters';
import { DynamicPage, DynamicPageSetting } from 'src/entities/setting';
import { Unsubscribe } from '@firebase/firestore';

export const useDynamicPageStore = defineStore('dynamicPage', () => {
  // State
  const dynamicPageSetting: Ref<DynamicPageSetting | null> = ref(null);
  let unSub: Unsubscribe; // Unsubscribe function for user subscription listener

  // Actions
  const listDynamicPageSettings = async (): Promise<void> => {
    const docRef = doc(firestore, 'settings', 'pages').withConverter(
      includeIdAndFixTimestamp<DynamicPageSetting>(),
    );

    // Fetch the initial document
    const snapshot = await getDoc(docRef);
    dynamicPageSetting.value = snapshot.data() ?? null;

    // Listen for real-time updates
    unSub = onSnapshot(docRef, (doc) => {
      dynamicPageSetting.value = doc.data() ?? null;
    });
  };

  const getDynamicPage = async (
    pageName: string,
  ): Promise<string | undefined> => {
    if (!dynamicPageSetting.value) {
      await listDynamicPageSettings();
    }

    const firstMatch: DynamicPage | undefined =
      dynamicPageSetting.value?.pages?.find(
        (obj: DynamicPage) => obj.page === pageName,
      );
    return firstMatch?.html_content;
  };

  const savePageByPageName = async (
    pageName: string,
    newHtmlContent: string,
  ): Promise<void> => {
    const docRef = doc(firestore, 'settings', 'pages').withConverter(
      includeIdAndFixTimestamp<DynamicPageSetting>(),
    );

    // Fetch current settings
    const snapshot = await getDoc(docRef);
    const currentSettings = snapshot.data();

    if (!currentSettings) {
      throw new Error('Dynamic page settings not found.');
    }

    // Find and update the specific page
    const pages = currentSettings.pages ?? [];
    const pageIndex = pages.findIndex(
      (page: DynamicPage) => page.page === pageName,
    );

    if (pageIndex === -1) {
      throw new Error(`Page with name "${pageName}" not found.`);
    }

    // Update the HTML content
    pages[pageIndex] = {
      ...pages[pageIndex],
      html_content: newHtmlContent,
    };

    // Save back to Firestore
    await updateDoc(docRef, { pages });
  };

  /**
   * Cleans up the listeners when the store is destroyed.
   */
  const cleanupListeners = (): void => {
    if (unSub) unSub();
  };

  // Return state and actions
  return {
    dynamicPageSetting,
    cleanupListeners,
    listDynamicPageSettings,
    getDynamicPage,
    savePageByPageName,
  };
});
