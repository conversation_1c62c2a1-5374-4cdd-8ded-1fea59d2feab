// bookSettingsStore.ts
import { defineStore } from 'pinia';
import { ref, Ref } from 'vue';
import { doc, getDoc, onSnapshot } from 'firebase/firestore';
import { firestore } from 'src/firebase';
import { includeIdAndFixTimestamp } from 'src/shared/api/firestore-converters';
import { BookSetting } from '../model/types';
import { Unsubscribe } from '@firebase/firestore';

/**
 * Use book settings store composed API.
 * @returns {object} The book settings store with state and actions.
 */
export const useBookSettingsStore = defineStore('bookSettings', () => {
  /** State: Current book setting */
  const bookSetting: Ref<BookSetting | null> = ref(null);
  let unSub: Unsubscribe; // Unsubscribe function for user subscription listener

  /**
   * Lists book settings by fetching them from Firestore and listening for updates.
   * @returns {Promise<void>} A promise that resolves when the initial fetch is complete.
   */
  const listBookSettings = async (): Promise<void> => {
    // Reference to the Firestore document with a custom converter
    const docRef = doc(firestore, 'settings', 'books').withConverter(
      includeIdAndFixTimestamp<BookSetting>(),
    );

    // Fetch the initial document
    const snapshot = await getDoc(docRef);
    bookSetting.value = snapshot.data() ?? null;

    // Listen for real-time updates
    unSub = onSnapshot(docRef, (doc) => {
      bookSetting.value = doc.data() ?? null;
    });
  };

  /**
   * Cleans up the listeners when the store is destroyed.
   */
  const cleanupListeners = (): void => {
    if (unSub) unSub();
  };

  // Return state and actions
  return {
    bookSetting,
    cleanupListeners,
    listBookSettings,
  };
});
