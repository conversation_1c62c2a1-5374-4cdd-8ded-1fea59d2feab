// PromptSettingStore.ts
import { defineStore } from 'pinia';
import { ref, Ref } from 'vue';
import { doc, getDoc, onSnapshot, setDoc } from 'firebase/firestore';
import { firestore } from 'src/firebase';
import { includeIdAndFixTimestamp } from 'src/shared/api/firestore-converters';
import { PromptSetting } from '../model/types';
import { Unsubscribe } from '@firebase/firestore';

/**
 * Use prompt settings store composed API.
 * @returns The book settings store with state and actions.
 */
export const usePromptSettingStore = defineStore('promptsettings', () => {
  /** State: Current prompt setting */
  const promptSetting: Ref<PromptSetting | null> = ref(null);

  /** Unsubscribe function for user subscription listener */
  let unSub: Unsubscribe;

  /**
   * Lists prompt settings by fetching them from Firestore and listening for updates.
   * @returns A promise that resolves when the initial fetch is complete.
   */
  const listPromptSetting = async (): Promise<void> => {
    const docRef = doc(firestore, 'settings', 'prompts').withConverter(
      includeIdAndFixTimestamp<PromptSetting>(),
    );

    const snapshot = await getDoc(docRef);
    promptSetting.value = snapshot.data() ?? null;

    unSub = onSnapshot(docRef, (doc) => {
      promptSetting.value = doc.data() ?? null;
    });
  };

  /**
   * Updates prompt settings in Firestore.
   * @param newData The new data to merge into the existing settings.
   */
  const setPromptsSettings = async (
    newData: Partial<PromptSetting>,
  ): Promise<void> => {
    await setDoc(doc(firestore, 'settings', 'prompts'), newData, {
      merge: true,
    });
  };

  /**
   * Cleans up the listeners when the store is destroyed.
   */
  const cleanupListeners = (): void => {
    if (unSub) unSub();
  };

  return {
    promptSetting,
    setPromptsSettings,
    cleanupListeners,
    listPromptSetting,
  };
});
