<template>
  <q-card
    :class="{
      'full-width': $q.screen.lt.sm, // Full width on xs and sm
    }"
  >
    <q-card-section>
      <h1>Privacy Policy</h1>
    </q-card-section>
    <q-separator />

    <q-card-section>
      <q-scroll-area
        :thumb-style="thumbStyle"
        :bar-style="barStyle"
        :style="{ height: `calc(100vh - ${pageHeight}px)` }"
      >
        <div v-html="page"></div>
      </q-scroll-area>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { barStyle, privacyPage, thumbStyle } from 'src/entities/setting';
import { useQuasar } from 'quasar';

const props = withDefaults(
  defineProps<{
    pageHeight?: number; // Optional height of the page
    pageWidth?: string; // Optional width of the page as a string
  }>(),
  {
    pageHeight: 250, // Default height of the page
    pageWidth: '1024px', // Default width of the page
  },
);
const page = ref(''); // Reference to store the page content
const $q = useQuasar(); // Quasar framework hook

onMounted(async () => {
  page.value = (await privacyPage()) as string; // Fetch and store the privacy page content on mount
});
</script>

<style scoped lang="scss"></style>
