<template>
  <q-card
    :class="{
      'full-width': $q.screen.lt.sm, // Full width on xs and sm
    }"
  >
    <q-card-section class="bg-primary text-white">
      <div class="row items-center">
        <q-icon name="public" size="2em" class="q-mr-md" />
        <div class="text-h5">Terms and Conditions</div>
      </div>
    </q-card-section>

    <q-separator />

    <q-card-section>
      <q-scroll-area
        :thumb-style="thumbStyle"
        :bar-style="barStyle"
        :style="{ height: `calc(100vh - ${pageHeight}px)` }"
      >
        <div v-html="page"></div>
      </q-scroll-area>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { barStyle, thumbStyle, tosPage } from 'src/entities/setting';
import { useQuasar } from 'quasar';

// Define component props with default values
const props = withDefaults(
  defineProps<{
    pageHeight?: number; // Optional prop for page height
    pageWidth?: string; // Optional prop for page width
  }>(),
  {
    pageHeight: 250, // Default height of the page
    pageWidth: '1024px', // Default width of the page
  },
);

const page = ref(''); // Reactive reference for storing page content
const $q = useQuasar(); // Quasar framework hook

onMounted(async () => {
  page.value = (await tosPage()) as string; // Fetch and store the page content on mount
});
</script>

<style scoped lang="scss"></style>
