import type { Image } from 'src/entities/image'; // Warning: import violates FSD

export interface BookOutlines {
  introduction?: ChapterOutline;
  conclusion?: ChapterOutline;
  outlines?: ChapterOutline[];
}

export interface ChapterOutline {
  /** The Firestore document id of the chapter in the "chapters" subcollection */
  id: string;
  /** The name given to this chapter, which will appear in the table of contents */
  title: string;
  /** The outline information */
  outline: string;
  /** A cached value of the chapter's word count to avoid requesting the chapter doc every time */
  wordCount: number;
  // The following properties are legacy for compatibility
  /** @deprecated */
  isSection: boolean;
  /** @deprecated */
  number: number;
  /** The audiobook information */
  audio?: string;
  assistantbot?: { [key: string]: any };
}

export type NewChapterOutline = Omit<ChapterOutline, 'id'>;

// Non-Fiction Book
export interface MissionQuestions {
  gender: 'Males' | 'Females' | 'Both';
  age: string[];
  description: string;
  need: string;
  problems: string;
  readerImpact: string;
  authorImpact: string;
}

// Auto-Bio Book
export interface AutoBioGraphyMissionQuestions {
  gender: 'Males' | 'Females' | 'Both';
  age: string[];
  keyMessage: string;
  mainReason: string;
  description: string;
  bookOrder: string;
  bookFocused: string;
}

export interface FaithStoryMissionQuestions {
  gender: 'Males' | 'Females' | 'Both';
  age: string[];
  keyLifeEvents: string;
  readersTakeaway: string;
  mainReason: string;
  description: string;
  bestDescription: string;
  otherBestDescription: string;
  bookOrder: string;
  bookFocused: string;
  authorImpact: string;
}

export interface NewBook {
  title: string;
  subtitle: string;
  authorId: string;
  authorName: string;
  category: string;
  pageSetup?: any;
  drawer?: {
    right: boolean;
    left: number;
  };
  introduction: NewChapterOutline;
  conclusion: NewChapterOutline;
  chapters: NewChapterOutline[];
  mission: string;
  coverColor?: string;
  deleted?: boolean;
  voice: {
    [key: string]: any;
  };

  missionQuestions:
    | MissionQuestions
    | AutoBioGraphyMissionQuestions
    | FaithStoryMissionQuestions
    | object;
  // The following properties are legacy for compatibility, but should also be migrated to separate data structures (subcollections)
  footnotes: any[];
  images: Image[];
  comments: any[];
}

export interface Book extends NewBook {
  id: string;
  createdAt: Date;
  introduction: ChapterOutline;
  conclusion: ChapterOutline;
  chapters: ChapterOutline[];
}

export interface BookChapters {
  introduction?: Chapter;
  conclusion?: Chapter;
  chapters?: Chapter[];
}

export interface Chapter {
  id: string;
  content: string;
}

export interface ChapterMedia {
  id: string;
  idRef?: string;
  bookId?: string;
  chapterId?: string;
  fileName?: string;
  src?: string;
  createdAt?: Date;
  transcription?: string;
  isProcessing?: boolean;
  openai_response?: string;
}
