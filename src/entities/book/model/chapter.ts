import { generateRandomId } from 'src/entities/user';

export type { Book, MissionQuestions, ChapterOutline } from './types';
import type { Chapter, ChapterMedia, NewChapterOutline } from './types';

export function createEmptyChapterOutline(): NewChapterOutline {
  return {
    title: '',
    outline: '',
    wordCount: 0,
    number: 0,
    isSection: false,
  };
}

export function createEmptyChapter(): Omit<Chapter, 'id'> {
  return {
    content: '',
  };
}

export function createEmptyChapterTranscription(): ChapterMedia {
  return {
    id: generateRandomId(20),
    bookId: '',
    chapterId: '',
    fileName: '',
    src: '',
    transcription: '',
    isProcessing: false,
    openai_response: '',
  };
}
