import { createEmptyChapterOutline, MissionQuestions } from './chapter';
import {
  AutoBioGraphyMissionQuestions,
  FaithStoryMissionQuestions,
  NewBook,
} from './types';

export function createEmptyBook(bookCategory: string = 'non-fiction'): NewBook {
  let missionQuestions:
    | MissionQuestions
    | AutoBioGraphyMissionQuestions
    | FaithStoryMissionQuestions;
  switch (bookCategory) {
    case 'autobiography':
      missionQuestions = {
        gender: 'Both',
        age: [],
        description: '',
        keyMessage: '',
        mainReason: '',
        bookOrder: '',
        bookFocused: '',
      } as AutoBioGraphyMissionQuestions;
      break;
    case 'faithstory':
      missionQuestions = {
        gender: 'Both',
        age: [],
        description: '',
        keyLifeEvents: '',
        readersTakeaway: '',
        mainReason: '',
        bookOrder: '',
        bookFocused: '',
        authorImpact: '',
        bestDescription: '',
        otherBestDescription: '',
      } as FaithStoryMissionQuestions;
      break;
    default:
      missionQuestions = {
        gender: 'Both',
        age: [],
        description: '',
        need: '',
        problems: '',
        readerImpact: '',
        authorImpact: '',
      } as MissionQuestions;
  }
  return {
    title: '',
    category: bookCategory as 'non-fiction' | 'autobiography' | 'faithstory',
    subtitle: '',
    authorId: '',
    authorName: '',
    introduction: createEmptyChapterOutline(),
    conclusion: createEmptyChapterOutline(),
    chapters: [],
    mission: '',
    voice: {},
    missionQuestions: missionQuestions,
    footnotes: [],
    comments: [],
    images: [],
  };
}
