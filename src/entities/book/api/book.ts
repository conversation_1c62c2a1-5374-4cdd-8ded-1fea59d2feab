import { firestore } from 'src/firebase';
import {
  addDoc,
  collection,
  doc,
  DocumentReference,
  getDoc,
  getDocs,
  onSnapshot,
  query,
  serverTimestamp,
  setDoc,
  updateDoc,
  where,
  writeBatch,
} from 'firebase/firestore';
import { type Ref, ref, unref } from 'vue';
import type { Book } from '../model';
import { includeIdAndFixTimestamp } from 'src/shared/api/firestore-converters';
import type { NewBook } from '../model/types';
import { getDocSync } from 'src/shared/api/firestore-sync';
import { ChapterOutline, createEmptyBook } from '../model';
import {
  listFormattedBookOutline,
  listOutlines,
  updateBookOutlinesFromOnboarding,
} from 'src/entities/book';
import { deleteBookByApi } from 'src/shared/api/book';
import { generateRandomId, user } from 'src/entities/user';

/**
 * List all of my books (with listening to updates from Firestore)
 * @param uid The current user's UID
 */
export function listBooks(
  uid: string,
  addNewBook: boolean = false,
): Ref<Book[]> {
  const booksQuery = query(
    collection(firestore, 'books'),
    where('authorId', '==', uid),
  ).withConverter(includeIdAndFixTimestamp<Book>());

  const initialBook: Book[] = [];
  const books = ref<Book[]>(initialBook);
  // Warning: possible memory leak
  onSnapshot(booksQuery, (snap) => {
    books.value = snap.docs.map((doc) => doc.data());
    if (addNewBook) {
      books.value.unshift(createEmptyBook() as Book);
    }
  });
  return books;
}
export async function listBooksWithoutSync(uid: string) {
  const booksQuery = query(
    collection(firestore, 'books'),
    where('authorId', '==', uid),
  ).withConverter(includeIdAndFixTimestamp<Book>());

  const books: Book[] = [];
  const bokkDocs = await getDocs(booksQuery);
  bokkDocs.docs.forEach((snap) => {
    books.push(snap.data()!);
  });

  return books;
}
/** Add a new book to Firestore */
export async function setNewBook(
  category: string = 'non-fiction',
): Promise<Book> {
  // Create a new book object
  const book = {
    ...createEmptyBook(category),
    authorId: user?.value?.uid,
    title: 'Untitled Book',
  };
  const books = collection(firestore, 'books');
  const bookDoc = await addDoc(books, {
    ...book,
    createdAt: serverTimestamp(),
  });

  // Create documents for the chapters in the outline
  await setDoc(doc(bookDoc, 'chapters', 'introduction'), {
    content: '',
  });
  await setDoc(doc(bookDoc, 'chapters', 'conclusion'), {
    content: '',
  });
  await setDoc(doc(bookDoc, 'outlines', 'introduction'), {
    id: 'introduction',
    title: 'Introduction',
    outline: '',
    isSection: false,
    number: 0,
    wordCount: 0,
  });
  await setDoc(doc(bookDoc, 'outlines', 'conclusion'), {
    id: 'conclusion',
    title: 'Conclusion',
    outline: '',
    isSection: false,
    number: 0,
    wordCount: 0,
  });

  const snap = await getDoc(
    bookDoc.withConverter(includeIdAndFixTimestamp<Book>()),
  );
  return snap.data()!;
}
export async function createBook(book: NewBook): Promise<Book> {
  const books = collection(firestore, 'books');
  const bookDoc = await addDoc(books, {
    ...book,
    createdAt: serverTimestamp(),
  });

  // Create documents for the chapters in the outline
  await setDoc(doc(bookDoc, 'chapters', 'introduction'), {
    content: '',
  });
  await setDoc(doc(bookDoc, 'chapters', 'conclusion'), {
    content: '',
  });
  await setDoc(doc(bookDoc, 'outlines', 'introduction'), {
    id: 'introduction',
    title: 'Introduction',
    outline: '',
    isSection: false,
    number: 0,
    wordCount: 0,
  });
  await setDoc(doc(bookDoc, 'outlines', 'conclusion'), {
    id: 'conclusion',
    title: 'Conclusion',
    outline: '',
    isSection: false,
    number: 0,
    wordCount: 0,
  });

  const chaptersIds = await Promise.all(
    book.chapters.map(async (chapter, idx) => {
      const chapterDoc = await addDoc(collection(bookDoc, 'chapters'), {
        content: '',
      });
      await setDoc(doc(bookDoc, 'outlines', chapterDoc.id), {
        id: chapterDoc.id,
        ...chapter,
      });
      return {
        ...chapter,
        id: chapterDoc.id,
      };
    }),
  );

  updateDoc(bookDoc, {
    'introduction.id': 'introduction',
    'conclusion.id': 'conclusion',
    chapters: chaptersIds,
  });

  const snap = await getDoc(
    bookDoc.withConverter(includeIdAndFixTimestamp<Book>()),
  );
  return snap.data()!;
}

/**
 * Returns a single snapshot (not automatically updated) of the book object
 *  from Firestore, and synchronizes changes to it back to Firestore.
 * @param id Book ID (of the Firestore document)
 */
export async function getBook(id: string): Promise<Ref<Book>> {
  const docRef = doc(firestore, 'books', id).withConverter(
    includeIdAndFixTimestamp<Book>(),
  );
  const book = await getDocSync(docRef);
  return book;
}

export async function updateBook(id: string, newData: Partial<Book>) {
  await updateBookOutlinesFromOnboarding(
    id,
    newData.chapters as ChapterOutline[],
  );
  return updateDoc(doc(firestore, 'books', id), newData);
}

export function setBook(id: string, newData: Partial<Book>) {
  setDoc(doc(firestore, 'books', id), newData, { merge: true });
}

/**
 * Function to delete a book
 * @param id - The book to delete
 */
export const deleteBookById = async (id: string) => {
  return await setDoc(
    doc(firestore, 'books', id),
    { deleted: true },
    { merge: true },
  );
};

export function deleteBook(id: string) {
  deleteBookByApi(id);
  // const bookDoc = doc(firestore, 'books', id);
  // const bookOutlines = unref(await listOutlines(id));
  //
  // const batch = writeBatch(firestore);
  // // Delete chapters of the book
  // for (const chapter of listFormattedBookOutline(bookOutlines)) {
  //   batch.delete(doc(bookDoc, 'chapters', chapter.id));
  //   batch.delete(doc(bookDoc, 'outlines', chapter.id));
  // }
  //
  // // Delete any versions of this book
  // const versionsCollection = collection(bookDoc, 'versions');
  // const versionsQuery = query(versionsCollection);
  // const versionsSnap = await getDocs(versionsQuery);
  //
  // for (const versionDoc of versionsSnap.docs) {
  //   // Fetch chapters as a subcollection of each version
  //   const chaptersCollection = collection(versionDoc.ref, 'chapters');
  //   const chaptersSnap = await getDocs(chaptersCollection);
  //
  //   // Fetch outlines as a subcollection of each version
  //   const outlinesCollection = collection(versionDoc.ref, 'outlines');
  //   const outlinesSnap = await getDocs(outlinesCollection);
  //
  //   // Delete chapters
  //   for (const chapterDoc of chaptersSnap.docs) {
  //     const chapterRef = chapterDoc.ref; // Reference to the chapter document
  //     batch.delete(chapterRef);
  //   }
  //
  //   // Delete outlines
  //   for (const outlineDoc of outlinesSnap.docs) {
  //     const outlineRef = outlineDoc.ref; // Reference to the outline document
  //     batch.delete(outlineRef);
  //   }
  //
  //   // Delete the version document itself
  //   batch.delete(versionDoc.ref);
  // }
  // batch.delete(bookDoc);
  // await batch.commit();
}
