import { htmlWordCount } from 'src/shared/lib/word-count';
import { watchDebounced } from '@vueuse/core';
import { firestore } from 'src/firebase';
import {
  arrayUnion,
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  setDoc,
  writeBatch,
} from 'firebase/firestore';
import { ref, type Ref, unref } from 'vue';
import { includeIdAndFixTimestamp } from 'src/shared/api/firestore-converters';
import type {
  Book,
  Chapter,
  ChapterMedia,
  ChapterOutline,
  NewChapterOutline,
} from '../model/types';
import { createEmptyChapter } from '../model';
import { getDocSync } from 'src/shared/api/firestore-sync';
import { listOutlines } from 'src/entities/book';
import { BookChapters } from '../model/types';

/**
 * Returns a single snapshot (not automatically updated) of the book object
 *  from Firestore, and synchronizes changes to it back to Firestore.
 * @param bookId Firestore document of the containing book
 * @param chapterId Chapter ID
 */
export async function getChapter(
  bookId: string,
  chapterId: string,
): Promise<Ref<Chapter>> {
  const docRef = doc(
    firestore,
    'books',
    bookId,
    'chapters',
    chapterId,
  ).withConverter(includeIdAndFixTimestamp<Chapter>());
  const chapter = await getDocSync(docRef);

  // Update word count

  const unwatch = watchDebounced(
    chapter,
    (newChapter) => {
      if (newChapter?.id) {
        updateWordCount(bookId, newChapter.id, newChapter.content);
      }
    },
    { debounce: 1000, deep: true },
  );

  return chapter;
}

/**
 * Returns a single snapshot (not automatically updated) of the book object
 *  from Firestore, and synchronizes changes to it back to Firestore.
 * @param bookId Firestore document of the containing book
 */
export async function listChapters(bookId: string) {
  const versionsQuery = query(
    collection(firestore, 'books', bookId, 'chapters'),
  ).withConverter(includeIdAndFixTimestamp<Chapter>());

  const chapterOutline: Ref<BookChapters> | undefined = ref<BookChapters>({});
  const outlines: Chapter[] = [];

  const outlineDocs = await getDocs(versionsQuery);
  outlineDocs.docs.forEach((snap) => {
    const outline = snap.data()!;
    if (!outline.id) outline.id = snap.ref.id;
    if (outline.id == 'introduction') {
      chapterOutline.value.introduction = outline;
    } else if (outline.id == 'conclusion') {
      chapterOutline.value.conclusion = outline;
    } else {
      outlines.push(outline);
    }
  });
  chapterOutline.value.chapters = outlines;

  return chapterOutline;
}

async function updateWordCount(
  bookId: string,
  chapterId: string,
  content: string,
) {
  const wordCount = htmlWordCount(content);

  const bookSnap = await getDoc(doc(firestore, 'books', bookId));
  const bookOutlines = unref(await listOutlines(bookId));

  const book = bookSnap.data() as Book;
  let chapter: ChapterOutline;
  if (chapterId === 'introduction') {
    chapter = bookOutlines.introduction as ChapterOutline;
    book.introduction.wordCount = wordCount;
  } else if (chapterId === 'conclusion') {
    chapter = bookOutlines.conclusion as ChapterOutline;
    book.conclusion.wordCount = wordCount;
  } else {
    chapter = bookOutlines.outlines?.find((ch) => ch.id === chapterId)!;
    // const idx = book.chapters.findIndex((ch) => ch.id === chapterId)!;
    // book.chapters[idx].wordCount = wordCount;
  }
  if (chapter && chapter.wordCount) {
    chapter.wordCount = wordCount;
  }
  // await setDoc(
  //   doc(firestore, 'books', bookId, 'outlines', chapterId),
  //   { id: chapterId, wordCount: wordCount },
  //   { merge: true },
  // );
  // await setDoc(doc(firestore, 'books', bookId), book);
}

export async function addChapter(
  bookId: string,
  chapterOutline: NewChapterOutline,
) {
  const emptyChapter = createEmptyChapter();
  const bookDoc = doc(firestore, 'books', bookId);
  const chaptersCol = collection(bookDoc, 'chapters');
  const outlinesCol = collection(bookDoc, 'outlines');

  const batch = writeBatch(firestore);
  // Because there is no `batch.add`
  const id = doc(chaptersCol).id;
  batch.set(doc(chaptersCol, id), emptyChapter);
  batch.update(bookDoc, {
    chapters: arrayUnion({
      ...chapterOutline,
      id,
    }),
  });
  batch.set(doc(outlinesCol, id), chapterOutline);
  await batch.commit();

  const newChapter = await getChapter(bookId, id);
  return newChapter.value;
}

export async function deleteChapter(bookId: string, chapterId: string) {
  const bookDoc = doc(firestore, 'books', bookId);
  const chapterDoc = doc(bookDoc, 'chapters', chapterId);
  const outlineDoc = doc(bookDoc, 'outlines', chapterId);

  const book = (await getDoc(bookDoc)).data() as Book;
  const batch = writeBatch(firestore);
  batch.delete(chapterDoc);
  batch.update(bookDoc, {
    chapters: book.chapters.filter((ch) => ch.id !== chapterId),
  });
  batch.delete(outlineDoc);

  await batch.commit();
}
