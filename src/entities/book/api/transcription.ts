import { firestore } from 'src/firebase';
import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  getDoc,
  getDocs,
  onSnapshot,
  orderBy,
  query,
  serverTimestamp,
  setDoc,
} from 'firebase/firestore';
import { includeIdAndFixTimestamp } from 'src/shared/api/firestore-converters';
import type { ChapterMedia } from '../model/types';
import { deleteAudioTranscribeSource } from 'src/features/book-transcribe';
import { ref, Ref } from 'vue';
import { getDocSync } from 'src/shared/api/firestore-sync';
import { BookOutlines, ChapterOutline } from '../model/types';

export async function addChapterTranscription(
  bookId: string,
  chapterId: string,
  newData: Partial<ChapterMedia>,
) {
  const bookDoc = doc(firestore, 'books', bookId);
  const chapterDoc = doc(bookDoc, 'chapters', chapterId);
  const chapterTranscriptions = collection(chapterDoc, 'transcriptions');
  const chapterTranscriptionDoc = await addDoc(chapterTranscriptions, {
    ...newData,
    createdAt: serverTimestamp(),
  });
  const snap = await getDoc(chapterTranscriptionDoc);

  const data = snap.data()!;

  return data;
}

export function updateChapterTranscription(
  bookId: string,
  chapterId: string,
  chapterTranscriptionId: string,
  newData: Partial<ChapterMedia>,
) {
  const bookDoc = doc(firestore, 'books', bookId);
  const chapterDoc = doc(bookDoc, 'chapters', chapterId);
  const chapterTranscriptionDoc = doc(
    chapterDoc,
    'transcriptions',
    chapterTranscriptionId,
  );
  setDoc(chapterTranscriptionDoc, newData, { merge: true });
}

export function deleteChapterTranscription(chaptermedia: ChapterMedia) {
  const bookDoc = doc(firestore, 'books', chaptermedia.bookId as string);
  const chapterDoc = doc(bookDoc, 'chapters', chaptermedia.chapterId as string);
  const chapterTranscriptionDoc = doc(
    chapterDoc,
    'transcriptions',
    chaptermedia.id as string,
  );
  deleteDoc(chapterTranscriptionDoc);
  deleteAudioTranscribeSource(chaptermedia);
}

export async function getChapterTranscription(
  chaptermedia: ChapterMedia,
): Promise<Ref<ChapterMedia>> {
  const bookDoc = doc(firestore, 'books', chaptermedia.bookId as string);
  const chapterDoc = doc(bookDoc, 'chapters', chaptermedia.chapterId as string);
  const chapterTranscriptionDoc = doc(
    chapterDoc,
    'transcriptions',
    chaptermedia.id as string,
  );
  const docRef = chapterTranscriptionDoc.withConverter(
    includeIdAndFixTimestamp<ChapterMedia>(),
  );
  const data = await getDocSync(docRef);
  return data;
}

export async function getChapterTranscriptions(
  bookId: string,
  chapterId: string,
): Promise<ChapterMedia[]> {
  const versionsQuery = query(
    collection(
      firestore,
      'books',
      bookId,
      'chapters',
      chapterId,
      'transcriptions',
    ),
    // orderBy('createdAt', 'desc'),
  );
  const transcripts: ChapterMedia[] = [] as ChapterMedia[];

  const transcriptDocs = await getDocs(versionsQuery);

  transcriptDocs.docs.forEach((doc) => {
    const transcript = doc.data()!;
    transcripts.push(transcript as ChapterMedia);
  });

  return transcripts;
}

type Callback = (data: ChapterMedia) => void;
export const setupChapterTranscriptionListener = (
  chaptermedia: ChapterMedia,
  callback: Callback,
) => {
  const unsubscribe = onSnapshot(
    doc(
      firestore,
      'books',
      chaptermedia.bookId as string,
      'chapters',
      chaptermedia.chapterId as string,
      'transcriptions',
      chaptermedia.id as string,
    ),
    (doc) => {
      if (doc.exists()) {
        // Call the callback with the document data
        // Create a new object without modifying any readonly fields
        const data = doc.data();
        callback(data as ChapterMedia);
      }
    },
    (err) => {},
  );
  return unsubscribe;
};
