import { firestore } from 'src/firebase';
import {
  collection,
  doc,
  getDocs,
  getDoc,
  orderBy,
  query,
  setDoc,
  writeBatch,
} from 'firebase/firestore';
import { type Ref, ref, unref } from 'vue';
import { includeIdAndFixTimestamp } from 'src/shared/api/firestore-converters';
import type { ChapterOutline, BookOutlines } from '../model/types';
import { getDocSync } from 'src/shared/api/firestore-sync';
import { BackupContent } from 'src/features/book-versioning';
import { htmlWordCount } from 'src/shared/lib/word-count';

/**
 * Returns a single snapshot (not automatically updated) of the book object
 *  from Firestore, and synchronizes changes to it back to Firestore.
 * @param bookId Firestore document of the containing book
 * @param chapterId Chapter ID
 */
export async function getOutline(
  bookId: string,
  chapterId: string,
): Promise<ChapterOutline> {
  const docRef = doc(
    firestore,
    'books',
    bookId,
    'outlines',
    chapterId,
  ).withConverter(includeIdAndFixTimestamp<ChapterOutline>());
  const outline = await getDoc(docRef);

  return outline.data()! as ChapterOutline;
}

/**
 * Returns a single snapshot (not automatically updated) of the book object
 * from Firestore, and synchronizes changes to it back to Firestore.
 * @param bookId Firestore document of the containing book
 */
export async function listOutlines(bookId: string): Promise<Ref<BookOutlines>> {
  const versionsQuery = query(
    collection(firestore, 'books', bookId, 'outlines'),
    orderBy('number', 'asc'),
  ).withConverter(includeIdAndFixTimestamp<ChapterOutline>());

  const chapterOutline: Ref<BookOutlines> = ref<BookOutlines>(
    {} as BookOutlines,
  );
  const outlines: ChapterOutline[] = [];

  // onSnapshot(versionsQuery, (snapshot) => {
  //   snapshot.docChanges().forEach((snap) => {
  //     const outline = snap.doc.data();
  //
  //     if (!outline.id) outline.id = snap.doc.ref.id;
  //
  //     if (outline.id === 'introduction') {
  //       console.log('-----------');
  //       console.log(outline);
  //       chapterOutline.value.introduction = outline;
  //     } else if (outline.id === 'conclusion') {
  //       chapterOutline.value.conclusion = outline;
  //     } else {
  //       const index = outlines.findIndex((o) => o.id === outline.id);
  //       if (index !== -1) {
  //         outlines.splice(index, 1, outline);
  //       } else {
  //         outlines.push(outline);
  //       }
  //     }
  //   });
  //   chapterOutline.value.outlines = [...outlines]; // Update the reactive outlines array
  // });

  const outlineDocs = await getDocs(versionsQuery);
  outlineDocs.docs.forEach((doc) => {
    const outline = doc.data();

    if (!outline.id) outline.id = doc.ref.id;

    if (outline.id === 'introduction') {
      chapterOutline.value.introduction = outline;
    } else if (outline.id === 'conclusion') {
      chapterOutline.value.conclusion = outline;
    } else {
      const index = outlines.findIndex((o) => o.id === outline.id);
      if (index !== -1) {
        outlines.splice(index, 1, outline);
      } else {
        outlines.push(outline);
      }
    }
  });
  chapterOutline.value.outlines = [...outlines]; // Update the reactive outlines array

  return chapterOutline;
}

export function listFormattedBookOutline(bookOutlines: BookOutlines) {
  const { introduction, outlines, conclusion } = bookOutlines;
  let chaptersOutlines: BackupContent[] | undefined = [...outlines!];
  chaptersOutlines.push(introduction as BackupContent);
  chaptersOutlines.push(conclusion as BackupContent);

  return chaptersOutlines;
}

export async function updateBookOutlinesFromOnboarding(
  id: string,
  newData: ChapterOutline[],
) {
  const bookDoc = doc(firestore, 'books', id);
  const bookOutlines = unref(await listOutlines(id));
  const batch = writeBatch(firestore);

  function saveBookOutlines(
    oldOutlines: ChapterOutline[],
    newOutlines: ChapterOutline[],
  ) {
    for (const outline of oldOutlines) {
      const foundIndex = newOutlines.findIndex(
        (oldOutline) => oldOutline.id === outline.id,
      );
      if (foundIndex !== -1) {
        const found = newOutlines[foundIndex];
        batch.set(
          doc(bookDoc, 'outlines', outline.id),
          {
            id: outline.id,
            number: foundIndex + 1,
            title: found.title,
          },
          { merge: true },
        );
      } else {
        batch.delete(doc(bookDoc, 'outlines', outline.id));
        batch.delete(doc(bookDoc, 'chapters', outline.id));
      }
    }

    newOutlines.forEach((newOutline) => {
      if (!newOutline.id) {
        const docRef = doc(collection(bookDoc, 'outlines'));
        newOutline.id = docRef.id;
        batch.set(docRef, newOutline);
        batch.set(doc(bookDoc, 'chapters', newOutline.id), { content: '' });
      }
    });
  }
  saveBookOutlines(bookOutlines.outlines as ChapterOutline[], newData);
  await batch.commit();
}

export async function updateOutlineNumbers(
  id: string,
  outlines: ChapterOutline[],
) {
  const bookDoc = doc(firestore, 'books', id);
  const batch = writeBatch(firestore);
  outlines.forEach((newOutline, key) => {
    batch.set(
      doc(bookDoc, 'outlines', newOutline.id),
      {
        id: newOutline.id,
        number: key + 1,
      },
      { merge: true },
    );
  });
  await batch.commit();
  return outlines;
}

export async function updateOutline(
  bookId: string,
  chapterId: string,
  newData: Partial<ChapterOutline>,
) {
  return setDoc(
    doc(firestore, 'books', bookId, 'outlines', chapterId),
    newData,
    {
      merge: true,
    },
  );
}

export function setOutlines(
  bookId: string,
  bookOutlines: Ref<BookOutlines>,
  chapterId: string,
  newData: Partial<ChapterOutline>,
): BookOutlines {
  if (chapterId) {
    updateOutline(bookId, chapterId, newData);
  }
  return bookOutlines.value;
}

export function setOutlineWordCount(
  bookId: string,
  title: string,
  bookOutlines: Ref<BookOutlines>,
  chapterId: string,
  content: string,
) {
  const wordCountTitle = htmlWordCount(title);
  const wordCount = htmlWordCount(content);
  const newData = {
    title: title,
    wordCount: wordCount + wordCountTitle,
  };
  bookOutlines.value = setOutlines(bookId, bookOutlines, chapterId, newData);

  return { wordCount: wordCount + wordCountTitle, outline: bookOutlines.value };
}

export function setOutlineAudioBook(
  bookId: string,
  bookOutlines: Ref<BookOutlines>,
  chapterId: string,
  audioUrl: string,
) {
  bookOutlines.value = setOutlines(bookId, bookOutlines, chapterId, {
    audio: audioUrl,
  });
  if (chapterId === 'introduction' && bookOutlines.value.introduction) {
    bookOutlines.value.introduction.audio = audioUrl;
  } else if (chapterId === 'conclusion' && bookOutlines.value.conclusion) {
    bookOutlines.value.conclusion.audio = audioUrl;
  } else {
    bookOutlines.value.outlines?.map((ch) => {
      if (ch.id === chapterId) {
        ch.audio = audioUrl;
      }
      return ch;
    });
  }
  return bookOutlines.value;
}
