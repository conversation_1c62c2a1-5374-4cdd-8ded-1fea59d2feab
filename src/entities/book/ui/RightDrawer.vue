<template>
  <div id="right-panel">
    <q-tabs
      dense
      outside-arrows
      mobile-arrows
      inline-label
      v-model="sideView"
      indicator-color="primary"
      active-color="primary"
    >
      <q-separator v-if="isMobileView" />
      <q-tab icon="img:/robot.png" name="prompts" v-if="!isReviewMode">
        <q-tooltip> Manny Prompts</q-tooltip>
      </q-tab>
      <q-separator vertical v-if="!isReviewMode" />
      <q-tab icon="library_books" name="outline" v-if="!isReviewMode">
        <q-tooltip> Outline</q-tooltip>
      </q-tab>
      <q-separator vertical v-if="!isReviewMode" />
      <q-tab icon="wallpaper" name="images" v-if="!isReviewMode">
        <q-tooltip> Images</q-tooltip>
      </q-tab>
      <q-separator vertical v-if="!isReviewMode" />
      <q-tab icon="transcribe" name="transcripts" v-if="!isReviewMode">
        <q-tooltip> Transcripts</q-tooltip>
      </q-tab>
      <q-separator vertical v-if="!isReviewMode" />

      <q-tab
        icon="star"
        name="reviews"
        v-if="(features ?? []).includes('review')"
      >
        <q-tooltip> Reviews</q-tooltip>
      </q-tab>
      <q-separator vertical />

      <!--
      <q-tab icon="lyrics" name="audiobook">
        <q-tooltip>AudioBook</q-tooltip>
      </q-tab>
      <q-separator vertical />


      <q-tab icon="book" name="details" v-if="!isReviewMode">
        <q-tooltip>Book</q-tooltip>
      </q-tab>
      -->
      <q-separator vertical v-if="isMobileView" />
      <q-tab
        v-if="isMobileView"
        @click="
          splitterModel = 10;
          drawerRight = false;
        "
        icon="keyboard_double_arrow_right"
        :name="sideView"
        class="editor-tab"
      >
        <q-tooltip> Show Editor </q-tooltip>
      </q-tab>
    </q-tabs>
    <q-separator />

    <q-card flat class="q-px-md q-mt-sm">
      <q-card-section class="q-pa-none">
        <q-item-label overline>
          <h2 class="text-capitalize q-ma-none">{{ sideView }}</h2>
        </q-item-label>
      </q-card-section>
      <q-card-section class="q-pa-none">
        <q-input
          dense
          v-model="selectedChapterOutline.title"
          :disable="isLoadingChapter === true"
          class="q-pa-none"
          :borderless="titleBorderless"
          @focusin="titleBorderless = false"
          @mouseenter="titleBorderless = false"
          @mouseleave="titleBorderless = true"
          input-style="padding: 10px; margin: 0"
        />
      </q-card-section>
    </q-card>

    <q-separator spaced />
    <!-- The "keep-alive" here is to prevent the TinyMCE instances in the outline from taking
       over as active when switching tabs due to rerendering. -->
    <q-tab-panels v-model="sideView" animated keep-alive>
      <q-tab-panel name="outline" class="q-py-sm">
        <BookOutline
          :readonly="isInVersionPreview"
          :book="book"
          :outlines="outlines"
          :selectedChapterId="selectedChapterId"
          v-model:selectedChapterOutline="selectedChapterOutline"
          v-model:isLoadingChapter="isLoadingChapter"
          v-model:onMannyLoading="onMannyLoading"
          v-model:sideView="sideView"
        />
      </q-tab-panel>

      <q-tab-panel name="images" class="q-py-sm">
        <BookImages
          :readonly="isInVersionPreview"
          :book="book"
          :outlines="outlines"
          :selectedChapterOutline="selectedChapterOutline"
          :selectedChapterId="selectedChapterId"
          v-model:isLoadingChapter="isLoadingChapter"
          v-model:onMannyLoading="onMannyLoading"
          v-model:sideView="sideView"
        />
      </q-tab-panel>
      <q-tab-panel name="transcripts" class="q-py-sm">
        <BookTranscribe
          :readonly="isInVersionPreview"
          :book="book"
          :outlines="outlines"
          :selectedChapterOutline="selectedChapterOutline"
          :selectedChapterId="selectedChapterId"
          v-model:isLoadingChapter="isLoadingChapter"
          v-model:onMannyLoading="onMannyLoading"
          v-model:transcripts="transcripts"
          v-model:sideView="sideView"
          v-model:onCanTranscribe="onCanTranscribe"
        />
      </q-tab-panel>
      <q-tab-panel name="prompts" class="q-py-sm">
        <BookPrompts
          :selectedPromptText="selectedPromptText"
          :book="book"
          v-model:drawerRight="drawerRight"
          :readonly="isInVersionPreview"
          v-model:isLoadingChapter="isLoadingChapter"
          v-model:onSelectedPrompt="onSelectedPrompt"
        />
      </q-tab-panel>
      <q-tab-panel name="audiobook" class="q-py-sm">
        <BookAudioBook
          :selectedPromptText="selectedPromptText"
          :book="book"
          :readonly="isInVersionPreview"
          :selectedChapterOutline="selectedChapterOutline"
          :editorContent="editorContent"
          v-model:isLoadingChapter="isLoadingChapter"
        />
      </q-tab-panel>
      <q-tab-panel name="reviews" class="q-py-sm">
        <BookReview
          v-if="reviewer"
          :author="author"
          :reviewer="reviewer"
          :slideReview="slideReview"
          :book="book"
          :selectedChapterOutline="selectedChapterOutline"
          v-model:isLoadingChapter="isLoadingChapter"
          :readonly="isInVersionPreview"
          :selectedChapterId="selectedChapterId"
        />
      </q-tab-panel>
      <!--
      <q-tab-panel name="details" class="q-py-sm">
        <BookGeneral
          :selectedChapterId="selectedChapterId"
          v-model:selectedChapterOutline="selectedChapterOutline"
          :readonly="isInVersionPreview"
          v-model:isLoadingChapter="isLoadingChapter"
          :book="book"
          @edit-mission="editingDialogActive = true"
        />
      </q-tab-panel>
      -->
    </q-tab-panels>

    <q-inner-loading
      :showing="isLoadingChapter"
      color="primary"
      label-class="text-primary"
      label-style="font-size: 1.1em"
      style="z-index: 1"
    >
      <q-spinner-dots color="primary" size="2em" />
      <div class="align-center">Hang tight, Loading....</div>
    </q-inner-loading>
  </div>
</template>

<script setup lang="ts">
import {
  Book,
  BookOutlines,
  ChapterMedia,
  ChapterOutline,
} from 'src/entities/book';
import { computed, ref, watch } from 'vue';
import BookImages from './components/BookImages.vue';
import BookOutline from './components/BookOutline.vue';
import BookGeneral from './components/BookGeneral.vue';
import BookPrompts from './components/BookPrompts.vue';
import BookReview from './components/BookReview.vue';
import { useVModels } from '@vueuse/core/index';
import BookAudioBook from './components/BookAudioBook.vue';
import {
  Author,
  isReviewMode,
  useAuthorStore,
  userLoggedIn,
} from 'src/entities/user';
import BookTranscribe from 'src/entities/book/ui/components/BookTranscribe.vue';

const emit = defineEmits<{
  'update:splitterModel': [splitterModel: number];
  'update:editingDialogActive': [editingDialogActive: boolean];
  'update:isLoadingChapter': [isLoadingChapter: boolean];
  'update:selectedPrompt': [selectedPrompt: string];
  'update:selectedNav': [selectedNav: string];
  'update:drawerRight': [drawerRight: boolean];
  'update:isMannyLoading': [isMannyLoading: boolean];
  'update:canTranscribe': [canTranscribe: boolean];
}>();
const props = defineProps<{
  outlines: BookOutlines;
  book: Book;
  selectedChapterOutline: ChapterOutline;
  transcripts: ChapterMedia[];
  selectedNav: string;
  selectedChapterId: string;
  chapterMaxWidth: number;
  isInVersionPreview: boolean;
  editingDialogActive: boolean;
  isLoadingChapter: boolean;
  drawerRight: boolean;
  splitterModel: number;
  isMobileView: boolean;
  readonly?: boolean;
  selectedPrompt: string;
  selectedPromptText: string;
  editorContent: string;
  isMannyLoading: boolean;
  canTranscribe: boolean;
  slideReview: Number;
}>();

const {
  editingDialogActive,
  isLoadingChapter,
  selectedNav,
  drawerRight,
  splitterModel,
  isMannyLoading,
  selectedPrompt,
  book,
  selectedChapterOutline,
  outlines,
  transcripts,
  canTranscribe,
} = useVModels(props, emit);

const onMannyLoading = ref(isMannyLoading.value as boolean);
const onSelectedPrompt = ref(selectedPrompt.value as string);
const onCanTranscribe = ref(canTranscribe.value as boolean);
const sideView = ref(selectedNav.value);
const titleBorderless = ref(true);
const authorStore = useAuthorStore();
// Review
// features
if (isReviewMode.value && book.value.authorId !== isReviewMode.value)
  isReviewMode.value = '';
const userFeatures: string[] = [];
const reviewer = ref<String | null>('');
let author = ref<Author>();
if (
  !isReviewMode.value &&
  userLoggedIn.value?.features &&
  userLoggedIn.value?.assignedAdminId
) {
  reviewer.value = userLoggedIn.value?.assignedAdminEmail || '';
  author.value = userLoggedIn.value;
  userFeatures.push(...userLoggedIn.value.features);
} else if (isReviewMode.value) {
  const authorRecord = await authorStore.getAuthorWithoutSync(
    book.value.authorId,
  );
  author.value = authorRecord;
  reviewer.value = authorRecord.email as string;
}
if (isReviewMode.value) {
  userFeatures.push('review');
}
const features = computed(() => userFeatures);

watch(
  [
    selectedPrompt,
    onSelectedPrompt,
    isMannyLoading,
    onMannyLoading,
    selectedNav,
    sideView,
    canTranscribe,
    onCanTranscribe,
  ],
  ([
    currentSelectedPrompt,
    currentOnSelectedPrompt,
    currentIsMannyLoading,
    currentOnMannyLoading,
    currentNav,
    currentSideView,
    currentCanTranscribe,
    currentOnCanTranscribe,
  ]) => {
    // Handle selectedPrompt and onSelectedPrompt synchronization
    onSelectedPrompt.value = currentSelectedPrompt as string;
    selectedPrompt.value = currentOnSelectedPrompt;

    // Handle isMannyLoading and onMannyLoading synchronization
    onMannyLoading.value = currentIsMannyLoading as boolean;
    isMannyLoading.value = currentOnMannyLoading;

    // Handle selectedNav and sideView synchronization
    sideView.value = currentNav;
    selectedNav.value = currentSideView;

    // Handle canTranscribe and onCanTranscribe synchronization
    onCanTranscribe.value = currentCanTranscribe as boolean;
    canTranscribe.value = currentOnCanTranscribe;
  },
);
</script>
<style lang="scss">
.editor-tab {
  .q-tab__icon {
    color: #000;
  }
  .q-tab__indicator {
    display: none;
  }
}
</style>
