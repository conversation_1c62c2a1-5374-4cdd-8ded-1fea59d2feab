<template>
  <q-slide-item
    @left="onLeft"
    @right="onRight"
    left-color="danger"
    right-color="primary"
  >
    <template
      v-slot:left
      v-if="!['introduction', 'conclusion'].includes(selectedOutline.id)"
    >
      <q-icon name="delete" /> Delete Chapter
    </template>
    <template v-slot:right>
      <q-icon name="img:robot.png" /> Generate Chapter Outline
    </template>
    <q-item clickable v-ripple :active="active" dense style="min-height: 38px">
      <q-item-section
        :class="{
          'draggable-item': !['introduction', 'conclusion'].includes(
            selectedOutline.id,
          ),
        }"
        avatar
      >
        <q-icon
          :class="{
            'draggable-item': !['introduction', 'conclusion'].includes(
              selectedOutline.id,
            ),
          }"
          name="folder"
        />
      </q-item-section>
      <q-item-section>
        <q-item-label>{{ title }} </q-item-label>
      </q-item-section>
      <q-item-section side v-if="!isInVersionPreview && !isReviewMode">
        <q-btn
          flat
          round
          dense
          icon="more_vert"
          :disable="isLoading == true"
          @click="emit('selectChapter')"
          class="more-button"
        >
          <q-menu>
            <q-list class="row justify-between">
              <q-item class="q-pa-xs">
                <q-item-section>
                  <q-btn
                    class="q-pa-xs"
                    flat
                    v-close-popup
                    @click="emit('toggleDrawer')"
                    icon="img:robot.png"
                  >
                    <q-tooltip>Let Manny Create a Chapter Outline</q-tooltip>
                  </q-btn>
                </q-item-section>
              </q-item>
              <q-item class="q-pa-xs" v-if="canDelete">
                <q-item-section>
                  <q-btn
                    class="q-pa-xs"
                    flat
                    v-close-popup
                    @click="emit('deleteChapter')"
                    icon="delete"
                    color="danger"
                  >
                    <q-tooltip>Delete</q-tooltip>
                  </q-btn>
                </q-item-section>
              </q-item>
              <q-item class="q-pa-xs">
                <q-item-section>
                  <q-btn class="q-pa-xs" flat icon="title" color="primary">
                    <q-tooltip>Chapter Title</q-tooltip>
                    <q-popup-edit
                      :ref="selectedOutline.id"
                      :disable="isLoading == true"
                      v-model="selectedOutline.title"
                      title="Edit chapter name"
                      buttons
                      label-set="Save"
                      v-slot="scope"
                      style="width: 235px"
                    >
                      <q-input
                        :disable="isLoading == true"
                        v-model="scope.value"
                        @update:model-value="
                          emit(
                            'saveChapterTitle',
                            selectedOutline.id,
                            scope.value,
                          )
                        "
                        dense
                        autofocus
                        @keyup.enter="scope.set"
                      />
                    </q-popup-edit>
                  </q-btn>
                </q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-btn>
      </q-item-section>
    </q-item>
  </q-slide-item>
</template>

<script setup lang="ts">
import { onBeforeUnmount, ref } from 'vue';
import { ChapterOutline } from 'src/entities/book';
import { useQuasar } from 'quasar';
import { useVModels } from '@vueuse/core/index';
import { isReviewMode } from 'src/entities/user';

const props = defineProps<{
  selectedOutline: ChapterOutline;
  isLoading: boolean;
  active: boolean;
  title: string;
  canDelete?: boolean;
  isInVersionPreview: boolean;
}>();

const emit = defineEmits<{
  selectChapter: [];
  toggleDrawer: [];
  saveChapterTitle: (id: string, title: string) => void;
  deleteChapter: [];
}>();
// const { isLoading } = useVModels(props, emit);
const $q = useQuasar();

const onLeft = ({ reset }) => {
  emit('deleteChapter');
  finalize(reset);
};
const onRight = ({ reset }) => {
  emit('toggleDrawer');
  finalize(reset);
};

let timer;

const finalize = (reset) => {
  timer = setTimeout(() => {
    reset();
  }, 1000);
};

onBeforeUnmount(() => {
  clearTimeout(timer);
});
</script>

<style scoped lang="scss">
.draggable-item {
  cursor: move !important;
}
</style>
