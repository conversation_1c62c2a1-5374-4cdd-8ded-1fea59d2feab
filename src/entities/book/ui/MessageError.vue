<script setup lang="ts">
const props = defineProps<{
  message: string;
  heading?: string;
  icon: string;
  textColor: string;
  iconColor: string;
  bgColor?: string;
  icon: string;
}>();
</script>

<template>
  <q-list
    bordered
    separator
    style="width: 100%"
    :style="{ backgroundColor: bgColor }"
  >
    <q-item style="width: 100%">
      <q-item-section avatar>
        <q-icon :color="iconColor" :name="icon" />
      </q-item-section>
      <q-item-section :class="textColor">
        <q-item-label overline v-if="heading">{{ heading }}</q-item-label>
        <q-item-label>{{ message }}</q-item-label>
      </q-item-section>
    </q-item>
  </q-list>
</template>

<style scoped lang="scss"></style>
