<template>
  <div id="chapters-list" class="container chapters-list">
    <q-btn
      label="Add Chapter"
      icon="playlist_add"
      color="primary"
      @click="addChapter(book.id)"
      :loading="addingChapter"
      :disable="isLoadingChapter"
      class="q-mb-md"
      v-if="!isInVersionPreview && !isReviewMode"
    />
    <q-scroll-area
      :thumb-style="thumbStyle"
      :bar-style="barStyle"
      class="book-chapters-nav"
      v-if="outlines.outlines?.length"
      :style="{
        width: '100%',
        height: $q.screen.lt.sm ? 'calc(100vh - 339px)' : 'calc(100vh - 245px)',
      }"
    >
      <div id="chapterDrawerList">
        <q-list :style="{ maxWidth: chapterMaxWidth + 'px' }">
          <ChapterItem
            :class="{
              'loading-chapter-nav': isLoadingChapter,
              'selected-chapter-nav': 'introduction' === selectedChapterId,
            }"
            :selectedOutline="outlines.introduction"
            :isLoading="isLoadingChapter === true"
            :isInVersionPreview="isInVersionPreview"
            :active="'introduction' === selectedChapterId"
            @click="selectedChapterId = 'introduction'"
            @selectChapter="selectedChapterId = 'introduction'"
            @saveChapterTitle="saveChapterTitle"
            @deleteChapter="deleteChapter(outlines.introduction, book.id)"
            @toggleDrawer="
              selectedChapterId = 'introduction';
              emits('toggleDrawer');
            "
            :title="formatTitle(outlines.introduction.title, 'Introduction')"
          />
        </q-list>
        <q-list :style="{ maxWidth: chapterMaxWidth + 'px' }">
          <draggable
            :list="outlines.outlines"
            :move="() => !isInVersionPreview"
            @end="saveChapterOrder"
            group="chapters"
            handle=".draggable-item, .q-slide-item__content"
            :animation="250"
            item-key="id"
          >
            <template #item="{ element: chapter, index }" class="chapter">
              <div>
                <ChapterItem
                  :class="{
                    'loading-chapter-nav': isLoadingChapter,
                    'selected-chapter-nav': chapter.id === selectedChapterId,
                  }"
                  canDelete
                  :selectedOutline="chapter"
                  :isLoading="isLoadingChapter === true"
                  :isInVersionPreview="isInVersionPreview"
                  :active="chapter.id === selectedChapterId"
                  @click="selectedChapterId = chapter.id"
                  @selectChapter="selectedChapterId = chapter.id"
                  @saveChapterTitle="saveChapterTitle"
                  @deleteChapter="deleteChapter(chapter, book.id)"
                  @toggleDrawer="
                    selectedChapterId = chapter.id;
                    emits('toggleDrawer');
                  "
                  :title="formatTitle(chapter.title, 'Chapter ' + (index + 1))"
                />
              </div>
            </template>
          </draggable>
        </q-list>
        <q-list :style="{ maxWidth: chapterMaxWidth + 'px' }">
          <ChapterItem
            :class="{
              'loading-chapter-nav': isLoadingChapter,
              'selected-chapter-nav': 'conclusion' === selectedChapterId,
            }"
            :selectedOutline="outlines.conclusion"
            :isLoading="isLoadingChapter === true"
            :isInVersionPreview="isInVersionPreview"
            :active="'conclusion' === selectedChapterId"
            @click="selectedChapterId = 'conclusion'"
            @selectChapter="selectedChapterId = 'conclusion'"
            @saveChapterTitle="saveChapterTitle"
            @deleteChapter="deleteChapter(outlines.conclusion, book.id)"
            @toggleDrawer="
              selectedChapterId = 'conclusion';
              emits('toggleDrawer');
            "
            :title="formatTitle(outlines.conclusion.title, 'Conclusion')"
          />
        </q-list>
      </div>
    </q-scroll-area>
    <q-inner-loading class="disable-loader" :showing="isLoadingChapter">
      <q-spinner-dots color="primary" size="0" />
    </q-inner-loading>
  </div>
</template>

<script setup lang="ts">
import {
  addChapter as addChapterFirebase,
  Book,
  BookOutlines,
  ChapterItem,
  ChapterOutline,
  createEmptyChapterOutline,
  deleteChapter as deleteChapterFirebase,
  getBook,
  listOutlines,
  setOutlines,
  updateOutlineNumbers,
} from 'src/entities/book';
import { useRoute } from 'vue-router';
import { useVModels } from '@vueuse/core/index';
import { computed, ref, unref } from 'vue';
import { useQuasar } from 'quasar';
import { confirmDeletion } from 'src/shared/lib/quasar-dialogs';
import draggable from 'vuedraggable';
import { barStyle, thumbStyle } from 'src/entities/setting';
import { isReviewMode } from 'src/entities/user';

const props = defineProps<{
  outlines: BookOutlines;
  book: Book;
  selectedChapterId: string;
  chapterMaxWidth: number;
  isLoadingChapter: boolean;
  isInVersionPreview: boolean;
}>();

const emits = defineEmits<{
  'update:selectedNav': [];
  toggleDrawer: [];
  'update:selectedChapterId': [id: string];
  'update:isLoadingChapter': [isLoadingChapter: boolean];
  'update:book': [book: Book];
  'update:outlines': [outlines: BookOutlines];
}>();

const route = useRoute();
const $q = useQuasar();
const bookId = route.params.id as string;

const {
  selectedChapterId,
  book,
  outlines,
  isLoadingChapter,
  chapterMaxWidth,
  isInVersionPreview,
} = useVModels(props, emits);

const chapterScrollHeight = computed(
  () =>
    38 *
    (outlines.value.outlines?.length > 7
      ? $q.screen.lt.xl
        ? 7
        : 12
      : outlines.value.outlines?.length + 2),
);

const addingChapter = ref(false);

const addChapter = async (bookId: string) => {
  addingChapter.value = true;
  isLoadingChapter.value = true;
  const number =
    outlines.value.outlines?.filter((ch) => !ch.isSection).length + 1;
  const newChapterOutline = createEmptyChapterOutline();
  newChapterOutline.number = number;

  const newChapter = await addChapterFirebase(bookId, newChapterOutline);

  // Update the book manually because I didn't add snapshot listeners to it yet
  book.value = unref(await getBook(bookId));
  outlines.value = unref(await listOutlines(bookId));

  selectedChapterId.value = newChapter.id;
  addingChapter.value = false;
  isLoadingChapter.value = false;
};

const deletingChapter = ref(false);
const deleteChapter = async (chapter: ChapterOutline, bookId: string) => {
  try {
    await confirmDeletion($q);
  } catch {
    return;
  }

  deletingChapter.value = true;
  await deleteChapterFirebase(bookId, chapter.id);

  // Update the book manually because I didn't add snapshot listeners to it yet
  book.value = unref(await getBook(bookId));
  outlines.value = unref(await listOutlines(bookId));

  // chapter loading is complete
  deletingChapter.value = false;
  isLoadingChapter.value = false;

  selectedChapterId.value = 'introduction';
};

const saveChapterTitle = async (chapterId: string, outlineTitle: string) => {
  outlines.value = setOutlines(book.value.id, outlines, chapterId, {
    title: outlineTitle,
  });
};
const saveChapterOrder = async (ev: any) => {
  if (outlines.value.outlines[ev.newIndex].id)
    selectedChapterId.value = outlines.value.outlines[ev.newIndex].id;
  outlines.value.outlines = await updateOutlineNumbers(
    book.value.id,
    outlines.value.outlines,
  );
};

const formatTitle = (title: string, compareTitle: string) => {
  if (compareTitle === title) return compareTitle;
  return title ? compareTitle + ' | ' + title : compareTitle;
};
</script>

<style scoped lang="scss">
#chapters-list {
  .disable-loader.q-inner-loading {
    background: none !important;
    cursor: not-allowed;
  }
}
</style>
