<template>
  <div class="manny-book-card">
    <div class="book-container">
      <!-- Book Cover -->
      <div class="book-cover" :style="coverStyle" @click="handleActionClick">
        <!-- Action Menu Button - Only for existing books -->
        <q-btn
          v-if="!isCreateBook && (user?.uid === book?.authorId || isAdmin)"
          class="action-menu-btn"
          round
          flat
          icon="more_vert"
          color="white"
          size="sm"
          @click.stop="handleMenuClick"
        >
          <q-menu cover auto-close>
            <q-list>
              <q-item clickable @click="showColorPicker = true" v-show="false">
                <q-item-section avatar>
                  <q-icon name="palette" />
                </q-item-section>
                <q-item-section>Change Color</q-item-section>
              </q-item>
              <q-separator />
              <q-item
                clickable
                @click="emits('edit')"
                v-if="isAuthor || isAdmin"
              >
                <q-item-section avatar>
                  <q-icon name="edit" />
                </q-item-section>
                <q-item-section>Edit</q-item-section>
              </q-item>
              <q-item
                clickable
                @click="validateAccess(`/books/${book.id}/export`)"
              >
                <q-item-section avatar>
                  <q-icon name="open_in_new" />
                </q-item-section>
                <q-item-section>Export</q-item-section>
              </q-item>
              <q-item
                clickable
                @click="validateAccess(`/books/${book.id}/versions`)"
                v-if="isAuthor || isAdmin"
              >
                <q-item-section avatar>
                  <q-icon name="history" />
                </q-item-section>
                <q-item-section>Versions</q-item-section>
              </q-item>
              <q-item
                clickable
                @click="emits('delete')"
                v-if="isAuthor || isAdmin"
              >
                <q-item-section avatar>
                  <q-icon name="delete" color="red" />
                </q-item-section>
                <q-item-section>Delete</q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-btn>

        <!-- Book Content - Existing Book -->
        <div v-if="!isCreateBook" class="book-content" :class="alignmentClass">
          <!-- Author Name -->
          <div class="book-author" :style="{ color: fontColor }">
            {{ book?.authorName || 'Unknown Author' }}
          </div>

          <!-- Title -->
          <div class="book-title" :style="{ color: fontColor }">
            {{ book?.title || 'Untitled Book' }}
          </div>

          <!-- Subtitle -->
          <div
            class="book-subtitle"
            v-if="book?.subtitle"
            v-show="book?.title.length < 20"
            :style="{ color: fontColor }"
          >
            {{
              book?.subtitle.length > 30
                ? book?.subtitle.slice(0, 30) + '...'
                : book?.subtitle
            }}
          </div>
        </div>

        <!-- Create Book Content -->
        <div
          v-else
          class="book-content create-content"
          :class="alignmentClass"
          @click="handleActionClick"
        >
          <div class="create-icon">
            <q-icon
              name="add_circle_outline"
              size="4rem"
              :style="{ color: fontColor }"
            />
          </div>

          <!-- Create Title -->
          <div class="create-title" :style="{ color: fontColor }">
            Create New Book
          </div>

          <!-- Create Subtitle -->
          <div class="create-subtitle" :style="{ color: fontColor }">
            Start your writing journey
          </div>
        </div>

        <!-- Book Footer Info - Only for existing books -->
        <div
          v-if="!isCreateBook"
          class="book-footer"
          :class="alignmentClass"
          v-show="false"
        >
          <div class="book-meta" :style="{ color: fontColor }">
            <div class="book-date">{{ formatDate(book?.createdAt) }}</div>
            <div class="book-category">
              <q-icon
                size="1.2rem"
                :color="fontColor"
                :name="
                  book?.category === 'autobiography'
                    ? 'local_library'
                    : 'collections_bookmark'
                "
              >
                <q-tooltip>{{ book?.category || 'non-fiction' }}</q-tooltip>
              </q-icon>
            </div>
          </div>
        </div>

        <!-- Book binding lines -->
        <div class="binding-lines">
          <div class="binding-line"></div>
          <div class="binding-line"></div>
          <div class="binding-line"></div>
        </div>
      </div>

      <!-- Book Spine -->
      <div class="book-spine" :style="spineStyle">
        <div class="spine-text" :style="{ color: fontColor }">
          {{ isCreateBook ? 'New' : book?.title || 'Untitled' }}
        </div>
      </div>

      <!-- Book Pages -->
      <div class="book-pages"></div>
    </div>
    <!-- Action Button -->
    <div class="book-action" v-show="false">
      <q-btn
        @click="handleActionClick"
        :color="bookColor"
        class="full-width"
        :label="getActionButtonLabel"
      />
    </div>
    <!-- Color Picker Dialog - Only for existing books -->
    <q-dialog v-if="!isCreateBook" v-model="showColorPicker">
      <q-card style="min-width: 300px">
        <q-card-section>
          <div class="text-h6">Choose Book Cover Color</div>
        </q-card-section>
        <q-card-section class="flex justify-center">
          <q-color
            v-model="bookColor"
            format-model="hex"
            @change="onColorChange"
            style="width: 100%"
          />
        </q-card-section>
        <q-card-actions align="right">
          <q-btn flat label="Reset" @click="resetColor" />
          <q-btn flat label="Cancel" @click="showColorPicker = false" />
          <q-btn flat label="Apply" color="primary" @click="applyColor" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import type { Book } from '../model/types';
import { date, useQuasar } from 'quasar';
import { isAdmin, isAuthor, isReviewMode, user } from 'src/entities/user';
import { computed, ref, toRef, onMounted } from 'vue';
import { useRouter } from 'vue-router';

const props = defineProps<{
  book?: Book;
  authorName?: string;
  isCreateBook?: boolean;
  defaultCoverColor?: string; // New prop for default cover color
  fontColor?: string; // New prop for font color
  textAlignment?: 'center' | 'left' | 'right'; // New prop for text alignment
}>();

const emits = defineEmits<{
  edit: [];
  delete: [];
  colorChange: [color: string];
  createNewBook: [];
}>();

const $q = useQuasar();
const router = useRouter();
const book = toRef(props, 'book');

// Color management with localStorage
const showColorPicker = ref(false);
const bookColor = ref(props.defaultCoverColor || '#1976d2');

// Default font color
const fontColor = computed(() => props.fontColor || 'white');

// Text alignment class
const alignmentClass = computed(() => {
  const alignment = props.textAlignment || 'center';
  return `text-align-${alignment}`;
});

// Your existing color utility functions and other methods remain the same...
function lightenColor(color: string, percent: number): string {
  const hex = color.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);

  const newR = Math.min(255, Math.round(r + (255 - r) * (percent / 100)));
  const newG = Math.min(255, Math.round(g + (255 - g) * (percent / 100)));
  const newB = Math.min(255, Math.round(b + (255 - b) * (percent / 100)));

  return `#${((1 << 24) + (newR << 16) + (newG << 8) + newB)
    .toString(16)
    .slice(1)}`;
}

function darkenColor(color: string, percent: number): string {
  const hex = color.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);

  const newR = Math.max(0, Math.round(r * (1 - percent / 100)));
  const newG = Math.max(0, Math.round(g * (1 - percent / 100)));
  const newB = Math.max(0, Math.round(b * (1 - percent / 100)));

  return `#${((1 << 24) + (newR << 16) + (newG << 8) + newB)
    .toString(16)
    .slice(1)}`;
}

// Storage keys
const getColorStorageKey = () => `book-color-${book.value?.id || 'create'}`;

// Load and save color functions
function loadSavedColor() {
  if (props.isCreateBook) {
    bookColor.value = props.defaultCoverColor || '#1976d2';
    return true;
  }

  try {
    // const savedColor = localStorage.getItem(getColorStorageKey());
    // if (savedColor && savedColor !== 'null') {
    //   bookColor.value = savedColor;
    //   return true;
    // }
  } catch (error) {
    console.error('Error loading color from localStorage:', error);
  }
  return false;
}

function saveColor(color: string) {
  if (props.isCreateBook) return;

  try {
    localStorage.setItem(getColorStorageKey(), color);
    console.log('Saved color for book:', book.value?.id, 'Color:', color);
  } catch (error) {
    console.error('Error saving color to localStorage:', error);
  }
}

// Load color from localStorage on mount
onMounted(() => {
  const loaded = loadSavedColor();
  if (!loaded && book.value?.coverColor) {
    bookColor.value = book.value.coverColor;
  } else if (!loaded) {
    bookColor.value = props.defaultCoverColor || '#1976d2';
  }
});

// Computed styles
const coverStyle = computed(() => {
  const baseColor = bookColor.value;
  const lightColor = lightenColor(baseColor, 30);
  const darkColor = darkenColor(baseColor, 20);

  const borderStyle = props.isCreateBook
    ? `2px dashed ${darkenColor(baseColor, 30)}`
    : `3px solid ${darkenColor(baseColor, 30)}`;

  return {
    background: `linear-gradient(145deg, ${lightColor} 0%, ${baseColor} 40%, ${darkColor} 100%)`,
    border: borderStyle,
  };
});

const spineStyle = computed(() => {
  const darkColor = darkenColor(bookColor.value, 60);
  const veryDarkColor = darkenColor(bookColor.value, 80);

  return {
    background: `linear-gradient(to right, ${veryDarkColor}, ${darkColor})`,
    borderColor: darkenColor(bookColor.value, 70),
  };
});

// Action button logic
const getActionButtonLabel = computed(() => {
  if (props.isCreateBook) return 'Create Book';

  return isReviewMode && book.value?.authorId === isReviewMode
    ? 'Review'
    : 'Write';
});

const handleActionClick = () => {
  if (props?.isCreateBook === true) {
    emits('createNewBook');
  } else if (book.value) {
    validateAccess('/books/' + book.value.id);
  }
};
const handleMenuClick = (event: Event) => {
  // The @click.stop already prevents bubbling,
  // but you can add additional logic here if needed
  console.log('Menu button clicked');
};
const validateAccess = async (access: string) => {
  await router.push({ path: access });
};

function formatDate(timestamp: string | Date) {
  return date.formatDate(timestamp, 'M-D-YY');
}

function onColorChange(color: string) {
  bookColor.value = color;
}

function applyColor() {
  saveColor(bookColor.value);
  showColorPicker.value = false;

  // Show success message
  $q.notify({
    message: 'Book cover color saved!',
    color: 'positive',
    position: 'top',
    timeout: 2000,
  });
}

function resetColor() {
  bookColor.value = props.isCreateBook
    ? props.defaultCoverColor || '#4CAF50'
    : props.defaultCoverColor || '#1976d2';
  if (!props.isCreateBook) {
    localStorage.removeItem(getColorStorageKey());
  }
}
</script>

<style scoped>
.manny-book-card {
  width: 280px;
  height: 380px;
  perspective: 1200px;
  margin: 0 5px;
  position: relative;
  transform: translateZ(50px) translateY(15px); /* Forward and slightly up */
  z-index: 1;
  transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Add shelf shadow effect */
.manny-book-card::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 10%;
  right: 15%;
  height: 8px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  filter: blur(4px);
  transform: translateZ(-1px);
  z-index: -1;
}

.book-cover {
  cursor: pointer;
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 4px 8px 8px 4px;
  box-shadow:
    0 15px 35px rgba(0, 0, 0, 0.3),
    0 5px 15px rgba(0, 0, 0, 0.2),
    inset 0 2px 4px rgba(255, 255, 255, 0.1),
    inset 0 -2px 4px rgba(0, 0, 0, 0.1),
    0 25px 15px -10px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  padding: 24px 20px;

  /** text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4); **/
  transform: translateZ(12px);
  overflow: hidden;
  position: relative;
}
/* Show action menu button on book cover hover */
.book-cover:hover .action-menu-btn {
  opacity: 1;
  visibility: visible;
}
.book-spine {
  position: absolute;
  left: -12px;
  top: 0;
  width: 24px;
  height: 100%;
  border-radius: 2px 0 0 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateZ(0px);
  box-shadow:
    -4px 0 8px rgba(0, 0, 0, 0.4),
    inset 2px 0 4px rgba(255, 255, 255, 0.05),
    0 20px 10px -8px rgba(0, 0, 0, 0.15);
  border: 2px solid;
  border-right: none;
}

.book-pages {
  position: absolute;
  right: -8px;
  top: 4px;
  width: 8px;
  height: calc(100% - 8px);
  background: white;
  border-radius: 0 2px 2px 0;
  box-shadow:
    2px 0 4px rgba(0, 0, 0, 0.2),
    inset -1px 0 2px rgba(0, 0, 0, 0.1),
    0 15px 8px -6px rgba(0, 0, 0, 0.1);
  background-image: repeating-linear-gradient(
    to bottom,
    transparent,
    transparent 6px,
    rgba(0, 0, 0, 0.05) 6px,
    rgba(0, 0, 0, 0.05) 6.5px
  );
}

.book-container {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  transition: transform 0.4s ease;
}

.book-container1:hover {
  transform: rotateY(-15deg) rotateX(5deg) scale(1.02);
}

.book-action {
  display: none;
}

.manny-book-card:hover::after {
  bottom: -12px;
  filter: blur(6px);
  background: rgba(0, 0, 0, 0.4);
  transition: all 0.4s ease;
}

/* All other existing styles... */
.binding-lines {
  position: absolute;
  left: 8px;
  top: 20%;
  bottom: 20%;
  width: 2px;
}

.binding-line {
  width: 100%;
  height: 20%;
  margin-bottom: 10%;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 1px;
}

.action-menu-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
  background: rgba(0, 0, 0, 0.2);
  max-width: 30.5px;
  /* Hide by default */
  opacity: 0;
  visibility: hidden;
  transition:
    opacity 0.3s ease,
    visibility 0.3s ease;
}

.book-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  text-align: center;
  padding: 20px 0;
  margin-left: 20px;
}

/* Text alignment classes */
.book-content.text-align-center {
  align-items: center;
  text-align: center;
}

.book-content.text-align-left {
  align-items: flex-start;
  text-align: left;
}

.book-content.text-align-right {
  align-items: flex-end;
  text-align: right;
}

/* Apply alignment to footer as well */
.book-footer.text-align-center .book-meta {
  justify-content: center;
}

.book-footer.text-align-left .book-meta {
  justify-content: flex-start;
}

.book-footer.text-align-right .book-meta {
  justify-content: flex-end;
}

/* Adjust title alignment specifically */
.book-content.text-align-center .book-title {
  justify-content: center;
}

.book-content.text-align-left .book-title {
  justify-content: flex-start;
}

.book-content.text-align-right .book-title {
  justify-content: flex-end;
}

/* Create content alignment */
.create-content.text-align-center {
  align-items: center;
}

.create-content.text-align-left {
  align-items: flex-start;
}

.create-content.text-align-right {
  align-items: flex-end;
}

/* Adjust borders for left/right alignment */
.book-content.text-align-left .book-author {
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  align-self: stretch;
  text-align: left;
}

.book-content.text-align-left .book-subtitle {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  align-self: stretch;
  text-align: left;
}

.book-content.text-align-right .book-author {
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  align-self: stretch;
  text-align: right;
}

.book-content.text-align-right .book-subtitle {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  align-self: stretch;
  text-align: right;
}

.create-content {
  justify-content: center;
  gap: 20px;
}

.create-icon {
  opacity: 0.9;
}

.create-title {
  font-size: 24px;
  font-weight: bold;
  line-height: 1.2;
  font-family: 'Georgia', serif;
}

.create-subtitle {
  font-size: 14px;
  font-style: italic;
  opacity: 0.85;
}

.book-author {
  font-size: 13px;
  font-weight: 600;
  letter-spacing: 2px;
  text-transform: uppercase;
  opacity: 0.95;
  margin-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  padding-bottom: 8px;
}

.book-title {
  font-size: 26px;
  font-weight: bold;
  line-height: 1.1;
  text-align: center;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px 0;
  word-wrap: break-word;
  hyphens: auto;
  font-family: 'Georgia', serif;
}

.book-subtitle {
  font-size: 15px;
  font-style: italic;
  opacity: 0.85;
  margin-top: 15px;
  line-height: 1.3;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 10px;
}

.book-footer {
  margin-top: auto;
}

.book-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  opacity: 0.8;
}

.spine-text {
  color: white;
  font-size: 11px;
  font-weight: bold;
  writing-mode: vertical-rl;
  text-orientation: mixed;
  letter-spacing: 1px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
}

/* Responsive adjustments - IMPROVED */
@media (max-width: 600px) {
  .manny-book-card {
    width: 240px;
    height: 280px; /* Reduced from 320px to 280px for better mobile experience */
    perspective: 800px; /* Reduced perspective for smaller screens */
    transform: translateZ(30px) translateY(10px); /* Reduced base forward position */
  }

  /* Ensure book cover maintains full height on mobile */
  .book-cover {
    height: 100%;
    padding: 18px 14px; /* Adjusted padding for new height */
    border-radius: 3px 6px 6px 3px;
  }

  /* Adjust spine for mobile */
  .book-spine {
    left: -10px;
    width: 20px;
    height: 100%;
  }

  /* Adjust pages for mobile */
  .book-pages {
    right: -6px;
    width: 6px;
    height: calc(100% - 6px);
    top: 3px;
  }

  /* Adjust content spacing for mobile */
  .book-content {
    padding: 14px 0;
    margin-left: 14px;
  }

  /* Adjust text sizes for more compact layout */
  .book-title {
    font-size: 20px; /* Reduced from 22px */
    margin: 14px 0;
    line-height: 1.1;
  }

  .book-author {
    font-size: 11px;
    margin-bottom: 14px;
    letter-spacing: 1.5px;
  }

  .book-subtitle {
    font-size: 12px;
    margin-top: 10px;
  }

  .create-title {
    font-size: 18px; /* Reduced from 20px */
  }

  .create-subtitle {
    font-size: 13px;
  }

  .create-content {
    gap: 16px; /* Reduced gap */
  }

  .create-icon .q-icon {
    font-size: 2.8rem !important; /* Reduced from 3rem */
  }

  /* Adjust shadows for mobile */
  .manny-book-card::after {
    bottom: -5px;
    height: 5px;
  }

  .manny-book-card:hover::after {
    bottom: -7px;
    height: 6px;
  }

  /* Adjust menu button for mobile */
  .action-menu-btn {
    top: 6px;
    right: 6px;
    max-width: 26px;
  }

  /* Adjust binding lines for mobile */
  .binding-lines {
    left: 6px;
  }

  /* Reduce spine text size */
  .spine-text {
    font-size: 9px;
  }

  /* Adjust book meta for mobile */
  .book-meta {
    font-size: 10px;
  }

  .book-content.text-align-left,
  .book-content.text-align-right {
    margin-left: 14px;
    padding: 14px 0;
  }

  /* Ensure proper spacing on mobile for left/right aligned text */
  .book-content.text-align-left .book-title,
  .book-content.text-align-right .book-title {
    margin: 14px 0;
  }
}

/* Extra small screens - More compact */
@media (max-width: 480px) {
  .manny-book-card {
    width: 200px;
    height: 240px; /* Reduced from 280px to 240px for very small screens */
    perspective: 600px; /* Further reduced perspective */
    transform: translateZ(20px) translateY(8px); /* Minimal base position */
  }

  .book-cover {
    padding: 14px 10px; /* Further reduced padding */
    border-radius: 2px 4px 4px 2px;
  }

  .book-content {
    padding: 10px 0;
    margin-left: 10px;
  }

  .book-title {
    font-size: 16px; /* Further reduced */
    margin: 10px 0;
  }

  .book-author {
    font-size: 10px;
    margin-bottom: 10px;
    letter-spacing: 1px;
  }

  .book-subtitle {
    font-size: 11px;
    margin-top: 8px;
  }

  .create-title {
    font-size: 16px;
  }

  .create-subtitle {
    font-size: 12px;
  }

  .create-content {
    gap: 12px;
  }

  .create-icon .q-icon {
    font-size: 2.2rem !important;
  }

  /* Smaller shadows for tiny screens */
  .manny-book-card::after {
    bottom: -4px;
    height: 4px;
  }

  .manny-book-card:hover::after {
    bottom: -6px;
    height: 5px;
  }
}

/* Tablet landscape - intermediate size */
@media (max-width: 768px) and (min-width: 601px) {
  .manny-book-card {
    width: 260px;
    height: 320px; /* Slightly taller for tablets */
    perspective: 1000px;
    transform: translateZ(40px) translateY(12px);
  }

  .book-cover {
    padding: 22px 18px;
  }

  .book-content {
    padding: 18px 0;
    margin-left: 18px;
  }

  .book-title {
    font-size: 24px;
    margin: 18px 0;
  }
}
</style>
