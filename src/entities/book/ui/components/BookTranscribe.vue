<template>
  <AudioUploadDialogComponent
    :chaptermedia="newChapterMedia"
    :onLoadingChapterMedia="false"
    onHasError=""
    v-model:onShowTranscription="onShowTranscription"
    @onTranscriptDone="onTranscriptDone"
    @onTranscriptionClose="onTranscriptionClose"
  />

  <div id="images" class="container q-gutter-y-md column">
    <q-btn
      label="Transcribe File"
      icon="img:robot.png"
      color="primary"
      @click="onTranscriptionStart"
      :loading="isUploading"
      :disable="
        isUploading || isLoadingChapter || isDisablingButton || !canTranscribe
      "
      class="q-mb-md"
    >
      <q-tooltip>Transcribe File</q-tooltip>
    </q-btn>
    <q-scroll-area
      style="height: 400px; width: 100%"
      :thumb-style="thumbStyle"
      :bar-style="barStyle"
    >
      <div class="image-container row">
        <TranscribeCard
          v-if="transcriptions && transcriptions.length"
          v-for="(transcript, index) in transcriptions"
          :key="index"
          :transcript="transcript"
          :transcripts="transcriptions"
          @deleteTranscription="
            deleteTranscription(transcript, selectedChapterId)
          "
        />

        <MessageError
          v-else
          heading="No Transcriptions found!"
          icon="warning"
          text-color="text-black"
          bg-color="#fff"
          icon-color="primary"
          message="Click the 'Transcribe File' button to upload and start transcribing."
        />
      </div>
    </q-scroll-area>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { confirmOverrideText, showError } from 'src/shared/lib/quasar-dialogs';
import {
  Book,
  BookOutlines,
  ChapterMedia,
  ChapterOutline,
  createEmptyChapterTranscription,
  deleteChapterTranscription,
  MessageError,
} from 'src/entities/book';
import { useQuasar } from 'quasar';
import { computedAsync, useVModels } from '@vueuse/core/index';
import {
  barStyle,
  maxChapterMediaTranscripts,
  primaryColor,
  thumbStyle,
} from 'src/entities/setting';
import TranscribeCard from 'src/entities/image/ui/TranscribeCard.vue';
import AudioUploadDialogComponent from 'src/features/book-transcribe/ui/AudioUploadDialogComponent.vue';
import { generateRandomId } from 'src/entities/user';

const $q = useQuasar();

const props = defineProps<{
  outlines: BookOutlines;
  book: Book;
  transcripts: ChapterMedia[];
  selectedChapterOutline: ChapterOutline;
  selectedChapterId: string;
  isLoadingChapter: boolean;
  onMannyLoading: boolean;
  onCanTranscribe: boolean;
  readonly?: boolean;
  sideView: string;
}>();

const emit = defineEmits<{
  'update:transcripts': [transcripts: ChapterMedia[]];
  'update:isLoadingChapter': [isLoadingChapter: boolean];
  'update:onMannyLoading': [onMannyLoading: boolean];
  'update:onCanTranscribe': [onCanTranscribe: boolean];
  'update:selectedChapterId': [id: string];
  'update:book': [book: Book];
  'update:outlines': [outlines: BookOutlines];
}>();

const {
  selectedChapterId,
  book,
  outlines,
  isLoadingChapter,
  transcripts,
  onMannyLoading,
  onCanTranscribe,
  sideView,
} = useVModels(props, emit);

const transcriptions = ref([]); // Local reactive variable for transcripts
const transcriptId = ref(generateRandomId(20));

const canTranscribe = computedAsync(async () => {
  const allowed =
    transcriptions.value.length < maxChapterMediaTranscripts.value;
  await new Promise((resolve) => setTimeout(resolve, 200));
  onCanTranscribe.value = allowed;
  return allowed;
}, false);
const isUploading = ref(false);
const onShowTranscription = ref(false);
const isDisablingButton = ref(false);
const newChapterMedia = computed(() => {
  return {
    ...createEmptyChapterTranscription(),
    bookId: book.value.id,
    chapterId: selectedChapterId.value,
    id: transcriptId.value,
  };
});

// Method to delete an image
const deleteTranscription = async (
  chaptermedia: ChapterMedia,
  currentChapter,
) => {
  onMannyLoading.value = false;
  const shouldOverride = await confirmOverrideText($q, {
    message: 'This action will delete your transcription.',
  });
  if (!shouldOverride) {
    return;
  }

  if (currentChapter !== selectedChapterId.value) {
    await showError($q, 'Image could not be deleted!');
    return;
  }

  isLoadingChapter.value = true;
  deleteChapterTranscription(chaptermedia);
  const targetIndex = transcriptions.value.findIndex(
    (transcript) => transcript?.id && transcript?.id === chaptermedia.id,
  );
  await new Promise((resolve) => setTimeout(resolve, 300));
  if (targetIndex !== -1) {
    // Remove the item with the matching postId and update posts.value
    transcriptions.value.splice(targetIndex, 1);
    onTranscriptionCount();
  }

  isLoadingChapter.value = false;
};
const onTranscriptDone = async (chaptermedia: ChapterMedia) => {
  transcriptions.value.unshift(chaptermedia);
  onTranscriptionCount();
};
const onTranscriptionClose = async () => {
  transcriptId.value = generateRandomId(20);
  onTranscriptionCount();
};
const onTranscriptionStart = async () => {
  isLoadingChapter.value = true;
  onShowTranscription.value = true;
};
const onTranscriptionCount = () => {
  onMannyLoading.value = false;
  isLoadingChapter.value = false;
};
watch(
  onMannyLoading,
  (currentOnTranscribing) => {
    if (currentOnTranscribing === true && sideView.value === 'transcripts')
      onShowTranscription.value = true;
  },
  { immediate: true },
);

// Watch the computedAsync to sync data into the local reactive variable
watch(
  transcripts,
  (newTranscripts) => {
    transcriptions.value = [...newTranscripts]; // Sync data from asyncTranscripts
  },
  { immediate: true },
);
</script>

<style lang="scss">
.filepond--drop-label,
.filepond--drop-label label {
  cursor: pointer;
}

#images .image-container {
  display: flex;
  justify-content: flex-start;
  overflow: hidden;
  flex-wrap: wrap !important;
}

#images .back-button {
  display: flex;
  cursor: pointer;
  font-size: 16px;
  line-height: 32px;
}
#images .back-button .q-icon {
  font-size: 32px;
}
</style>
