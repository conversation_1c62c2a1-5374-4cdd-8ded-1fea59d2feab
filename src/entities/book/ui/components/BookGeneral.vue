<template>
  <div class="q-pl-none q-pr-xs q-gutter-md">
    <q-input
      v-model="book.authorName"
      :disable="readonly"
      label="Author Name"
      type="text"
      class="q-mb-md"
      outlined
    />
  </div>
  <q-btn
    label="Book Foundations"
    icon="auto_stories"
    color="primary"
    align="center"
    @click="emits('edit-mission')"
  />
</template>

<script lang="ts" setup>
import { type Book } from 'src/entities/book';
import { useVModels } from '@vueuse/core';

const props = defineProps<{
  book: Book;
  readonly?: boolean;
}>();

const emits = defineEmits<{
  'edit-mission': [];
  'update:book': [book: Book];
}>();

const { book } = useVModels(props, emits);
</script>

<style lang="scss"></style>
