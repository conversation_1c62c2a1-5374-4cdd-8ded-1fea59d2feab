<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/nonfiction/NewBookFlow.vue (refactored) -->
<template>
  <!-- Modern Non-Fiction Mission Flow -->
  <NonFictionMissionFlow
    v-model:show="onBoardingActive"
    v-model:mission="mission"
    v-model:questions="missionQuestions"
    v-model:title="title"
    v-model:subtitle="subtitle"
    v-model:chapters="chapters"
    @complete="complete"
    @update:show="onShowUpdate"
  />
</template>

<script setup lang="ts">
import { computed, ref, toRef, watch } from 'vue';
import { useQuasar } from 'quasar';
import { useRouter } from 'vue-router';
import { useVModel, watchDebounced } from '@vueuse/core';

import type {
  Book,
  NewBook,
  ChapterOutline,
} from 'src/entities/book/model/types';
import { setBook, updateBook } from 'src/entities/book';
import { ACTIONS, loggingService, PAGES } from 'src/entities/log';

import { NonFictionMissionFlow } from '../index';

export type NewBookStarter = Pick<
  NewBook,
  'title' | 'subtitle' | 'mission' | 'missionQuestions' | 'chapters'
>;

export type ExistingBookStarter = Book;
export type ExistingBookOutlinesStarter = ChapterOutline[];

interface NonFictionQuestions {
  gender: 'Male' | 'Female' | 'Both';
  age: string[];
  description: string;
  need: string;
  problems: string;
  readerImpact: string;
  authorImpact: string;
}

const props = defineProps<{
  modal: boolean;
  initialValues?: ExistingBookStarter;
  initialOutlines?: ExistingBookOutlinesStarter;
  shouldUpdate?: boolean;
}>();

const emit = defineEmits<{
  cancel: [];
  completed: [book: NewBookStarter];
  updated: [id: string, book: ExistingBookStarter];
}>();

const $q = useQuasar();
const router = useRouter();

const onBoardingActive = computed(() => props.modal || false);
const initialValues = toRef(props, 'initialValues');
const initialOutlines = toRef(props, 'initialOutlines');
const shouldUpdate = toRef(props, 'shouldUpdate');

// Convert legacy mission questions format to new format
const convertLegacyQuestions = (legacyQuestions: any): NonFictionQuestions => {
  return {
    gender: legacyQuestions?.gender || 'Both',
    age: legacyQuestions?.age || [],
    description: legacyQuestions?.description || '',
    need: legacyQuestions?.need || '',
    problems: legacyQuestions?.problems || '',
    readerImpact: legacyQuestions?.readerImpact || '',
    authorImpact: legacyQuestions?.authorImpact || '',
  };
};

// Initialize reactive data
const missionQuestions = ref<NonFictionQuestions>(
  convertLegacyQuestions(initialValues.value?.missionQuestions),
);

const mission = ref(initialValues.value?.mission || '');
const title = ref(initialValues.value?.title || '');
const subtitle = ref(initialValues.value?.subtitle || '');
const chapters = ref<ChapterOutline[]>(
  initialOutlines.value?.length
    ? initialOutlines.value
    : initialValues.value?.chapters || [],
);

// Watch for prop changes and update refs accordingly
watch(
  () => initialValues.value,
  (newValue) => {
    if (newValue) {
      missionQuestions.value = convertLegacyQuestions(
        newValue.missionQuestions,
      );
      mission.value = newValue.mission || mission.value;
      title.value = newValue.title || title.value;
      subtitle.value = newValue.subtitle || subtitle.value;
      if (!initialOutlines.value?.length) {
        chapters.value = newValue.chapters || [];
      }
    }
  },
  { immediate: true },
);

watch(
  () => initialOutlines.value,
  (newValue) => {
    if (newValue?.length) {
      chapters.value = newValue;
    }
  },
  { immediate: true },
);

// Define the module or component name
const MODULE_NAME = 'book_foundations';

const onShowUpdate = (show: boolean) => {
  if (!show) emit('cancel');
};
async function complete() {
  const bookData = {
    mission: mission.value,
    title: title.value,
    subtitle: subtitle.value,
    chapters: chapters.value,
    missionQuestions: missionQuestions.value,
  };

  try {
    if (initialValues.value === undefined) {
      // Creating new book
      emit('completed', bookData);

      await loggingService.logAction(
        PAGES.BOOKMISSIONSTATEMENT,
        ACTIONS.CREATE,
        `New non-fiction book created: ${title.value || 'Untitled book'}`,
        MODULE_NAME,
      );
    } else {
      // Updating existing book
      const updatedBook = {
        ...initialValues.value,
        id: initialValues.value?.id,
        ...bookData,
      };

      emit('updated', initialValues.value?.id, updatedBook);

      await loggingService.logAction(
        PAGES.BOOKMISSIONSTATEMENT,
        ACTIONS.UPDATE,
        `Non-fiction book updated: ${title.value || 'Untitled book'}`,
        MODULE_NAME,
        initialValues.value?.id as string,
      );

      // Auto-navigate to book if updating
      if (shouldUpdate.value === true && initialValues.value?.id) {
        await router.push(`/books/${initialValues.value.id}`);
      }
    }
  } catch (error) {
    console.error('Error completing book flow:', error);
    $q.notify({
      color: 'negative',
      message: 'An error occurred while saving your book. Please try again.',
      icon: 'error',
    });
  }
}

// Computed book object for autosave functionality
const book = computed(() => {
  if (!initialValues.value?.id) return null;

  return {
    ...initialValues.value,
    id: initialValues.value?.id,
    mission: mission.value,
    title: title.value,
    subtitle: subtitle.value,
    chapters: chapters.value,
    missionQuestions: missionQuestions.value,
  };
});

// Auto-save functionality for existing books
watchDebounced(
  book,
  (newBook) => {
    if (newBook && shouldUpdate.value !== true) {
      setBook(newBook.id, newBook);
    }
  },
  { debounce: 500 },
);

// Handle updates on book chapters
watchDebounced(
  chapters,
  (newChapters) => {
    if (book.value && shouldUpdate.value !== true) {
      const updatedBook = { ...book.value, chapters: newChapters };
      updateBook(book.value.id, updatedBook);
    }
  },
  { debounce: 500, deep: true },
);

// Handle mission updates
watchDebounced(
  mission,
  (newMission) => {
    if (book.value && shouldUpdate.value !== true) {
      const updatedBook = { ...book.value, mission: newMission };
      updateBook(book.value.id, updatedBook);
    }
  },
  { debounce: 500 },
);

// Handle title updates
watchDebounced(
  title,
  (newTitle) => {
    if (book.value && shouldUpdate.value !== true) {
      const updatedBook = { ...book.value, title: newTitle };
      updateBook(book.value.id, updatedBook);
    }
  },
  { debounce: 500 },
);

// Handle subtitle updates
watchDebounced(
  subtitle,
  (newSubtitle) => {
    if (book.value && shouldUpdate.value !== true) {
      const updatedBook = { ...book.value, subtitle: newSubtitle };
      updateBook(book.value.id, updatedBook);
    }
  },
  { debounce: 500 },
);

// Handle questions updates
watchDebounced(
  missionQuestions,
  (newQuestions) => {
    if (book.value && shouldUpdate.value !== true) {
      const updatedBook = { ...book.value, missionQuestions: newQuestions };
      updateBook(book.value.id, updatedBook);
    }
  },
  { debounce: 500, deep: true },
);
</script>
