<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/nonfiction/NonFictionMissionFlow.vue (updated) -->
<template>
  <OnboardingContainer
    :show="show"
    :current-step="currentStep"
    :total-steps="totalSteps"
    :current-step-title="stepTitles[currentStep]"
    @update:show="$emit('update:show', $event)"
  >
    <!-- Step 1: Gender -->
    <GenderStep
      v-if="currentStep === 1"
      v-model:questions="genderQuestions"
      @next="nextStep"
      @back="prevStep"
    />

    <!-- Step 2: Age -->
    <AgeStep
      v-if="currentStep === 2"
      v-model:questions="ageQuestions"
      @next="nextStep"
      @back="prevStep"
    />

    <!-- Step 3: Description -->
    <DescriptionStep
      v-if="currentStep === 3"
      v-model:questions="descriptionQuestions"
      placeholder="Example: Busy entrepreneurs who want to scale their business while maintaining work-life balance"
      book-type="Non-Fiction"
      @next="nextStep"
      @back="prevStep"
      @open-transcription="handleTranscription"
    />

    <!-- Step 4: Core Need -->
    <CoreNeedStep
      v-if="currentStep === 4"
      v-model:questions="coreNeedQuestions"
      :loading="loadingNeed"
      @next="nextStep"
      @back="prevStep"
      @generate-need="generateNeed"
    />

    <!-- Step 5: Problems -->
    <ProblemsStep
      v-if="currentStep === 5"
      v-model:questions="problemsQuestions"
      :loading="loadingProblems"
      @next="nextStep"
      @back="prevStep"
      @generate-problems="generateProblems"
    />

    <!-- Step 6: Transformations -->
    <TransformationsStep
      v-if="currentStep === 6"
      v-model:questions="transformationsQuestions"
      :loading="loadingTransformations"
      @next="nextStep"
      @back="prevStep"
      @generate-transformations="generateTransformations"
    />

    <!-- Step 7: Author Impact -->
    <AuthorImpactStep
      v-if="currentStep === 7"
      v-model:questions="authorImpactQuestions"
      @next="nextStep"
      @back="prevStep"
      @open-transcription="handleTranscription"
    />

    <!-- Step 8: Mission Statement -->
    <MissionStatementStep
      v-if="currentStep === 8"
      v-model:mission="mission"
      :loading="loadingMission"
      @next="nextStep"
      @back="prevStep"
      @generate-mission="generateMission"
    />

    <!-- Step 9: Title & Subtitle -->
    <NonFictionTitleSubtitleStep
      v-if="currentStep === 9"
      v-model:title-model-value="title"
      v-model:subtitle-model-value="subtitle"
      :questions="allQuestions"
      @next="nextStep"
      @back="prevStep"
    />

    <!-- Step 10: Chapters -->
    <NonFictionChaptersStep
      v-if="currentStep === 10"
      v-model="chapters"
      :title="title"
      :subtitle="subtitle"
      :questions="allQuestions"
      @complete="completeNonFiction"
      @back="prevStep"
    />
  </OnboardingContainer>

  <!-- Transcription Dialog -->
  <q-dialog v-model="transcriptionDialog" persistent>
    <BookTranscriptionApp
      @completed="completedTranscript"
      @close="transcriptionDialog = false"
    />
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import { composeText } from 'src/shared/api/openai';
import { confirmOverrideText, warn } from 'src/shared/lib/quasar-dialogs';
import {
  OnboardingContainer,
  GenderStep,
  AgeStep,
  DescriptionStep,
} from '../shared';
import BookTranscriptionApp from '../../BookTranscriptionApp.vue';

import CoreNeedStep, { type CoreNeedQuestions } from './steps/CoreNeedStep.vue';
import ProblemsStep, { type ProblemsQuestions } from './steps/ProblemsStep.vue';
import TransformationsStep, {
  type TransformationsQuestions,
} from './steps/TransformationsStep.vue';
import AuthorImpactStep, {
  type AuthorImpactQuestions,
} from './steps/AuthorImpactStep.vue';
import MissionStatementStep from './steps/MissionStatementStep.vue';
import NonFictionTitleSubtitleStep from './steps/NonFictionTitleSubtitleStep.vue';
import NonFictionChaptersStep from './steps/NonFictionChaptersStep.vue';

interface ChapterOutline {
  title: string;
  outline: string;
  wordCount: number;
  number: number;
  isSection: boolean;
}

const props = defineProps<{
  show: boolean;
  mission: string;
  questions: {
    gender: 'Male' | 'Female' | 'Both';
    age: string[];
    description: string;
    need: string;
    problems: string;
    readerImpact: string;
    authorImpact: string;
  };
  title?: string;
  subtitle?: string;
  chapters?: ChapterOutline[];
}>();

const emit = defineEmits<{
  'update:show': [value: boolean];
  'update:mission': [value: string];
  'update:questions': [value: any];
  'update:title': [value: string];
  'update:subtitle': [value: string];
  'update:chapters': [value: ChapterOutline[]];
  complete: [];
}>();

const $q = useQuasar();
const giveNotice = ref(false);

// Step management
const currentStep = ref(1);
const totalSteps = 10;

const stepTitles = {
  1: 'Target Gender',
  2: 'Target Age',
  3: 'Reader Description',
  4: 'Core Need',
  5: 'Problems & Fears',
  6: 'Transformations',
  7: 'Author Impact',
  8: 'Mission Statement',
  9: 'Title & Subtitle',
  10: 'Chapter Outline',
};

// Question states - now separated
const genderQuestions = reactive({
  gender: props.questions.gender,
});

const ageQuestions = reactive({
  age: [...props.questions.age],
});

const descriptionQuestions = reactive({
  description: props.questions.description,
});

const coreNeedQuestions = reactive<CoreNeedQuestions>({
  need: props.questions.need,
});

const problemsQuestions = reactive<ProblemsQuestions>({
  problems: props.questions.problems,
});

const transformationsQuestions = reactive<TransformationsQuestions>({
  readerImpact: props.questions.readerImpact,
});

const authorImpactQuestions = reactive<AuthorImpactQuestions>({
  authorImpact: props.questions.authorImpact,
});

const mission = ref(props.mission);
const title = ref(props.title || '');
const subtitle = ref(props.subtitle || '');
const chapters = ref<ChapterOutline[]>(props.chapters || []);

// Loading states
const loadingNeed = ref(false);
const loadingProblems = ref(false);
const loadingTransformations = ref(false);
const loadingMission = ref(false);

// Transcription
const transcriptionDialog = ref(false);
const selectedField = ref('');

// Computed property for all questions combined
const allQuestions = computed(() => ({
  gender: genderQuestions.gender,
  age: ageQuestions.age,
  description: descriptionQuestions.description,
  need: coreNeedQuestions.need,
  problems: problemsQuestions.problems,
  readerImpact: transformationsQuestions.readerImpact,
  authorImpact: authorImpactQuestions.authorImpact,
}));

// Navigation
const nextStep = () => {
  if (currentStep.value < totalSteps) {
    currentStep.value++;
  }
};

const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
  } else {
    emit('update:show', false);
  }
};

// Transcription handling
const handleTranscription = (field: string) => {
  selectedField.value = field;
  transcriptionDialog.value = true;
};

const completedTranscript = (data: any) => {
  const content = data.content;
  switch (selectedField.value) {
    case 'description':
      descriptionQuestions.description = content;
      break;
    case 'authorImpact':
      authorImpactQuestions.authorImpact = content;
      break;
  }
  transcriptionDialog.value = false;
};

// AI Generation functions
const ageConcat = new Intl.ListFormat('en', {
  style: 'long',
  type: 'conjunction',
});

const ageGroups = computed(() => ageConcat.format(ageQuestions.age));
const results = ref({} as { [key: string]: any });

// Prompts (updated to use new separate question objects)
const promptNeed = computed(
  () => `
  Author's Target Audience: ${descriptionQuestions.description}
  Gender: ${genderQuestions.gender}
  Age: ${ageGroups.value}
  As an author, your goal is to create a list of 3-5 core needs of the target audience provided, using their demographic details.
  Given the details above, provide 3-5 core needs of someone in this gender, age band, and main interest or category. The response should follow the criteria below:
  1. core needs of this avatar using the demographic details
  2. do not separate by age band
  3. less than 10 words
  4. provide each response in a new line, without a number, bullet point, or break tag
`,
);

const promptProblems = computed(
  () => `
  Author's Target Audience: ${descriptionQuestions.description}
  Gender: ${genderQuestions.gender}
  Age: ${ageGroups.value}
  As an author, your goal is to create a list of 3-5 problems and fears of the target audience provided, using their demographic details.
  The response should follow the criteria below:
  1. short sentences. less than 10 words
  2. do not separate by age band
  3. problems and fears of this target audiences
  4. provide each response in a new line, without a number, bullet point, or break tag
`,
);

const promptTransformations = computed(
  () => `
  Gender: ${genderQuestions.gender}
  Age: ${ageGroups.value}
  Author's Target Audience: ${descriptionQuestions.description}
  Core Needs: ${coreNeedQuestions.need}
  As an author, your goal is to create a list of 3-4 transformations someone will experience on completing your book, given the avatar demographics, core needs and problems and fears given above. The response should follow the criteria below:
  1. short sentences, less than 10 words
  2. exciting, inspiring, motivating
  3. provide each response in a new line, without a number, bullet point, or break tag
`,
);

const promptMission = computed(
  () => `
  Gender: ${genderQuestions.gender}
  Age groups: ${ageGroups.value}
  Main interest or category: ${descriptionQuestions.description}
  Core needs the book will meet: ${coreNeedQuestions.need}
  Problems and fears: ${problemsQuestions.problems}
  For the author, this book will: ${authorImpactQuestions.authorImpact}.

  Using the details above, write a mission statement for this avatar given the following criteria:
  1. give the avatar a name using the gender provided
  2. introduce the avatar using name and demographics
  3. write the statement in a punchy, positive, inspiring, sales language
  4. write the statement in no longer than 3-5 sentences and within 60-75 words
  5. always close the mission statement with how the book will help the author of this book

  Sample: "Your target audience is Bob, a 37 year old father from Canada, who is a project manager. He is concerned about steady work over a long period of time, developing his skills, and advancing in his career. This book will give him a path about never having to worry where his next client is going to come from, increases his income by double, and give him free time. It will help you by having Bob join your program, and invest with you for ongoing coaching."
`,
);

async function generateNeed() {
  if (
    !genderQuestions.gender ||
    ageQuestions.age.length === 0 ||
    !descriptionQuestions.description
  ) {
    warn($q, {
      title: 'Missing fields',
      message: 'Please fill in all the previous fields first.',
    });
    return;
  }

  let promptRequest = promptNeed.value;
  if (coreNeedQuestions.need && giveNotice.value) {
    const shouldDelete = await confirmOverrideText($q);
    if (!shouldDelete) return;
  }

  loadingNeed.value = true;
  if (results.value.need) {
    promptRequest = `${promptRequest} Please refrain from utilizing the following results or repeating the same sentences from the following results. ${results.value.need}`;
  }

  try {
    const response = await composeText(promptRequest);
    coreNeedQuestions.need = response;
    results.value.need = `${results.value.need} ${response}`;
  } catch (e) {
    console.error(e);
    $q.notify({ color: 'negative', message: 'Error generating content: ' + e });
  } finally {
    giveNotice.value = true;
    loadingNeed.value = false;
  }
}

async function generateProblems() {
  if (
    !genderQuestions.gender ||
    ageQuestions.age.length === 0 ||
    !descriptionQuestions.description ||
    !coreNeedQuestions.need
  ) {
    warn($q, {
      title: 'Missing fields',
      message: 'Please fill in all the previous fields first.',
    });
    return;
  }

  let promptRequest = promptProblems.value;
  if (problemsQuestions.problems && giveNotice.value) {
    const shouldDelete = await confirmOverrideText($q);
    if (!shouldDelete) return;
  }

  loadingProblems.value = true;
  if (results.value.problems) {
    promptRequest = `${promptRequest} Please refrain from utilizing the following results or repeating the same sentences from the following results. ${results.value.problems}`;
  }

  try {
    const response = await composeText(promptRequest);
    problemsQuestions.problems = response;
    results.value.problems = `${results.value.problems} ${response}`;
  } catch (e) {
    console.error(e);
    $q.notify({ color: 'negative', message: 'Error generating content: ' + e });
  } finally {
    giveNotice.value = true;
    loadingProblems.value = false;
  }
}

async function generateTransformations() {
  if (
    !genderQuestions.gender ||
    ageQuestions.age.length === 0 ||
    !descriptionQuestions.description ||
    !coreNeedQuestions.need
  ) {
    warn($q, {
      title: 'Missing fields',
      message: 'Please fill in all the previous fields first.',
    });
    return;
  }

  let promptRequest = promptTransformations.value;
  if (transformationsQuestions.readerImpact && giveNotice.value) {
    const shouldDelete = await confirmOverrideText($q);
    if (!shouldDelete) return;
  }

  loadingTransformations.value = true;
  if (results.value.transformations) {
    promptRequest = `${promptRequest} Please refrain from utilizing the following results or repeating the same sentences from the following results. ${results.value.transformations}`;
  }

  try {
    const response = await composeText(promptRequest);
    transformationsQuestions.readerImpact = response;
    results.value.transformations = `${results.value.transformations} ${response}`;
  } catch (e) {
    console.error(e);
    $q.notify({ color: 'negative', message: 'Error generating content: ' + e });
  } finally {
    giveNotice.value = true;
    loadingTransformations.value = false;
  }
}

async function generateMission() {
  let promptRequest = promptMission.value;
  if (mission.value && giveNotice.value) {
    const shouldDelete = await confirmOverrideText($q);
    if (!shouldDelete) return;
  }

  loadingMission.value = true;
  if (results.value.mission) {
    promptRequest = `${promptRequest} Please refrain from utilizing the following results or repeating the same sentences from the following results. ${results.value.mission}`;
  }

  try {
    const response = await composeText(promptRequest);
    mission.value = response;
    results.value.mission = `${results.value.mission} ${response}`;
  } catch (e) {
    console.error(e);
    $q.notify({ color: 'negative', message: 'Error generating content: ' + e });
  } finally {
    giveNotice.value = true;
    loadingMission.value = false;
  }
}

const completeNonFiction = () => {
  // Update the main questions object
  const updatedQuestions = {
    gender: genderQuestions.gender,
    age: ageQuestions.age,
    description: descriptionQuestions.description,
    need: coreNeedQuestions.need,
    problems: problemsQuestions.problems,
    readerImpact: transformationsQuestions.readerImpact,
    authorImpact: authorImpactQuestions.authorImpact,
  };

  emit('update:questions', updatedQuestions);
  emit('update:mission', mission.value);
  emit('update:title', title.value);
  emit('update:subtitle', subtitle.value);
  emit('update:chapters', chapters.value);
  emit('complete');
};

// Initialize on mount
onMounted(async () => {
  if (ageQuestions.age.length) {
    // Auto-generate if we have existing data
    if (coreNeedQuestions.need) {
      try {
        await generateNeed();
      } catch (e) {
        console.error(e);
      }
    }
    if (problemsQuestions.problems) {
      try {
        await generateProblems();
      } catch (e) {
        console.error(e);
      }
    }
    if (transformationsQuestions.readerImpact) {
      try {
        await generateTransformations();
      } catch (e) {
        console.error(e);
      }
    }
    if (mission.value) {
      try {
        await generateMission();
      } catch (e) {
        console.error(e);
      }
    }
  }
});
</script>
