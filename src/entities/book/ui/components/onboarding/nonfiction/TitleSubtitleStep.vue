<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/nonfiction/TitleSubtitleStep.vue -->
<template>
  <LegacyScrollArea>
    <LegacySection
      class="flex justify-between"
      :show-header="true"
      title="Write your Title and Subtitle"
    >
      <template #header-actions v-if="1 != 1">
        <q-btn
          v-if="!showSuggestions"
          @click="generateTitleSubtitlePairs"
          :loading="loading"
          :disable="loading"
          color="primary"
          icon="img:robot.png"
        >
          &nbsp;&nbsp;Ask Manny
          <q-tooltip>Ask <PERSON> for Title & Subtitle suggestions</q-tooltip>
        </q-btn>
      </template>
    </LegacySection>
    <q-separator />

    <LegacySection>
      <div class="q-gutter-md">
        <LegacyInput
          v-model="title"
          label="Book Title"
          outlined
          placeholder="Enter your book title..."
        />

        <LegacyInput
          v-model="subtitle"
          label="Book Subtitle"
          outlined
          placeholder="Enter your book subtitle..."
        />
      </div>

      <!-- Legacy Suggestion List -->
      <LegacySuggestionList
        :title-subtitle-pairs="titleSubtitlePairs"
        :selected-title="title"
        :selected-subtitle="subtitle"
        :loading="loading"
        @select-title="handleTitleSelect"
        @select-subtitle="handleSubtitleSelect"
        @select-pair="handlePairSelect"
        @hide="hideSuggestions"
        @regenerate="generateTitleSubtitlePairs"
      />
    </LegacySection>
  </LegacyScrollArea>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useVModel, useVModels } from '@vueuse/core';
import { useQuasar } from 'quasar';
import { composeText } from 'src/shared/api/openai';
import { confirmOverrideText, warn } from 'src/shared/lib/quasar-dialogs';
import { MissionQuestions } from 'src/entities/book/model/types';
import {
  LegacySection,
  LegacyScrollArea,
  LegacyInput,
  LegacySuggestionList,
} from '../shared';

interface TitleSubtitlePair {
  title: string;
  subtitle: string;
}

const props = withDefaults(
  defineProps<{
    titleModelValue: string;
    subtitleModelValue: string;
    questions?: MissionQuestions;
    mission?: string;
    updating?: boolean;
  }>(),
  {
    updating: false,
    mission: '',
  },
);

const emit = defineEmits<{
  'update:titleModelValue': [value: string];
  'update:subtitleModelValue': [value: string];
}>();

const { titleModelValue: title, subtitleModelValue: subtitle } = useVModels(
  props,
  emit,
);
const questions = useVModel(props, 'questions', emit);
const $q = useQuasar();

// Component state
const loading = ref(false);
const showSuggestions = ref(false);
const titleSubtitlePairs = ref<TitleSubtitlePair[]>([]);
const results = ref('');

// Computed properties
const canGenerate = computed(() => {
  return (
    (questions.value?.need &&
      questions.value?.problems &&
      questions.value?.readerImpact &&
      questions.value?.authorImpact) ||
    props.mission
  );
});

// Event handlers
const handleTitleSelect = (selectedTitle: string) => {
  title.value = selectedTitle;
};

const handleSubtitleSelect = (selectedSubtitle: string) => {
  subtitle.value = selectedSubtitle;
};

const handlePairSelect = (pair: TitleSubtitlePair) => {
  title.value = pair.title;
  subtitle.value = pair.subtitle;
};

const hideSuggestions = () => {
  showSuggestions.value = false;
};

// AI prompt for combined title-subtitle generation
const combinedPrompt = computed(() => {
  let promptStr = `You are a master book title creator with expertise in marketing, psychology, and publishing.
Your task: create 5 original, compelling book TITLE + SUBTITLE pairs for a non-fiction book.

  <p>Target Reader Gender: ${questions?.value?.gender}</p>
  <p>Target Reader Age: ${questions?.value?.age}</p>`;

  if (questions.value?.description) {
    promptStr += `<p>Target Reader Description: ${questions.value.description}</p>`;
  }

  if (questions.value?.need) {
    promptStr += `<p>Core Needs: ${questions.value.need}</p>`;
  }
  if (questions.value?.problems) {
    promptStr += `<p>Top Problems/Fears: ${questions.value.problems}</p>`;
  }
  if (questions.value?.readerImpact) {
    promptStr += `<p>Desired Transformation: ${questions.value.readerImpact}</p>`;
  }
  if (questions.value?.authorImpact) {
    promptStr += `<p>Author Impact: ${questions.value.authorImpact}</p>`;
  }
  if (props.mission) {
    promptStr += `<p>Mission Statement: ${props.mission}</p>`;
  }

  promptStr += `

Requirements:
1. Titles must be EXACTLY 3 words (no exceptions).
2. Subtitles must include EXACTLY 3 benefits, formatted like:
   The Definitive Guide to XYZ
   How to XYZ
   An Expert's Guide to XYZ
   The Proven Path to XYZ
   Secrets to XYZ
   The New Rules of XYZ
3. Do NOT use generic clichés like "unlock purpose" or "find inner peace."
4. Make them curiosity-provoking, emotional, and market-aware, similar to top non-fiction bestsellers.
5. Each title should be on its own line, followed by its subtitle on the next line
6. Separate each pair with a blank line
7. Use plain text without numbering, bullets, or special formatting
8. Make titles provocative and curiosity-inducing
9. Make subtitles benefit-focused and compelling

Format example:
Title One
Subtitle describing benefits and promises

Title Two
Subtitle describing benefits and promises

[continue for all 5 pairs]`;

  return promptStr;
});

// Generate title-subtitle pairs
async function generateTitleSubtitlePairs() {
  if (!canGenerate.value) {
    warn($q, {
      title: 'Missing Information',
      message:
        'Manny needs your mission statement and core needs or transformations to generate suggestions.',
    });
    return;
  }

  // Check if user wants to override existing content
  if (titleSubtitlePairs.value.length > 0) {
    const shouldOverride = await confirmOverrideText($q);
    if (!shouldOverride) {
      return;
    }
  }

  let promptRequest = combinedPrompt.value;

  // Add previous results to avoid repetition
  if (results.value) {
    promptRequest += `\n\nPlease avoid repeating or being similar to these previous results:\n${results.value}`;
  }

  loading.value = true;

  try {
    const response = await composeText(promptRequest);

    // Parse the response to extract title-subtitle pairs
    const pairs = parseTitleSubtitleResponse(response);

    if (pairs.length >= 3) {
      titleSubtitlePairs.value = pairs;
      showSuggestions.value = true;
      // Store results to avoid repetition in future calls
      results.value = `${results.value}\n${response}`;
    } else {
      throw new Error('Unexpected response format from AI');
    }
  } catch (e) {
    console.error(e);
    $q.notify({
      type: 'negative',
      message:
        'An error occurred while generating suggestions. Please try again.',
      position: 'top',
    });
  } finally {
    loading.value = false;
  }
}

// Parse AI response to extract title-subtitle pairs
function parseTitleSubtitleResponse(response: string): TitleSubtitlePair[] {
  const pairs: TitleSubtitlePair[] = [];

  // Split by double newlines to get pairs, then by single newlines within pairs
  const lines = response
    .trim()
    .split('\n')
    .filter((line) => line.trim() !== '');

  for (let i = 0; i < lines.length; i += 2) {
    if (i + 1 < lines.length) {
      const title = lines[i]
        .replace(/^\d+\.\s*/, '')
        .replace(/^"|"$/g, '')
        .trim();
      const subtitle = lines[i + 1]
        .replace(/^\d+\.\s*/, '')
        .replace(/^"|"$/g, '')
        .trim();

      if (title && subtitle) {
        pairs.push({ title, subtitle });
      }
    }
  }

  return pairs;
}
</script>
