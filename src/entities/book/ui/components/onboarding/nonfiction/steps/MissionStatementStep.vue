<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/nonfiction/steps/MissionStatementStep.vue -->
<template>
  <OnboardingStep
    title="Your Mission Statement"
    description="Based on your answers, here's your personalized mission statement. You can edit it as needed."
    section-title="Generated Mission"
    next-label="Complete Mission"
    next-icon="check"
    :loading="loading"
    @next="$emit('complete')"
    @back="$emit('back')"
  >
    <OnboardingGrid :columns="1" gap="24px">
      <div class="question-section">
        <div class="mission-actions q-mb-md">
          <q-btn
            color="primary"
            icon="img:robot.png"
            label="Generate Mission Statement"
            @click="$emit('generate-mission')"
            :loading="loading"
          />
        </div>

        <div class="mission-editor">
          <q-editor
            v-model="mission"
            placeholder="Your personalized mission statement will appear here..."
            class="mission-statement-editor"
          />
        </div>
      </div>
    </OnboardingGrid>
  </OnboardingStep>
</template>

<script setup lang="ts">
import { useVModels } from '@vueuse/core';
import { OnboardingStep, OnboardingGrid } from '../../shared';

const props = defineProps<{
  mission: string;
  loading?: boolean;
}>();

const emit = defineEmits<{
  'update:mission': [value: string];
  complete: [];
  back: [];
  'generate-mission': [];
}>();

const { mission } = useVModels(props, emit);
</script>

<style lang="scss" scoped>
.question-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.mission-actions {
  display: flex;
  justify-content: center;
}

.mission-editor {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.mission-statement-editor {
  :deep(.q-editor__content) {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    min-height: 200px;

    &::before {
      opacity: 0.4;
    }
  }
}
</style>
