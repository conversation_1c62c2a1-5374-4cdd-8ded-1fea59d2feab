<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/nonfiction/steps/AuthorImpactStep.vue -->
<template>
  <OnboardingStep
    title="What will this book do for you?"
    description="Define how this book will benefit you personally and professionally."
    section-title="Author Impact"
    :disabled="isStepDisabled"
    @next="$emit('next')"
    @back="$emit('back')"
  >
    <OnboardingGrid :columns="1" gap="24px">
      <div class="question-section">
        <h3 class="question-title">
          What are 2-3 things that you want this book to do for you and your
          business?
        </h3>
        <OnboardingInput
          v-model="questions.authorImpact"
          type="textarea"
          placeholder="Example: Establish me as a thought leader, generate leads for my consulting business, create passive income"
          :rows="4"
        >
          <template #append>
            <q-btn flat icon="mic" @click="openTranscription('authorImpact')">
              <q-tooltip>Transcribe</q-tooltip>
            </q-btn>
          </template>
        </OnboardingInput>
      </div>
    </OnboardingGrid>
  </OnboardingStep>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useVModels } from '@vueuse/core';
import { OnboardingStep, OnboardingInput, OnboardingGrid } from '../../shared';

export interface AuthorImpactQuestions {
  authorImpact: string;
}

const props = defineProps<{
  questions: AuthorImpactQuestions;
}>();

const emit = defineEmits<{
  'update:questions': [value: AuthorImpactQuestions];
  next: [];
  back: [];
  'open-transcription': [field: string];
}>();

const { questions } = useVModels(props, emit);

const isStepDisabled = computed(() => {
  return !questions.value.authorImpact.trim();
});

const openTranscription = (field: string) => {
  emit('open-transcription', field);
};
</script>

<style lang="scss" scoped>
.question-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.question-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  margin: 0;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}
</style>
