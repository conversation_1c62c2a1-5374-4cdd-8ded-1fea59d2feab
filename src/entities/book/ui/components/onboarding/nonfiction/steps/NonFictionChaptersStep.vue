<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/nonfiction/steps/NonFictionChaptersStep.vue -->
<template>
  <OnboardingChapters
    v-model="chapters"
    :title="title"
    :subtitle="subtitle"
    :questions="questions"
    book-type="Non-Fiction"
    step-title="Create Your Non-Fiction Chapters"
    step-description="Organize your expertise into logical chapters that guide readers through your methodology."
    next-label="Complete Non-Fiction Setup"
    next-icon="check"
    :ai-prompt="nonFictionChapterPrompt"
    @complete="$emit('complete')"
    @back="$emit('back')"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useVModel } from '@vueuse/core';
import { OnboardingChapters } from '../../shared';

interface ChapterOutline {
  title: string;
  outline: string;
  wordCount: number;
  number: number;
  isSection: boolean;
}

interface Props {
  modelValue: ChapterOutline[];
  title: string;
  subtitle: string;
  questions?: any;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  'update:modelValue': [value: ChapterOutline[]];
  complete: [];
  back: [];
}>();

const chapters = useVModel(props, 'modelValue', emit);

// Non-Fiction specific chapter generation prompt
const nonFictionChapterPrompt = computed(() => {
  return `Using the book title and subtitle provided below, generate 9 chapter title outlines for a non-fiction book using the following criteria:
  <ol>
    <li>Provide a numbered list. For example: "1. Foundation for Success"</li>
    <li>Each chapter title should be 4-6 words, based on the methodology, system, or framework suggested by the title, subtitle, and the author's expertise.</li>
    <li>The chapter titles should follow a logical progression that teaches the reader step-by-step.</li>
    <li>The chapter titles should reflect the core need and transformations the book promises.</li>
    <li>Use authoritative, educational language that demonstrates expertise and provides value.</li>
    <li>Structure should build from foundational concepts to advanced implementation.</li>
    <li>Avoid using Markdown or HTML formatting in your response.</li>
  </ol>
  <br />
  The provided information is as follows:
  <ul>
    <li>Book title: ${props.title}</li>
    <li>Book subtitle: ${props.subtitle}</li>
    <li>Target Audience: ${props.questions?.description}</li>
    <li>Core Need: ${props.questions?.need}</li>
    <li>Problems Solved: ${props.questions?.problems}</li>
    <li>Reader Transformations: ${props.questions?.readerImpact}</li>
    <li>Author Goals: ${props.questions?.authorImpact}</li>
  </ul>`;
});
</script>
