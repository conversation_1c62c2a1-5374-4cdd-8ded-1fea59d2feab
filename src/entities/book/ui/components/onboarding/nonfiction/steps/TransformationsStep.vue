<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/nonfiction/steps/TransformationsStep.vue -->
<template>
  <OnboardingStep
    title="What transformations will readers experience?"
    description="Define the positive outcomes and changes your readers will achieve after completing your book."
    section-title="Reader Transformations"
    :disabled="isStepDisabled"
    @next="$emit('next')"
    @back="$emit('back')"
  >
    <OnboardingGrid :columns="1" gap="24px">
      <div class="question-section">
        <h3 class="question-title">
          When someone completes your book, what are 3-4 transformations they
          will experience?
        </h3>
        <OnboardingInput
          v-model="questions.readerImpact"
          type="textarea"
          placeholder="Example: Confidence in making investment decisions, clear retirement plan, reduced financial stress, increased wealth"
          :rows="5"
        >
          <template #append>
            <OnboardingMannyButton
              :loading="loading"
              :disabled="loading"
              @click="$emit('generate-transformations')"
            />
          </template>
        </OnboardingInput>
      </div>
    </OnboardingGrid>
  </OnboardingStep>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useVModels } from '@vueuse/core';
import {
  OnboardingStep,
  OnboardingInput,
  OnboardingGrid,
  OnboardingMannyButton,
} from '../../shared';

export interface TransformationsQuestions {
  readerImpact: string;
}

const props = defineProps<{
  questions: TransformationsQuestions;
  loading?: boolean;
}>();

const emit = defineEmits<{
  'update:questions': [value: TransformationsQuestions];
  next: [];
  back: [];
  'generate-transformations': [];
}>();

const { questions } = useVModels(props, emit);

const isStepDisabled = computed(() => {
  return !questions.value.readerImpact.trim();
});
</script>

<style lang="scss" scoped>
.question-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.question-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  margin: 0;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}
</style>
