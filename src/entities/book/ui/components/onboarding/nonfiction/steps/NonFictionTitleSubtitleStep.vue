<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/nonfiction/steps/NonFictionTitleSubtitleStep.vue -->
<template>
  <OnboardingTitleSubtitle
    v-model:title-model-value="title"
    v-model:subtitle-model-value="subtitle"
    :questions="questions"
    book-type="Non-Fiction"
    step-title="Create Your Non-Fiction Title & Subtitle"
    step-description="Craft compelling titles that communicate your expertise and attract your target audience."
    title-label="Non-Fiction Title"
    subtitle-label="Non-Fiction Subtitle"
    title-placeholder="Enter your non-fiction book title..."
    subtitle-placeholder="Enter your non-fiction book subtitle..."
    :ai-prompt="nonFictionPrompt"
    @next="$emit('next')"
    @back="$emit('back')"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useVModels } from '@vueuse/core';
import { OnboardingTitleSubtitle } from '../../shared';

interface Props {
  titleModelValue: string;
  subtitleModelValue: string;
  questions?: any;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  'update:titleModelValue': [value: string];
  'update:subtitleModelValue': [value: string];
  next: [];
  back: [];
}>();

const { titleModelValue: title, subtitleModelValue: subtitle } = useVModels(
  props,
  emit,
);

// Non-Fiction specific AI prompt
const nonFictionPrompt = computed(() => {
  if (!props.questions) return '';

  return `You are a master book title creator with expertise in marketing, psychology, and publishing.
Your task: create 5 original, compelling book TITLE + SUBTITLE pairs for a Non-Fiction book.

  <p>Target Reader Gender: ${props.questions?.gender}</p>
  <p>Target Reader Age: ${props.questions?.age}</p>
  <p>Target Audience Description: ${props.questions?.description}</p>
  <p>Core Need: ${props.questions?.need}</p>
  <p>Problems and Fears: ${props.questions?.problems}</p>
  <p>Reader Transformations: ${props.questions?.readerImpact}</p>
  <p>Author Goals: ${props.questions?.authorImpact}</p>

Requirements:
1. Titles must be EXACTLY 3 words (no exceptions).
2. Subtitles must include EXACTLY 3 benefits, formatted like:
   The Ultimate Guide to Success, Wealth, and Freedom
   Master Your Business, Transform Your Life, and Build Your Legacy
   Proven Strategies for Growth, Profit, and Impact
3. Titles should be authoritative, solution-focused, and promise transformation.
4. Use power words that convey expertise and results.
5. Make them curiosity-provoking and benefit-driven for the target audience.
6. Each title should be on its own line, followed by its subtitle on the next line
7. Separate each pair with a blank line
8. Use plain text without numbering, bullets, or special formatting

Format example:
Title One
Subtitle describing benefits and promises

Title Two
Subtitle describing benefits and promises

[continue for all 5 pairs]`;
});
</script>
