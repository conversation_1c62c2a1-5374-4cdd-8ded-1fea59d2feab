<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/nonfiction/steps/ProblemsStep.vue -->
<template>
  <OnboardingStep
    title="What problems do your readers face?"
    description="Identifying specific problems helps create solutions that truly resonate with your audience."
    section-title="Problems & Fears"
    :disabled="isStepDisabled"
    @next="$emit('next')"
    @back="$emit('back')"
  >
    <OnboardingGrid :columns="1" gap="24px">
      <div class="question-section">
        <h3 class="question-title">
          List 3-5 problems and fears that your ideal reader has
        </h3>
        <OnboardingInput
          v-model="questions.problems"
          type="textarea"
          placeholder="Example: Fear of running out of money in retirement, not knowing where to invest, feeling overwhelmed by financial jargon"
          :rows="5"
        >
          <template #append>
            <OnboardingMannyButton
              :loading="loading"
              :disabled="loading"
              @click="$emit('generate-problems')"
            />
          </template>
        </OnboardingInput>
      </div>
    </OnboardingGrid>
  </OnboardingStep>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useVModels } from '@vueuse/core';
import {
  OnboardingStep,
  OnboardingInput,
  OnboardingGrid,
  OnboardingMannyButton,
} from '../../shared';

export interface ProblemsQuestions {
  problems: string;
}

const props = defineProps<{
  questions: ProblemsQuestions;
  loading?: boolean;
}>();

const emit = defineEmits<{
  'update:questions': [value: ProblemsQuestions];
  next: [];
  back: [];
  'generate-problems': [];
}>();

const { questions } = useVModels(props, emit);

const isStepDisabled = computed(() => {
  return !questions.value.problems.trim();
});
</script>

<style lang="scss" scoped>
.question-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.question-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  margin: 0;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}
</style>
