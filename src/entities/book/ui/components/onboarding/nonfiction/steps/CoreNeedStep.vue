<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/nonfiction/steps/CoreNeedStep.vue -->
<template>
  <OnboardingStep
    title="What core need will your book meet?"
    description="Understanding the primary need helps create focused, valuable content for your readers."
    section-title="Core Need"
    :disabled="isStepDisabled"
    @next="$emit('next')"
    @back="$emit('back')"
  >
    <OnboardingGrid :columns="1" gap="24px">
      <div class="question-section">
        <h3 class="question-title">
          What is the core need that your book will meet for them?
        </h3>
        <OnboardingInput
          v-model="questions.need"
          type="textarea"
          placeholder="Example: Learning how to manage their time effectively to reduce stress"
          :rows="4"
        >
          <template #append>
            <OnboardingMannyButton
              :loading="loading"
              :disabled="loading"
              @click="$emit('generate-need')"
            />
          </template>
        </OnboardingInput>
      </div>
    </OnboardingGrid>
  </OnboardingStep>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useVModels } from '@vueuse/core';
import {
  OnboardingStep,
  OnboardingInput,
  OnboardingGrid,
  OnboardingMannyButton,
} from '../../shared';

export interface CoreNeedQuestions {
  need: string;
}

const props = defineProps<{
  questions: CoreNeedQuestions;
  loading?: boolean;
}>();

const emit = defineEmits<{
  'update:questions': [value: CoreNeedQuestions];
  next: [];
  back: [];
  'generate-need': [];
}>();

const { questions } = useVModels(props, emit);

const isStepDisabled = computed(() => {
  return !questions.value.need.trim();
});
</script>

<style lang="scss" scoped>
.question-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.question-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  margin: 0;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}
</style>
