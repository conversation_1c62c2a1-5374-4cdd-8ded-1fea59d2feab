// /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/nonfiction/index.ts (final)
export { default as NonFictionMissionFlow } from './NonFictionMissionFlow.vue';
export { default as NewBookFlow } from './NewBookFlow.vue';

// Individual step components
export { default as TargetAudienceStep } from './steps/TargetAudienceStep.vue';
export { default as CoreNeedStep } from './steps/CoreNeedStep.vue';
export { default as ProblemsStep } from './steps/ProblemsStep.vue';
export { default as TransformationsStep } from './steps/TransformationsStep.vue';
export { default as AuthorImpactStep } from './steps/AuthorImpactStep.vue';
export { default as MissionStatementStep } from './steps/MissionStatementStep.vue';
export { default as NonFictionTitleSubtitleStep } from './steps/NonFictionTitleSubtitleStep.vue';
export { default as NonFictionChaptersStep } from './steps/NonFictionChaptersStep.vue';

// Legacy components for backward compatibility
export { default as MissionStep } from './MissionStep.vue';
export { default as ChaptersStep } from './ChaptersStep.vue';
export { default as TitleSubtitleStep } from './TitleSubtitleStep.vue';
