<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/autobiography/AutoBioMissionFlow.vue (fixed recursive updates) -->
<template>
  <OnboardingContainer
    :show="show"
    :current-step="currentStep"
    :total-steps="totalSteps"
    :current-step-title="stepTitles[currentStep]"
    @update:show="$emit('update:show', $event)"
  >
    <!-- Step 1: Main Reason -->
    <AutoBioMainReasonStep
      v-if="currentStep === 1"
      v-model:questions="mainReasonQuestions"
      @next="nextStep"
      @back="prevStep"
      @open-transcription="handleTranscription"
    />

    <!-- Step 2: Book Focus -->
    <BookFocusStep
      v-if="currentStep === 2"
      v-model:questions="bookFocusQuestions"
      @next="nextStep"
      @back="prevStep"
    />

    <!-- Step 3: Book Order -->
    <BookOrderStep
      v-if="currentStep === 3"
      v-model:questions="bookOrderQuestions"
      @next="nextStep"
      @back="prevStep"
    />

    <!-- Step 4: Gender -->
    <GenderStep
      v-if="currentStep === 4"
      v-model:questions="genderQuestions"
      @next="nextStep"
      @back="prevStep"
    />

    <!-- Step 5: Age -->
    <AgeStep
      v-if="currentStep === 5"
      v-model:questions="ageQuestions"
      @next="nextStep"
      @back="prevStep"
    />

    <!-- Step 6: Description -->
    <DescriptionStep
      v-if="currentStep === 6"
      v-model:questions="descriptionQuestions"
      placeholder="Examples: Young professionals seeking work-life balance, empty nesters looking for new purpose, entrepreneurs wanting inspiration"
      book-type="Autobiography"
      @next="nextStep"
      @back="prevStep"
      @open-transcription="handleTranscription"
    />

    <!-- Step 7: Key Message -->
    <KeyMessageStep
      v-if="currentStep === 7"
      v-model:questions="keyMessageQuestions"
      :loading="loadingKeyMessage"
      @next="nextStep"
      @back="prevStep"
      @open-transcription="handleTranscription"
      @generate-key-message="generateKeyMessage"
    />

    <!-- Step 8: Title & Subtitle -->
    <AutoBioTitleSubtitleStep
      v-if="currentStep === 8"
      v-model:title-model-value="titleModel"
      v-model:subtitle-model-value="subtitleModel"
      :questions="allQuestions"
      @next="nextStep"
      @back="prevStep"
    />

    <!-- Step 9: Chapters -->
    <AutoBioChaptersStep
      v-if="currentStep === 9"
      v-model="chaptersModel"
      :title="titleModel"
      :subtitle="subtitleModel"
      :questions="allQuestions"
      @complete="completeAutoBio"
      @back="prevStep"
    />
  </OnboardingContainer>

  <!-- Transcription Dialog -->
  <q-dialog v-model="transcriptionDialog" persistent>
    <BookTranscriptionApp
      @completed="completedTranscript"
      @close="transcriptionDialog = false"
    />
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useQuasar } from 'quasar';
import { composeText } from 'src/shared/api/openai';
import { confirmOverrideText, warn } from 'src/shared/lib/quasar-dialogs';
import {
  OnboardingContainer,
  GenderStep,
  AgeStep,
  DescriptionStep,
} from '../shared';
import BookTranscriptionApp from '../../BookTranscriptionApp.vue';

import AutoBioMainReasonStep, {
  type AutoBioMainReasonQuestions,
} from './steps/AutoBioMainReasonStep.vue';
import BookFocusStep, {
  type BookFocusQuestions,
} from './steps/BookFocusStep.vue';
import BookOrderStep, {
  type BookOrderQuestions,
} from './steps/BookOrderStep.vue';
import KeyMessageStep, {
  type KeyMessageQuestions,
} from './steps/KeyMessageStep.vue';
import AutoBioTitleSubtitleStep from './steps/AutoBioTitleSubtitleStep.vue';
import AutoBioChaptersStep from './steps/AutoBioChaptersStep.vue';

interface AutoBioQuestions {
  gender: 'Male' | 'Female' | 'Both';
  age: string[];
  keyMessage: string;
  mainReason: string;
  description: string;
  bookOrder: string;
  bookFocused: string;
}

interface ChapterOutline {
  title: string;
  outline: string;
  wordCount: number;
  number: number;
  isSection: boolean;
}

const props = defineProps<{
  show: boolean;
  questions: AutoBioQuestions;
  title?: string;
  subtitle?: string;
  chapters?: ChapterOutline[];
}>();

const emit = defineEmits<{
  'update:show': [value: boolean];
  'update:questions': [value: AutoBioQuestions];
  'update:title': [value: string];
  'update:subtitle': [value: string];
  'update:chapters': [value: ChapterOutline[]];
  complete: [];
}>();

const $q = useQuasar();
const giveNotice = ref(false);

// Step management
const currentStep = ref(1);
const totalSteps = 9;

const stepTitles = {
  1: 'Your Motivation',
  2: 'Book Focus',
  3: 'Story Structure',
  4: 'Target Gender',
  5: 'Target Age',
  6: 'Reader Description',
  7: 'Key Message',
  8: 'Title & Subtitle',
  9: 'Chapter Outline',
};

// Question states - prevent recursion by not watching props directly
const mainReasonQuestions = reactive<AutoBioMainReasonQuestions>({
  mainReason: props.questions.mainReason,
});

const bookFocusQuestions = reactive<BookFocusQuestions>({
  bookFocused: props.questions.bookFocused,
});

const bookOrderQuestions = reactive<BookOrderQuestions>({
  bookOrder: props.questions.bookOrder,
});

const genderQuestions = reactive({
  gender: props.questions.gender,
});

const ageQuestions = reactive({
  age: [...props.questions.age],
});

const descriptionQuestions = reactive({
  description: props.questions.description,
});

const keyMessageQuestions = reactive<KeyMessageQuestions>({
  keyMessage: props.questions.keyMessage,
});

// Use computed models to prevent recursive updates
const titleModel = computed({
  get: () => props.title || '',
  set: (value) => emit('update:title', value),
});

const subtitleModel = computed({
  get: () => props.subtitle || '',
  set: (value) => emit('update:subtitle', value),
});

const chaptersModel = computed({
  get: () => props.chapters || [],
  set: (value) => emit('update:chapters', value),
});

// Loading states
const loadingKeyMessage = ref(false);

// Transcription
const transcriptionDialog = ref(false);
const selectedField = ref('');

// Computed property for all questions combined
const allQuestions = computed(() => ({
  mainReason: mainReasonQuestions.mainReason,
  bookFocused: bookFocusQuestions.bookFocused,
  bookOrder: bookOrderQuestions.bookOrder,
  gender: genderQuestions.gender,
  age: ageQuestions.age,
  description: descriptionQuestions.description,
  keyMessage: keyMessageQuestions.keyMessage,
}));

// Only emit updates when internal data changes (not when props change)
let isUpdatingFromProps = false;

// Watch props and update internal state without causing loops
watch(
  () => props.questions,
  (newQuestions) => {
    isUpdatingFromProps = true;

    mainReasonQuestions.mainReason = newQuestions.mainReason;
    bookFocusQuestions.bookFocused = newQuestions.bookFocused;
    bookOrderQuestions.bookOrder = newQuestions.bookOrder;
    genderQuestions.gender = newQuestions.gender;
    ageQuestions.age = [...newQuestions.age];
    descriptionQuestions.description = newQuestions.description;
    keyMessageQuestions.keyMessage = newQuestions.keyMessage;

    setTimeout(() => {
      isUpdatingFromProps = false;
    }, 0);
  },
  { immediate: true, deep: true },
);

// Watch for changes in questions and emit updates (only when not updating from props)
watch(
  () => allQuestions.value,
  (newQuestions) => {
    if (!isUpdatingFromProps) {
      emit('update:questions', newQuestions);
    }
  },
  { deep: true },
);

// Navigation
const nextStep = () => {
  if (currentStep.value < totalSteps) {
    currentStep.value++;
  }
};

const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
  } else {
    emit('update:show', false);
  }
};

// Transcription handling
const handleTranscription = (field: string) => {
  selectedField.value = field;
  transcriptionDialog.value = true;
};

const completedTranscript = (data: any) => {
  const content = data.content;
  switch (selectedField.value) {
    case 'mainReason':
      mainReasonQuestions.mainReason = content;
      break;
    case 'description':
      descriptionQuestions.description = content;
      break;
    case 'keyMessage':
      keyMessageQuestions.keyMessage = content;
      break;
  }
  transcriptionDialog.value = false;
};

// AI Generation
const ageConcat = new Intl.ListFormat('en', {
  style: 'long',
  type: 'conjunction',
});

const ageGroups = computed(() => ageConcat.format(ageQuestions.age));
const results = ref({} as { [key: string]: any });

const promptKeyMessage = computed(
  () => `
  <p>Main Reason: ${mainReasonQuestions.mainReason}</p>
  <p>Book Structure: ${bookOrderQuestions.bookOrder}</p>
  <p>Book Focused: ${bookFocusQuestions.bookFocused}</p>
  <br/>
  You are an award-winning ghostwriter with decades of experience writing bestselling autobiographies and memoirs. Your specialty is helping new authors discover the heart of their story—the message that will resonate most deeply with readers.
  <br/>
  Based on the following details, write a single, emotionally compelling key message or takeaway that readers should remember after finishing this book:
  <ul>
  <li>Main reason for writing: ${mainReasonQuestions.mainReason}</li>
  <li>Book focus: ${bookFocusQuestions.bookFocused}</li>
  <li>Book structure: ${bookOrderQuestions.bookOrder}</li>
  </ul>
  Respond with 1–2 emotionally impactful sentences. The response should feel like a back cover hook or a central theme. Do not include bullet points, labels, or any formatting—just the raw text.
`,
);

async function generateKeyMessage() {
  if (
    (!genderQuestions.gender ||
      ageQuestions.age.length === 0 ||
      !descriptionQuestions.description) &&
    giveNotice.value
  ) {
    warn($q, {
      title: 'Missing fields',
      message: 'Please fill in all the previous fields first.',
    });
    return;
  }

  let promptRequest = promptKeyMessage.value;
  if (keyMessageQuestions.keyMessage && giveNotice.value) {
    const shouldDelete = await confirmOverrideText($q);
    if (!shouldDelete) return;
  }

  loadingKeyMessage.value = true;
  if (results.value.keyMessage) {
    promptRequest = `${promptRequest} Please refrain from utilizing the following results or repeating the same sentences from the following results. ${results.value.keyMessage}`;
  }

  try {
    const response = await composeText(promptRequest);
    keyMessageQuestions.keyMessage = response;
    results.value.keyMessage = `${results.value.keyMessage} ${response}`;
  } catch (e) {
    console.error(e);
    $q.notify({ color: 'negative', message: 'Error generating content: ' + e });
  } finally {
    loadingKeyMessage.value = false;
    giveNotice.value = true;
  }
}

const completeAutoBio = () => {
  emit('complete');
};

// Initialize on mount
onMounted(async () => {
  if (ageQuestions.age.length && keyMessageQuestions.keyMessage) {
    try {
      await generateKeyMessage();
    } catch (e) {
      console.error(e);
    }
  }
});
</script>
