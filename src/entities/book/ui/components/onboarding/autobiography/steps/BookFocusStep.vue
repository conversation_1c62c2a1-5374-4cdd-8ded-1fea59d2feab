<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/autobiography/steps/BookFocusStep.vue -->
<template>
  <OnboardingStep
    title="What's your book's focus?"
    description="Understanding your book's purpose helps shape the narrative and connect with readers."
    section-title="Book Focus"
    :disabled="isStepDisabled"
    @next="$emit('next')"
    @back="$emit('back')"
  >
    <OnboardingGrid :columns="1" gap="24px">
      <div class="question-section">
        <h3 class="question-title">
          Is this book more legacy-focused, inspirational, educational, or
          transformational?
        </h3>
        <OnboardingGrid :columns="1" gap="12px">
          <OnboardingCard
            v-for="option in bookFocusOptions"
            :key="option.value"
            :title="option.label"
            :description="option.description"
            :selected="questions.bookFocused === option.value"
            @select="questions.bookFocused = option.value"
          />
        </OnboardingGrid>
      </div>
    </OnboardingGrid>
  </OnboardingStep>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useVModels } from '@vueuse/core';
import { OnboardingStep, OnboardingCard, OnboardingGrid } from '../../shared';

export interface BookFocusQuestions {
  bookFocused: string;
}

const props = defineProps<{
  questions: BookFocusQuestions;
}>();

const emit = defineEmits<{
  'update:questions': [value: BookFocusQuestions];
  next: [];
  back: [];
}>();

const { questions } = useVModels(props, emit);

const bookFocusOptions = [
  {
    value: 'Legacy-focused',
    label: 'Legacy-focused',
    description:
      'Preserving family history and memories for future generations',
  },
  {
    value: 'Inspirational',
    label: 'Inspirational',
    description: 'Motivating others through your life experiences and triumphs',
  },
  {
    value: 'Educational',
    label: 'Educational',
    description: 'Teaching lessons learned through your life journey',
  },
  {
    value: 'Transformational',
    label: 'Transformational',
    description:
      'Helping others transform their lives through your experiences',
  },
];

const isStepDisabled = computed(() => {
  return !questions.value.bookFocused;
});
</script>

<style lang="scss" scoped>
.question-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.question-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  margin: 0;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}
</style>
