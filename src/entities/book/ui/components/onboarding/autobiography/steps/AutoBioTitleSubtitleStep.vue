<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/autobiography/steps/AutoBioTitleSubtitleStep.vue -->
<template>
  <OnboardingTitleSubtitle
    v-model:title-model-value="title"
    v-model:subtitle-model-value="subtitle"
    :questions="questions"
    book-type="Autobiography"
    step-title="Create Your Autobiography Title & Subtitle"
    step-description="Craft compelling titles that capture your life story and connect with readers."
    title-label="Autobiography Title"
    subtitle-label="Autobiography Subtitle"
    title-placeholder="Enter your autobiography title..."
    subtitle-placeholder="Enter your autobiography subtitle..."
    :ai-prompt="autobiographyPrompt"
    @next="$emit('next')"
    @back="$emit('back')"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useVModels } from '@vueuse/core';
import { OnboardingTitleSubtitle } from '../../shared';

interface Props {
  titleModelValue: string;
  subtitleModelValue: string;
  questions?: any;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  'update:titleModelValue': [value: string];
  'update:subtitleModelValue': [value: string];
  next: [];
  back: [];
}>();

const { titleModelValue: title, subtitleModelValue: subtitle } = useVModels(props, emit);

// Autobiography specific AI prompt
const autobiographyPrompt = computed(() => {
  if (!props.questions) return '';

  return `You are a master book title creator with expertise in marketing, psychology, and publishing.
Your task: create 5 original, compelling book TITLE + SUBTITLE pairs for an Autobiography book.

  <p>Target Reader Gender: ${props.questions?.gender}</p>
  <p>Target Reader Age: ${props.questions?.age}</p>
  <p>Main Reason for Writing: ${props.questions?.mainReason}</p>
  <p>Book Focus: ${props.questions?.bookFocused}</p>
  <p>Book Structure: ${props.questions?.bookOrder}</p>
  <p>Target Audience Description: ${props.questions?.description}</p>
  <p>Key Message: ${props.questions?.keyMessage}</p>

Requirements:
1. Titles must be EXACTLY 3 words (no exceptions).
2. Subtitles must include EXACTLY 3 benefits, formatted like:
   A Journey of Resilience, Growth, and Triumph
   From Struggle to Success, Pain to Purpose, and Dreams to Reality
   My Story of Courage, Faith, and Transformation
3. Titles should be personal, inspiring, and emotionally authentic.
4. Focus on the human experience, growth, and life lessons.
5. Make them curiosity-provoking and relatable to the target audience.
6. Each title should be on its own line, followed by its subtitle on the next line
7. Separate each pair with a blank line
8. Use plain text without numbering, bullets, or special formatting

Format example:
Title One
Subtitle describing benefits and promises

Title Two
Subtitle describing benefits and promises

[continue for all 5 pairs]`;
});
</script>
