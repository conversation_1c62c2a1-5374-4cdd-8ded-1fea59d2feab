<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/autobiography/steps/AutoBioChaptersStep.vue -->
<template>
  <OnboardingChapters
    v-model="chapters"
    :title="title"
    :subtitle="subtitle"
    :questions="questions"
    book-type="Autobiography"
    step-title="Create Your Autobiography Chapters"
    step-description="Organize your life story into compelling chapters that take readers through your journey."
    next-label="Complete Autobiography Setup"
    next-icon="check"
    :ai-prompt="autobiographyChapterPrompt"
    @complete="$emit('complete')"
    @back="$emit('back')"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useVModel } from '@vueuse/core';
import { OnboardingChapters } from '../../shared';

interface ChapterOutline {
  title: string;
  outline: string;
  wordCount: number;
  number: number;
  isSection: boolean;
}

interface Props {
  modelValue: ChapterOutline[];
  title: string;
  subtitle: string;
  questions?: any;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  'update:modelValue': [value: ChapterOutline[]];
  complete: [];
  back: [];
}>();

const chapters = useVModel(props, 'modelValue', emit);

// Autobiography specific chapter generation prompt
const autobiographyChapterPrompt = computed(() => {
  return `Using the book title and subtitle provided below, generate 9 chapter title outlines for an autobiography using the following criteria:
  <ol>
    <li>Provide a numbered list. For example: "1. Humble Beginnings"</li>
    <li>Each chapter title should be 4-6 words, based on life stages, pivotal moments, or themes suggested by the title, subtitle, and the author's story.</li>
    <li>The chapter titles should follow the structure preference (chronological or thematic) chosen by the author.</li>
    <li>The chapter titles should reflect the key message and focus of the autobiography.</li>
    <li>Use evocative, personal language that captures emotion and human experience.</li>
    <li>Include titles that show growth, challenges overcome, and lessons learned.</li>
    <li>Avoid using Markdown or HTML formatting in your response.</li>
  </ol>
  <br />
  The provided information is as follows:
  <ul>
    <li>Book title: ${props.title}</li>
    <li>Book subtitle: ${props.subtitle}</li>
    <li>Main Reason for Writing: ${props.questions?.mainReason}</li>
    <li>Book Focus: ${props.questions?.bookFocused}</li>
    <li>Book Structure: ${props.questions?.bookOrder}</li>
    <li>Target Audience: ${props.questions?.description}</li>
    <li>Key Message: ${props.questions?.keyMessage}</li>
  </ul>`;
});
</script>
