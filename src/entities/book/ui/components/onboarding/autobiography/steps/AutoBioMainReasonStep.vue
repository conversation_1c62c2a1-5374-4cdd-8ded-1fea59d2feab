<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/autobiography/steps/AutoBioMainReasonStep.vue -->
<template>
  <OnboardingStep
    title="Why share your story?"
    description="Understanding your motivation helps create an authentic and compelling autobiography."
    section-title="Your Motivation"
    :disabled="isStepDisabled"
    @next="$emit('next')"
    @back="$emit('back')"
  >
    <OnboardingGrid :columns="1" gap="24px">
      <div class="question-section">
        <h3 class="question-title">
          What is the main reason you want to share your story?
        </h3>
        <OnboardingInput
          v-model="questions.mainReason"
          type="textarea"
          placeholder="Examples: Inspire others, leave a legacy, share life lessons, help others overcome challenges"
          :rows="4"
        >
          <template #append>
            <q-btn flat icon="mic" @click="openTranscription('mainReason')">
              <q-tooltip>Transcribe</q-tooltip>
            </q-btn>
          </template>
        </OnboardingInput>
      </div>
    </OnboardingGrid>
  </OnboardingStep>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useVModels } from '@vueuse/core';
import { OnboardingStep, OnboardingInput, OnboardingGrid } from '../../shared';

export interface AutoBioMainReasonQuestions {
  mainReason: string;
}

const props = defineProps<{
  questions: AutoBioMainReasonQuestions;
}>();

const emit = defineEmits<{
  'update:questions': [value: AutoBioMainReasonQuestions];
  next: [];
  back: [];
  'open-transcription': [field: string];
}>();

const { questions } = useVModels(props, emit);

const isStepDisabled = computed(() => {
  return !questions.value.mainReason.trim();
});

const openTranscription = (field: string) => {
  emit('open-transcription', field);
};
</script>

<style lang="scss" scoped>
.question-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.question-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  margin: 0;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}
</style>
