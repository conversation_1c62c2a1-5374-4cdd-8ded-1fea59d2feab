<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/autobiography/steps/BookOrderStep.vue -->
<template>
  <OnboardingStep
    title="How will you structure your story?"
    description="The structure helps readers follow your journey and understand your growth."
    section-title="Story Structure"
    :disabled="isStepDisabled"
    @next="$emit('next')"
    @back="$emit('back')"
  >
    <OnboardingGrid :columns="1" gap="24px">
      <div class="question-section">
        <h3 class="question-title">Do you want the order of your book to be chronological or thematic?</h3>
        <OnboardingGrid :columns="1" gap="12px">
          <OnboardingCard
            v-for="option in bookOrderOptions"
            :key="option.value"
            :title="option.label"
            :description="option.description"
            :selected="questions.bookOrder === option.value"
            @select="questions.bookOrder = option.value"
          />
        </OnboardingGrid>
      </div>
    </OnboardingGrid>
  </OnboardingStep>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useVModels } from '@vueuse/core';
import {
  OnboardingStep,
  OnboardingCard,
  OnboardingGrid,
} from '../../shared';

export interface BookOrderQuestions {
  bookOrder: string;
}

const props = defineProps<{
  questions: BookOrderQuestions;
}>();

const emit = defineEmits<{
  'update:questions': [value: BookOrderQuestions];
  next: [];
  back: [];
}>();

const { questions } = useVModels(props, emit);

const bookOrderOptions = [
  {
    value: 'Chronological',
    label: 'Chronological (Life Stages)',
    description: 'Tell your story in the order events happened - childhood, youth, adulthood',
  },
  {
    value: 'Thematic',
    label: 'Thematic (Lessons & Turning Points)',
    description: 'Organize by themes like challenges, relationships, career, personal growth',
  },
];

const isStepDisabled = computed(() => {
  return !questions.value.bookOrder;
});
</script>

<style lang="scss" scoped>
.question-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.question-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  margin: 0;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}
</style>
