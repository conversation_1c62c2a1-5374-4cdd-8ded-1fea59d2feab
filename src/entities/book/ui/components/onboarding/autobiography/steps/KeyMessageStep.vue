<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/autobiography/steps/KeyMessageStep.vue -->
<template>
  <OnboardingStep
    title="What's your key message?"
    description="Define the central message or takeaway that readers should remember from your story."
    section-title="Key Message"
    :disabled="isStepDisabled"
    next-label="Complete Autobiography"
    next-icon="check"
    @next="$emit('complete')"
    @back="$emit('back')"
  >
    <OnboardingGrid :columns="1" gap="24px">
      <div class="question-section">
        <h3 class="question-title">
          What key message or takeaway do you want readers to get from your
          book?
        </h3>
        <OnboardingInput
          v-model="questions.keyMessage"
          type="textarea"
          placeholder="Examples: Never give up on your dreams, family is everything, every setback is a setup for a comeback"
          :rows="5"
        >
          <template #append>
            <q-btn flat icon="mic" @click="openTranscription('keyMessage')">
              <q-tooltip>Transcribe</q-tooltip>
            </q-btn>
            <OnboardingMannyButton
              :loading="loading"
              :disabled="loading"
              @click="$emit('generate-key-message')"
            />
          </template>
        </OnboardingInput>
      </div>
    </OnboardingGrid>
  </OnboardingStep>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useVModels } from '@vueuse/core';
import {
  OnboardingStep,
  OnboardingInput,
  OnboardingGrid,
  OnboardingMannyButton,
} from '../../shared';

export interface KeyMessageQuestions {
  keyMessage: string;
}

const props = defineProps<{
  questions: KeyMessageQuestions;
  loading?: boolean;
}>();

const emit = defineEmits<{
  'update:questions': [value: KeyMessageQuestions];
  complete: [];
  back: [];
  'open-transcription': [field: string];
  'generate-key-message': [];
}>();

const { questions } = useVModels(props, emit);

const isStepDisabled = computed(() => {
  return !questions.value.keyMessage.trim();
});

const openTranscription = (field: string) => {
  emit('open-transcription', field);
};
</script>

<style lang="scss" scoped>
.question-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.question-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  margin: 0;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}
</style>
