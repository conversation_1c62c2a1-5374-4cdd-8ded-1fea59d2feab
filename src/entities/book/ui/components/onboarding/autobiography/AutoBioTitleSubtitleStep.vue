<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/autobiography/AutoBioTitleSubtitleStep.vue -->
<template>
  <LegacyScrollArea>
    <LegacySection
      class="flex justify-between"
      :show-header="true"
      title="Write your Autobiography Title and Subtitle"
    >
    </LegacySection>
    <q-separator />

    <LegacySection>
      <div class="q-gutter-md">
        <LegacyInput
          v-model="title"
          label="Autobiography Title"
          outlined
          placeholder="Enter your autobiography title..."
        />

        <LegacyInput
          v-model="subtitle"
          label="Autobiography Subtitle"
          outlined
          placeholder="Enter your autobiography subtitle..."
        />
      </div>

      <!-- Legacy Suggestion List -->
      <LegacySuggestionList
        :title-subtitle-pairs="titleSubtitlePairs"
        :selected-title="title"
        :selected-subtitle="subtitle"
        :loading="loading"
        @select-title="handleTitleSelect"
        @select-subtitle="handleSubtitleSelect"
        @select-pair="handlePairSelect"
        @hide="hideSuggestions"
        @regenerate="generateTitleSubtitlePairs"
      />
    </LegacySection>
  </LegacyScrollArea>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useVModel, useVModels } from '@vueuse/core';
import { useQuasar } from 'quasar';
import { composeText } from 'src/shared/api/openai';
import { confirmOverrideText, warn } from 'src/shared/lib/quasar-dialogs';
import { AutoBioGraphyMissionQuestions } from 'src/entities/book/model/types';
import {
  LegacySection,
  LegacyScrollArea,
  LegacyInput,
  LegacySuggestionList,
} from '../shared';

interface TitleSubtitlePair {
  title: string;
  subtitle: string;
}

const props = withDefaults(
  defineProps<{
    titleModelValue: string;
    subtitleModelValue: string;
    questions?: AutoBioGraphyMissionQuestions;
    updating?: boolean;
  }>(),
  {
    updating: false,
  },
);

const emit = defineEmits<{
  'update:titleModelValue': [value: string];
  'update:subtitleModelValue': [value: string];
}>();

const { titleModelValue: title, subtitleModelValue: subtitle } = useVModels(
  props,
  emit,
);
const questions = useVModel(props, 'questions', emit);
const $q = useQuasar();

// Component state
const loading = ref(false);
const showSuggestions = ref(false);
const titleSubtitlePairs = ref<TitleSubtitlePair[]>([]);
const results = ref('');

// Computed properties
const canGenerate = computed(() => {
  return (
    questions?.value?.mainReason &&
    questions?.value?.bookFocused &&
    questions?.value?.bookOrder &&
    questions?.value?.keyMessage &&
    questions?.value?.gender &&
    questions?.value?.age &&
    questions?.value?.description
  );
});

// Event handlers
const handleTitleSelect = (selectedTitle: string) => {
  title.value = selectedTitle;
};

const handleSubtitleSelect = (selectedSubtitle: string) => {
  subtitle.value = selectedSubtitle;
};

const handlePairSelect = (pair: TitleSubtitlePair) => {
  title.value = pair.title;
  subtitle.value = pair.subtitle;
};

const hideSuggestions = () => {
  showSuggestions.value = false;
};

// AI prompt for combined title-subtitle generation
const combinedPrompt = computed(() => {
  let promptStr = `You are a master book title creator with expertise in marketing, psychology, and publishing.
Your task: create 5 original, compelling book TITLE + SUBTITLE pairs for an Autobiography / Memoir book.

    <p>Main Reason: ${questions?.value?.mainReason}</p>
  <p>Book Focused: ${questions?.value?.bookFocused}</p>
  <p>Book Structure: ${questions?.value?.bookOrder}</p>
  <p>Key Message: ${questions?.value?.keyMessage}</p>
  <p>Gender: ${questions?.value?.gender}</p>
  <p>Age: ${questions?.value?.age}</p>
  <p>Description: ${questions?.value?.description}</p>`;

  promptStr += `

Requirements:
1. Titles must be EXACTLY 3 words (no exceptions).
2. Subtitles must include EXACTLY 3 benefits, formatted like:
   A Journey of Love, Loss, and Legacy
   Stories of Struggle, Survival, and Strength
   My Path Through Chaos, Courage, and Change
3. Titles should feel personal, emotional, and reflective, capturing the author's unique voice.
4. Do NOT use generic clichés like "Life Lessons Learned" or "My Incredible Journey."
5. Make them gripping, authentic, and marketable — similar to bestselling memoirs that connect through vulnerability and storytelling.
6. Each title should be on its own line, followed by its subtitle on the next line
7. Separate each pair with a blank line
8. Use plain text without numbering, bullets, or special formatting
9. Make titles provocative and curiosity-inducing
10. Make subtitles benefit-focused and compelling

Format example:
Title One
Subtitle describing benefits and promises

Title Two
Subtitle describing benefits and promises

[continue for all 5 pairs]`;

  return promptStr;
});

// Generate title-subtitle pairs
async function generateTitleSubtitlePairs() {
  if (!canGenerate.value) {
    warn($q, {
      title: 'Missing Information',
      message:
        'Manny needs your mission statement details to generate suggestions.',
    });
    return;
  }

  // Check if user wants to override existing content
  if (titleSubtitlePairs.value.length > 0) {
    const shouldOverride = await confirmOverrideText($q);
    if (!shouldOverride) {
      return;
    }
  }

  let promptRequest = combinedPrompt.value;

  // Add previous results to avoid repetition
  if (results.value) {
    promptRequest += `\n\nPlease avoid repeating or being similar to these previous results:\n${results.value}`;
  }

  loading.value = true;

  try {
    const response = await composeText(promptRequest);

    // Parse the response to extract title-subtitle pairs
    const pairs = parseTitleSubtitleResponse(response);

    if (pairs.length >= 3) {
      titleSubtitlePairs.value = pairs;
      showSuggestions.value = true;
      // Store results to avoid repetition in future calls
      results.value = `${results.value}\n${response}`;
    } else {
      throw new Error('Unexpected response format from AI');
    }
  } catch (e) {
    console.error(e);
    $q.notify({
      type: 'negative',
      message:
        'An error occurred while generating suggestions. Please try again.',
      position: 'top',
    });
  } finally {
    loading.value = false;
  }
}

// Parse AI response to extract title-subtitle pairs
function parseTitleSubtitleResponse(response: string): TitleSubtitlePair[] {
  const pairs: TitleSubtitlePair[] = [];

  // Split by double newlines to get pairs, then by single newlines within pairs
  const lines = response
    .trim()
    .split('\n')
    .filter((line) => line.trim() !== '');

  for (let i = 0; i < lines.length; i += 2) {
    if (i + 1 < lines.length) {
      const title = lines[i]
        .replace(/^\d+\.\s*/, '')
        .replace(/^"|"$/g, '')
        .trim();
      const subtitle = lines[i + 1]
        .replace(/^\d+\.\s*/, '')
        .replace(/^"|"$/g, '')
        .trim();

      if (title && subtitle) {
        pairs.push({ title, subtitle });
      }
    }
  }

  return pairs;
}
</script>
