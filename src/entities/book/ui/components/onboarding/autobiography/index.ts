// /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/autobiography/index.ts (final)
export { default as AutoBioMissionFlow } from './AutoBioMissionFlow.vue';
export { default as NewAutoBioFlow } from './NewAutoBioFlow.vue';

// Individual step components
export { default as AutoBioMainReasonStep } from './steps/AutoBioMainReasonStep.vue';
export { default as BookFocusStep } from './steps/BookFocusStep.vue';
export { default as BookOrderStep } from './steps/BookOrderStep.vue';
export { default as AutoBioTargetAudienceStep } from './steps/AutoBioTargetAudienceStep.vue';
export { default as KeyMessageStep } from './steps/KeyMessageStep.vue';
export { default as AutoBioTitleSubtitleStep } from './steps/AutoBioTitleSubtitleStep.vue';
export { default as AutoBioChaptersStep } from './steps/AutoBioChaptersStep.vue';

// Legacy components for backward compatibility
export { default as AutoBioMissionStep } from './AutoBioMissionStep.vue';
