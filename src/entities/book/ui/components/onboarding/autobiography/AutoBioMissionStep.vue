<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/autobiography/AutoBioMissionStep.vue -->
<template>
  <LegacyScrollArea>
    <LegacySection
      class="flex justify-between"
      :show-header="true"
      title="Write your Autobiography Book"
    />
    <q-separator />

    <LegacySection>
      <section>
        <LegacyInput
          v-model="mainReason"
          label="What is the main reason you want to share your story?"
          type="textarea"
          outlined
        >
          <template #append>
            <q-btn flat icon="mic" @click="openTranscription('mainReason')">
              <q-tooltip>Transcribe</q-tooltip>
            </q-btn>
          </template>
        </LegacyInput>
      </section>

      <section class="q-mt-md">
        <LegacyInput
          v-model="bookFocused"
          label="Is this book more legacy-focused, inspirational, educational, or transformational?"
          type="select"
          :options="bookFocusedOptions"
          outlined
        />
      </section>

      <section class="q-mt-md">
        <LegacyInput
          v-model="bookOrder"
          label="Do you want the order of your book to be chronological (life stages) or thematic (lessons, challenges, turning points)?"
          type="select"
          :options="bookOrderOptions"
          outlined
        />
      </section>

      <section class="q-mt-md">
        <LegacyInput
          v-model="gender"
          label="Is your target audience a specific gender?"
          type="select"
          :options="['Male', 'Female', 'Both']"
          outlined
        />
      </section>

      <section class="q-mt-md">
        <LegacyInput
          v-model="age"
          label="Is your target audience a specific age?"
          type="select"
          :options="ageOptions"
          multiple
          use-chips
          outlined
        />
      </section>

      <section class="q-mt-md">
        <LegacyInput
          v-model="description"
          label="Describe your target audience in a few words."
          type="textarea"
          outlined
        >
          <template #append>
            <q-btn flat icon="mic" @click="openTranscription('description')">
              <q-tooltip>Transcribe</q-tooltip>
            </q-btn>
          </template>
        </LegacyInput>
      </section>

      <section class="q-mt-md">
        <LegacyInput
          v-model="keyMessage"
          label="What key message or takeaway do you want readers to get from your book?"
          type="textarea"
          outlined
        >
          <template #append>
            <q-btn flat icon="mic" @click="openTranscription('keyMessage')">
              <q-tooltip>Transcribe</q-tooltip>
            </q-btn>
            <LegacyMannyButton
              :loading="loadingKeyMessage"
              :disabled="loadingKeyMessage"
              tooltip="Ask Manny"
              @click="generateKeyMessage(false)"
            />
          </template>
        </LegacyInput>
      </section>
    </LegacySection>
  </LegacyScrollArea>

  <q-dialog v-model="transcriptionDialog" persistent>
    <BookTranscriptionApp
      @completed="completedTranscript"
      @close="transcriptionDialog = false"
    />
  </q-dialog>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { useVModels, toRefs } from '@vueuse/core';
import { useQuasar } from 'quasar';
import { composeText } from 'src/shared/api/openai';
import { confirmOverrideText, warn } from 'src/shared/lib/quasar-dialogs';
import BookTranscriptionApp from '../../BookTranscriptionApp.vue';
import {
  LegacySection,
  LegacyScrollArea,
  LegacyInput,
  LegacyMannyButton,
} from '../shared';

const transcriptionDialog = ref(false);

export interface Questions {
  gender: 'Males' | 'Females' | 'Both';
  age: string[];
  keyMessage: string;
  mainReason: string;
  description: string;
  bookOrder: string;
  bookFocused: string;
}

const props = withDefaults(
  defineProps<{
    questions: Questions;
    updating?: boolean;
    isScreenBiggerMd?: boolean;
  }>(),
  {
    updating: false,
  },
);
const emit = defineEmits<{
  'update:questions': [value: Questions];
}>();
const { questions } = useVModels(props, emit);
const {
  gender,
  age,
  description,
  keyMessage,
  mainReason,
  bookOrder,
  bookFocused,
} = toRefs(questions);

const $q = useQuasar();

const ageConcat = new Intl.ListFormat('en', {
  style: 'long',
  type: 'conjunction',
});

enum Modes {
  Manual,
  Help,
}
const selectedField = ref('');
const mode = ref(Modes.Manual);
const completedTranscript = (data: any) => {
  const content = data.content;
  switch (selectedField.value) {
    case 'keyMessage':
      keyMessage.value = content;
      break;
    case 'description':
      description.value = content;
      break;
    case 'mainReason':
      mainReason.value = content;
      break;
  }
  transcriptionDialog.value = false;
};

// Helper function for transcription
const openTranscription = (field: string) => {
  selectedField.value = field;
  transcriptionDialog.value = true;
};

const ageOptions = [
  'Any Age',
  'Under 18',
  '18-25',
  '25-35',
  '35-50',
  '50-65',
  '65+',
];
const bookOrderOptions = ['Chronological', 'Thematic'];
const bookFocusedOptions = [
  'Legacy-focused',
  'Inspirational',
  'Educational',
  'Transformational',
];

const loadingKeyMessage = ref(false);
async function generateKeyMessage(invoking: boolean = false) {
  if (
    (!gender.value || age.value.length === 0 || !description.value) &&
    !invoking
  ) {
    warn($q, {
      title: 'Missing fields',
      message: 'Please fill in all the previous fields first.',
    });
    return;
  }
  let promptRequest: string = updatedPrompt.value.keyMessage;
  if (keyMessage.value && !invoking) {
    const shouldDelete = await confirmOverrideText($q);

    if (!shouldDelete) {
      return;
    }
    results.value.keyMessage =
      typeof results.value.keyMessage === 'string'
        ? results.value.keyMessage
        : await results.value.keyMessage;
  }

  if (!invoking) loadingKeyMessage.value = true;
  if (results.value.keyMessage) {
    promptRequest = `${promptRequest} Please refrain from utilizing the following results or repeating the same sentences from the following results. ${results.value.keyMessage}`;
  }
  try {
    const response = await composeText(promptRequest);
    if (!invoking) keyMessage.value = response;
    // save the results
    results.value.keyMessage = `${results.value.keyMessage} ${response}`;
  } catch (e) {
    console.error(e);
    alert('An error occurred: ' + e);
  } finally {
    loadingKeyMessage.value = false;
  }
}

const ageGroups = computed(() => ageConcat.format(age.value));

// Updating of Open AI Prompt
const results = ref({} as { [key: string]: any });

const promptKeyMessage = computed(
  () => `
  <p>Main Reason: ${questions.value.mainReason}</p>
  <p>Book Structure: ${questions.value.bookOrder}</p>
  <p>Book Focused: ${questions.value.bookFocused}</p>
  <br/>
  You are an award-winning ghostwriter with decades of experience writing bestselling autobiographies and memoirs. Your specialty is helping new authors discover the heart of their story—the message that will resonate most deeply with readers.
  <br/>
  Based on the following details, write a single, emotionally compelling key message or takeaway that readers should remember after finishing this book:
  <ul>
  <li>Main reason for writing: ${mainReason.value}</li>
  <li>Book focus: ${bookFocused.value}</li>
  <li>Book structure: ${bookOrder.value}</li>
  </ul>
  Respond with 1–2 emotionally impactful sentences. The response should feel like a back cover hook or a central theme. Do not include bullet points, labels, or any formatting—just the raw text.
`,
);

onMounted(async () => {
  if (age.value.length) {
    mode.value = Modes.Help;
  }
  if (keyMessage.value) {
    try {
      await generateKeyMessage(true);
    } catch (e) {
      console.error(e);
    }
  }
});

const updatedPrompt = computed(() => ({
  keyMessage: promptKeyMessage.value,
}));
</script>
