// /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/index.ts (updated)
export * from './shared';
export * from './nonfiction';
export * from './faithstory';
export * from './autobiography';

// Main flow components
export { default as NonFictionMissionFlow } from './nonfiction/NonFictionMissionFlow.vue';
export { default as FaithStoryMissionFlow } from './faithstory/FaithStoryMissionFlow.vue';
export { default as AutoBioMissionFlow } from './autobiography/AutoBioMissionFlow.vue';

// Legacy components for backward compatibility
export { default as MissionStep } from './nonfiction/MissionStep.vue';
export { default as FaithStoryMissionStep } from './faithstory/FaithStoryMissionStep.vue';
export { default as AutoBioMissionStep } from './autobiography/AutoBioMissionStep.vue';
