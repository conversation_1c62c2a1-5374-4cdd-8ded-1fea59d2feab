<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/shared/OnboardingChapterList.vue -->
<template>
  <div class="chapter-list">
    <div class="chapter-items">
      <draggable
        v-model="chapterItems"
        item-key="number"
        handle=".drag-handle"
        class="chapter-draggable"
      >
        <template #item="{ element: chapter, index }">
          <div class="chapter-item">
            <OnboardingInput
              v-model="chapterItems[index].title"
              :loading="loading"
              :disable="loading"
              outlined
              :label="`Chapter ${index + 1}`"
              class="chapter-input"
            >
              <template v-slot:prepend>
                <q-icon name="menu" class="drag-handle" />
              </template>

              <template v-slot:append>
                <q-btn
                  icon="delete"
                  color="negative"
                  flat
                  :disable="loading"
                  @click="$emit('delete-chapter', index)"
                >
                  <q-tooltip>Delete Chapter</q-tooltip>
                </q-btn>
              </template>
            </OnboardingInput>
          </div>
        </template>
      </draggable>
    </div>

    <div v-if="chapterItems.length === 0" class="empty-state">
      <q-icon name="auto_stories" size="4rem" color="grey-5" />
      <p class="empty-text">
        No chapters yet. Add your first chapter or let Manny generate them for
        you.
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import draggable from 'vuedraggable';
import { OnboardingInput } from '../shared';

interface ChapterItem {
  title: string;
  number: number;
  [key: string]: any;
}

interface Props {
  modelValue: ChapterItem[];
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
});

const emit = defineEmits<{
  'update:modelValue': [chapters: ChapterItem[]];
  'delete-chapter': [index: number];
}>();

const chapterItems = computed({
  get: () => props.modelValue,
  set: (value) => {
    // Renumber chapters after reordering
    const renumbered = value.map((chapter, index) => ({
      ...chapter,
      number: index + 1,
    }));
    emit('update:modelValue', renumbered);
  },
});
</script>

<style lang="scss" scoped>
.chapter-list {
  width: 100%;
}

.chapter-items {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.chapter-draggable {
  width: 100%;
}

.chapter-item {
  width: 100%;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 0.5rem;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}

.chapter-input {
  width: 100%;
}

.drag-handle {
  cursor: grab;
  color: rgba(255, 255, 255, 0.7);

  &:active {
    cursor: grabbing;
  }

  &:hover {
    color: white;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
}

.empty-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.1rem;
  margin: 1rem 0 0 0;
  max-width: 400px;
  line-height: 1.5;
}

// Sortable styles
.sortable-ghost {
  opacity: 0.5;
  background: rgba($primary, 0.2);
}

.sortable-chosen {
  background: rgba($primary, 0.1);
}

.sortable-drag {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba($primary, 0.5);
}
</style>
