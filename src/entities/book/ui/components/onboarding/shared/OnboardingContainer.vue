<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/shared/OnboardingContainer.vue -->
<template>
  <q-dialog
    v-model="showDialog"
    persistent
    maximized
    transition-show="slide-up"
    transition-hide="slide-down"
  >
    <q-card class="onboarding-card">
      <!-- Progress bar at top -->
      <div class="progress-container">
        <div
          class="progress-bar"
          :style="{ width: `${(currentStep / totalSteps) * 100}%` }"
        ></div>
      </div>

      <q-card-section class="q-pa-none">
        <div class="onboarding-content">
          <!-- Mobile header with step indicator -->
          <div class="mobile-header q-pa-md q-pb-none">
            <div class="row items-center justify-between q-gutter-sm">
              <div class="text-subtitle1 text-weight-medium">
                Step {{ currentStep }} of {{ totalSteps }}
              </div>
              <div class="text-caption">{{ currentStepTitle }}</div>
            </div>
          </div>

          <!-- Centered content area -->
          <div class="content-area">
            <div class="centered-container">
              <slot />
            </div>
          </div>
        </div>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  show: boolean;
  currentStep: number;
  totalSteps: number;
  currentStepTitle: string;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  'update:show': [value: boolean];
}>();

const showDialog = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value),
});
</script>

<style lang="scss" scoped>
// Same styling as UserOnboarding.vue
.onboarding-card {
  border-radius: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-image: url('/images/ui/body-bg.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  overflow-x: hidden;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1;
  }

  > * {
    position: relative;
    z-index: 2;
  }

  .text-caption {
    color: white !important;
  }
}

.progress-container {
  height: 4px;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 10;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, $primary 0%, lighten($primary, 15%) 100%);
  transition: width 0.3s ease;
  border-radius: 0 2px 2px 0;
  box-shadow: 0 0 8px rgba($primary, 0.5);
}

.onboarding-content {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 4px);
}

.mobile-header {
  display: flex;
  align-items: center;
  padding: 16px 20px;

  .text-subtitle1 {
    font-weight: 600;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }

  .text-caption {
    color: white;
    font-size: 0.9rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }
}

.content-area {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
  overflow-y: auto;

  @media (min-width: 768px) {
    align-items: center;
    padding: 40px;
  }
}

.centered-container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>
