<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/shared/LegacyChapterList.vue -->
<template>
  <div>
    <ul style="list-style: none" class="q-pa-none">
      <draggable v-model="chapterList" item-key="number">
        <template #item="{ element: chapter, index }" class="chapter">
          <li class="q-mb-sm">
            <q-input
              v-model="chapterList[index].title"
              :loading="loading"
              :disable="loading"
              outlined
              :label="'Chapter ' + (index + 1)"
            >
              <template v-slot:before>
                <q-icon name="menu" />
              </template>

              <template v-slot:append>
                <q-btn
                  icon="delete"
                  color="danger"
                  flat
                  :disable="loading"
                  @click="$emit('delete-chapter', index)"
                >
                  <q-tooltip>Delete Chapter</q-tooltip>
                </q-btn>
              </template>
            </q-input>
          </li>
        </template>
      </draggable>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import draggable from 'vuedraggable';

interface Chapter {
  title: string;
  number: number;
  [key: string]: any;
}

interface Props {
  modelValue: Chapter[];
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
});

const emit = defineEmits<{
  'update:modelValue': [chapters: Chapter[]];
  'delete-chapter': [index: number];
}>();

const chapterList = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});
</script>
