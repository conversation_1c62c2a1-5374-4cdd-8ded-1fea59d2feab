<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/shared/legacy/LegacyStep.vue -->
<template>
  <q-step :name="name" :title="title" :icon="icon" :done="done">
    <div class="legacy-step-content">
      <slot />
    </div>

    <!-- Always show actions, but adapt layout based on screen size -->
    <template v-if="showActions && $slots.actions">
      <!-- Desktop/Tablet: Show actions at bottom with separator -->
      <template v-if="isLargerScreen">
        <q-separator class="q-mt-md" />
        <q-card-section class="q-pa-md">
          <q-card-actions align="right" class="q-pa-none">
            <slot name="actions" />
          </q-card-actions>
        </q-card-section>
      </template>

      <!-- Mobile: Show actions in a fixed bottom bar -->
      <template v-else>
        <div class="legacy-step-mobile-actions">
          <q-separator />
          <q-card-section class="q-pa-sm">
            <q-card-actions class="q-pa-none mobile-actions">
              <slot name="actions" />
            </q-card-actions>
          </q-card-section>
        </div>
      </template>
    </template>
  </q-step>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue';
import { useQuasar } from 'quasar';

interface Props {
  name: string | number;
  title: string;
  icon?: string;
  done?: boolean;
  showActions?: boolean;
}

withDefaults(defineProps<Props>(), {
  icon: 'create_new_folder',
  done: false,
  showActions: true,
});

const $q = useQuasar();

// More reliable responsive detection
const isLargerScreen = computed(() => $q.screen.gt.sm);
</script>

<style scoped>
.legacy-step-content {
  min-height: 200px;
}

.legacy-step-mobile-actions {
  margin-top: auto;
}

.mobile-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mobile-actions :deep(.q-btn) {
  width: 100%;
  justify-content: center;
}

/* Ensure mobile actions are properly spaced */
@media (max-width: 600px) {
  .mobile-actions {
    gap: 12px;
  }

  .mobile-actions :deep(.q-btn) {
    min-height: 44px; /* Touch-friendly button height */
  }
}
</style>
