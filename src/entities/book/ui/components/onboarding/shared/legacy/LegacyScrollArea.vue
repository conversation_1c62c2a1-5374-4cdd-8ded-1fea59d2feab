<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/shared/legacy/LegacyScrollArea.vue -->
<template>
  <q-scroll-area
    :thumb-style="thumbStyle"
    :bar-style="barStyle"
    :style="scrollAreaStyle"
    class="legacy-scroll-area"
    ref="scrollAreaRef"
  >
    <div class="scroll-content" ref="scrollContentRef">
      <slot />
    </div>
  </q-scroll-area>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, nextTick } from 'vue';
import { useQuasar } from 'quasar';
import { barStyle, thumbStyle } from 'src/entities/setting';

const $q = useQuasar();
const scrollAreaRef = ref(null);
const scrollContentRef = ref(null);

// Dynamic height calculation based on actual screen size and available space
const scrollAreaStyle = computed(() => {
  const baseHeight = '100vh';
  let offsetHeight: string;

  // Calculate offset based on actual screen size and content
  // Increased offsets to account for action buttons that might be clipped
  if ($q.screen.lt.sm) {
    // Mobile: Account for header, action buttons, and extra spacing
    offsetHeight = '320px'; // Increased from 280px
  } else if ($q.screen.lt.md) {
    // Tablet
    offsetHeight = '340px'; // Increased from 300px
  } else {
    // Desktop
    offsetHeight = '360px'; // Increased from 320px
  }

  return {
    height: `calc(${baseHeight} - ${offsetHeight})`,
    minHeight: '250px', // Reduced minimum to allow more flexibility
    maxHeight: '75vh', // Slightly reduced to leave more space for actions
  };
});

// Ensure scroll area updates when content changes
const updateScrollArea = () => {
  nextTick(() => {
    if (scrollAreaRef.value) {
      scrollAreaRef.value.setScrollPosition('vertical', 0, 0);
    }
  });
};

onMounted(() => {
  updateScrollArea();

  // Listen for window resize to update scroll area
  window.addEventListener('resize', updateScrollArea);
});

onUnmounted(() => {
  window.removeEventListener('resize', updateScrollArea);
});
</script>

<style scoped>
.legacy-scroll-area {
  width: 100%;
  /* Ensure scroll area doesn't interfere with action buttons */
  position: relative;
}

.scroll-content {
  padding: 0;
  min-height: min-content;
  /* Ensure content has enough bottom space */
  padding-bottom: 2rem;
}

/* Mobile-specific adjustments */
@media (max-width: 600px) {
  .legacy-scroll-area {
    /* Ensure proper touch scrolling on mobile */
    -webkit-overflow-scrolling: touch;
  }

  .scroll-content {
    /* Add more bottom padding to prevent content from being hidden behind actions */
    padding-bottom: 3rem;
  }
}

/* Tablet adjustments */
@media (min-width: 601px) and (max-width: 1024px) {
  .scroll-content {
    padding-bottom: 2.5rem;
  }
}

/* Desktop adjustments */
@media (min-width: 1025px) {
  .scroll-content {
    padding-bottom: 2rem;
  }
}

/* Ensure scroll area works well in all orientations */
@media (orientation: landscape) and (max-height: 500px) {
  .legacy-scroll-area {
    max-height: 55vh; /* Reduced for landscape to ensure action buttons are visible */
    min-height: 180px;
  }

  .scroll-content {
    padding-bottom: 1.5rem;
  }
}

/* Handle very small screens */
@media (max-width: 400px) {
  .scroll-content {
    padding-bottom: 4rem; /* Extra space for very small screens */
  }
}

/* Ensure smooth scrolling */
.legacy-scroll-area :deep(.q-scrollarea__container) {
  scroll-behavior: smooth;
}
</style>
