<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/shared/OnboardingCard.vue -->
<template>
  <q-card
    :class="['option-card', selected ? 'selected-card' : '']"
    clickable
    @click="$emit('select')"
  >
    <q-card-section class="row items-center no-wrap">
      <div v-if="icon" class="col-auto">
        <q-avatar color="primary" text-color="white" size="42px">
          <q-icon :name="icon" size="24px" />
        </q-avatar>
      </div>
      <div class="col q-ml-md">
        <div class="text-subtitle1 text-weight-medium">
          {{ title }}
        </div>
        <div v-if="description" class="text-caption">
          {{ description }}
        </div>
      </div>
      <div class="col-auto">
        <q-radio
          v-if="selectionType === 'radio'"
          :model-value="selected"
          :val="true"
          color="primary"
        />
        <q-checkbox v-else :model-value="selected" color="primary" />
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
interface Props {
  title: string;
  description?: string;
  icon?: string;
  selected?: boolean;
  selectionType?: 'radio' | 'checkbox';
}

withDefaults(defineProps<Props>(), {
  selected: false,
  selectionType: 'radio',
});

defineEmits<{
  select: [];
}>();
</script>

<style lang="scss" scoped>
.option-card {
  width: 100%;
  max-width: 400px;
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  }

  .q-card__section {
    padding: 16px;
  }

  .text-subtitle1 {
    font-size: 1rem;
    line-height: 1.4;
  }

  .text-caption {
    color: rgba(0, 0, 0, 0.6);
    line-height: 1.4;
  }
}

.selected-card {
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  color: white;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
  }

  .text-caption {
    color: white;
  }

  .text-subtitle1 {
    color: white;
  }
}

.q-avatar {
  background: linear-gradient(135deg, $primary 0%, darken($primary, 15%) 100%);
  box-shadow: 0 4px 8px rgba($primary, 0.2);
}
</style>
