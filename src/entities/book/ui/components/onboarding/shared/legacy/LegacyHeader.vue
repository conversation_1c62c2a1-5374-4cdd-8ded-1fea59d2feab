<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/shared/LegacyHeader.vue -->
<template>
  <q-card-section class="q-pa-none">
    <q-bar class="bg-primary text-white">
      <q-icon name="img:robot.png" />

      <div>{{ title }}</div>

      <q-space />
      <div v-if="autosave === true" class="q-mr-md">
        Auto-saving <q-spinner-dots color="white" size="0.7em" />
        <q-tooltip :offset="[0, 8]">Auto save feature on</q-tooltip>
      </div>
      <div v-else class="q-mr-xs">
        <q-btn
          dense
          flat
          icon="save"
          @click="$emit('save')"
          label="Click here to save"
          color="white"
        >
          <q-tooltip class="bg-white text-primary"
            >Save your progress</q-tooltip
          >
        </q-btn>
      </div>

      <q-btn
        dense
        flat
        icon="close"
        @click="$emit('close')"
        v-if="autosave === true"
      >
        <q-tooltip class="bg-white text-primary">Close</q-tooltip>
      </q-btn>
    </q-bar>
  </q-card-section>
</template>

<script setup lang="ts">
interface Props {
  title?: string;
  autosave?: boolean;
}

withDefaults(defineProps<Props>(), {
  title: 'Book Foundations',
  autosave: true,
});

defineEmits<{
  save: [];
  close: [];
}>();
</script>
