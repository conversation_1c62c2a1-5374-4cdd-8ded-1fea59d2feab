<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/shared/LegacySection.vue -->
<template>
  <q-card-section :class="sectionClass">
    <div v-if="showHeader" class="flex justify-between q-gutter-x-md">
      <div class="text-h7">{{ title }}</div>
      <slot name="header-actions" />
    </div>
    <slot />
  </q-card-section>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  title?: string;
  showHeader?: boolean;
  class?: string;
}

const props = withDefaults(defineProps<Props>(), {
  showHeader: false,
});

const sectionClass = computed(() => props.class || '');
</script>
