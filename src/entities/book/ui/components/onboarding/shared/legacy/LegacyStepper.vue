<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/shared/LegacyStepper.vue -->
<template>
  <q-stepper
    v-model="currentStep"
    header-nav
    animated
    class="new-book-dialog"
    :contracted="$q.screen.lt.sm"
  >
    <slot />
  </q-stepper>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  currentStep: number;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  'update:currentStep': [step: number];
}>();

const currentStep = computed({
  get: () => props.currentStep,
  set: (value) => emit('update:currentStep', value),
});
</script>

<style>
.new-book-dialog {
  width: 100%;
  font-size: 1.125rem;
  box-shadow: none;
}
.new-book-dialog .q-stepper__title {
  font-size: 1.25rem;
  font-weight: 500;
}
.q-stepper--horizontal .q-stepper__step-inner {
  padding: 0;
}
</style>
