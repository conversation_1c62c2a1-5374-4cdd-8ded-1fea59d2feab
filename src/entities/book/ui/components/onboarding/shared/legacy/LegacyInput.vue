<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/shared/LegacyInput.vue -->
<template>
  <q-input
    v-if="type !== 'select'"
    v-model="inputValue"
    :outlined="outlined"
    :label="label"
    :placeholder="placeholder"
    :disable="disabled"
    :loading="loading"
    :type="inputType"
    :autogrow="type === 'textarea'"
    :rows="rows"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <template v-slot:prepend v-if="prependIcon">
      <q-icon :name="prependIcon" />
    </template>
    <template v-slot:append v-if="$slots.append">
      <slot name="append" />
    </template>
  </q-input>

  <q-select
    v-else
    v-model="inputValue"
    :outlined="outlined"
    :label="label"
    :options="options"
    :disable="disabled"
    :loading="loading"
    :multiple="multiple"
    :use-chips="useChips"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <template v-slot:prepend v-if="prependIcon">
      <q-icon :name="prependIcon" />
    </template>
    <template v-slot:append v-if="$slots.append">
      <slot name="append" />
    </template>
  </q-select>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  modelValue: string | string[];
  label?: string;
  placeholder?: string;
  disabled?: boolean;
  loading?: boolean;
  outlined?: boolean;
  type?: string;
  rows?: number;
  prependIcon?: string;
  options?: string[] | any[];
  multiple?: boolean;
  useChips?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  outlined: true,
  type: 'text',
  multiple: false,
  useChips: false,
});

const inputValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const inputType = computed(() => {
  if (props.type === 'textarea') return 'textarea';
  return props.type || 'text';
});

const emit = defineEmits<{
  'update:modelValue': [value: string | string[]];
}>();
</script>
