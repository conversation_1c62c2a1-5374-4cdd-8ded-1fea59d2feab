// /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/shared/index.ts (updated)
export { default as OnboardingContainer } from './OnboardingContainer.vue';
export { default as OnboardingStep } from './OnboardingStep.vue';
export { default as OnboardingCard } from './OnboardingCard.vue';
export { default as OnboardingInput } from './OnboardingInput.vue';
export { default as OnboardingGrid } from './OnboardingGrid.vue';
export { default as OnboardingMannyButton } from './OnboardingMannyButton.vue';
export { default as OnboardingTitleSubtitle } from './OnboardingTitleSubtitle.vue';
export { default as OnboardingTitleSuggestionList } from './OnboardingTitleSuggestionList.vue';
export { default as OnboardingChapters } from './OnboardingChapters.vue';
export { default as OnboardingChapterList } from './OnboardingChapterList.vue';

// Shared step components
export { default as GenderStep } from './steps/GenderStep.vue';
export { default as AgeStep } from './steps/AgeStep.vue';
export { default as DescriptionStep } from './steps/DescriptionStep.vue';

// Legacy components (for backward compatibility)
export { default as LegacyChapterList } from './legacy/LegacyChapterList.vue';
export { default as LegacyHeader } from './legacy/LegacyHeader.vue';
export { default as LegacyInput } from './legacy/LegacyInput.vue';
export { default as LegacyMannyButton } from './legacy/LegacyMannyButton.vue';
export { default as LegacyScrollArea } from './legacy/LegacyScrollArea.vue';
export { default as LegacySection } from './legacy/LegacySection.vue';
export { default as LegacyStep } from './legacy/LegacyStep.vue';
export { default as LegacyStepper } from './legacy/LegacyStepper.vue';
export { default as LegacySuggestionList } from './legacy/LegacySuggestionList.vue';
// Component types for better TypeScript support
export interface OnboardingStep {
  id: string;
  label: string;
}

export interface OnboardingOption {
  label: string;
  value: string;
  description?: string;
  icon?: string;
}

export interface OnboardingSuggestion {
  label: string;
  value: string;
  description?: string;
}

export interface OnboardingSuggestionColumn {
  key: string;
  title: string;
  icon?: string;
  suggestions: OnboardingSuggestion[];
}
