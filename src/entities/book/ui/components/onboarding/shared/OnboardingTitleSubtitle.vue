<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/shared/OnboardingTitleSubtitle.vue -->
<template>
  <OnboardingStep
    :title="stepTitle"
    :description="stepDescription"
    section-title="Title & Subtitle"
    :disabled="isStepDisabled"
    @next="$emit('next')"
    @back="$emit('back')"
  >
    <OnboardingGrid :columns="1" gap="24px">
      <!-- Manual Title Input -->
      <div class="question-section">
        <h3 class="question-title">{{ titleLabel }}</h3>
        <OnboardingInput
          v-model="title"
          :label="titleLabel"
          outlined
          :placeholder="titlePlaceholder"
        />
      </div>

      <!-- Manual Subtitle Input -->
      <div class="question-section">
        <h3 class="question-title">{{ subtitleLabel }}</h3>
        <OnboardingInput
          v-model="subtitle"
          :label="subtitleLabel"
          outlined
          :placeholder="subtitlePlaceholder"
        />
      </div>

      <!-- AI Generation Section -->
      <div class="suggestion-section" v-if="canGenerate">
        <OnboardingTitleSuggestionList
          :title-subtitle-pairs="titleSubtitlePairs"
          :selected-title="title"
          :selected-subtitle="subtitle"
          :loading="loading"
          @select-title="handleTitleSelect"
          @select-subtitle="handleSubtitleSelect"
          @select-pair="handlePairSelect"
          @hide="hideSuggestions"
          @regenerate="generateTitleSubtitlePairs"
        />
      </div>
    </OnboardingGrid>
  </OnboardingStep>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useVModel } from '@vueuse/core';
import { useQuasar } from 'quasar';
import { composeText } from 'src/shared/api/openai';
import { confirmOverrideText, warn } from 'src/shared/lib/quasar-dialogs';
import { OnboardingStep, OnboardingInput, OnboardingGrid } from '../shared';
import OnboardingTitleSuggestionList from './OnboardingTitleSuggestionList.vue';

interface TitleSubtitlePair {
  title: string;
  subtitle: string;
}

interface Props {
  titleModelValue: string;
  subtitleModelValue: string;
  questions?: any;
  bookType?: string;
  stepTitle?: string;
  stepDescription?: string;
  titleLabel?: string;
  subtitleLabel?: string;
  titlePlaceholder?: string;
  subtitlePlaceholder?: string;
  aiPrompt?: string;
}

const props = withDefaults(defineProps<Props>(), {
  bookType: 'book',
  stepTitle: 'Create Your Title & Subtitle',
  stepDescription:
    'Craft compelling titles that capture your story and attract readers.',
  titleLabel: 'Book Title',
  subtitleLabel: 'Book Subtitle',
  titlePlaceholder: 'Enter your book title...',
  subtitlePlaceholder: 'Enter your book subtitle...',
});

const emit = defineEmits<{
  'update:titleModelValue': [value: string];
  'update:subtitleModelValue': [value: string];
  next: [];
  back: [];
}>();

const title = useVModel(props, 'titleModelValue', emit);
const subtitle = useVModel(props, 'subtitleModelValue', emit);

const $q = useQuasar();

// Component state
const loading = ref(false);
const showSuggestions = ref(false);
const titleSubtitlePairs = ref<TitleSubtitlePair[]>([]);
const results = ref('');

// Computed properties
const isStepDisabled = computed(() => {
  return !title.value.trim() || !subtitle.value.trim();
});

const canGenerate = computed(() => {
  return props.questions && Object.keys(props.questions).length > 0;
});

// Event handlers
const handleTitleSelect = (selectedTitle: string) => {
  title.value = selectedTitle;
};

const handleSubtitleSelect = (selectedSubtitle: string) => {
  subtitle.value = selectedSubtitle;
};

const handlePairSelect = (pair: TitleSubtitlePair) => {
  title.value = pair.title;
  subtitle.value = pair.subtitle;
};

const hideSuggestions = () => {
  showSuggestions.value = false;
};

// Default AI prompt for title-subtitle generation
const defaultPrompt = computed(() => {
  if (!props.questions) return '';

  let promptStr = `You are a master book title creator with expertise in marketing, psychology, and publishing.
Your task: create 5 original, compelling book TITLE + SUBTITLE pairs for a ${props.bookType} book.

Requirements:
1. Titles must be EXACTLY 3 words (no exceptions).
2. Subtitles must include EXACTLY 3 benefits, formatted like:
   One Woman's Journey Through Doubt, Renewal, and Hope
   Finding Strength in Trials, Grace in Chaos, and Purpose in Faith
   A Testimony of Pain, Perseverance, and Redemption
3. Titles should sound personal, story-driven, and emotionally authentic — avoid "guidebook" or textbook formulas.
4. Do NOT use generic clichés like "Unlock Purpose" or "Find Inner Peace."
5. Make them curiosity-provoking, heartfelt, and relatable — similar to bestselling memoirs and personal stories.
6. Each title should be on its own line, followed by its subtitle on the next line
7. Separate each pair with a blank line
8. Use plain text without numbering, bullets, or special formatting
9. Make titles provocative and curiosity-inducing
10. Make subtitles benefit-focused and compelling

Format example:
Title One
Subtitle describing benefits and promises

Title Two
Subtitle describing benefits and promises

[continue for all 5 pairs]`;

  return promptStr;
});

// Generate title-subtitle pairs
async function generateTitleSubtitlePairs() {
  if (!canGenerate.value) {
    warn($q, {
      title: 'Missing Information',
      message: 'Please complete the previous steps to generate suggestions.',
    });
    return;
  }

  // Check if user wants to override existing content
  if (titleSubtitlePairs.value.length > 0) {
    const shouldOverride = await confirmOverrideText($q);
    if (!shouldOverride) {
      return;
    }
  }

  let promptRequest = props.aiPrompt || defaultPrompt.value;

  // Add previous results to avoid repetition
  if (results.value) {
    promptRequest += `\n\nPlease avoid repeating or being similar to these previous results:\n${results.value}`;
  }

  loading.value = true;

  try {
    const response = await composeText(promptRequest);

    // Parse the response to extract title-subtitle pairs
    const pairs = parseTitleSubtitleResponse(response);

    if (pairs.length >= 3) {
      titleSubtitlePairs.value = pairs;
      showSuggestions.value = true;
      // Store results to avoid repetition in future calls
      results.value = `${results.value}\n${response}`;
    } else {
      throw new Error('Unexpected response format from AI');
    }
  } catch (e) {
    console.error(e);
    $q.notify({
      type: 'negative',
      message:
        'An error occurred while generating suggestions. Please try again.',
      position: 'top',
    });
  } finally {
    loading.value = false;
  }
}

// Parse AI response to extract title-subtitle pairs
function parseTitleSubtitleResponse(response: string): TitleSubtitlePair[] {
  const pairs: TitleSubtitlePair[] = [];

  // Split by double newlines to get pairs, then by single newlines within pairs
  const lines = response
    .trim()
    .split('\n')
    .filter((line) => line.trim() !== '');

  for (let i = 0; i < lines.length; i += 2) {
    if (i + 1 < lines.length) {
      const title = lines[i]
        .replace(/^\d+\.\s*/, '')
        .replace(/^"|"$/g, '')
        .trim();
      const subtitle = lines[i + 1]
        .replace(/^\d+\.\s*/, '')
        .replace(/^"|"$/g, '')
        .trim();

      if (title && subtitle) {
        pairs.push({ title, subtitle });
      }
    }
  }

  return pairs;
}
</script>

<style lang="scss" scoped>
.question-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.question-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  margin: 0;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.suggestion-section {
  width: 100%;
  margin-top: 2rem;
}
</style>
