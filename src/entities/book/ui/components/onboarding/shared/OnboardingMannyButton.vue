<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/shared/OnboardingMannyButton.vue -->
<template>
  <q-btn
    flat
    :icon="icon"
    :loading="loading"
    :disable="disabled"
    @click="$emit('click')"
    class="manny-btn"
  >
    <q-tooltip v-if="tooltip">{{ tooltip }}</q-tooltip>
  </q-btn>
</template>

<script setup lang="ts">
interface Props {
  loading?: boolean;
  disabled?: boolean;
  icon?: string;
  tooltip?: string;
}

withDefaults(defineProps<Props>(), {
  loading: false,
  disabled: false,
  icon: 'img:robot.png',
  tooltip: 'Ask Manny',
});

defineEmits<{
  click: [];
}>();
</script>

<style lang="scss" scoped></style>
