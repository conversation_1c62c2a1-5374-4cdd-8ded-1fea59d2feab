<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/shared/OnboardingTitleSuggestionList.vue -->
<template>
  <div class="title-suggestions">
    <div class="suggestion-actions q-mt-md flex justify-center">
      <q-btn
        icon="img:robot.png"
        color="primary"
        label="Generate Title & Subtitle Ideas"
        @click="$emit('regenerate')"
        :loading="loading"
        size="md"
      />
    </div>

    <div class="legacy-suggestions" v-if="titleSubtitlePairs.length > 0">
      <div class="text-h7 q-mb-md">AI Generated Suggestions:</div>

      <div class="suggestions-grid">
        <!-- Title Column -->
        <div class="suggestion-column">
          <div class="column-header">
            <div class="column-title">
              <q-icon name="title" class="q-mr-sm" />
              Title Options
            </div>
          </div>
          <div class="suggestion-items">
            <div
              v-for="(pair, index) in titleSubtitlePairs"
              :key="`title-${index}`"
              class="suggestion-item"
              :class="{ selected: selectedTitle === pair.title }"
              @click="selectTitle(pair.title)"
            >
              <div class="suggestion-text">
                {{ pair.title }}
              </div>
              <q-icon
                v-if="selectedTitle === pair.title"
                name="check_circle"
                color="positive"
                size="sm"
              />
            </div>
          </div>
        </div>

        <!-- Subtitle Column -->
        <div class="suggestion-column">
          <div class="column-header">
            <div class="column-title">
              <q-icon name="subtitles" class="q-mr-sm" />
              Subtitle Options
            </div>
          </div>
          <div class="suggestion-items">
            <div
              v-for="(pair, index) in titleSubtitlePairs"
              :key="`subtitle-${index}`"
              class="suggestion-item"
              :class="{ selected: selectedSubtitle === pair.subtitle }"
              @click="selectSubtitle(pair.subtitle)"
            >
              <div class="suggestion-text">
                {{ pair.subtitle }}
              </div>
              <q-icon
                v-if="selectedSubtitle === pair.subtitle"
                name="check_circle"
                color="positive"
                size="sm"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Pair Selection -->
      <div class="quick-pairs q-mt-md" v-if="titleSubtitlePairs.length > 0">
        <div class="text-subtitle1 q-mb-sm">Quick Pair Selection:</div>
        <div class="pair-chips">
          <q-chip
            v-for="(pair, index) in titleSubtitlePairs"
            :key="`pair-${index}`"
            :selected="isSelectedPair(pair)"
            clickable
            @click="selectPair(pair)"
            :color="isSelectedPair(pair) ? 'primary' : 'grey-3'"
            :text-color="isSelectedPair(pair) ? 'white' : 'dark'"
            size="md"
          >
            <div class="pair-chip-content">
              <div class="pair-chip-title">{{ pair.title }}</div>
              <div class="pair-chip-subtitle">{{ pair.subtitle }}</div>
            </div>
            <q-icon
              v-if="isSelectedPair(pair)"
              name="check"
              size="xs"
              class="q-ml-sm"
            />
          </q-chip>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface TitleSubtitlePair {
  title: string;
  subtitle: string;
}

interface Props {
  titleSubtitlePairs: TitleSubtitlePair[];
  selectedTitle?: string;
  selectedSubtitle?: string;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  selectedTitle: '',
  selectedSubtitle: '',
  loading: false,
});

const emit = defineEmits<{
  'select-title': [title: string];
  'select-subtitle': [subtitle: string];
  'select-pair': [pair: TitleSubtitlePair];
  hide: [];
  regenerate: [];
}>();

const isSelectedPair = (pair: TitleSubtitlePair) => {
  return (
    props.selectedTitle === pair.title &&
    props.selectedSubtitle === pair.subtitle
  );
};

const selectTitle = (title: string) => {
  emit('select-title', title);
};

const selectSubtitle = (subtitle: string) => {
  emit('select-subtitle', subtitle);
};

const selectPair = (pair: TitleSubtitlePair) => {
  emit('select-pair', pair);
};
</script>

<style lang="scss" scoped>
.title-suggestions {
  width: 100%;
}

.legacy-suggestions {
  margin-top: 2rem;

  .text-h7 {
    color: white;
    text-align: center;
    font-weight: 600;
  }
}

.suggestions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-top: 1rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

.suggestion-column {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
  backdrop-filter: blur(10px);
}

.column-header {
  margin-bottom: 1rem;
}

.column-title {
  color: white;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.suggestion-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.suggestion-item {
  padding: 0.75rem;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
  }

  &.selected {
    background: rgba($primary, 0.3);
    border: 1px solid rgba($primary, 0.6);
  }
}

.suggestion-text {
  color: white;
  font-size: 0.9rem;
  line-height: 1.4;
}

.quick-pairs {
  .text-subtitle1 {
    color: white;
    text-align: center;
  }
}

.pair-chips {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: center;
}

.pair-chip-content {
  text-align: center;
}

.pair-chip-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.pair-chip-subtitle {
  font-size: 0.8rem;
  opacity: 0.9;
}
</style>
