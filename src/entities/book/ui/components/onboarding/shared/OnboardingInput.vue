<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/shared/OnboardingInput.vue -->
<template>
  <q-input
    v-if="type !== 'select'"
    v-model="inputValue"
    :outlined="outlined"
    :label="label"
    :placeholder="placeholder"
    :disable="disabled"
    :loading="loading"
    :type="inputType"
    :autogrow="type === 'textarea'"
    :rows="rows"
    :rules="rules"
    class="onboarding-input"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <template v-slot:prepend v-if="prependIcon">
      <q-icon :name="prependIcon" />
    </template>
    <template v-slot:append v-if="$slots.append">
      <slot name="append" />
    </template>
  </q-input>

  <q-select
    v-else
    v-model="inputValue"
    :outlined="outlined"
    :label="label"
    :options="options"
    :disable="disabled"
    :loading="loading"
    :multiple="multiple"
    :use-chips="useChips"
    :rules="rules"
    class="onboarding-input"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <template v-slot:prepend v-if="prependIcon">
      <q-icon :name="prependIcon" />
    </template>
    <template v-slot:append v-if="$slots.append">
      <slot name="append" />
    </template>
  </q-select>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  modelValue: string | string[];
  label?: string;
  placeholder?: string;
  disabled?: boolean;
  loading?: boolean;
  outlined?: boolean;
  type?: string;
  rows?: number;
  prependIcon?: string;
  options?: string[] | any[];
  multiple?: boolean;
  useChips?: boolean;
  rules?: ((val: any) => boolean | string)[];
}

const props = withDefaults(defineProps<Props>(), {
  outlined: true,
  type: 'text',
  multiple: false,
  useChips: false,
});

const inputValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const inputType = computed(() => {
  if (props.type === 'textarea') return 'textarea';
  return props.type || 'text';
});

const emit = defineEmits<{
  'update:modelValue': [value: string | string[]];
}>();
</script>

<style lang="scss" scoped>
.onboarding-input {
  :deep(.q-field--outlined .q-field__control),
  :deep(.q-field__control) {
    border-radius: 8px;
    border: 1.5px solid var(--theme-border-light);
    background-color: var(--theme-background-primary);
  }

  :deep(.q-field--outlined .q-field__control:hover),
  :deep(.q-field__control:hover) {
    background: rgba(255, 255, 255, 1);
  }

  :deep(.q-field__label) {
    color: rgba(0, 0, 0, 0.8);
  }

  :deep(.q-field__messages) {
    color: white;
  }
}
</style>
