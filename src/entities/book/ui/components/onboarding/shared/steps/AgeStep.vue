<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/shared/steps/AgeStep.vue -->
<template>
  <OnboardingStep
    title="What age group are you targeting?"
    description="Different age groups have different interests, challenges, and communication preferences."
    section-title="Target Age"
    :disabled="isStepDisabled"
    @next="$emit('next')"
    @back="$emit('back')"
  >
    <OnboardingGrid :columns="1" gap="16px">
      <div class="question-section">
        <h3 class="question-title">
          Select the age groups for your ideal readers
        </h3>
        <OnboardingGrid :columns="2" gap="12px">
          <OnboardingCard
            v-for="age in ageOptions"
            :key="age.value"
            :title="age.label"
            :description="age.description"
            :selected="questions.age.includes(age.value)"
            selection-type="checkbox"
            @select="toggleAge(age.value)"
          />
        </OnboardingGrid>
      </div>
    </OnboardingGrid>
  </OnboardingStep>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useVModels } from '@vueuse/core';
import { OnboardingStep, OnboardingCard, OnboardingGrid } from '../../shared';

export interface AgeQuestions {
  age: string[];
}

const props = defineProps<{
  questions: AgeQuestions;
}>();

const emit = defineEmits<{
  'update:questions': [value: AgeQuestions];
  next: [];
  back: [];
}>();

const { questions } = useVModels(props, emit);

const ageOptions = [
  {
    value: 'Any Age',
    label: 'Any Age',
    description: 'All age groups welcome',
  },
  {
    value: 'Under 18',
    label: 'Under 18',
    description: 'Teens and younger readers',
  },
  {
    value: '18-25',
    label: '18-25',
    description: 'Young adults starting their journey',
  },
  {
    value: '25-35',
    label: '25-35',
    description: 'Young professionals and new parents',
  },
  {
    value: '35-50',
    label: '35-50',
    description: 'Established professionals and parents',
  },
  {
    value: '50-65',
    label: '50-65',
    description: 'Experienced adults and pre-retirees',
  },
  {
    value: '65+',
    label: '65+',
    description: 'Seniors and retirees',
  },
];

const isStepDisabled = computed(() => {
  return questions.value.age.length === 0;
});

const toggleAge = (age: string) => {
  const index = questions.value.age.indexOf(age);
  if (index > -1) {
    questions.value.age.splice(index, 1);
  } else {
    questions.value.age.push(age);
  }
};
</script>

<style lang="scss" scoped>
.question-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.question-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  margin: 0;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}
</style>
