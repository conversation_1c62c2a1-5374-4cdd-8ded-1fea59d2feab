<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/shared/steps/GenderStep.vue -->
<template>
  <OnboardingStep
    title="Who is your ideal reader?"
    description="Understanding your target audience helps create content that truly resonates."
    section-title="Target Gender"
    :disabled="isStepDisabled"
    @next="$emit('next')"
    @back="$emit('back')"
  >
    <OnboardingGrid :columns="1" gap="16px">
      <div class="question-section">
        <h3 class="question-title">
          Is your target audience a specific gender?
        </h3>
        <OnboardingGrid :columns="1" gap="12px">
          <OnboardingCard
            v-for="option in genderOptions"
            :key="option.value"
            :title="option.label"
            :description="option.description"
            :selected="questions.gender === option.value"
            @select="questions.gender = option.value"
          />
        </OnboardingGrid>
      </div>
    </OnboardingGrid>
  </OnboardingStep>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useVModels } from '@vueuse/core';
import { OnboardingStep, OnboardingCard, OnboardingGrid } from '../../shared';

export interface GenderQuestions {
  gender: 'Male' | 'Female' | 'Both';
}

const props = defineProps<{
  questions: GenderQuestions;
}>();

const emit = defineEmits<{
  'update:questions': [value: GenderQuestions];
  next: [];
  back: [];
}>();

const { questions } = useVModels(props, emit);

const genderOptions = [
  {
    value: 'Male',
    label: 'Male',
    description: 'Targeting primarily male readers',
  },
  {
    value: 'Female',
    label: 'Female',
    description: 'Targeting primarily female readers',
  },
  {
    value: 'Both',
    label: 'Both',
    description: 'Targeting readers of all genders',
  },
];

const isStepDisabled = computed(() => {
  return !questions.value.gender;
});
</script>

<style lang="scss" scoped>
.question-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.question-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  margin: 0;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}
</style>
