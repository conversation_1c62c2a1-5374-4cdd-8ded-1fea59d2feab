<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/shared/OnboardingStep.vue -->
<template>
  <div class="onboarding-step">
    <!-- Step header -->
    <div class="step-header q-mb-lg">
      <h1 class="step-title">{{ title }}</h1>
      <p v-if="description" class="step-description">{{ description }}</p>
    </div>

    <!-- Form content -->
    <div class="form-container">
      <q-card flat class="form-card bg-transparent">
        <q-card-section>
          <h2 v-if="sectionTitle" class="form-section-title">
            {{ sectionTitle }}
          </h2>
          <div class="form-content">
            <slot />
          </div>
        </q-card-section>
      </q-card>
    </div>

    <!-- Navigation buttons -->
    <div class="navigation-buttons">
      <div class="button-group">
        <q-btn
          v-if="showBack"
          flat
          color="white"
          icon="arrow_back"
          :label="backLabel"
          @click="$emit('back')"
          class="nav-btn"
        />

        <q-btn
          v-if="showNext"
          unelevated
          color="primary"
          :label="nextLabel"
          :icon-right="nextIcon"
          @click="$emit('next')"
          :disable="disabled"
          :loading="loading"
          class="nav-btn primary-btn"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title: string;
  description?: string;
  sectionTitle?: string;
  showBack?: boolean;
  showNext?: boolean;
  backLabel?: string;
  nextLabel?: string;
  nextIcon?: string;
  disabled?: boolean;
  loading?: boolean;
}

withDefaults(defineProps<Props>(), {
  showBack: true,
  showNext: true,
  backLabel: 'Back',
  nextLabel: 'Continue',
  nextIcon: 'arrow_forward',
  disabled: false,
  loading: false,
});

defineEmits<{
  back: [];
  next: [];
}>();
</script>

<style lang="scss" scoped>
.step-header {
  text-align: center;
  margin-bottom: 32px;
  width: 100%;

  @media (min-width: 768px) {
    margin-bottom: 48px;
  }
}

.step-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: white;
  margin: 0 0 16px 0;
  line-height: 1.3;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);

  @media (min-width: 768px) {
    font-size: 2rem;
    margin-bottom: 20px;
  }
}

.step-description {
  font-size: 1rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);

  @media (min-width: 768px) {
    font-size: 1.1rem;
    line-height: 1.7;
  }
}

.form-container {
  width: 100%;
  display: flex;
  justify-content: center;
}

.form-card {
  width: 100%;

  .q-card__section {
    padding: 24px;

    @media (min-width: 768px) {
      padding: 32px;
    }
  }
}

.form-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.form-section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
  margin: 0 0 24px 0;
  line-height: 1.4;
  text-align: center;
  width: 100%;

  @media (min-width: 768px) {
    font-size: 1.375rem;
    margin-bottom: 28px;
  }
}

.navigation-buttons {
  padding: 24px 20px;
  display: flex;
  justify-content: center;
  width: 100%;

  @media (min-width: 768px) {
    padding: 32px 40px;
  }
}

.button-group {
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: center;

  @media (max-width: 600px) {
    flex-direction: column;
    gap: 12px;
    width: 100%;
  }
}

.nav-btn {
  min-width: 120px;
  height: 44px;
  font-weight: 600;
  border-radius: 8px;

  @media (max-width: 600px) {
    width: 100%;
    max-width: 280px;
  }

  &.primary-btn {
    background: linear-gradient(
      90deg,
      $primary 0%,
      lighten($primary, 10%) 100%
    );
    box-shadow: 0 4px 12px rgba($primary, 0.3);
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba($primary, 0.4);
    }
  }

  &:not(.primary-btn) {
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.5);
    }
  }
}
</style>
