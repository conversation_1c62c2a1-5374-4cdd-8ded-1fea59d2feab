<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/shared/OnboardingChapters.vue -->
<template>
  <OnboardingStep
    :title="stepTitle"
    :description="stepDescription"
    section-title="Chapter Outline"
    :disabled="false"
    :next-label="nextLabel"
    :next-icon="nextIcon"
    @next="$emit('complete')"
    @back="$emit('back')"
  >
    <OnboardingGrid :columns="1" gap="24px">
      <!-- Header Actions -->
      <div class="chapter-actions">
        <div class="action-buttons">
          <q-btn
            color="primary"
            outline
            :disable="loading"
            @click="addNewChapter"
            label="Add Chapter"
            icon="add"
          />
          <q-btn
            @click="generateChapters"
            :disable="loading"
            :loading="loading"
            color="primary"
            icon="img:robot.png"
            label="Ask Manny"
          />
        </div>
      </div>

      <!-- Chapter List -->
      <div class="chapter-list-container">
        <OnboardingChapterList
          v-model="chapterList"
          :loading="loading"
          @delete-chapter="deleteChapter"
        />
      </div>
    </OnboardingGrid>
  </OnboardingStep>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useVModel } from '@vueuse/core';
import { useQuasar } from 'quasar';
import {
  confirmOverrideText,
  showError,
  warn,
} from 'src/shared/lib/quasar-dialogs';
import { composeText } from 'src/shared/api/openai';
import { OnboardingStep, OnboardingGrid } from '../shared';
import OnboardingChapterList from './OnboardingChapterList.vue';

interface ChapterOutline {
  title: string;
  outline: string;
  wordCount: number;
  number: number;
  isSection: boolean;
}

interface Props {
  modelValue: ChapterOutline[];
  title: string;
  subtitle: string;
  questions?: any;
  bookType?: string;
  stepTitle?: string;
  stepDescription?: string;
  nextLabel?: string;
  nextIcon?: string;
  aiPrompt?: string;
}

const props = withDefaults(defineProps<Props>(), {
  bookType: 'book',
  stepTitle: 'Create Your Chapter Outline',
  stepDescription:
    'Organize your story into compelling chapters that guide readers through your journey.',
  nextLabel: 'Complete Setup',
  nextIcon: 'check',
});

const emit = defineEmits<{
  'update:modelValue': [value: ChapterOutline[]];
  complete: [];
  back: [];
}>();

const chapterList = useVModel(props, 'modelValue', emit);

const $q = useQuasar();
const loading = ref(false);
const results = ref('');

const addNewChapter = () => {
  chapterList.value.push({
    title: '',
    outline: '',
    wordCount: 0,
    number: chapterList.value.length + 1,
    isSection: false,
  });
};

const deleteChapter = async (index: number) => {
  const shouldDelete = await confirmOverrideText($q, {
    message: `Are you sure you want to delete this chapter?`,
  });
  if (!shouldDelete) {
    return;
  }
  chapterList.value.splice(index, 1);

  // Renumber remaining chapters
  chapterList.value.forEach((chapter, idx) => {
    chapter.number = idx + 1;
  });
};

// Default AI prompt for chapter generation
const defaultPrompt = computed(() => {
  if (!props.questions) return '';

  return `Using the book title and subtitle provided below, generate 9 chapter title outlines using the following criteria:
  <ol>
    <li>Provide a numbered list. For example: "1. Chasing Shadows in the Rain"</li>
    <li>Each chapter title should be 4-6 words, based on the emotional tone, key events, or personal themes suggested by the title, subtitle, and the author's answers.</li>
    <li>The chapter titles should loosely follow the order the author chose.</li>
    <li>The chapter titles should reflect the author's story and purpose.</li>
    <li>Use vivid, compelling, and memorable language that captures emotion, growth, or pivotal moments.</li>
    <li>Avoid using Markdown or HTML formatting in your response.</li>
  </ol>
  <br />
  The provided information is as follows:
  <ul>
    <li>Book title: ${props.title}</li>
    <li>Book subtitle: ${props.subtitle}</li>
  </ul>`;
});

async function generateChapters() {
  const { title, subtitle } = props;
  if (!title || !subtitle) {
    warn($q, {
      title: 'Missing information',
      message: 'You must first fill the title and subtitle',
    });
    return;
  }

  let promptRequest = props.aiPrompt || defaultPrompt.value;

  if (chapterList.value.length) {
    const shouldDelete = await confirmOverrideText($q, {
      message:
        'This action will delete all chapters and their content that you have written. Are you sure you want to proceed?',
    });

    if (!shouldDelete) {
      return;
    }
  }

  loading.value = true;

  if (results.value) {
    promptRequest = `${promptRequest} Please refrain from utilizing the following results or repeating the same sentences from the following results. ${results.value}`;
  }

  try {
    const response = await composeText(promptRequest);

    const newChapters: ChapterOutline[] = response
      .trim()
      .split('\n')
      .filter((line) => /^\d+\./.test(line))
      .map((line) => line.replace(/^\d+\.\s*/, ''))
      .map((line) => line.replace(/^"|"$/g, ''))
      .filter((line) => line.length > 0)
      .map((chapter, idx) => ({
        title: chapter,
        wordCount: 0,
        isSection: false,
        number: idx + 1,
        outline: '',
      }));

    chapterList.value = newChapters;

    // Save the results
    results.value = `${results.value} ${response}`;
  } catch (e) {
    console.error(e);
    showError($q, (e as Error).message);
  } finally {
    loading.value = false;
  }
}
</script>

<style lang="scss" scoped>
.chapter-actions {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;

  @media (max-width: 600px) {
    flex-direction: column;
    width: 100%;
    max-width: 300px;

    .q-btn {
      width: 100%;
    }
  }
}

.chapter-list-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}
</style>
