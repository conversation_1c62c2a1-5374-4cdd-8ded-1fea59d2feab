<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/shared/OnboardingGrid.vue -->
<template>
  <div class="onboarding-grid" :class="gridClass">
    <slot />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  columns?: number;
  gap?: string;
  centered?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  columns: 1,
  gap: '16px',
  centered: true,
});

const gridClass = computed(() => ({
  'grid-centered': props.centered,
  [`grid-cols-${props.columns}`]: true,
}));
</script>

<style lang="scss" scoped>
.onboarding-grid {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: v-bind(gap);

  &.grid-centered {
    align-items: center;
  }

  &.grid-cols-2 {
    @media (min-width: 600px) {
      display: grid;
      grid-template-columns: 1fr 1fr;
      align-items: start;
    }
  }

  &.grid-cols-3 {
    @media (min-width: 768px) {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      align-items: start;
    }
  }

  &.grid-cols-4 {
    @media (min-width: 992px) {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      align-items: start;
    }
  }
}
</style>
