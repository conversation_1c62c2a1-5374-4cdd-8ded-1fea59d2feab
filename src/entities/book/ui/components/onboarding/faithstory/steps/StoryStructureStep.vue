<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/faithstory/steps/StoryStructureStep.vue -->
<template>
  <OnboardingStep
    title="How will you structure your story?"
    description="The structure and focus help readers connect with your message."
    section-title="Story Structure & Focus"
    :disabled="isStepDisabled"
    @next="$emit('next')"
    @back="$emit('back')"
  >
    <OnboardingGrid :columns="1" gap="24px">
      <!-- Structure Selection -->
      <div class="question-section">
        <h3 class="question-title">
          Do you want to tell your story in order of life events or grouped by
          themes?
        </h3>
        <OnboardingGrid :columns="1" gap="12px">
          <OnboardingCard
            v-for="option in structureOptions"
            :key="option.value"
            :title="option.label"
            :description="option.description"
            :selected="questions.bookOrder === option.value"
            @select="questions.bookOrder = option.value"
          />
        </OnboardingGrid>
      </div>

      <!-- Faith Background -->
      <div class="question-section">
        <h3 class="question-title">
          What is your spiritual or faith background?
        </h3>
        <OnboardingInput
          v-model="questions.bookFocused"
          type="textarea"
          placeholder="Examples: Christian, Muslim, New Age, Spiritual but not religious"
          :rows="3"
        >
          <template #append>
            <q-btn flat icon="mic" @click="openTranscription('bookFocused')">
              <q-tooltip>Transcribe</q-tooltip>
            </q-btn>
          </template>
        </OnboardingInput>
      </div>
    </OnboardingGrid>
  </OnboardingStep>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useVModels } from '@vueuse/core';
import {
  OnboardingStep,
  OnboardingCard,
  OnboardingInput,
  OnboardingGrid,
} from '../../shared';

export interface StoryStructureQuestions {
  bookOrder: string;
  bookFocused: string;
}

const props = defineProps<{
  questions: StoryStructureQuestions;
}>();

const emit = defineEmits<{
  'update:questions': [value: StoryStructureQuestions];
  next: [];
  back: [];
  'open-transcription': [field: string];
}>();

const { questions } = useVModels(props, emit);

const structureOptions = [
  {
    value: 'Chronological',
    label: 'Chronological',
    description: 'Tell your story in the order events happened in your life',
  },
  {
    value: 'Thematic',
    label: 'Thematic',
    description: 'Group your story by themes like faith, healing, growth',
  },
];

const isStepDisabled = computed(() => {
  return !questions.value.bookOrder || !questions.value.bookFocused.trim();
});

const openTranscription = (field: string) => {
  emit('open-transcription', field);
};
</script>

<style lang="scss" scoped>
.question-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.question-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  margin: 0;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}
</style>
