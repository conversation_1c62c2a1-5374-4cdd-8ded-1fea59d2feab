<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/faithstory/steps/ReadersTakeawayStep.vue -->
<template>
  <OnboardingStep
    title="What will readers take away?"
    description="Define the impact and transformation you want your story to have on readers."
    section-title="Reader Takeaway"
    :disabled="isStepDisabled"
    @next="$emit('next')"
    @back="$emit('back')"
  >
    <OnboardingGrid :columns="1" gap="24px">
      <div class="question-section">
        <h3 class="question-title">
          What do you hope readers will take away from your story?
        </h3>
        <OnboardingInput
          v-model="questions.readersTakeaway"
          type="textarea"
          placeholder="Examples: Hope, belief in miracles, deeper faith, encouragement"
          :rows="4"
        >
          <template #append>
            <q-btn
              flat
              icon="mic"
              @click="openTranscription('readersTakeaway')"
            >
              <q-tooltip>Transcribe</q-tooltip>
            </q-btn>
            <OnboardingMannyButton
              :loading="loading"
              :disabled="loading"
              @click="$emit('generate-takeaway')"
            />
          </template>
        </OnboardingInput>
      </div>
    </OnboardingGrid>
  </OnboardingStep>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useVModels } from '@vueuse/core';
import {
  OnboardingStep,
  OnboardingInput,
  OnboardingGrid,
  OnboardingMannyButton,
} from '../../shared';

export interface ReadersTakeawayQuestions {
  readersTakeaway: string;
}

const props = defineProps<{
  questions: ReadersTakeawayQuestions;
  loading?: boolean;
}>();

const emit = defineEmits<{
  'update:questions': [value: ReadersTakeawayQuestions];
  next: [];
  back: [];
  'open-transcription': [field: string];
  'generate-takeaway': [];
}>();

const { questions } = useVModels(props, emit);

const isStepDisabled = computed(() => {
  return !questions.value.readersTakeaway.trim();
});

const openTranscription = (field: string) => {
  emit('open-transcription', field);
};
</script>

<style lang="scss" scoped>
.question-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.question-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  margin: 0;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}
</style>
