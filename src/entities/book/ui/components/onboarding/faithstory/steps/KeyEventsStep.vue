<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/faithstory/steps/KeyEventsStep.vue -->
<template>
  <OnboardingStep
    title="Key moments in your journey"
    description="What are the pivotal spiritual moments or life events you want to share?"
    section-title="Key Life Events"
    :disabled="isStepDisabled"
    @next="$emit('next')"
    @back="$emit('back')"
  >
    <OnboardingGrid :columns="1" gap="24px">
      <div class="question-section">
        <h3 class="question-title">
          What are 3-5 key life events or spiritual moments you want to include?
        </h3>
        <OnboardingInput
          v-model="questions.keyLifeEvents"
          type="textarea"
          placeholder="Examples: Conversion experience, answered prayer, healing, calling, ministry moment"
          :rows="5"
        >
          <template #append>
            <q-btn flat icon="mic" @click="openTranscription('keyLifeEvents')">
              <q-tooltip>Transcribe</q-tooltip>
            </q-btn>
            <OnboardingMannyButton
              :loading="loading"
              :disabled="loading"
              @click="$emit('generate-key-events')"
            />
          </template>
        </OnboardingInput>
      </div>
    </OnboardingGrid>
  </OnboardingStep>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useVModels } from '@vueuse/core';
import {
  OnboardingStep,
  OnboardingInput,
  OnboardingGrid,
  OnboardingMannyButton,
} from '../../shared';

export interface KeyEventsQuestions {
  keyLifeEvents: string;
}

const props = defineProps<{
  questions: KeyEventsQuestions;
  loading?: boolean;
}>();

const emit = defineEmits<{
  'update:questions': [value: KeyEventsQuestions];
  next: [];
  back: [];
  'open-transcription': [field: string];
  'generate-key-events': [];
}>();

const { questions } = useVModels(props, emit);

const isStepDisabled = computed(() => {
  return !questions.value.keyLifeEvents.trim();
});

const openTranscription = (field: string) => {
  emit('open-transcription', field);
};
</script>

<style lang="scss" scoped>
.question-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.question-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  margin: 0;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}
</style>
