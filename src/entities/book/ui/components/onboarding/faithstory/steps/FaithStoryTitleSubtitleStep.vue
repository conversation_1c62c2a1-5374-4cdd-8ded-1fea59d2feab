<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/faithstory/steps/FaithStoryTitleSubtitleStep.vue -->
<template>
  <OnboardingTitleSubtitle
    v-model:title-model-value="title"
    v-model:subtitle-model-value="subtitle"
    :questions="questions"
    book-type="Faith Story"
    step-title="Create Your Faith Story Title & Subtitle"
    step-description="Craft compelling titles that capture your spiritual journey and inspire readers."
    title-label="Faith Story Title"
    subtitle-label="Faith Story Subtitle"
    title-placeholder="Enter your faith story title..."
    subtitle-placeholder="Enter your faith story subtitle..."
    :ai-prompt="faithStoryPrompt"
    @next="$emit('next')"
    @back="$emit('back')"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useVModels } from '@vueuse/core';
import { OnboardingTitleSubtitle } from '../../shared';

interface Props {
  titleModelValue: string;
  subtitleModelValue: string;
  questions?: any;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  'update:titleModelValue': [value: string];
  'update:subtitleModelValue': [value: string];
  next: [];
  back: [];
}>();

const { titleModelValue: title, subtitleModelValue: subtitle } = useVModels(
  props,
  emit,
);

// Faith Story specific AI prompt
const faithStoryPrompt = computed(() => {
  if (!props.questions) return '';

  return `You are a master book title creator with expertise in marketing, psychology, and publishing.
Your task: create 5 original, compelling book TITLE + SUBTITLE pairs for a Faith Story book.

  <p>Target Reader Gender: ${props.questions?.gender}</p>
  <p>Target Reader Age: ${props.questions?.age}</p>
  <p>Main Reason for Sharing Story: ${props.questions?.mainReason}</p>
  <p>Type of Faith Story: ${
    props.questions?.otherBestDescription || props.questions?.bestDescription
  }</p>
  <p>Story Structure Preference: ${props.questions?.bookOrder}</p>
  <p>Spiritual Background: ${props.questions?.bookFocused}</p>
  <p>Target Audience Description: ${props.questions?.description}</p>
  <p>Key Life Events or Spiritual Moments: ${props.questions?.keyLifeEvents}</p>
  <p>Reader Takeaway: ${props.questions?.readersTakeaway}</p>
  <p>Book Goals: ${props.questions?.authorImpact}</p>

Requirements:
1. Titles must be EXACTLY 3 words (no exceptions).
2. Subtitles must include EXACTLY 3 benefits, formatted like:
   One Woman's Journey Through Doubt, Renewal, and Hope
   Finding Strength in Trials, Grace in Chaos, and Purpose in Faith
   A Testimony of Pain, Perseverance, and Redemption
3. Titles should sound personal, story-driven, and emotionally authentic — avoid "guidebook" or textbook formulas.
4. Do NOT use generic clichés like "Unlock Purpose" or "Find Inner Peace."
5. Make them curiosity-provoking, heartfelt, and relatable — similar to bestselling faith memoirs and spiritual journeys.
6. Each title should be on its own line, followed by its subtitle on the next line
7. Separate each pair with a blank line
8. Use plain text without numbering, bullets, or special formatting
9. Make titles provocative and curiosity-inducing
10. Make subtitles benefit-focused and compelling

Format example:
Title One
Subtitle describing benefits and promises

Title Two
Subtitle describing benefits and promises

[continue for all 5 pairs]`;
});
</script>
