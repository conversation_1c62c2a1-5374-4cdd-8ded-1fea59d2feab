<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/faithstory/steps/TargetDescriptionStep.vue -->
<template>
  <OnboardingStep
    title="Describe your readers"
    description="Help us understand who you're writing for so your story resonates deeply."
    section-title="Reader Description"
    :disabled="isStepDisabled"
    @next="$emit('next')"
    @back="$emit('back')"
  >
    <OnboardingGrid :columns="1" gap="24px">
      <div class="question-section">
        <h3 class="question-title">
          Describe your target audience in a few words
        </h3>
        <OnboardingInput
          v-model="questions.description"
          type="textarea"
          placeholder="Examples: Women struggling with doubt, men leaving prison, teens in crisis, fellow believers"
          :rows="4"
        >
          <template #append>
            <q-btn flat icon="mic" @click="openTranscription('description')">
              <q-tooltip>Transcribe</q-tooltip>
            </q-btn>
          </template>
        </OnboardingInput>
      </div>
    </OnboardingGrid>
  </OnboardingStep>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useVModels } from '@vueuse/core';
import { OnboardingStep, OnboardingInput, OnboardingGrid } from '../../shared';

export interface TargetDescriptionQuestions {
  description: string;
}

const props = defineProps<{
  questions: TargetDescriptionQuestions;
}>();

const emit = defineEmits<{
  'update:questions': [value: TargetDescriptionQuestions];
  next: [];
  back: [];
  'open-transcription': [field: string];
}>();

const { questions } = useVModels(props, emit);

const isStepDisabled = computed(() => {
  return !questions.value.description.trim();
});

const openTranscription = (field: string) => {
  emit('open-transcription', field);
};
</script>

<style lang="scss" scoped>
.question-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.question-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  margin: 0;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}
</style>
