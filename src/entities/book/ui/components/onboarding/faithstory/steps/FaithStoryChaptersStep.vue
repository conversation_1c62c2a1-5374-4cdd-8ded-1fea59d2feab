<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/faithstory/steps/FaithStoryChaptersStep.vue -->
<template>
  <OnboardingChapters
    v-model="chapters"
    :title="title"
    :subtitle="subtitle"
    :questions="questions"
    book-type="Faith Story"
    step-title="Create Your Faith Story Chapters"
    step-description="Organize your spiritual journey into compelling chapters that guide readers through your story."
    next-label="Complete Faith Story"
    next-icon="check"
    :ai-prompt="faithStoryChapterPrompt"
    @complete="$emit('complete')"
    @back="$emit('back')"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useVModel } from '@vueuse/core';
import { OnboardingChapters } from '../../shared';

interface ChapterOutline {
  title: string;
  outline: string;
  wordCount: number;
  number: number;
  isSection: boolean;
}

interface Props {
  modelValue: ChapterOutline[];
  title: string;
  subtitle: string;
  questions?: any;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  'update:modelValue': [value: ChapterOutline[]];
  complete: [];
  back: [];
}>();

const chapters = useVModel(props, 'modelValue', emit);

// Faith Story specific chapter generation prompt
const faithStoryChapterPrompt = computed(() => {
  return `Using the book title and subtitle provided below, generate 9 chapter title outlines using the following criteria:
  <ol>
    <li>Provide a numbered list. For example: "1. Chasing Shadows in the Rain"</li>
    <li>Each chapter title should be 4-6 words, based on the emotional tone, key events, or personal themes suggested by the title, subtitle, and the author's answers.</li>
    <li>The chapter titles should loosely follow the order the author chose (chronological if they selected life stages, or thematic if they selected lessons/challenges/turning points).</li>
    <li>The chapter titles should reflect the author's stated main reason for sharing their story.</li>
    <li>Use vivid, compelling, and memorable language that captures emotion, growth, or pivotal spiritual moments.</li>
    <li>Avoid using Markdown or HTML formatting in your response.</li>
  </ol>
  <br />
  The provided information is as follows:
  <ul>
    <li>Book title: ${props.title}</li>
    <li>Book subtitle: ${props.subtitle}</li>
    <li>Main Reason for Sharing Story: ${props.questions?.mainReason}</li>
    <li>Type of Faith Story: ${
      props.questions?.otherBestDescription || props.questions?.bestDescription
    }</li>
    <li>Story Structure Preference: ${props.questions?.bookOrder}</li>
    <li>Spiritual Background: ${props.questions?.bookFocused}</li>
    <li>Target Audience Description: ${props.questions?.description}</li>
    <li>Key Life Events or Spiritual Moments: ${props.questions
      ?.keyLifeEvents}</li>
    <li>Reader Takeaway: ${props.questions?.readersTakeaway}</li>
    <li>Book Goals: ${props.questions?.authorImpact}</li>
    <li>Target audience: ${props.questions?.description}</li>
    <li>Target audience age: ${props.questions?.age}</li>
  </ul>`;
});
</script>
