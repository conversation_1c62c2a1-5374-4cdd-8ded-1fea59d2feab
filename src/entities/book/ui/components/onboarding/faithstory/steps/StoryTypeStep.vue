<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/faithstory/steps/StoryTypeStep.vue -->
<template>
  <OnboardingStep
    title="What describes your faith story?"
    description="Understanding the type of your story helps structure it effectively."
    section-title="Story Type"
    :disabled="isStepDisabled"
    @next="$emit('next')"
    @back="$emit('back')"
  >
    <OnboardingGrid :columns="1" gap="24px">
      <!-- Story Type Selection -->
      <div class="question-section">
        <h3 class="question-title">Which best describes your faith story?</h3>
        <OnboardingGrid :columns="1" gap="12px">
          <OnboardingCard
            v-for="option in storyTypeOptions"
            :key="option.value"
            :title="option.label"
            :description="option.description"
            :selected="questions.bestDescription === option.value"
            @select="questions.bestDescription = option.value"
          />
        </OnboardingGrid>

        <!-- Other Description -->
        <div v-if="questions.bestDescription === 'Other'" class="q-mt-md">
          <OnboardingInput
            v-model="questions.otherBestDescription"
            type="textarea"
            label="Please specify your story type"
            placeholder="Describe what type of faith story you want to share"
            :rows="3"
          >
            <template #append>
              <q-btn
                flat
                icon="mic"
                @click="openTranscription('otherBestDescription')"
              >
                <q-tooltip>Transcribe</q-tooltip>
              </q-btn>
            </template>
          </OnboardingInput>
        </div>
      </div>
    </OnboardingGrid>
  </OnboardingStep>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useVModels } from '@vueuse/core';
import {
  OnboardingStep,
  OnboardingCard,
  OnboardingInput,
  OnboardingGrid,
} from '../../shared';

export interface StoryTypeQuestions {
  bestDescription: string;
  otherBestDescription: string;
}

const props = defineProps<{
  questions: StoryTypeQuestions;
}>();

const emit = defineEmits<{
  'update:questions': [value: StoryTypeQuestions];
  next: [];
  back: [];
  'open-transcription': [field: string];
}>();

const { questions } = useVModels(props, emit);

const storyTypeOptions = [
  {
    value: 'A personal testimony of coming to faith',
    label: 'Personal Testimony',
    description: 'How you came to faith and what it means to you',
  },
  {
    value: 'A story of transformation through faith',
    label: 'Transformation Story',
    description: 'How faith changed your life and perspective',
  },
  {
    value: 'A series of faith-based life lessons',
    label: 'Life Lessons',
    description: 'Wisdom and insights gained through your faith journey',
  },
  {
    value: 'A story of overcoming adversity with faith',
    label: 'Overcoming Adversity',
    description: 'How faith helped you through difficult times',
  },
  {
    value: 'Other',
    label: 'Other',
    description: 'A different type of faith story',
  },
];

const isStepDisabled = computed(() => {
  if (!questions.value.bestDescription) return true;
  if (
    questions.value.bestDescription === 'Other' &&
    !questions.value.otherBestDescription.trim()
  )
    return true;
  return false;
});

const openTranscription = (field: string) => {
  emit('open-transcription', field);
};
</script>

<style lang="scss" scoped>
.question-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.question-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  margin: 0;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}
</style>
