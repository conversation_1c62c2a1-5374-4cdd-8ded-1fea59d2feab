<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/faithstory/steps/FaithAuthorImpactStep.vue -->
<template>
  <OnboardingStep
    title="What will this book do for you?"
    description="Define how sharing your faith story will impact your life and ministry."
    section-title="Author Impact"
    :disabled="isStepDisabled"
    next-label="Complete Faith Story"
    next-icon="check"
    @next="$emit('complete')"
    @back="$emit('back')"
  >
    <OnboardingGrid :columns="1" gap="24px">
      <div class="question-section">
        <h3 class="question-title">
          What are 2-3 things you hope this book does for you?
        </h3>
        <OnboardingInput
          v-model="questions.authorImpact"
          type="textarea"
          placeholder="Examples: Healing, leaving a legacy, grow a platform, lead others to God"
          :rows="4"
        >
          <template #append>
            <q-btn flat icon="mic" @click="openTranscription('authorImpact')">
              <q-tooltip>Transcribe</q-tooltip>
            </q-btn>
            <OnboardingMannyButton
              :loading="loading"
              :disabled="loading"
              @click="$emit('generate-author-impact')"
            />
          </template>
        </OnboardingInput>
      </div>
    </OnboardingGrid>
  </OnboardingStep>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useVModels } from '@vueuse/core';
import {
  OnboardingStep,
  OnboardingInput,
  OnboardingGrid,
  OnboardingMannyButton,
} from '../../shared';

export interface FaithAuthorImpactQuestions {
  authorImpact: string;
}

const props = defineProps<{
  questions: FaithAuthorImpactQuestions;
  loading?: boolean;
}>();

const emit = defineEmits<{
  'update:questions': [value: FaithAuthorImpactQuestions];
  complete: [];
  back: [];
  'open-transcription': [field: string];
  'generate-author-impact': [];
}>();

const { questions } = useVModels(props, emit);

const isStepDisabled = computed(() => {
  return !questions.value.authorImpact.trim();
});

const openTranscription = (field: string) => {
  emit('open-transcription', field);
};
</script>

<style lang="scss" scoped>
.question-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.question-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  margin: 0;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}
</style>
