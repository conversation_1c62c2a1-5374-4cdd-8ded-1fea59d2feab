<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/faithstory/steps/FaithTargetAudienceStep.vue -->
<template>
  <OnboardingStep
    title="Who is your ideal reader?"
    description="Understanding your faith story audience helps create content that truly connects."
    section-title="Target Audience"
    :disabled="isStepDisabled"
    @next="$emit('next')"
    @back="$emit('back')"
  >
    <OnboardingGrid :columns="1" gap="24px">
      <!-- Gender Selection -->
      <div class="question-section">
        <h3 class="question-title">Is your target audience a specific gender?</h3>
        <OnboardingGrid :columns="1" gap="12px">
          <OnboardingCard
            v-for="option in genderOptions"
            :key="option"
            :title="option"
            :selected="questions.gender === option"
            @select="questions.gender = option"
          />
        </OnboardingGrid>
      </div>

      <!-- Age Selection -->
      <div class="question-section">
        <h3 class="question-title">Is your target audience a specific age?</h3>
        <OnboardingGrid :columns="2" gap="12px">
          <OnboardingCard
            v-for="age in ageOptions"
            :key="age"
            :title="age"
            :selected="questions.age.includes(age)"
            selection-type="checkbox"
            @select="toggleAge(age)"
          />
        </OnboardingGrid>
      </div>
    </OnboardingGrid>
  </OnboardingStep>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useVModels } from '@vueuse/core';
import {
  OnboardingStep,
  OnboardingCard,
  OnboardingGrid,
} from '../../shared';

export interface FaithTargetAudienceQuestions {
  gender: 'Male' | 'Female' | 'Both';
  age: string[];
}

const props = defineProps<{
  questions: FaithTargetAudienceQuestions;
}>();

const emit = defineEmits<{
  'update:questions': [value: FaithTargetAudienceQuestions];
  next: [];
  back: [];
}>();

const { questions } = useVModels(props, emit);

const genderOptions = ['Male', 'Female', 'Both'];
const ageOptions = [
  'Any Age',
  'Under 18',
  '18-25',
  '25-35',
  '35-50',
  '50-65',
  '65+',
];

const isStepDisabled = computed(() => {
  return !questions.value.gender || questions.value.age.length === 0;
});

const toggleAge = (age: string) => {
  const index = questions.value.age.indexOf(age);
  if (index > -1) {
    questions.value.age.splice(index, 1);
  } else {
    questions.value.age.push(age);
  }
};
</script>

<style lang="scss" scoped>
.question-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.question-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  margin: 0;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}
</style>
