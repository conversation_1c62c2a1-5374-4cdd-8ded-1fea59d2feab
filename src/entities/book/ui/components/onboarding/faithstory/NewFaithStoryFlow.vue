<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/faithstory/NewFaithStoryFlow.vue (fixed recursive updates) -->
<template>
  <!-- Modern Faith Story Mission Flow -->
  <FaithStoryMissionFlow
    v-model:show="onBoardingActive"
    v-model:questions="missionQuestions"
    v-model:title="title"
    v-model:subtitle="subtitle"
    v-model:chapters="chapters"
    @complete="complete"
  />
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, toRef, watch } from 'vue';

import type {
  Book,
  NewBook,
  ChapterOutline,
  FaithStoryMissionQuestions,
} from 'src/entities/book/model/types';
import { listOutlines, setBook, updateBook } from 'src/entities/book';

import { confirmOverrideText } from 'src/shared/lib/quasar-dialogs';
import { useQuasar } from 'quasar';

import { ACTIONS, loggingService, PAGES } from 'src/entities/log';
import { useVModel, watchDebounced } from '@vueuse/core';
import { useRouter } from 'vue-router';

import { FaithStoryMissionFlow } from '../index';

export type NewBookStarter = Pick<
  NewBook,
  'title' | 'subtitle' | 'mission' | 'missionQuestions' | 'chapters'
>;

export type ExistingBookStarter = Book;
export type ExistingBookOutlinesStarter = ChapterOutline[];

const props = defineProps<{
  modal: boolean;
  initialValues?: ExistingBookStarter;
  initialOutlines?: ExistingBookOutlinesStarter;
  shouldUpdate?: boolean;
}>();

const emit = defineEmits<{
  cancel: [];
  completed: [book: NewBookStarter];
  updated: [id: string, book: ExistingBookStarter];
}>();

const router = useRouter();
const $q = useQuasar();

const initialValues = toRef(props, 'initialValues');
const initialOutlines = toRef(props, 'initialOutlines');
const shouldUpdate = toRef(props, 'shouldUpdate');

const updating = computed(() => initialValues.value != undefined);

// Convert legacy mission questions format to new format
const convertLegacyQuestions = (
  legacyQuestions: any,
): FaithStoryMissionQuestions => {
  return {
    gender: legacyQuestions?.gender || 'Both',
    age: legacyQuestions?.age || [],
    description: legacyQuestions?.description || '',
    bestDescription:
      legacyQuestions?.bestDescription ||
      'A personal testimony of coming to faith',
    otherBestDescription: legacyQuestions?.otherBestDescription || '',
    keyLifeEvents: legacyQuestions?.keyLifeEvents || '',
    readersTakeaway: legacyQuestions?.readersTakeaway || '',
    mainReason: legacyQuestions?.mainReason || '',
    bookOrder: legacyQuestions?.bookOrder || 'Chronological',
    bookFocused: legacyQuestions?.bookFocused || '',
    authorImpact: legacyQuestions?.authorImpact || '',
  };
};

// Initialize reactive data - using computed to prevent recursive updates
const onBoardingActive = computed({
  get: () => props.modal,
  set: (value) => {
    if (!value) {
      emit('cancel');
    }
  },
});

const missionQuestions = ref<FaithStoryMissionQuestions>(
  convertLegacyQuestions(initialValues.value?.missionQuestions),
);

const mission = ref(initialValues.value?.mission || '');
const title = ref(initialValues.value?.title || '');
const subtitle = ref(initialValues.value?.subtitle || '');
const chapters = ref<ChapterOutline[]>(
  initialOutlines.value?.length
    ? initialOutlines.value
    : initialValues.value?.chapters || [],
);

// Watch for prop changes and update refs accordingly - with guards against recursion
watch(
  () => initialValues.value,
  (newValue) => {
    if (newValue) {
      const newQuestions = convertLegacyQuestions(newValue.missionQuestions);

      // Only update if actually different to prevent loops
      if (
        JSON.stringify(missionQuestions.value) !== JSON.stringify(newQuestions)
      ) {
        missionQuestions.value = newQuestions;
      }

      if (mission.value !== (newValue.mission || '')) {
        mission.value = newValue.mission || mission.value;
      }

      if (title.value !== (newValue.title || '')) {
        title.value = newValue.title || title.value;
      }

      if (subtitle.value !== (newValue.subtitle || '')) {
        subtitle.value = newValue.subtitle || subtitle.value;
      }

      if (!initialOutlines.value?.length) {
        const newChapters = newValue.chapters || [];
        if (JSON.stringify(chapters.value) !== JSON.stringify(newChapters)) {
          chapters.value = newChapters;
        }
      }
    }
  },
  { immediate: true },
);

watch(
  () => initialOutlines.value,
  (newValue) => {
    if (newValue?.length) {
      if (JSON.stringify(chapters.value) !== JSON.stringify(newValue)) {
        chapters.value = newValue;
      }
    }
  },
  { immediate: true },
);

// Define the module or component name
const MODULE_NAME = 'book_foundations';

async function complete() {
  const bookData = {
    mission: mission.value,
    title: title.value,
    subtitle: subtitle.value,
    chapters: chapters.value,
    missionQuestions: missionQuestions.value,
  };

  try {
    if (initialValues.value === undefined) {
      // Creating new book
      emit('completed', bookData);

      await loggingService.logAction(
        PAGES.BOOKMISSIONSTATEMENT,
        ACTIONS.CREATE,
        `New faith story book created: ${title.value || 'Untitled book'}`,
        MODULE_NAME,
      );
    } else {
      // Updating existing book
      const updatedBook = {
        ...initialValues.value,
        id: initialValues.value?.id,
        ...bookData,
      };

      emit('updated', initialValues.value?.id, updatedBook);

      await loggingService.logAction(
        PAGES.BOOKMISSIONSTATEMENT,
        ACTIONS.UPDATE,
        `Faith story book updated: ${title.value || 'Untitled book'}`,
        MODULE_NAME,
        initialValues.value?.id as string,
      );

      // Auto-navigate to book if updating
      if (shouldUpdate.value === true && initialValues.value?.id) {
        await router.push(`/books/${initialValues.value.id}`);
      }
    }
  } catch (error) {
    console.error('Error completing faith story flow:', error);
    $q.notify({
      color: 'negative',
      message:
        'An error occurred while saving your faith story book. Please try again.',
      icon: 'error',
    });
  }
}

// Computed book object for autosave functionality
const book = computed(() => {
  if (!initialValues.value?.id) return null;

  return {
    ...initialValues.value,
    id: initialValues.value?.id,
    mission: mission.value,
    title: title.value,
    subtitle: subtitle.value,
    chapters: chapters.value,
    missionQuestions: missionQuestions.value,
  };
});

// Auto-save functionality for existing books - with debounce to prevent loops
watchDebounced(
  book,
  (newBook) => {
    if (newBook && shouldUpdate.value !== true) {
      setBook(newBook.id, newBook);
    }
  },
  { debounce: 500 },
);

// Handle updates on book chapters
watchDebounced(
  chapters,
  (newChapters) => {
    if (book.value && shouldUpdate.value !== true) {
      const updatedBook = { ...book.value, chapters: newChapters };
      updateBook(book.value.id, updatedBook);
    }
  },
  { debounce: 500, deep: true },
);

// Handle mission updates
watchDebounced(
  mission,
  (newMission) => {
    if (book.value && shouldUpdate.value !== true) {
      const updatedBook = { ...book.value, mission: newMission };
      updateBook(book.value.id, updatedBook);
    }
  },
  { debounce: 500 },
);

// Handle title updates
watchDebounced(
  title,
  (newTitle) => {
    if (book.value && shouldUpdate.value !== true) {
      const updatedBook = { ...book.value, title: newTitle };
      updateBook(book.value.id, updatedBook);
    }
  },
  { debounce: 500 },
);

// Handle subtitle updates
watchDebounced(
  subtitle,
  (newSubtitle) => {
    if (book.value && shouldUpdate.value !== true) {
      const updatedBook = { ...book.value, subtitle: newSubtitle };
      updateBook(book.value.id, updatedBook);
    }
  },
  { debounce: 500 },
);

// Handle questions updates
watchDebounced(
  missionQuestions,
  (newQuestions) => {
    if (book.value && shouldUpdate.value !== true) {
      const updatedBook = { ...book.value, missionQuestions: newQuestions };
      updateBook(book.value.id, updatedBook);
    }
  },
  { debounce: 500, deep: true },
);
</script>
