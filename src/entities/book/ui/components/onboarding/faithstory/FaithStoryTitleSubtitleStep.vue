<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/faithstory/FaithStoryTitleSubtitleStep.vue -->
<template>
  <LegacyScrollArea>
    <LegacySection
      class="flex justify-between"
      :show-header="true"
      title="Write your Faith Story Title and Subtitle"
    >
      <template #header-actions v-if="1 != 1">
        <q-btn
          v-if="!showSuggestions"
          @click="generateTitleSubtitlePairs"
          :loading="loading"
          :disable="loading"
          color="primary"
          icon="img:robot.png"
        >
          &nbsp;&nbsp;Ask <PERSON>
          <q-tooltip>Ask <PERSON> for Title & Subtitle suggestions</q-tooltip>
        </q-btn>
      </template>
    </LegacySection>
    <q-separator />

    <LegacySection>
      <div class="q-gutter-md">
        <LegacyInput
          v-model="title"
          label="Faith Story Title"
          outlined
          placeholder="Enter your faith story title..."
        />

        <LegacyInput
          v-model="subtitle"
          label="Faith Story Subtitle"
          outlined
          placeholder="Enter your faith story subtitle..."
        />
      </div>

      <!-- Legacy Suggestion List -->
      <LegacySuggestionList
        :title-subtitle-pairs="titleSubtitlePairs"
        :selected-title="title"
        :selected-subtitle="subtitle"
        :loading="loading"
        @select-title="handleTitleSelect"
        @select-subtitle="handleSubtitleSelect"
        @select-pair="handlePairSelect"
        @hide="hideSuggestions"
        @regenerate="generateTitleSubtitlePairs"
      />
    </LegacySection>
  </LegacyScrollArea>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useVModel, useVModels } from '@vueuse/core';
import { useQuasar } from 'quasar';
import { composeText } from 'src/shared/api/openai';
import { confirmOverrideText, warn } from 'src/shared/lib/quasar-dialogs';
import { FaithStoryMissionQuestions } from 'src/entities/book/model/types';
import {
  LegacySection,
  LegacyScrollArea,
  LegacyInput,
  LegacySuggestionList,
} from '../shared';

interface TitleSubtitlePair {
  title: string;
  subtitle: string;
}

const props = withDefaults(
  defineProps<{
    titleModelValue: string;
    subtitleModelValue: string;
    questions?: FaithStoryMissionQuestions;
    updating?: boolean;
  }>(),
  {
    updating: false,
  },
);

const emit = defineEmits<{
  'update:titleModelValue': [value: string];
  'update:subtitleModelValue': [value: string];
}>();

const { titleModelValue: title, subtitleModelValue: subtitle } = useVModels(
  props,
  emit,
);
const questions = useVModel(props, 'questions', emit);
const $q = useQuasar();

// Component state
const loading = ref(false);
const showSuggestions = ref(false);
const titleSubtitlePairs = ref<TitleSubtitlePair[]>([]);
const results = ref('');

// Computed properties
const canGenerate = computed(() => {
  return (
    questions?.value?.mainReason &&
    (questions?.value?.otherBestDescription ||
      questions?.value?.bestDescription) &&
    questions?.value?.bookOrder &&
    questions?.value?.bookFocused &&
    questions?.value?.description &&
    questions?.value?.keyLifeEvents &&
    questions?.value?.readersTakeaway &&
    questions?.value?.authorImpact
  );
});

// Event handlers
const handleTitleSelect = (selectedTitle: string) => {
  title.value = selectedTitle;
};

const handleSubtitleSelect = (selectedSubtitle: string) => {
  subtitle.value = selectedSubtitle;
};

const handlePairSelect = (pair: TitleSubtitlePair) => {
  title.value = pair.title;
  subtitle.value = pair.subtitle;
};

const hideSuggestions = () => {
  showSuggestions.value = false;
};

// AI prompt for combined title-subtitle generation
const combinedPrompt = computed(() => {
  let promptStr = `You are a master book title creator with expertise in marketing, psychology, and publishing.
Your task: create 5 original, compelling book TITLE + SUBTITLE pairs for a Faith Story book.

    <p>Target Reader Gender: ${questions?.value?.gender}</p>
  <p>Target Reader Age: ${questions?.value?.age}</p>

  <p>Main Reason for Sharing Story: ${questions?.value?.mainReason}</p>
  <p>Type of Faith Story: ${
    questions?.value?.otherBestDescription || questions?.value?.bestDescription
  }</p>
  <p>Story Structure Preference: ${questions?.value?.bookOrder}</p>
  <p>Spiritual Background: ${questions?.value?.bookFocused}</p>
  <p>Target Audience Description: ${questions?.value?.description}</p>
  <p>Key Life Events or Spiritual Moments: ${questions?.value
    ?.keyLifeEvents}</p>
  <p>Reader Takeaway: ${questions?.value?.readersTakeaway}</p>
  <p>Book Goals: ${questions?.value?.authorImpact}</p>`;

  promptStr += `

Requirements:
1. Titles must be EXACTLY 3 words (no exceptions).
2. Subtitles must include EXACTLY 3 benefits, formatted like:
   One Woman's Journey Through Doubt, Renewal, and Hope
   Finding Strength in Trials, Grace in Chaos, and Purpose in Faith
   A Testimony of Pain, Perseverance, and Redemption
3. Titles should sound personal, story-driven, and emotionally authentic — avoid "guidebook" or textbook formulas.
4. Do NOT use generic clichés like "Unlock Purpose" or "Find Inner Peace."
5. Make them curiosity-provoking, heartfelt, and relatable — similar to bestselling faith memoirs and spiritual journeys.
6. Each title should be on its own line, followed by its subtitle on the next line
7. Separate each pair with a blank line
8. Use plain text without numbering, bullets, or special formatting
9. Make titles provocative and curiosity-inducing
10. Make subtitles benefit-focused and compelling

Format example:
Title One
Subtitle describing benefits and promises

Title Two
Subtitle describing benefits and promises

[continue for all 5 pairs]`;

  return promptStr;
});
// Generate title-subtitle pairs
async function generateTitleSubtitlePairs() {
  if (!canGenerate.value) {
    warn($q, {
      title: 'Missing Information',
      message: 'Manny needs your faith story details to generate suggestions.',
    });
    return;
  }

  // Check if user wants to override existing content
  if (titleSubtitlePairs.value.length > 0) {
    const shouldOverride = await confirmOverrideText($q);
    if (!shouldOverride) {
      return;
    }
  }

  let promptRequest = combinedPrompt.value;

  // Add previous results to avoid repetition
  if (results.value) {
    promptRequest += `\n\nPlease avoid repeating or being similar to these previous results:\n${results.value}`;
  }

  loading.value = true;

  try {
    const response = await composeText(promptRequest);

    // Parse the response to extract title-subtitle pairs
    const pairs = parseTitleSubtitleResponse(response);

    if (pairs.length >= 3) {
      titleSubtitlePairs.value = pairs;
      showSuggestions.value = true;
      // Store results to avoid repetition in future calls
      results.value = `${results.value}\n${response}`;
    } else {
      throw new Error('Unexpected response format from AI');
    }
  } catch (e) {
    console.error(e);
    $q.notify({
      type: 'negative',
      message:
        'An error occurred while generating suggestions. Please try again.',
      position: 'top',
    });
  } finally {
    loading.value = false;
  }
}

// Parse AI response to extract title-subtitle pairs
function parseTitleSubtitleResponse(response: string): TitleSubtitlePair[] {
  const pairs: TitleSubtitlePair[] = [];

  // Split by double newlines to get pairs, then by single newlines within pairs
  const lines = response
    .trim()
    .split('\n')
    .filter((line) => line.trim() !== '');

  for (let i = 0; i < lines.length; i += 2) {
    if (i + 1 < lines.length) {
      const title = lines[i]
        .replace(/^\d+\.\s*/, '')
        .replace(/^"|"$/g, '')
        .trim();
      const subtitle = lines[i + 1]
        .replace(/^\d+\.\s*/, '')
        .replace(/^"|"$/g, '')
        .trim();

      if (title && subtitle) {
        pairs.push({ title, subtitle });
      }
    }
  }

  return pairs;
}
</script>
