<!-- /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/faithstory/FaithStoryMissionFlow.vue (fixed recursive updates) -->
<template>
  <OnboardingContainer
    :show="show"
    :current-step="currentStep"
    :total-steps="totalSteps"
    :current-step-title="stepTitles[currentStep]"
    @update:show="$emit('update:show', $event)"
  >
    <!-- Step 1: Gender -->
    <GenderStep
      v-if="currentStep === 1"
      v-model:questions="genderQuestions"
      @next="nextStep"
      @back="prevStep"
    />

    <!-- Step 2: Age -->
    <AgeStep
      v-if="currentStep === 2"
      v-model:questions="ageQuestions"
      @next="nextStep"
      @back="prevStep"
    />

    <!-- Step 3: Main Reason -->
    <MainReasonStep
      v-if="currentStep === 3"
      v-model:questions="mainReasonQuestions"
      @next="nextStep"
      @back="prevStep"
      @open-transcription="handleTranscription"
    />

    <!-- Step 4: Story Type -->
    <StoryTypeStep
      v-if="currentStep === 4"
      v-model:questions="storyTypeQuestions"
      @next="nextStep"
      @back="prevStep"
      @open-transcription="handleTranscription"
    />

    <!-- Step 5: Story Structure -->
    <StoryStructureStep
      v-if="currentStep === 5"
      v-model:questions="storyStructureQuestions"
      @next="nextStep"
      @back="prevStep"
      @open-transcription="handleTranscription"
    />

    <!-- Step 6: Description -->
    <DescriptionStep
      v-if="currentStep === 6"
      v-model:questions="descriptionQuestions"
      placeholder="Examples: Women struggling with doubt, men leaving prison, teens in crisis, fellow believers"
      book-type="Faith Story"
      @next="nextStep"
      @back="prevStep"
      @open-transcription="handleTranscription"
    />

    <!-- Step 7: Key Events -->
    <KeyEventsStep
      v-if="currentStep === 7"
      v-model:questions="keyEventsQuestions"
      :loading="loadingKeyLifeEvents"
      @next="nextStep"
      @back="prevStep"
      @open-transcription="handleTranscription"
      @generate-key-events="generateKeyLifeEvents"
    />

    <!-- Step 8: Readers Takeaway -->
    <ReadersTakeawayStep
      v-if="currentStep === 8"
      v-model:questions="readersTakeawayQuestions"
      :loading="loadingReadersTakeaway"
      @next="nextStep"
      @back="prevStep"
      @open-transcription="handleTranscription"
      @generate-takeaway="generateReadersTakeaway"
    />

    <!-- Step 9: Author Impact -->
    <FaithAuthorImpactStep
      v-if="currentStep === 9"
      v-model:questions="authorImpactQuestions"
      :loading="loadingAuthorImpact"
      @next="nextStep"
      @back="prevStep"
      @open-transcription="handleTranscription"
      @generate-author-impact="generateAuthorImpact"
    />

    <!-- Step 10: Title & Subtitle -->
    <FaithStoryTitleSubtitleStep
      v-if="currentStep === 10"
      v-model:title-model-value="titleModel"
      v-model:subtitle-model-value="subtitleModel"
      :questions="allQuestions"
      @next="nextStep"
      @back="prevStep"
    />

    <!-- Step 11: Chapters -->
    <FaithStoryChaptersStep
      v-if="currentStep === 11"
      v-model="chaptersModel"
      :title="titleModel"
      :subtitle="subtitleModel"
      :questions="allQuestions"
      @complete="completeFaithStory"
      @back="prevStep"
    />
  </OnboardingContainer>

  <!-- Transcription Dialog -->
  <q-dialog v-model="transcriptionDialog" persistent>
    <BookTranscriptionApp
      @completed="completedTranscript"
      @close="transcriptionDialog = false"
    />
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useQuasar } from 'quasar';
import { composeText } from 'src/shared/api/openai';
import { confirmOverrideText } from 'src/shared/lib/quasar-dialogs';
import {
  OnboardingContainer,
  GenderStep,
  AgeStep,
  DescriptionStep,
} from '../shared';
import BookTranscriptionApp from '../../BookTranscriptionApp.vue';

import MainReasonStep, {
  type MainReasonQuestions,
} from './steps/MainReasonStep.vue';
import StoryTypeStep, {
  type StoryTypeQuestions,
} from './steps/StoryTypeStep.vue';
import StoryStructureStep, {
  type StoryStructureQuestions,
} from './steps/StoryStructureStep.vue';
import KeyEventsStep, {
  type KeyEventsQuestions,
} from './steps/KeyEventsStep.vue';
import ReadersTakeawayStep, {
  type ReadersTakeawayQuestions,
} from './steps/ReadersTakeawayStep.vue';
import FaithAuthorImpactStep, {
  type FaithAuthorImpactQuestions,
} from './steps/FaithAuthorImpactStep.vue';
import FaithStoryTitleSubtitleStep from './steps/FaithStoryTitleSubtitleStep.vue';
import FaithStoryChaptersStep from './steps/FaithStoryChaptersStep.vue';

interface FaithStoryQuestions {
  gender: 'Male' | 'Female' | 'Both';
  age: string[];
  keyLifeEvents: string;
  readersTakeaway: string;
  mainReason: string;
  bestDescription: string;
  otherBestDescription: string;
  description: string;
  bookOrder: string;
  bookFocused: string;
  authorImpact: string;
}

interface ChapterOutline {
  title: string;
  outline: string;
  wordCount: number;
  number: number;
  isSection: boolean;
}

const props = defineProps<{
  show: boolean;
  questions: FaithStoryQuestions;
  title?: string;
  subtitle?: string;
  chapters?: ChapterOutline[];
}>();

const emit = defineEmits<{
  'update:show': [value: boolean];
  'update:questions': [value: FaithStoryQuestions];
  'update:title': [value: string];
  'update:subtitle': [value: string];
  'update:chapters': [value: ChapterOutline[]];
  complete: [];
}>();

const $q = useQuasar();
const giveNotice = ref(false);

// Step management
const currentStep = ref(1);
const totalSteps = 11;

const stepTitles = {
  1: 'Target Gender',
  2: 'Target Age',
  3: 'Your Why',
  4: 'Story Type',
  5: 'Story Structure',
  6: 'Reader Description',
  7: 'Key Events',
  8: 'Reader Takeaway',
  9: 'Author Impact',
  10: 'Title & Subtitle',
  11: 'Chapter Outline',
};

// Question states - prevent recursion by not watching props directly
const genderQuestions = reactive({
  gender: props.questions.gender,
});

const ageQuestions = reactive({
  age: [...props.questions.age],
});

const mainReasonQuestions = reactive<MainReasonQuestions>({
  mainReason: props.questions.mainReason,
});

const storyTypeQuestions = reactive<StoryTypeQuestions>({
  bestDescription: props.questions.bestDescription,
  otherBestDescription: props.questions.otherBestDescription,
});

const storyStructureQuestions = reactive<StoryStructureQuestions>({
  bookOrder: props.questions.bookOrder,
  bookFocused: props.questions.bookFocused,
});

const descriptionQuestions = reactive({
  description: props.questions.description,
});

const keyEventsQuestions = reactive<KeyEventsQuestions>({
  keyLifeEvents: props.questions.keyLifeEvents,
});

const readersTakeawayQuestions = reactive<ReadersTakeawayQuestions>({
  readersTakeaway: props.questions.readersTakeaway,
});

const authorImpactQuestions = reactive<FaithAuthorImpactQuestions>({
  authorImpact: props.questions.authorImpact,
});

// Use computed models to prevent recursive updates
const titleModel = computed({
  get: () => props.title || '',
  set: (value) => emit('update:title', value),
});

const subtitleModel = computed({
  get: () => props.subtitle || '',
  set: (value) => emit('update:subtitle', value),
});

const chaptersModel = computed({
  get: () => props.chapters || [],
  set: (value) => emit('update:chapters', value),
});

// Loading states
const loadingKeyLifeEvents = ref(false);
const loadingReadersTakeaway = ref(false);
const loadingAuthorImpact = ref(false);

// Transcription
const transcriptionDialog = ref(false);
const selectedField = ref('');

// Computed property for all questions combined
const allQuestions = computed(() => ({
  gender: genderQuestions.gender,
  age: ageQuestions.age,
  mainReason: mainReasonQuestions.mainReason,
  bestDescription: storyTypeQuestions.bestDescription,
  otherBestDescription: storyTypeQuestions.otherBestDescription,
  bookOrder: storyStructureQuestions.bookOrder,
  bookFocused: storyStructureQuestions.bookFocused,
  description: descriptionQuestions.description,
  keyLifeEvents: keyEventsQuestions.keyLifeEvents,
  readersTakeaway: readersTakeawayQuestions.readersTakeaway,
  authorImpact: authorImpactQuestions.authorImpact,
}));

// Only emit updates when internal data changes (not when props change)
let isUpdatingFromProps = false;

// Watch props and update internal state without causing loops
watch(
  () => props.questions,
  (newQuestions) => {
    isUpdatingFromProps = true;

    genderQuestions.gender = newQuestions.gender;
    ageQuestions.age = [...newQuestions.age];
    mainReasonQuestions.mainReason = newQuestions.mainReason;
    storyTypeQuestions.bestDescription = newQuestions.bestDescription;
    storyTypeQuestions.otherBestDescription = newQuestions.otherBestDescription;
    storyStructureQuestions.bookOrder = newQuestions.bookOrder;
    storyStructureQuestions.bookFocused = newQuestions.bookFocused;
    descriptionQuestions.description = newQuestions.description;
    keyEventsQuestions.keyLifeEvents = newQuestions.keyLifeEvents;
    readersTakeawayQuestions.readersTakeaway = newQuestions.readersTakeaway;
    authorImpactQuestions.authorImpact = newQuestions.authorImpact;

    setTimeout(() => {
      isUpdatingFromProps = false;
    }, 0);
  },
  { immediate: true, deep: true },
);

// Watch for changes in questions and emit updates (only when not updating from props)
watch(
  () => allQuestions.value,
  (newQuestions) => {
    if (!isUpdatingFromProps) {
      emit('update:questions', newQuestions);
    }
  },
  { deep: true },
);

// Navigation
const nextStep = () => {
  if (currentStep.value < totalSteps) {
    currentStep.value++;
  }
};

const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
  } else {
    emit('update:show', false);
  }
};

// Transcription handling
const handleTranscription = (field: string) => {
  selectedField.value = field;
  transcriptionDialog.value = true;
};

const completedTranscript = (data: any) => {
  const content = data.content;
  switch (selectedField.value) {
    case 'mainReason':
      mainReasonQuestions.mainReason = content;
      break;
    case 'otherBestDescription':
      storyTypeQuestions.otherBestDescription = content;
      break;
    case 'bookFocused':
      storyStructureQuestions.bookFocused = content;
      break;
    case 'description':
      descriptionQuestions.description = content;
      break;
    case 'keyLifeEvents':
      keyEventsQuestions.keyLifeEvents = content;
      break;
    case 'readersTakeaway':
      readersTakeawayQuestions.readersTakeaway = content;
      break;
    case 'authorImpact':
      authorImpactQuestions.authorImpact = content;
      break;
  }
  transcriptionDialog.value = false;
};

// AI Generation functions (simplified versions)
async function generateKeyLifeEvents() {
  if (keyEventsQuestions.keyLifeEvents && giveNotice.value) {
    const shouldDelete = await confirmOverrideText($q);
    if (!shouldDelete) return;
  }

  loadingKeyLifeEvents.value = true;
  try {
    const prompt = `Based on the faith story details provided, generate 3-5 key life events or spiritual moments that would be compelling to include in this faith story:

    Main Reason: ${mainReasonQuestions.mainReason}
    Story Type: ${
      storyTypeQuestions.otherBestDescription ||
      storyTypeQuestions.bestDescription
    }
    Spiritual Background: ${storyStructureQuestions.bookFocused}
    Target Audience: ${descriptionQuestions.description}

    Please provide specific, meaningful life events that would resonate with the target audience.`;

    const response = await composeText(prompt);
    keyEventsQuestions.keyLifeEvents = response;
  } catch (e) {
    console.error(e);
    $q.notify({ color: 'negative', message: 'Error generating content: ' + e });
  } finally {
    giveNotice.value = true;
    loadingKeyLifeEvents.value = false;
  }
}

async function generateReadersTakeaway() {
  if (readersTakeawayQuestions.readersTakeaway && giveNotice.value) {
    const shouldDelete = await confirmOverrideText($q);
    if (!shouldDelete) return;
  }

  loadingReadersTakeaway.value = true;
  try {
    const prompt = `Based on the faith story details, generate what readers should take away from this story:

    Main Reason: ${mainReasonQuestions.mainReason}
    Story Type: ${
      storyTypeQuestions.otherBestDescription ||
      storyTypeQuestions.bestDescription
    }
    Key Events: ${keyEventsQuestions.keyLifeEvents}
    Target Audience: ${descriptionQuestions.description}

    Focus on transformation, hope, and spiritual growth that readers can apply to their own lives.`;

    const response = await composeText(prompt);
    readersTakeawayQuestions.readersTakeaway = response;
  } catch (e) {
    console.error(e);
    $q.notify({ color: 'negative', message: 'Error generating content: ' + e });
  } finally {
    giveNotice.value = true;
    loadingReadersTakeaway.value = false;
  }
}

async function generateAuthorImpact() {
  if (authorImpactQuestions.authorImpact && giveNotice.value) {
    const shouldDelete = await confirmOverrideText($q);
    if (!shouldDelete) return;
  }

  loadingAuthorImpact.value = true;
  try {
    const prompt = `Based on the faith story details, generate 2-3 things this book should do for the author:

    Main Reason: ${mainReasonQuestions.mainReason}
    Story Type: ${
      storyTypeQuestions.otherBestDescription ||
      storyTypeQuestions.bestDescription
    }
    Reader Impact: ${readersTakeawayQuestions.readersTakeaway}

    Focus on personal healing, ministry impact, legacy building, and spiritual growth for the author.`;

    const response = await composeText(prompt);
    authorImpactQuestions.authorImpact = response;
  } catch (e) {
    console.error(e);
    $q.notify({ color: 'negative', message: 'Error generating content: ' + e });
  } finally {
    giveNotice.value = true;
    loadingAuthorImpact.value = false;
  }
}

const completeFaithStory = () => {
  emit('complete');
};
</script>
