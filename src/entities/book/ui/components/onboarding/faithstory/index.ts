// /home/<USER>/Documents/manuscriptr/app/src/entities/book/ui/components/onboarding/faithstory/index.ts (updated)
export { default as FaithStoryMissionFlow } from './FaithStoryMissionFlow.vue';
export { default as NewFaithStoryFlow } from './NewFaithStoryFlow.vue';

// Individual step components
export { default as FaithTargetAudienceStep } from './steps/FaithTargetAudienceStep.vue';
export { default as MainReasonStep } from './steps/MainReasonStep.vue';
export { default as StoryTypeStep } from './steps/StoryTypeStep.vue';
export { default as StoryStructureStep } from './steps/StoryStructureStep.vue';
export { default as TargetDescriptionStep } from './steps/TargetDescriptionStep.vue';
export { default as KeyEventsStep } from './steps/KeyEventsStep.vue';
export { default as ReadersTakeawayStep } from './steps/ReadersTakeawayStep.vue';
export { default as FaithAuthorImpactStep } from './steps/FaithAuthorImpactStep.vue';
export { default as FaithStoryTitleSubtitleStep } from './steps/FaithStoryTitleSubtitleStep.vue';
export { default as FaithStoryChaptersStep } from './steps/FaithStoryChaptersStep.vue';

// Legacy components for backward compatibility
export { default as FaithStoryMissionStep } from './FaithStoryMissionStep.vue';
