<template>
  <div class="q-gutter-y-md column">
    <q-btn
      label="Generate Outline"
      icon="img:robot.png"
      color="primary"
      @click="generateChapterOutline"
      :disable="isLoadingChapter || readonly"
    >
      <q-tooltip>Let <PERSON> Create a Chapter Outline</q-tooltip>
    </q-btn>
    <q-editor
      v-model="selectedChapterOutline.outline"
      :disable="isLoadingChapter === true"
      :toolbar="toolbarItems"
      min-height="15em"
      paragraph-tag="p"
      height="calc(100vh - 340px)"
      @update:model-value="saveChapter"
    />
  </div>
</template>

<script lang="ts" setup>
import { confirmOverrideText } from 'src/shared/lib/quasar-dialogs';
import {
  type Book,
  ChapterOutline,
  BookOutlines,
  updateOutline,
} from 'src/entities/book';
import { useVModels } from '@vueuse/core';
import { useQuasar } from 'quasar';
import { onOpenAIButtons } from 'src/features/book-prompts';
import { markdownToHtmlConverter } from 'src/features/book-markdown';
import { watch } from 'vue';

const props = defineProps<{
  book: Book;
  selectedChapterOutline: ChapterOutline;
  onMannyLoading: boolean;
  outlines: BookOutlines;
  selectedChapterId: string;
  sideView: string;
  isLoadingChapter: boolean;
  readonly?: boolean;
}>();

const emits = defineEmits<{
  'update:onMannyLoading': [onMannyLoading: boolean];
  'update:selectedChapterId': [id: string];
  'update:isLoadingChapter': [isLoadingChapter: boolean];
  'update:selectedChapterOutline': [selectedChapterOutline: ChapterOutline];
  'update:book': [book: Book];
}>();

const {
  selectedChapterId,
  book,
  isLoadingChapter,
  selectedChapterOutline,
  onMannyLoading,
  outlines,
  sideView,
} = useVModels(props, emits);

const $q = useQuasar();

async function generateChapterOutline() {
  onMannyLoading.value = false;
  const chapter: ChapterOutline = selectedChapterOutline.value;
  isLoadingChapter.value = true;

  const chapterTitle = ['introduction', 'conclusion'].includes(chapter.id)
    ? chapter.id
    : chapter.title;
  const chapterOutline = chapter.outline;
  let chapterIdx = -1;
  let nextChapterTitle: string = '';
  if (chapter.id === 'introduction') {
    nextChapterTitle = outlines.value.outlines[0]?.title;
  } else if (chapter.id === 'conclusion') {
    // do nothing
  } else {
    chapterIdx = outlines.value.outlines.findIndex(
      (ch) => ch.id === chapter.id,
    );
    if (chapterIdx != -1) {
      if (chapterIdx + 1 < outlines.value.outlines?.length) {
        nextChapterTitle = outlines.value.outlines[chapterIdx + 1].title;
      } else {
        nextChapterTitle = outlines.value.conclusion.title;
      }
    }
  }

  if (chapterOutline && selectedChapterOutline.length) {
    const confirm = await confirmOverrideText($q);
    if (!confirm) {
      isLoadingChapter.value = false;
      onMannyLoading.value = false;
      return;
    }
  }

  const prompt = `Write a chapter outline given the following book details:
    Title: "${book.value.title}"
    Subtitle: "${book.value.subtitle}"
    Chapter Title: "${chapterTitle}"
    Following Chapter Title: "${nextChapterTitle ?? ''}"
    Follow the guidelines below:
    1. Start with a hook that sets the chapter up (for example: a story, Statistics, Shocking Facts, Strong Statement, Interesting Question, Metaphor, Quotation etc.). Format should be Hook: Chapter Title. Use heading4 and paragraph for the content and add a horizontal line.
    2. Insert the text: “Placeholder Story”. Use heading4 and add a horizontal line.
    3. Create 3 main points for the chapter. (Format should be Main Point 1: Point Use heading4). Each point should have 3-5 points of content (Format should be unordered list.).
    4. After the 3 main points Insert the text: “Placeholder Story”. Use heading4 and add a horizontal line.
    5. Insert "Next Steps Placeholder ". Use heading4 and add a horizontal line.
    6. End with segue that transitions to the next chapter (using Following Chapter Title). Format should be Segue: The Next Chapter Title. Use heading4 and paragraph for the content and add a horizontal line.
    Include everything a writer who doesn't know about this topic would need to be able to write a great book. Ensure the response is in html format, with no Markdown.`;

  const response = await onOpenAIButtons('outline', prompt, book.value.id);
  // response = markdownToHtmlConverter(response);

  selectedChapterOutline.value.outline = response;
  await saveChapter();
  isLoadingChapter.value = false;
  onMannyLoading.value = false;
}

const toolbarItems = [
  [
    'bold',
    'italic',
    'underline',
    'unordered',
    'outdent',
    'indent',
    'undo',
    'redo',
  ],
];

// saving the outline
async function saveChapter() {
  await updateOutline(book.value.id, selectedChapterOutline.value.id, {
    outline: selectedChapterOutline.value.outline,
  });
}

watch(
  onMannyLoading,
  (currentOnGeneratingOutline) => {
    if (currentOnGeneratingOutline === true && sideView.value === 'outline')
      generateChapterOutline();
  },
  { immediate: true },
);
</script>

<style lang="scss"></style>
