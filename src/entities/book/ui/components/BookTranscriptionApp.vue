<template>
  <q-card
    class="transcription-app"
    :class="{ 'transcription-app--dark': $q.dark.isActive }"
    style="
      max-width: 100vw;
      max-height: 90vh;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    "
  >
    <!-- Modern Header -->
    <q-card-section class="app-header">
      <div class="header-content">
        <div class="robot-avatar">
          <q-img src="robot.png" width="40px" />
          <div class="pulse-ring" v-if="isRecording"></div>
        </div>
        <div class="header-text">
          <div class="title">{{ headerTitle }}</div>
          <div class="subtitle" v-if="isRecording">
            <span class="recording-indicator">
              <span></span>
              <span></span>
              <span></span>
            </span>
            Recording in progress...
          </div>
          <div class="subtitle" v-else-if="validating">
            <span class="typing-dots">
              <span></span>
              <span></span>
              <span></span>
            </span>
            Preparing device...
          </div>
          <div
            class="subtitle success text-white"
            v-else-if="transcriptionText.length > 0"
          >
            <q-icon name="check_circle" size="16px" class="q-mr-xs" />
            Transcription ready
          </div>
          <div class="subtitle" v-else>Ready to record</div>
        </div>
        <div class="header-actions">
          <q-btn
            flat
            round
            size="sm"
            v-if="transcriptionText.length > 0"
            :icon="showTranscription ? 'visibility_off' : 'visibility'"
            @click="showTranscription = !showTranscription"
            class="header-btn"
            color="white"
          >
            <q-tooltip>{{
              showTranscription ? 'Hide Text' : 'Show Text'
            }}</q-tooltip>
          </q-btn>
          <!-- Settings Toggle -->
          <q-btn
            flat
            round
            size="sm"
            icon="settings"
            @click="toggleSettings"
            v-if="!isRecording"
            class="header-btn"
            color="white"
          >
            <q-tooltip>Mic Settings</q-tooltip>
          </q-btn>
          <!-- Close Button -->
          <q-btn
            flat
            round
            size="sm"
            icon="close"
            @click="emit('close')"
            class="header-btn"
            color="white"
          />
        </div>
      </div>
    </q-card-section>

    <!-- Compact Settings Panel -->
    <q-slide-transition>
      <q-card-section v-show="showSettings" class="settings-panel">
        <div class="settings-content">
          <div class="settings-header">
            <q-icon name="mic" size="18px" class="q-mr-xs" />
            <span class="settings-title">Microphone Settings</span>
          </div>
          <q-select
            v-model="selectedMicrophone"
            :options="microphoneOptions"
            label="Select Microphone"
            option-value="deviceId"
            option-label="label"
            emit-value
            map-options
            outlined
            dense
            class="settings-select"
            :disable="!isBrowserSupported || microphoneOptions.length === 0"
          >
            <template #prepend>
              <q-icon name="mic" />
            </template>
          </q-select>
        </div>
      </q-card-section>
    </q-slide-transition>

    <!-- Error State -->
    <q-card-section v-if="!isBrowserSupported" class="error-section">
      <div class="error-content">
        <q-icon name="error" size="48px" color="negative" />
        <div class="error-text">
          <div class="error-title">Browser Not Supported</div>
          <div class="error-message">
            Your browser doesn't support microphone access or no microphone is
            available.
          </div>
        </div>
      </div>
    </q-card-section>

    <!-- Recording Interface -->
    <q-card-section
      v-else
      class="recording-section"
      style="flex: 1; display: flex; flex-direction: column; overflow: hidden"
    >
      <!-- Compact Recording Controls -->
      <q-slide-transition>
        <div class="recording-controls" v-show="!showTranscription">
          <div>
            <!-- Audio Visualization -->
            <div class="visualization-container" v-if="isRecording">
              <AVMedia
                :line-color="$q.dark.isActive ? '#90cdf4' : '#667eea'"
                audio-controls
                :media="mediaStream"
                type="line"
                :canv-height="40"
                :canv-width="$q.screen.gt.sm ? 300 : 200"
                :canv-fill-color="$q.dark.isActive ? '#1e293b' : '#f1f5f9'"
                class="audio-visualizer"
              />
            </div>

            <!-- Ready State Message -->
            <div v-else class="ready-message">
              <q-icon name="mic" size="32px" color="primary" />
              <div class="ready-text">
                {{ validating ? 'Preparing device...' : 'Ready to record' }}
              </div>
            </div>
          </div>

          <!-- Control Buttons -->
          <div class="control-buttons">
            <q-btn
              :icon="!isRecording ? 'mic' : 'stop'"
              :color="isRecording ? 'negative' : 'primary'"
              @click="isRecording ? stopTranscription() : startTranscription()"
              :disable="validating || loadingFixGrammar"
              :loading="validating"
              size="lg"
              round
              class="record-btn"
            >
              <q-tooltip>{{
                isRecording ? 'Stop Recording' : 'Start Recording'
              }}</q-tooltip>
            </q-btn>
          </div>
        </div>
      </q-slide-transition>

      <!-- Modern Transcription Display -->
      <q-slide-transition>
        <div v-show="showTranscription" class="transcription-container">
          <!-- Live Transcription with Typing Effect -->
          <div v-if="isRecording" class="live-transcription">
            <div class="transcription-header">
              <q-btn
                size="12px"
                :icon="!isRecording ? 'mic' : 'stop'"
                :color="isRecording ? 'negative' : 'primary'"
                @click="
                  isRecording ? stopTranscription() : startTranscription()
                "
                :disable="validating || loadingFixGrammar"
                :loading="validating"
                round
                class="record-btn small"
              >
                <q-tooltip>{{
                  isRecording ? 'Stop Recording' : 'Start Recording'
                }}</q-tooltip>
              </q-btn>

              <span class="transcription-title">Live Transcription</span>
              <div class="live-indicator">
                <span class="live-dot"></span>
                LIVE
              </div>
            </div>
            <div class="transcription-content live">
              <div class="transcription-text" ref="liveContainer">
                <div v-html="displayedTranscription"></div>
                <span
                  class="modern-cursor"
                  v-if="displayedTranscription"
                ></span>
              </div>
              <div v-if="!displayedTranscription" class="waiting-message">
                <span class="typing-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </span>
                Listening for speech...
              </div>
            </div>
          </div>

          <!-- Editable Transcription -->
          <div v-else-if="transcriptionText.length" class="edit-transcription">
            <div class="transcription-header">
              <q-btn
                size="12px"
                :icon="!isRecording ? 'mic' : 'stop'"
                :color="isRecording ? 'negative' : 'primary'"
                @click="
                  isRecording ? stopTranscription() : startTranscription()
                "
                :disable="validating || loadingFixGrammar"
                :loading="validating"
                round
                class="record-btn small"
              >
                <q-tooltip>{{
                  isRecording ? 'Stop Recording' : 'Start Recording'
                }}</q-tooltip>
              </q-btn>

              <span class="transcription-title">Transcription</span>
              <div class="live-indicator">
                <span class="live-dot editing"></span>
                Editing
              </div>
            </div>
            <div class="transcription-content">
              <q-input
                v-model="transcriptionText"
                type="textarea"
                placeholder="Your transcription will appear here..."
                outlined
                autogrow
                :disable="loadingFixGrammar"
                class="transcription-input"
                :input-style="{
                  minHeight: '120px',
                  fontSize: '15px',
                  lineHeight: '1.6',
                }"
              />
            </div>
          </div>
        </div>
      </q-slide-transition>
    </q-card-section>

    <!-- Fixed Action Buttons at Bottom -->
    <q-card-actions
      v-if="transcriptionText.length > 0 && !isRecording"
      class="action-buttons-fixed"
    >
      <q-btn
        flat
        size="sm"
        label="Clear"
        :color="$q.dark.isActive ? 'white' : 'grey-8'"
        @click="clearText"
        icon="backspace"
        class="action-btn"
        :disable="loadingFixGrammar || isRecording"
        v-if="showTranscription"
      >
        <q-tooltip>Clear Transcription</q-tooltip>
      </q-btn>

      <q-space />

      <q-btn
        flat
        size="sm"
        label="Fix Grammar"
        :color="$q.dark.isActive ? 'white' : 'primary'"
        @click="fixGrammarUsingManny"
        :loading="loadingFixGrammar"
        :disable="loadingFixGrammar || isRecording"
        icon="img:robot.png"
        class="action-btn"
        v-if="showTranscription"
      >
        <q-tooltip>Fix Grammar using Manny</q-tooltip>
      </q-btn>

      <q-btn
        unelevated
        label="Use Transcription"
        color="primary"
        @click="useTranscription"
        :disable="!canUseContent || isRecording || loadingFixGrammar"
        icon="check"
        class="use-btn"
      >
        <q-tooltip>Use Transcription in Book</q-tooltip>
      </q-btn>
    </q-card-actions>
  </q-card>
</template>

<script setup lang="ts">
import {
  ref,
  watch,
  onUnmounted,
  onBeforeUnmount,
  nextTick,
  computed,
} from 'vue';
import RecordRTC, { StereoAudioRecorder } from 'recordrtc';
import { getSpeechRecognitionToken } from 'src/shared/api/speech';
import { composeText } from 'src/shared/api/openai';
import { useQuasar } from 'quasar';
import { AVMedia } from 'vue-audio-visual';
import { confirmOverrideText } from 'src/shared/lib/quasar-dialogs';

interface MediaDevice {
  deviceId: string;
  label: string;
}
interface MessageBase {
  audio_start: number;
  text: string;
  message_type: string;
}
interface MessagePartial extends MessageBase {
  message_type: 'PartialTranscript';
}
interface MessageFinal extends MessageBase {
  message_type: 'FinalTranscript';
  punctuated: boolean;
  text_formatted: boolean;
}
type TranscriptionMessage = MessagePartial | MessageFinal;

const $q = useQuasar();
const validating = ref<boolean>(false);
const selectedMicrophone = ref<string | null>(null);
const microphoneOptions = ref<MediaDevice[]>([]);
const isBrowserSupported = ref<boolean>(true);
const isRecording = ref<boolean>(false);
const canUseContent = ref<boolean>(false);
const loadingFixGrammar = ref<boolean>(false);
const transcriptionText = ref<string>('');
let socket: WebSocket | null = null;
let recorder: RecordRTC | null = null;
const showSettings = ref(false);
const audioBlobUrl = ref('');
const audioElement = ref<HTMLAudioElement | null>(null);
const displayText = ref('');
const fullText = ref('');
const typingIndex = ref(0);
const typingSpeed = 25; // milliseconds per character - smoother timing
const mediaStream = ref<MediaStream | null>(null);
const showTranscription = ref<boolean>(false);

// Modern typing effect variables
const displayedTranscription = ref('');
const liveContainer = ref<HTMLElement>();
let typingQueue: string[] = [];
let isTyping = ref(false);
let typingTimeout: NodeJS.Timeout | null = null;

// Computed properties for modern UI
const headerTitle = computed(() => {
  if (isRecording.value) return 'Recording Audio';
  if (validating.value) return 'Preparing Device';
  if (transcriptionText.value.length > 0) return 'Transcription Complete';
  return 'Real-Time Transcription';
});

const emit = defineEmits<{
  completed: [content: string];
  close?: [];
}>();

// Smooth real-time typing effect - only animate new content
const processTypingQueue = () => {
  if (typingQueue.length === 0 || isTyping.value) {
    return;
  }

  isTyping.value = true;
  const newContent = typingQueue[typingQueue.length - 1]; // Get the latest content
  typingQueue = []; // Clear the queue

  // Find what's actually new by comparing with current content
  const currentContent = displayedTranscription.value;

  // If new content is shorter or same, just update without animation
  if (newContent.length <= currentContent.length) {
    displayedTranscription.value = newContent;
    isTyping.value = false;
    return;
  }

  // Find the new part that needs to be typed
  const newPart = newContent.substring(currentContent.length);

  if (!newPart.trim()) {
    displayedTranscription.value = newContent;
    isTyping.value = false;
    return;
  }

  // Use requestAnimationFrame for smoother animation
  let newCharIndex = 0;
  let lastTime = 0;

  const typeNextChar = (currentTime: number) => {
    if (currentTime - lastTime >= typingSpeed) {
      if (newCharIndex < newPart.length) {
        displayedTranscription.value =
          currentContent + newPart.substring(0, newCharIndex + 1);
        newCharIndex++;
        lastTime = currentTime;

        // Auto-scroll to bottom smoothly
        nextTick(() => {
          if (liveContainer.value) {
            liveContainer.value.scrollTop = liveContainer.value.scrollHeight;
          }
        });
      }
    }

    if (newCharIndex < newPart.length) {
      requestAnimationFrame(typeNextChar);
    } else {
      isTyping.value = false;
      // Process any new content that arrived while typing
      if (typingQueue.length > 0) {
        setTimeout(processTypingQueue, 10);
      }
    }
  };

  requestAnimationFrame(typeNextChar);
};

const addToTypingQueue = (content: string) => {
  typingQueue.push(content);
  if (!isTyping.value) {
    processTypingQueue();
  }
};

// Legacy typing function for compatibility
const typeCharacter = async () => {
  while (typingIndex.value < fullText.value.length) {
    displayText.value += fullText.value[typingIndex.value];
    typingIndex.value++;
    await new Promise((resolve) => setTimeout(resolve, typingSpeed));
  }
};

const canvasWidth = ref<number>(500);
const container = ref<HTMLElement | null>(null);
const typewriterContainer = ref<HTMLElement | null>(null);

// Cleanup
onUnmounted(async () => {
  await nextTick();
  cleanupTyping();
  updateCanvasWidth();
  scrollToBottom();
  if (audioBlobUrl.value) URL.revokeObjectURL(audioBlobUrl.value);
  if (audioElement.value) audioElement.value = null;

  window.removeEventListener('resize', updateCanvasWidth);
});

const scrollToBottom = () => {
  if (typewriterContainer.value) {
    // Smooth scroll (optional)
    typewriterContainer.value.scrollTop =
      typewriterContainer.value.scrollHeight;
  }
};
const updateCanvasWidth = () => {
  if (container.value) {
    canvasWidth.value = container.value.offsetWidth;
  }
};

// Optional: Watch for recording state change
watch(
  () => isRecording.value,
  (newVal) => {
    if (newVal) {
      // Wait for DOM to update before accessing container
      setTimeout(() => {
        updateCanvasWidth();
      }, 0);
    }
  },
);
watch(
  () => transcriptionText.value,
  () => {
    // Use nextTick to wait for DOM update
    setTimeout(() => {
      scrollToBottom();
    }, 0);
  },
);

onBeforeUnmount(() => {
  window.removeEventListener('resize', updateCanvasWidth);
});
// Existing functions remain unchanged
const useTranscription = () => {
  emit('completed', { content: transcriptionText.value });
  transcriptionText.value = '';
  displayedTranscription.value = '';
  showTranscription.value = false;
};

const requestMicrophoneAccess = async (): Promise<void> => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    stream.getTracks().forEach((track) => track.stop());
    await fetchMicrophones();
  } catch (error) {
    console.warn('Microphone permission denied or not available.', error);
    isBrowserSupported.value = false;
    microphoneOptions.value = [];
  }
};

const fetchMicrophones = async (): Promise<void> => {
  try {
    const devices = await navigator.mediaDevices.enumerateDevices();
    const audioInputDevices = devices.filter(
      (device) => device.kind === 'audioinput',
    );
    microphoneOptions.value = audioInputDevices.map((device, index) => ({
      deviceId: device.deviceId,
      label: device.label || `Microphone ${index + 1}`,
    }));
    if (microphoneOptions.value.length > 0 && !selectedMicrophone.value) {
      selectedMicrophone.value = microphoneOptions.value[0].deviceId;
    }
    isBrowserSupported.value = true;
  } catch (error) {
    console.error('Error enumerating devices:', error);
    isBrowserSupported.value = false;
  }
};

const ensureValidSelectedMic = () => {
  // Implementation remains unchanged
};

// Start transcription with waveform
const startTranscription = async (): Promise<void> => {
  canUseContent.value = false;
  validating.value = true;
  if (!selectedMicrophone.value) {
    isBrowserSupported.value = false;
    return;
  } else {
    isBrowserSupported.value = true;
  }
  try {
    const token = await getSpeechRecognitionToken();
    socket = new WebSocket(
      `wss://api.assemblyai.com/v2/realtime/ws?sample_rate=16000&token=${token}`,
    );

    socket.onerror = (event) => {
      console.error('WebSocket error:', event);
      socket?.close();
      socket = null;
      $q.notify({
        message: 'An error has occurred. Please try again.',
        color: 'negative',
        timeout: 3000,
        icon: 'error',
      });
      isRecording.value = false;
      validating.value = false;
    };

    socket.onclose = () => {
      console.log('WebSocket closed');
      socket = null;
      isRecording.value = false;
      validating.value = false;
    };

    let texts: Record<number, string> = {};

    socket.onmessage = async ({ data }) => {
      if (!socket) return;

      const res = JSON.parse(data) as TranscriptionMessage;
      texts[res.audio_start] = res.text;

      const sorted = Object.keys(texts)
        .map(Number)
        .sort((a, b) => a - b)
        .map((key) => texts[key])
        .join(' ');

      // Show partial transcripts in real-time with typing effect
      // if (res.message_type === 'PartialTranscript') {
      //
      // }

      if (res.message_type === 'FinalTranscript') {
        // Combine existing transcription with current partial text
        const fullLiveText = transcriptionText.value
          ? `${transcriptionText.value} ${sorted}`
          : sorted;

        addToTypingQueue(fullLiveText);

        // Add final transcript to the main transcription
        const finalText = sorted.trim();
        if (finalText) {
          transcriptionText.value =
            `${transcriptionText.value.trim()} ${finalText}`.trim();
          // Update live display to show the complete transcription
          displayedTranscription.value = transcriptionText.value;
          delete texts[res.audio_start];
        }
      }
    };

    socket.onopen = async () => {
      try {
        let audioConstraints: MediaStreamConstraints = {
          audio: {
            deviceId: selectedMicrophone.value
              ? { exact: selectedMicrophone.value }
              : undefined,
          },
        };
        let currentStream: MediaStream;
        try {
          currentStream = await navigator.mediaDevices.getUserMedia(
            audioConstraints,
          );

          mediaStream.value = currentStream;
        } catch (error: any) {
          if (error.name === 'OverconstrainedError') {
            console.warn(
              'Selected mic not found. Falling back to default mic.',
            );
            audioConstraints.audio = true; // fallback to any available mic
            currentStream = await navigator.mediaDevices.getUserMedia(
              audioConstraints,
            );
          } else {
            throw error;
          }
        }

        recorder = new RecordRTC(currentStream, {
          type: 'audio',
          mimeType: 'audio/webm;codecs=pcm',
          recorderType: StereoAudioRecorder,
          timeSlice: 250,
          desiredSampRate: 16000,
          numberOfAudioChannels: 1,
          bufferSize: 4096,
          audioBitsPerSecond: 128000,
          ondataavailable: async (blob) => {
            const base64data = await readAsDataURL(blob);
            socket?.send(
              JSON.stringify({
                audio_data: base64data.split('base64,')[1],
              }),
            );
          },
        });

        validating.value = false;
        isRecording.value = true;
        showSettings.value = false;
        recorder.startRecording();
      } catch (error) {
        isRecording.value = false;
        validating.value = false;
        console.error('Error setting up recorder:', error);
        socket?.close();
        socket = null;
        $q.notify({
          message: 'An error has occurred. Please try again.',
          color: 'negative',
          timeout: 3000,
          icon: 'error',
        });
      }
    };
  } catch (err) {
    isRecording.value = false;
    validating.value = false;
    socket = null;
    console.error('Failed to start transcription:', err);
  }
};

// Cleanup typing effects
const cleanupTyping = () => {
  if (typingTimeout) {
    clearTimeout(typingTimeout);
    typingTimeout = null;
  }
  isTyping.value = false;
  typingQueue = [];
  // Cancel any pending animation frames
  if (typeof window !== 'undefined') {
    // Note: We don't store the animation frame ID, but setting isTyping to false
    // will stop the animation loop naturally
  }
};

// Stop transcription and visualization
const stopTranscription = (): void => {
  recorder?.stopRecording();
  recorder?.destroy();
  recorder = null;

  socket?.send(JSON.stringify({ terminate_session: true }));
  socket?.close();
  socket = null;

  // Clean up typing effects
  cleanupTyping();

  // Ensure live display shows the final transcription
  if (transcriptionText.value.length > 0) {
    displayedTranscription.value = transcriptionText.value;
    canUseContent.value = true;
  } else {
    canUseContent.value = false;
  }

  isRecording.value = false;
};

const fixGrammarUsingManny = async (): Promise<string> => {
  try {
    const confirm = await confirmOverrideText($q, {
      title: 'Fix Grammar',
      message:
        'Would you like us to send your transcription to Manny for a quick grammar update?',
    });
    if (!confirm) return;
    loadingFixGrammar.value = true;
    const promptRequest = `Please correct the grammar, punctuation, spelling, and any other language-related issues in the following text: ${transcriptionText.value}.`;
    transcriptionText.value = await composeText(promptRequest);
  } catch (e) {
    console.error(e);
    $q.notify({
      message: 'An error has occurred. Please try again.',
      color: 'negative',
      timeout: 3000,
      icon: 'error',
    });
  } finally {
    loadingFixGrammar.value = false;
  }
};

const clearText = async () => {
  const confirmed = await confirmOverrideText($q, {
    title: 'Clear Transcription',
    message: 'Are you sure you want to clear the transcription text?',
  });
  if (!confirmed) return;
  transcriptionText.value = '';
  displayedTranscription.value = '';
  showTranscription.value = false;
};

const readAsDataURL = (blob: Blob): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
};

if (!navigator.mediaDevices?.getUserMedia) {
  isBrowserSupported.value = false;
} else {
  requestMicrophoneAccess();
}

navigator.mediaDevices.addEventListener('devicechange', async () => {
  console.log('Device list changed');
  await fetchMicrophones();
  ensureValidSelectedMic();
});

const toggleSettings = () => {
  showSettings.value = !showSettings.value;
};
</script>

<style scoped lang="scss">
/* Modern Transcription App Styles */
.transcription-app {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 500px;
  max-width: 90vw;
  background: var(--q-bg-secondary, #fafafa);
  color: var(--q-text-color);
}

.transcription-app--dark {
  background: var(--q-dark-page);
}

/* Header Styles */
.app-header {
  background: var(--q-primary);
  color: white;
  padding: 16px 20px;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.robot-avatar {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pulse-ring {
  position: absolute;
  width: 52px;
  height: 52px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

.header-text {
  flex: 1;
}

.title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 2px;
}

.subtitle {
  font-size: 13px;
  opacity: 0.9;
  display: flex;
  align-items: center;
}

.subtitle.success {
  color: white;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.header-btn {
  color: rgba(255, 255, 255, 0.8);
}

.header-btn:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
}

/* Recording Indicator */
.recording-indicator {
  display: inline-flex;
  gap: 2px;
  margin-right: 8px;
}

.recording-indicator span {
  width: 4px;
  height: 4px;
  background-color: var(--q-negative);
  border-radius: 50%;
  animation: recording-pulse 1.4s infinite ease-in-out;
}

.recording-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}
.recording-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes recording-pulse {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Typing Dots */
.typing-dots {
  display: inline-flex;
  gap: 2px;
  margin-right: 8px;
}

.typing-dots span {
  width: 4px;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}
.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Settings Panel */
.settings-panel {
  background: var(--q-bg-tertiary, #f8fafc);
  border-top: 1px solid var(--q-separator-color);
  flex-shrink: 0;
}

body.body--dark .settings-panel {
  background: var(--q-dark-secondary);
}

.settings-content {
  padding: 16px 20px;
}

.settings-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  color: var(--q-text-secondary);
  font-weight: 500;
  font-size: 14px;
}

.settings-select {
  margin-top: 8px;
}

/* Error Section */
.error-section {
  padding: 40px 20px;
  text-align: center;
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.error-text {
  max-width: 300px;
}

.error-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--q-text-color);
  margin-bottom: 8px;
}

.error-message {
  font-size: 14px;
  color: var(--q-text-secondary);
  line-height: 1.5;
}

/* Recording Section */
.recording-section {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  min-height: 0; /* Important for flex child to shrink */
}

.recording-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 20px 0;
}

.visualization-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60px;
}

.audio-visualizer {
  border-radius: 8px;
  background: var(--q-bg-secondary);
  padding: 10px;
}

body.body--dark .audio-visualizer {
  background: var(--q-dark-secondary);
}

.ready-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 20px;
  text-align: center;
}

.ready-text {
  font-size: 16px;
  color: var(--q-text-secondary);
  font-weight: 500;
}

.control-buttons {
  display: flex;
  gap: 16px;
  align-items: center;
}

.record-btn {
  width: 64px;
  height: 64px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
  &.small {
    width: 24px;
    height: 24px;
  }
}

body.body--dark .record-btn {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.record-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

body.body--dark .record-btn:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

.toggle-btn {
  width: 48px;
  height: 48px;
}

/* Transcription Container */
.transcription-container {
}

.transcription-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: var(--q-bg-tertiary, #f8fafc);
  border-radius: 8px 8px 0 0;
  border: 1px solid var(--q-separator-color);
  border-bottom: none;
}

body.body--dark .transcription-header {
  background: var(--q-dark-secondary);
}

.transcription-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--q-text-color);
}

.live-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 600;
  color: var(--q-negative);
}

.live-dot {
  width: 8px;
  height: 8px;
  background-color: var(--q-negative);
  border-radius: 50%;
  animation: live-pulse 2s infinite;
}

.live-dot.editing {
  background-color: var(--q-positive);
}

@keyframes live-pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}

.transcription-content {
  border: 1px solid var(--q-separator-color);
  border-radius: 0 0 8px 8px;
  background: var(--q-card-bg);
  min-height: 120px;
}

.transcription-content.live {
  background: var(--q-bg-secondary, #fafafa);
}

body.body--dark .transcription-content.live {
  background: var(--q-dark-secondary);
}

.transcription-text {
  padding: 16px;
  font-family:
    'Inter',
    -apple-system,
    BlinkMacSystemFont,
    sans-serif;
  font-size: 15px;
  line-height: 1.6;
  color: var(--q-text-color);
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 200px;
  overflow-y: auto;
  scroll-behavior: smooth;
  transition: all 0.1s ease;
}

.modern-cursor {
  display: inline-block;
  width: 2px;
  height: 20px;
  background: var(--q-primary);
  margin-left: 2px;
  animation: modernBlink 1.2s ease-in-out infinite;
  vertical-align: text-bottom;
  border-radius: 1px;
  transition: all 0.1s ease;
}

@keyframes modernBlink {
  0%,
  45% {
    opacity: 1;
    transform: scaleY(1);
  }
  50%,
  95% {
    opacity: 0.3;
    transform: scaleY(0.8);
  }
  100% {
    opacity: 1;
    transform: scaleY(1);
  }
}

.waiting-message {
  padding: 40px 16px;
  text-align: center;
  color: var(--q-text-secondary);
  font-style: italic;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.transcription-input {
  border: none !important;
}

.transcription-input .q-field__control {
  border-radius: 0 0 8px 8px !important;
}

/* Fixed Action Buttons */
.action-buttons-fixed {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: var(--q-card-bg);
  border-top: 1px solid var(--q-separator-color);
  flex-shrink: 0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}

body.body--dark .action-buttons-fixed {
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.2);
}

/* Legacy action buttons (if any remain) */
.action-buttons {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 0;
  margin-top: 16px;
  border-top: 1px solid var(--q-separator-color);
}

.action-btn {
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.use-btn {
  padding: 8px 20px;
  border-radius: 8px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(var(--q-primary-rgb), 0.3);
  transition: all 0.2s ease;
}

.use-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(var(--q-primary-rgb), 0.4);
}

/* Mobile Responsive */
@media (max-width: 599px) {
  .app-header {
    padding: 12px 16px;
  }

  .header-content {
    gap: 8px;
  }

  .title {
    font-size: 14px;
  }

  .subtitle {
    font-size: 12px;
  }

  .recording-section {
    padding: 16px;
    padding-bottom: 8px; /* Less bottom padding since buttons are fixed */
  }

  .recording-controls {
    gap: 16px;
    padding: 16px 0;
  }

  .control-buttons {
    gap: 12px;
  }

  .record-btn {
    width: 56px;
    height: 56px;
  }

  .toggle-btn {
    width: 40px;
    height: 40px;
  }

  /* Fixed action buttons mobile layout */
  .action-buttons-fixed {
    padding: 12px 16px;
    flex-wrap: wrap;
    gap: 8px;
  }

  .action-buttons-fixed .q-btn {
    flex: 1;
    min-width: 0;
    font-size: 12px;
  }

  .action-buttons-fixed .use-btn {
    order: -1; /* Primary action first on mobile */
    flex: 1 1 100%; /* Full width for primary button */
    margin-bottom: 8px;
  }

  .action-buttons-fixed .q-space {
    display: none; /* Hide spacer on mobile */
  }

  /* Legacy action buttons mobile (if any remain) */
  .action-buttons {
    flex-direction: column;
    gap: 8px;
    padding: 12px 16px;
  }

  .action-buttons .q-btn {
    width: 100%;
  }

  /* Transcription container mobile adjustments */
  .transcription-text {
    max-height: 150px; /* Reduce height on mobile to save space */
  }

  /* Ensure content doesn't get cut off */
  .transcription-app {
    max-height: 100vh !important;
  }
}

/* Scrollbar styling */
.transcription-text::-webkit-scrollbar {
  width: 6px;
}

.transcription-text::-webkit-scrollbar-track {
  background: var(--q-scrollbar-track, #f1f1f1);
}

body.body--dark .transcription-text::-webkit-scrollbar-track {
  background: var(--q-dark-scrollbar-track, #2d3748);
}

.transcription-text::-webkit-scrollbar-thumb {
  background: var(--q-scrollbar-thumb, #c1c1c1);
  border-radius: 3px;
}

body.body--dark .transcription-text::-webkit-scrollbar-thumb {
  background: var(--q-dark-scrollbar-thumb, #4a5568);
}

.transcription-text::-webkit-scrollbar-thumb:hover {
  background: var(--q-scrollbar-thumb-hover, #a8a8a8);
}

body.body--dark .transcription-text::-webkit-scrollbar-thumb:hover {
  background: var(--q-dark-scrollbar-thumb-hover, #5a6578);
}
</style>
