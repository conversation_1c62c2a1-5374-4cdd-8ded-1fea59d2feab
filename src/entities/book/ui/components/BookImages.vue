<template>
  <div id="images" class="container q-gutter-y-md column">
    <q-btn
      label="Upload Image"
      icon="img:robot.png"
      color="primary"
      @click="addWithInsert"
      :loading="isUploading"
      :disable="isUploading || isLoadingChapter || isDisablingButton"
      class="q-mb-md"
    >
      <q-tooltip>Upload Image</q-tooltip>
    </q-btn>
    <q-scroll-area
      style="height: 400px; width: 100%"
      :thumb-style="thumbStyle"
      :bar-style="barStyle"
    >
      <div class="image-container row">
        <ImageCard
          v-if="groupedImages && groupedImages[selectedChapterId]"
          v-for="(image, index) in groupedImages[selectedChapterId]"
          :key="index"
          :image="image"
          :groupedImages="groupedImages[selectedChapterId]"
          @deleteImage="deleteImage(image, selectedChapterId)"
          @replaceImage="replaceImage(image, selectedChapterId)"
          @insertImage="insertImage(image, selectedChapterId)"
        />

        <MessageError
          v-else
          heading="No Images found!"
          icon="warning"
          text-color="text-black"
          bg-color="#fff"
          icon-color="primary"
          message="Click the 'Upload Image' button to upload."
        />
      </div>
    </q-scroll-area>
    <div v-show="false" id="form">
      <div @click="isUploading = !isUploading" class="back-button q-mb-md">
        <q-icon name="arrow_back_ios" color="primary" />Back
      </div>
      <file-pond
        name="onMannyLoading"
        ref="pond"
        label-idle="Images (Click, drop, or paste)"
        v-bind:allow-multiple="false"
        :max-files="50"
        max-file-size="10MB"
        accepted-file-types="image/jpeg, image/png, image/gif, image/bmp, application/pdf"
        file-validate-type-label-expected-types="Only image and pdf files allowed"
        @addfile="onAddImage"
        @updatefiles="updateImages"
        @init="initUploadImage"
        class="q-mb-md"
      />
      <div class="column items-start">
        <q-btn
          label="Upload"
          icon="save"
          :loading="saving"
          :disable="!canSave"
          color="primary"
          @click="uploadImages"
          class="q-mb-md"
        >
          <template v-slot:loading>
            <q-spinner />
          </template>
        </q-btn>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, onUpdated } from 'vue';
import { storage } from 'src/firebase';
import {
  ref as storageRef,
  uploadString,
  uploadBytes,
  deleteObject,
  getDownloadURL,
} from 'firebase/storage';
import vueFilePond from 'vue-filepond';
import FilePondPluginFileValidateSize from 'filepond-plugin-file-validate-size/dist/filepond-plugin-file-validate-size.esm.js';
import FilePondPluginFileValidateType from 'filepond-plugin-file-validate-type/dist/filepond-plugin-file-validate-type.esm.js';
import FilePondPluginImageCrop from 'filepond-plugin-image-crop/dist/filepond-plugin-image-crop.esm.js';
import FilePondPluginImageResize from 'filepond-plugin-image-resize/dist/filepond-plugin-image-resize.esm.js';
import FilePondPluginImageValidateSize from 'filepond-plugin-image-validate-size/dist/filepond-plugin-image-validate-size.esm.js';
import FilePondPluginImageTransform from 'filepond-plugin-image-transform/dist/filepond-plugin-image-transform.esm.js';
import 'filepond/dist/filepond.min.css';
const FilePond = vueFilePond(
  FilePondPluginFileValidateSize,
  FilePondPluginFileValidateType,
  FilePondPluginImageCrop,
  FilePondPluginImageResize,
  FilePondPluginImageValidateSize,
  FilePondPluginImageTransform,
);
import Jimp from 'jimp';
import { type Image, isPDF, ImageCard } from 'src/entities/image';
import { computedAsync } from '@vueuse/core';
import {
  confirmOverrideText,
  showError,
  warn,
} from 'src/shared/lib/quasar-dialogs';
import {
  Book,
  BookOutlines,
  ChapterOutline,
  MessageError,
} from 'src/entities/book';
import { useQuasar } from 'quasar';
import { user, userLoggedIn } from 'src/entities/user';
import shortid from 'shortid';
import { useVModels } from '@vueuse/core/index';
import { barStyle, primaryColor, thumbStyle } from 'src/entities/setting';

const $q = useQuasar();

const props = defineProps<{
  outlines: BookOutlines;
  book: Book;
  selectedChapterOutline: ChapterOutline;
  selectedChapterId: string;
  isLoadingChapter: boolean;
  onMannyLoading: boolean;
  readonly?: boolean;
  sideView: string;
}>();

const emit = defineEmits<{
  'update:isLoadingChapter': [isLoadingChapter: boolean];
  'update:onMannyLoading': [onMannyLoading: boolean];
  'update:selectedChapterId': [id: string];
  'update:book': [book: Book];
  'update:outlines': [outlines: BookOutlines];
}>();

const {
  selectedChapterId,
  book,
  outlines,
  onMannyLoading,
  isLoadingChapter,
  sideView,
} = useVModels(props, emit);

const isReferenceImage = ref(true);
// Validate image function
const validateImage = async (image: Image): Promise<boolean> => {
  return new Promise<boolean>((resolve) => {
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = image.url;
  });
};

const images = computed(() => book.value.images);
// Define computedAsync for groupedImages
const groupedImages = computedAsync(async () => {
  const imagesFil = images.value.filter(
    (img) => !img.isThumb && img.assignment === selectedChapterId.value,
  );

  // Validate all images
  const validationResults = await Promise.all(imagesFil.map(validateImage));

  // Filter out invalid images
  const validImages = imagesFil.filter((_, index) => validationResults[index]);

  // Group valid images by assignment
  const valids = validImages.reduce(
    (acc, image) => {
      acc[image.assignment] ??= [];
      acc[image.assignment].push(image);
      return acc;
    },
    {} as Record<string, Image[]>,
  );

  return valids;
}, {});

const isUploading = ref(false);
const isDisablingButton = ref(false);
const isInserting = ref(false);
const isReplacing = ref(false);
const imageToReplace = ref({});
const imagesToUpload = ref([]);
const uploadCount = ref(0);
const saving = ref(false);
const pond = ref(null);

const validImages = computed(() =>
  imagesToUpload.value.filter(
    (image) =>
      (image.file.type.indexOf('image/') === 0 ||
        image.file.type.indexOf('application/pdf') === 0) &&
      image.file.size <= 10485760,
  ),
);
const canSave = computed(() => validImages.value.length > 0);

// Method to handle adding an image
const onAddImage = (error, image: Image) => {
  try {
    if (error) {
      console.log(error);
      return;
    }

    if (!image) {
      console.log('no image');
      return;
    }

    if (isInserting.value) uploadImages();
  } catch (error) {
    showError($q, (error as Error).message);
  }
};

// Method to update images
const updateImages = (files: FileList) => {
  imagesToUpload.value = files;
};

// Method to upload images
const uploadImages = () => {
  if (!canSave.value) return;

  saving.value = true;
  isUploading.value = true;
  isLoadingChapter.value = true;

  const assignment = selectedChapterId.value;

  console.log('Upload Images');

  validImages.value.forEach(async (image, index) => {
    // image is pdf
    if (isPDF(image.file)) {
      uploadCount.value += 1;
      await uploadFileToStorage(image.file, assignment);
      return;
    } else {
      uploadCount.value += 2;
    }

    // if (isInserting.value && isReplacing.value) {
    //   deleteImage(imageToReplace.value, assignment);
    //   isReplacing.value = false;
    //   imageToReplace.value = {};
    // }

    // image is standard image file
    const imageReader = new FileReader();
    const thumbReader = new FileReader();
    const imageFile = image.file;
    const thumbFile = image.file;
    const id = shortid.generate();

    // check if image already exists, if it exists do not upload in the storage
    let existImage = {};
    const idx = images.value.findIndex(
      (img) => img.filename === imageFile.name && img.isThumb == false,
    );
    const idxThumb = images.value.findIndex(
      (img) => img.filename === imageFile.name && img.isThumb == true,
    );
    if (idx !== -1) {
      existImage.image = {
        id: id,
        assignment: assignment,
        isThumb: false,
        isExists: true,
        filename: `${images.value[idx].filename}  (Copy)`,
        type: images.value[idx]?.type,
        url: images.value[idx]?.url,
        mainId: images.value[idx].id,
      };
      existImage.thumb = {
        id: id,
        isExists: true,
        assignment: assignment,
        isThumb: true,
        filename: `${images.value[idx].filename}  (Copy)`,
        type: images.value[idxThumb]?.type,
        url: images.value[idxThumb]?.url,
        mainId: images.value[idx].id,
      };
    }
    imageReader.onload = (e) => {
      if (existImage?.image) {
        images.value.push(existImage.image);
        insertImageRef(existImage.image);
        uploadCount.value -= 1;
        if (uploadCount.value == 0) saveImages();
        console.log('Images after saving (image): ', images.value);
        isLoadingChapter.value = false;
      } else {
        const arrayBuffer = imageReader.result as ArrayBuffer;

        Jimp.read(Buffer.from(arrayBuffer), (err, image) => {
          if (err) {
            throw err;
            isLoadingChapter.value = false;
          }
          image.getBase64Async(Jimp.AUTO).then((imageString) => {
            uploadStringToStorage(
              imageFile,
              imageString,
              false,
              assignment,
              id,
            );
            console.log('Images after saving (image): ', images.value);
          });
        });
      }
    };

    thumbReader.onload = (e) => {
      if (existImage?.thumb) {
        images.value.push(existImage.thumb);
        uploadCount.value -= 1;
        if (uploadCount.value == 0) saveImages();
        console.log('Images after saving (thumb): ', images.value);
        isLoadingChapter.value = false;
      } else {
        const arrayBuffer = thumbReader.result as ArrayBuffer;

        Jimp.read(Buffer.from(arrayBuffer), (err, image) => {
          if (err) {
            throw err;
            isLoadingChapter.value = false;
          }
          image.cover(200, 200);
          image.getBase64Async(Jimp.AUTO).then((imageString) => {
            uploadStringToStorage(thumbFile, imageString, true, assignment, id);
            console.log('Images after saving (thumb): ', images.value);
          });
        });
      }
    };

    imageReader.readAsArrayBuffer(imageFile);
    thumbReader.readAsArrayBuffer(thumbFile);
  });

  isInserting.value = false;
};

// Method to upload image string to storage
const uploadStringToStorage = (
  imageFile,
  imageString,
  isThumb = false,
  assignment,
  id,
) => {
  isLoadingChapter.value = true;
  const pathName = isThumb ? 'thumb_' + id : id;
  const userL = user?.value?.uid;
  const storagePath = `${userL}/bookimages/${book.value.id}/${selectedChapterId.value}/${pathName}`;
  const storageRefe = storageRef(storage, storagePath);
  uploadString(storageRefe, imageString, 'data_url')
    .then((snapshot) => {
      console.log('Uploaded image: ', imageFile);
      getDownloadURL(storageRefe).then((downloadURL) => {
        console.log('Get image url: ', downloadURL);
        const imageToSave = {
          url: downloadURL,
          filename: imageFile.name,
          type: imageFile.type,
          assignment: assignment,
          id: id,
          isThumb: isThumb,
          mainId: '',
          isExists: false,
        };
        images.value.push(imageToSave);
        console.log('Images after saving: ', images.value);
        if (isThumb === false) {
          insertImageRef(imageToSave);
        }

        uploadCount.value -= 1;
        if (uploadCount.value == 0) saveImages();
        isLoadingChapter.value = false;
      });
    })
    .catch((error) => {
      isLoadingChapter.value = false;
      showError($q, (error as Error).message);
    });
};

// Method to upload a file to storage
const uploadFileToStorage = (imageFile, assignment) => {
  const userL = user?.value?.uid;
  const storagePath = `${userL}/bookimages/${book.value.id}/${selectedChapterId.value}/${imageFile.name}`;
  const storageRefe = storageRef(storage, storagePath);
  uploadBytes(storageRefe, imageFile)
    .then((snapshot) => {
      console.log('Uploaded image: ', imageFile);
      getDownloadURL(storageRefe).then((downloadURL) => {
        console.log('Get image url: ', downloadURL);
        images.value.push({
          url: downloadURL,
          filename: imageFile.name,
          type: imageFile.type,
          assignment: assignment,
          isThumb: false,
        });
        uploadCount.value -= 1;
        if (uploadCount.value == 0) saveImages();
      });
    })
    .catch((error) => {
      showError($q, (error as Error).message);
    });
};

// Method to delete an image
const deleteImage = async (image, currentChapter) => {
  if (currentChapter !== selectedChapterId.value) {
    await showError($q, 'Image could not be deleted!');
    return;
  }
  if (!image.isExists) {
    let count = 0; // Initialize count variable
    for (let i = images.value.length - 1; i >= 0; i--) {
      const img = images.value[i];
      if (image.id === img.mainId && !img.isThumb) {
        count++; // Increment count when a matching element is found
      }
    }

    if (count) {
      const shouldDelete = await confirmOverrideText($q, {
        message: `Are you sure you want to delete all identical images (${
          count + 1
        }) from the chapters of your book?  Note that deleting these images will break their references`,
      });

      if (!shouldDelete) {
        return;
      }
    } else {
      const shouldDelete = await confirmOverrideText($q, {
        message: `Are you sure you want to delete this image?`,
      });

      if (!shouldDelete) {
        return;
      }
    }
    isUploading.value = true;
    isLoadingChapter.value = true;
    try {
      const userL = user?.value?.uid;
      const storagePath = `${userL}/bookimages/${book.value.id}/${selectedChapterId.value}/${image.id}`;
      const imageRef = storageRef(storage, storagePath);
      deleteObject(imageRef)
        .then((snapshot) => {
          console.log('Deleted image: ', image.id);
          const index = images.value.findIndex(
            (img) => img.id === image.id && !img.isThumb,
          );
          if (index !== -1) {
            images.value.splice(index, 1);
            const storageThumbPath = `${userL}/bookimages/${book.value.id}/${selectedChapterId.value}/thumb_${image.id}`;
            const thumbRef = storageRef(storage, storageThumbPath);
            removeImageRef(image);

            deleteObject(thumbRef).then((snapshot) => {
              console.log('Deleted thumb: ', image.filename);
              const thumbIndex = images.value.findIndex(
                (img) => img.id === image.id && img.isThumb,
              );
              if (thumbIndex !== -1) {
                images.value.splice(thumbIndex, 1);
                saveImages();
              }

              // remove all instances of child images
              if (count) {
                for (let i = images.value.length - 1; i >= 0; i--) {
                  const img = images.value[i];
                  if (image.id === img.mainId) {
                    removeImageRef(img);
                    images.value.splice(i, 1);
                  }
                }
              }
            });
          }
          isUploading.value = false;
          isLoadingChapter.value = false;
        })
        .catch((error) => {
          removeBrokenImages(image);
          isUploading.value = false;
          isLoadingChapter.value = false;
        });
    } catch (err) {
      removeBrokenImages(image);
      isUploading.value = false;
      isLoadingChapter.value = false;
    }
  } else {
    removeBrokenImages(image);
    isUploading.value = false;
    isLoadingChapter.value = false;
  }
};

const removeBrokenImages = (image) => {
  console.log('Broken images found', image);
  const index = images.value.findIndex(
    (img) => img.id === image.id && !img.isThumb,
  );
  if (index !== -1) {
    images.value.splice(index, 1);
    removeImageRef(image);
    saveImages();
  }
  const thumbIndex = images.value.findIndex(
    (img) => img.id === image.id && img.isThumb,
  );
  if (thumbIndex !== -1) {
    images.value.splice(thumbIndex, 1);
    saveImages();
  }
};
// Method to insert an image
const insertImage = (image, currentChapter) => {
  if (currentChapter !== selectedChapterId.value) {
    showError($q, 'Image could not be inserted!');
    return;
  }
  const idx = images.value.findIndex(
    (img) => img.id === image.id && img.isThumb == false,
  );
  if (idx !== -1) {
    insertImageRef(images.value[idx]);
  } else {
    showError($q, 'Image could not be inserted!');
  }
};

// Method to replace an image
const replaceImage = (image, currentChapter) => {
  if (currentChapter !== selectedChapterId.value) {
    showError($q, 'Image could not be replaced!');
    return;
  }
  isReplacing.value = true;
  imageToReplace.value = image;
  // @ts-expect-error Until EventBus is removed
  window.EventBus.$emit('insert-image');
};

// Method to save images
const saveImages = () => {
  // Assuming pond.value is accessible in Vue 3
  isUploading.value = false;
  saving.value = false;
  pond.value.removeFiles();
};

// Method to add with insert
const addWithInsert = () => {
  onMannyLoading.value = false;
  if (!pond.value) return;
  // Assuming pond.value is accessible in Vue 3
  pond.value.browse();
  isInserting.value = true;
};

const initUploadImage = () => {
  if (onMannyLoading.value === true) addWithInsert();
};

// Method to remove the image to the editor
const removeImageRef = (image) => {
  const editor = tinymce.get('editor');
  tinymce.setActive(editor);

  const content = editor.getContent();

  // Adjusted regex to find the 'id' attribute instead of 'src'
  const refText = isReferenceImage.value
    ? `{{${image.filename}}}`
    : `<img id="${image.id}" src= "${image.url}" />`;

  const regex = new RegExp(refText, 'g');
  const newContent = content.replace(regex, '');

  editor.setContent(newContent);
};

// Method to insert the image to the editor
const insertImageRef = (image) => {
  const editor = tinymce.get('editor');
  tinymce.setActive(editor);

  const newRefText = isReferenceImage.value
    ? `{{${image.filename}}}`
    : `<img id="${image.id}" src= "${image.url}"  style="width: 100%"/>`;

  tinymce.activeEditor.selection.collapse();
  tinymce.activeEditor.insertContent(newRefText);
};

watch(
  onMannyLoading,
  (currentOnImageUploading) => {
    if (currentOnImageUploading === true && sideView.value === 'images')
      initUploadImage();
  },
  { immediate: true },
);
</script>

<style lang="scss">
.filepond--drop-label,
.filepond--drop-label label {
  cursor: pointer;
}

#images .image-container {
  display: flex;
  justify-content: flex-start;
  overflow: hidden;
  flex-wrap: wrap !important;
}

#images .back-button {
  display: flex;
  cursor: pointer;
  font-size: 16px;
  line-height: 32px;
}
#images .back-button .q-icon {
  font-size: 32px;
}
</style>
