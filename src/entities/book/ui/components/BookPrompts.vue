<template>
  <div class="q-gutter-y-md column">
    <q-btn
      label="Generate Content"
      icon="img:robot.png"
      color="primary"
      @click="generateMannyPrompt"
      :disable="!canGenerateResult"
    >
      <q-tooltip>Let <PERSON> Generate a Content</q-tooltip>
    </q-btn>
    <MessageError
      v-if="!selectedText"
      icon="info"
      icon-color="primary"
      message="To use <PERSON>, highlight some content."
    />
    <q-select
      dense
      @update:model-value="onPromptSelectUpdate"
      v-model="mannyPrompt"
      :options="optionsPrompt"
      map-options
      label="Select a Prompt"
    />
    <q-input
      ref="message"
      v-model="mannyPromptMessage"
      label="Additional Instructions"
      type="textarea"
      dense
      clearable
      :rules="[
        (val) => {
          isInputValid =
            val.length === 0 || (val.length <= 300 && val.length >= 20)
              ? true
              : false;
          if (val.length > 0 && val.length > 300)
            return 'Must not exceed 300 characters.';
          else if (val.length > 0 && val.length < 20)
            return 'Please enter a minimum of 20 characters.';
          else return isInputValid;
        },
      ]"
    />
  </div>
  <q-dialog
    v-model="compareDialog"
    persistent
    transition-show="scale"
    transition-hide="scale"
    :maximized="maximizedToggle"
  >
    <q-card style="min-width: 60vw">
      <q-card-section class="q-pa-none">
        <q-bar class="bg-primary text-white">
          <q-icon name="img:robot.png" />

          <div>{{ mannyPromptText }}</div>

          <q-space />

          <q-btn
            dense
            flat
            :icon="!maximizedToggle ? 'fullscreen' : 'fullscreen_exit'"
            @click="maximizedToggle = !maximizedToggle"
          >
            <q-tooltip v-if="!maximizedToggle" class="bg-white text-primary"
              >Maximize</q-tooltip
            >
          </q-btn>
          <q-btn dense flat icon="close" v-close-popup>
            <q-tooltip class="bg-white text-primary">Close</q-tooltip>
          </q-btn>
        </q-bar>
      </q-card-section>

      <q-card-section class="q-py-none">
        <q-splitter
          :limits="[50, 100]"
          v-model="splitterModel"
          style="height: calc(100vh - 250px)"
          :class="$q.dark.isActive ? 'bg-dark' : 'bg-white'"
        >
          <template v-slot:before>
            <div class="q-pa-md splitter-container">
              <div
                class="text-h6 sticky-header"
                :class="
                  $q.dark.isActive
                    ? 'bg-dark text-white'
                    : 'bg-white text-primary'
                "
              >
                Selected Text
              </div>
              <q-separator />
              <div v-html="selectedText" class="q-pt-md" />
            </div>
          </template>

          <template v-slot:after>
            <div class="q-pa-md splitter-container">
              <div
                class="text-h6 sticky-header"
                :class="
                  $q.dark.isActive
                    ? 'bg-dark text-white'
                    : 'bg-white text-primary'
                "
              >
                <q-img src="robot.png" width="30px" /> Generated Result
              </div>
              <q-separator />
              <div v-html="generatedResult" class="q-pt-md" />
            </div>
          </template>
        </q-splitter>
      </q-card-section>
      <q-card-actions
        align="right"
        :class="maximizedToggle ? 'absolute-bottom' : ''"
      >
        <q-btn flat label="Cancel" color="danger" v-close-popup />
        <q-btn
          flat
          label="Insert After Selection"
          color="primary"
          icon="add"
          @click="onInsertContent(true)"
          v-close-popup
        >
          <q-tooltip
            >Insert text immediately after the selected area.</q-tooltip
          >
        </q-btn>
        <q-btn
          flat
          label="Replace in Selection"
          color="purple"
          icon="cached"
          @click="onInsertContent(false)"
          v-close-popup
        >
          <q-tooltip
            >Replace text within the currently selected area.</q-tooltip
          >
        </q-btn>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script lang="ts" setup>
import { computed, onUpdated, ref, watch } from 'vue';
import { useVModels } from '@vueuse/core';
import { useQuasar } from 'quasar';
import { onOpenAIButtons } from 'src/features/book-prompts';
import { Book, MessageError } from 'src/entities/book';
import { showError } from 'src/shared/lib/quasar-dialogs';
import type TinyMCE from 'tinymce';

declare const tinymce: typeof TinyMCE;

const props = defineProps<{
  book: Book;
  isLoadingChapter: boolean;
  onSelectedPrompt: string;
  selectedPromptText: string;
  drawerRight: boolean;
  readonly?: boolean;
}>();

const emits = defineEmits<{
  'update:onSelectedPrompt': [onSelectedPrompt: string];
  'update:book': [book: Book];
  'update:isLoadingChapter': [isLoadingChapter: boolean];
}>();

const { isLoadingChapter, book, onSelectedPrompt, drawerRight } = useVModels(
  props,
  emits,
);

const optionsPrompt = ref([
  { label: 'Please Select Prompt', value: '' },
  { label: 'Rewrite', value: 'rewrite' },
  { label: 'Simplify', value: 'simplify' },
  { label: 'Add A Content', value: 'addContent' },
  { label: 'Add A Story', value: 'addStory' },
  { label: 'Edit Grammar, Spelling and Punctuation', value: 'grammar' },
]);
const mannyPrompt = ref('');
const compareDialog = ref(false);
const splitterModel = ref(50);
const mannyPromptMessage = ref('');
const generatedResult = ref('');

// validation
const maximizedToggle = ref(false);
const isInputValid = ref(true);
const mannyPromptText = computed(() => {
  const mannyPromptSelected = mannyPrompt.value.value ?? mannyPrompt.value;
  return (
    optionsPrompt.value.find((opt) => mannyPromptSelected === opt.value)
      ?.label ?? 'Rewrite'
  );
});
const selectedText = ref('');
const $q = useQuasar();
const canGenerateResult = computed(
  () =>
    (mannyPrompt.value.value || mannyPrompt.value) &&
    isInputValid.value &&
    selectedText.value,
);

const onPromptSelectUpdate = () => {
  const mannyPromptSelected = mannyPrompt.value.value ?? mannyPrompt.value;
  onSelectedPrompt.value = '';
  if (mannyPromptSelected) mannyPromptMessage.value = '';
};
const generateMannyPrompt = async () => {
  onSelectedPrompt.value = '';
  const mannyPromptSelected = mannyPrompt.value.value ?? mannyPrompt.value;
  isLoadingChapter.value = true;
  getSelectedText();
  if (selectedText.value) {
    let prompt = selectedText.value;
    let additionalContext = '';
    if (mannyPromptMessage.value) {
      additionalContext = `.Please ensure that you follow the instructions below:\n ${mannyPromptMessage.value}`;
    }

    const response = await onOpenAIButtons(
      mannyPromptSelected,
      prompt,
      book.value.id,
      additionalContext,
    );
    if (response) {
      compareDialog.value = true;
      generatedResult.value = response;
    }
    if ($q.screen.xs) {
      drawerRight.value = false;
    } else {
      drawerRight.value = true;
    }
  } else {
    showError(
      $q,
      'To proceed, Please highlight or select the content you want to modify in the editor.',
    );
  }

  isLoadingChapter.value = false;
};
const onInsertContent = (isAdd = false) => {
  if (tinymce) {
    const editor = tinymce.get('editor');
    tinymce.setActive(editor);
    const { selection } = editor;
    setHightlightedText(editor, selection, generatedResult.value, isAdd);
  }
};

const getSelectedText = () => {
  if (tinymce) {
    const editor = tinymce.get('editor');
    tinymce.setActive(editor);
    const { selection } = editor;
    if (!selection) return '';
    const selectionText = selection.getContent({
      format: 'html',
    });
    selectedText.value = selectionText;
    return selectionText;
  }
};

const setHightlightedText = (
  editor: any,
  selection: any,
  response: string,
  action?: string,
) => {
  const evId = 'insert-' + Date.now();
  let contentResponse = `<div id="${evId}">${response}</div>`;
  const currentBookmark = editor.selection.getBookmark(2);
  if (currentBookmark) {
    if (action) {
      contentResponse = `<div id="${evId}">${contentResponse}</div>`;
    }
    editor.selection.moveToBookmark(currentBookmark);
    if (action) {
      editor.selection.collapse();
      editor.insertContent(contentResponse, { format: 'html' });
    } else {
      selection.setContent(contentResponse, { format: 'html' });
    }
  } else {
    selection.setContent(contentResponse, { format: 'html' });
  }

  // selects the updated content
  const bodyElement = editor.getBody();
  const textNode = bodyElement.querySelector(`#${evId}`) as Node;
  if (textNode) {
    selection.select(textNode);
    // bodyElement.querySelector(`#${evId}`).scrollIntoView();
  } else {
    console.error('Node not found.');
  }
};
onUpdated(() => {
  getSelectedText();
});

watch(
  props,
  (currentProps) => {
    selectedText.value = currentProps.selectedPromptText;
  },
  { immediate: true },
);
watch(
  onSelectedPrompt,
  (currentOnSelectedPrompt) => {
    if (currentOnSelectedPrompt) {
      const mannyPromptSelected = mannyPrompt.value.value ?? mannyPrompt.value;
      if (
        mannyPromptSelected &&
        currentOnSelectedPrompt !== mannyPromptSelected
      ) {
        mannyPromptMessage.value = '';
      }
      mannyPrompt.value = currentOnSelectedPrompt as string;
      generateMannyPrompt();
    }
  },
  { immediate: true },
);
</script>

<style lang="scss">
.splitter-container {
  position: relative; /* Container for sticky positioning */
  height: 100%; /* Full height to ensure scrolling */
  overflow: auto; /* Enable scrolling */
  padding-top: 0 !important;
  scroll-behavior: smooth;
}

.sticky-header {
  position: sticky;
  top: 0;
  padding-top: 10px;
  padding-bottom: 10px;
  background: white; /* Ensure the header has a solid background */
  z-index: 1; /* Ensure the header is above other content */
}
</style>
