<template>
  <div id="audiobook" class="container">
    <AudioBookCard
      :audioUrl="selectedChapterOutline"
      @deleteAudio="deleteAudio"
      @refreshAudio="refreshAudio"
    />
  </div>
</template>

<script setup lang="ts">
import { Book, ChapterOutline, updateOutline } from 'src/entities/book';
import AudioBookCard from 'src/entities/audiobook/ui/AudioBookCard.vue';
import { useVModels } from '@vueuse/core/index';
import {
  deleteChapterAudioBook,
  storeChapterAudioBook,
} from 'src/entities/audiobook';
import { ref } from 'vue';

const props = defineProps<{
  book: Book;
  selectedChapterOutline: ChapterOutline;
  editorContent: string;
  isLoadingChapter: boolean;
  readonly?: boolean;
}>();

const emits = defineEmits<{
  'update:isLoadingChapter': [isLoadingChapter: boolean];
  'update:book': [book: Book];
}>();

const { book, selectedChapterOutline, isLoadingChapter, editorContent } =
  useVModels(props, emits);

const isUploading = ref(false);

const deleteAudio = async () => {
  isLoadingChapter.value = true;
  isUploading.value = true;
  try {
    await deleteChapterAudioBook(book.value, selectedChapterOutline.value);
  } catch (err) {
    console.log(err);
  }

  selectedChapterOutline.value.audio = '';
  await updateOutline(book.value.id, selectedChapterOutline.value.id, {
    audio: '',
  });
  isUploading.value = false;
  isLoadingChapter.value = false;
};
const refreshAudio = async () => {
  isUploading.value = true;
  isLoadingChapter.value = true;
  const downloadUrl = await storeChapterAudioBook(
    book.value,
    selectedChapterOutline.value,
    editorContent.value,
  );

  selectedChapterOutline.value.audio = downloadUrl;
  await updateOutline(book.value.id, selectedChapterOutline.value.id, {
    audio: downloadUrl,
  });
  isUploading.value = false;
  isLoadingChapter.value = false;
};
</script>
<style scoped lang="scss"></style>
