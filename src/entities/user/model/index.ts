export type { Author } from './types';

import { computed } from 'vue'; // Import ref correctly
import { useAuthStore } from 'src/entities/user/store';
import { createPinia, setActivePinia } from 'pinia';

// Initialize Pinia only if not already set up in main.ts
const pinia = createPinia();
setActivePinia(pinia);

const authStore = useAuthStore();

// Computed property to check if user is logged in
export const isLoggedIn = computed(() => authStore.isLoggedIn);
export const getCurrentUser = async () => await authStore.getCurrentUser();

// Reactive references for user data
export const user = computed(() => authStore.user);

// Reactive state for loading indicator (readonly, since it updates internally)
export const loadingState = computed({
  get: () => authStore.loadingState,
  set: (value) => (authStore.loadingState = value), // Optional if you want to manually update
});

// Reactive state for review mode (read/write)
export const isReviewMode = computed({
  get: () => authStore.isReviewMode,
  set: (value) => (authStore.isReviewMode = value),
});

// Reactive state for login as another user (read/write)
export const loginAs = computed({
  get: () => authStore.loginAs,
  set: (value) => (authStore.loginAs = value),
});

// Reactive state to track if the users list is loaded
export const isUsersListLoaded = computed({
  get: () => authStore.isUsersListLoaded,
  set: (value) => (authStore.isUsersListLoaded = value),
});

/**
 * Get the snapshot document of the authenticated user.
 */
export const userLoggedIn = computed(() => authStore.userLoggedIn);

/**
 * Determines if the user is an admin.
 */
export const isAdmin = computed(() => authStore.isAdmin);

/**
 * Determines if the user is a reviewer.
 */
export const isReviewer = computed(() => authStore.isReviewer);

/**
 * Determines if the user is an author.
 */
export const isAuthor = computed(() => authStore.isAuthor);
