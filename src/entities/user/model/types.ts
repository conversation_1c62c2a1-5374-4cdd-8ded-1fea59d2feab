import { FieldValue, Unsubscribe } from '@firebase/firestore';

/**
 * Author interface definition.
 */
export interface Author {
  id?: string;
  email?: string;
  allowed_books?: number;
  accountDeletionRequestedAt?: number | null;
  accountDeletionScheduledDate?: number | null;
  firstName?: string;
  lastName?: string;
  name?: string;
  publishingExperience?: string;
  firstSignIn?: string | FieldValue;
  stripeId?: string;
  paypalId?: string;
  penName?: string;
  is_author?: boolean;
  assignedAdminId?: string;
  assignedAdminEmail?: string;
  features?: string[];
  unsubscribe?: Unsubscribe;
  notifications?: number;
  notifications_read?: number;
  authorsToReview?: { [key: string]: any };
  address?: { [key: string]: any } | null;
  writingGoals?: { [key: string]: any } | null;
  genres?: { [key: string]: any } | null;
}

/**
 * User onboarding information collected during initial setup
 */
export interface UserOnboardingInfo {
  firstName: string;
  lastName: string;
  penName: string | null;
  email: string;
  publishingExperience: 'never' | 'one' | 'multiple';
  writingGoals: {
    ownBooks: boolean;
    forOthers: boolean;
  };
  genres: {
    nonFiction: boolean;
    autobiography: boolean;
    fiction: boolean;
    romance: boolean;
  };
  address: {
    line1: string;
    line2: string | null;
    city: string;
    state: string;
    zip: string;
    country: string;
  };
  onboardingCompleted: boolean;
}
