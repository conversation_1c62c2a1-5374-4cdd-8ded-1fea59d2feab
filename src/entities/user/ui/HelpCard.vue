<template>
  <q-card
    class="help-support-card"
    :class="{
      'floating-card': barShow,
      'help-support-card--dark': $q.dark.isActive,
    }"
    :style="
      $q.screen.lt.sm
        ? 'width: calc(100vw - 20px)'
        : 'min-width: calc(50vw - 600px)'
    "
  >
    <q-card-section class="q-pa-none" v-if="barShow">
      <q-bar class="card-header">
        <q-avatar size="28px" class="q-mr-sm">
          <q-img src="robot.png" />
        </q-avatar>

        <div class="text-weight-medium">Help and Support</div>

        <q-space />
        <q-btn
          dense
          flat
          round
          icon="minimize"
          @click="emits('minimizeToggle')"
          class="header-btn"
        >
          <q-tooltip>Minimize</q-tooltip>
        </q-btn>
        <q-btn
          dense
          flat
          round
          icon="close"
          @click="emits('closeToggle')"
          class="header-btn"
        >
          <q-tooltip>Close</q-tooltip>
        </q-btn>
      </q-bar>
    </q-card-section>

    <q-card-section class="card-content">
      <div class="text-h5 text-primary text-weight-medium q-mb-lg">
        Reach out to the Manuscriptr Team
      </div>

      <q-form id="contact-form" class="q-gutter-md" @submit.prevent="onSubmit">
        <q-input
          v-model="name"
          label="Name"
          name="subject"
          required
          outlined
          class="modern-input"
          :rules="[validateRequired]"
          :bg-color="$q.dark.isActive ? 'grey-9' : 'white'"
        >
          <template v-slot:prepend>
            <q-icon name="person" color="primary" />
          </template>
        </q-input>

        <q-input
          v-model="email"
          label="Email"
          name="email"
          type="email"
          required
          outlined
          class="modern-input"
          :rules="[validateRequired, validateEmail]"
          :bg-color="$q.dark.isActive ? 'grey-9' : 'white'"
        >
          <template v-slot:prepend>
            <q-icon name="email" color="primary" />
          </template>
        </q-input>

        <q-input
          v-model="message"
          label="Message"
          name="body"
          type="textarea"
          required
          outlined
          class="modern-input"
          :rules="[validateRequired, validateMessageLength]"
          autogrow
          min-rows="4"
          :bg-color="$q.dark.isActive ? 'grey-9' : 'white'"
        >
          <template v-slot:prepend>
            <q-icon name="chat" color="primary" />
          </template>
          <template v-slot:hint>
            <span class="text-caption message-counter">
              {{ message.length }}/200 characters
            </span>
          </template>
        </q-input>
      </q-form>
    </q-card-section>

    <q-card-actions align="right" class="card-actions q-pa-md">
      <q-btn
        label="Send Message"
        type="submit"
        color="primary"
        class="send-button"
        icon-right="send"
        @click="onSubmit"
        :disable="isSending || !canSendEmail"
        :loading="isSending"
        unelevated
      />
    </q-card-actions>

    <q-inner-loading :showing="isSending" color="primary" class="custom-loader">
      <div class="column items-center">
        <q-spinner-dots color="primary" size="40px" />
        <div class="q-mt-sm row items-center">
          <q-avatar size="28px" class="q-mr-sm">
            <q-img src="robot.png" />
          </q-avatar>
          <span class="text-primary text-weight-medium loading-text">
            Sending your message...
          </span>
        </div>
      </div>
    </q-inner-loading>
  </q-card>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useQuasar } from 'quasar';
import { user } from 'src/entities/user';
import { httpsCallable } from 'firebase/functions';
import { functions } from 'src/firebase';
import { useRoute } from 'vue-router';

// Type definition for email details
type EmailDetails = {
  to: string[];
  from: string;
  subject: string;
  text: string;
  html: string;
};

// Definition of component event emitters
const emits = defineEmits<{
  minimizeToggle: [];
  closeToggle: [];
}>();

const { notify } = useQuasar(); // Quasar notification service instance
const route = useRoute(); // Current router instance
const $q = useQuasar();
const barShow = ref(true); // State to show or hide the bar
const name = ref<string>(''); // Ref for storing the name input
const email = ref<string>(''); // Ref for storing the email input
const message = ref<string>(''); // Ref for storing the message input
const isSending = ref<boolean>(false); // State to manage sending status

/**
 * Validates if the field is required.
 * @param val - The value to validate.
 * @returns A string with an error message or true if valid.
 */
const validateRequired = (val: string): string | true =>
  val.length === 0 ? 'This field is required.' : true;

/**
 * Validates the email format.
 * @param val - The email to validate.
 * @returns A string with an error message or true if valid.
 */
const validateEmail = (val: string): string | true =>
  /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val) ? true : 'Please enter a valid email.';

/**
 * Validates the length of the message.
 * @param val - The message to validate.
 * @returns A string with an error message or true if valid.
 */
const validateMessageLength = (val: string): string | true => {
  if (val.length === 0) return 'This field is required.';
  if (val.length < 20) return 'Please enter a minimum of 20 characters.';
  if (val.length > 200) return 'Must not exceed 200 characters.';
  return true;
};

/**
 * Sends an email with the help request details.
 * @param emailDetails - The email details.
 * @returns A promise that resolves when the email is sent.
 */
const sendEmail = async (emailDetails: EmailDetails): Promise<boolean> => {
  const sendEmailFunction = httpsCallable(functions, 'sendEmail');
  return sendEmailFunction(emailDetails).then((response) => {
    console.log('Send email: ', response);
    return true;
  });
};

const canSendEmail = computed(() => {
  // Computed property to determine if the email can be sent
  return !(
    validateRequired(name.value) !== true ||
    validateRequired(email.value) !== true ||
    validateEmail(email.value) !== true ||
    validateRequired(message.value) !== true ||
    validateMessageLength(message.value) !== true
  );
});

/**
 * Handles the form submission.
 */
const onSubmit = async (): Promise<void> => {
  if (!canSendEmail.value) {
    return;
  }

  isSending.value = true;
  try {
    await sendEmail({
      to: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      from: email.value,
      subject: `New help request from ${name.value}`,
      text: `${message.value}\n\nMessage from: ${email.value}`,
      html: '',
    });
    notify({
      message: 'Message sent successfully!',
      color: 'positive',
      position: 'top',
      timeout: 3000,
      icon: 'check_circle',
    });

    message.value = '';
  } catch (error) {
    console.error('Failed to send message:', error);
    notify({
      message: 'Failed to send message. Please try again.',
      color: 'negative',
      position: 'top',
      timeout: 3000,
      icon: 'error',
    });
  } finally {
    isSending.value = false;
  }
};

/**
 * Lifecycle hook that runs once the component is mounted.
 */
onMounted(() => {
  // Set name from user entity if available
  name.value = user.value?.displayName || '';
  // Set email from user entity if available
  email.value = user.value?.email || '';
  // Check if the current route is under help section
  if (route.path.startsWith('/help')) {
    // Hide the bar if on help page
    barShow.value = false;
  }
});
</script>

<style lang="scss" scoped>
.help-support-card {
  border-radius: 16px;
  overflow: hidden;
  background: white;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &--dark {
    background: var(--q-dark);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
  }

  &.floating-card {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);

    &.help-support-card--dark {
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
    }
  }

  .card-header {
    height: 56px;
    background: linear-gradient(
      135deg,
      var(--q-primary),
      var(--q-primary-dark)
    );
    color: white;
    font-size: 16px;

    body.body--dark & {
      background: linear-gradient(
        135deg,
        var(--q-primary-light),
        var(--q-primary)
      );
    }

    .header-btn {
      border-radius: 50%;
      opacity: 0.8;
      transition: all 0.2s ease;

      &:hover {
        opacity: 1;
        background: rgba(255, 255, 255, 0.2);
      }
    }
  }

  .card-content {
    padding: 32px;

    .text-h5 {
      color: var(--q-primary);

      body.body--dark & {
        color: var(--q-primary-light);
      }
    }
  }

  .card-actions {
    background: rgba(var(--q-primary-rgb), 0.03);
    border-top: 1px solid rgba(var(--q-primary-rgb), 0.08);

    body.body--dark & {
      background: rgba(var(--q-primary-rgb), 0.1);
      border-top-color: rgba(var(--q-primary-rgb), 0.2);
    }
  }

  .send-button {
    border-radius: 12px;
    padding: 8px 24px;
    font-weight: 500;
    background: linear-gradient(
      135deg,
      var(--q-primary),
      var(--q-primary-dark)
    );
    box-shadow: 0 4px 15px rgba(var(--q-primary-rgb), 0.25);
    transition: all 0.3s ease;

    &:not(:disabled):hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(var(--q-primary-rgb), 0.35);
    }

    &:disabled {
      opacity: 0.7;
    }

    body.body--dark & {
      background: linear-gradient(
        135deg,
        var(--q-primary-light),
        var(--q-primary)
      );
    }
  }

  .custom-loader {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);

    body.body--dark & {
      background: rgba(var(--q-dark-rgb), 0.9);
    }

    .loading-text {
      color: var(--q-primary);

      body.body--dark & {
        color: var(--q-primary-light);
      }
    }
  }

  .modern-input {
    :deep(.q-field__control) {
      border-radius: 8px;
      transition: all 0.2s ease;

      &:hover {
        border-color: var(--q-primary);
      }
    }

    :deep(.q-field--outlined .q-field__control) {
      background: transparent;

      body.body--dark & {
        border-color: rgba(var(--q-text-color-rgb), 0.3);

        &:hover {
          border-color: var(--q-primary-light);
        }
      }
    }

    :deep(.q-field__label) {
      color: var(--q-text-color);
      opacity: 0.7;
    }

    .message-counter {
      color: var(--q-text-color);
      opacity: 0.6;

      body.body--dark & {
        opacity: 0.7;
      }
    }
  }
}
</style>
