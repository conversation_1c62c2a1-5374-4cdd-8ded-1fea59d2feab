<template>
  <q-card
    class="user-profile-card"
    :class="{ 'user-profile-card--dark': $q.dark.isActive }"
  >
    <div class="profile-header">
      <div class="avatar-container">
        <q-avatar size="120px" class="user-avatar">
          <q-img src="robot.png" />
        </q-avatar>
        <div class="avatar-badge" v-if="userLoggedIn?.stripeId">
          <q-icon name="verified" color="white" size="24px" />
        </div>
      </div>
    </div>

    <div class="profile-content">
      <h2 class="user-name">{{ user?.displayName || 'User' }}</h2>
      <p class="user-email">{{ user?.email }}</p>

      <div
        class="user-status"
        v-if="userLoggedIn?.stripeId && !userLoggedIn.paypalId"
      >
        <q-badge color="positive" class="status-badge">
          <q-icon name="check_circle" size="xs" class="q-mr-xs" />
          Premium Member
        </q-badge>
      </div>

      <q-separator class="q-my-md" />

      <div
        class="account-details"
        v-if="
          userLoggedIn?.accountDeletionRequestedAt &&
          userLoggedIn?.accountDeletionScheduledDate
        "
      >
        <div class="detail-item">
          <div class="detail-label">Deletion Requested</div>
          <div class="detail-value">
            {{ getHumanReadableDate(userLoggedIn?.accountDeletionRequestedAt) }}
          </div>
        </div>

        <div class="detail-item">
          <div class="detail-label">Scheduled Deletion</div>
          <div class="detail-value text-negative">
            {{
              getHumanReadableDate(userLoggedIn?.accountDeletionScheduledDate)
            }}
          </div>
        </div>
      </div>
    </div>

    <div
      class="profile-actions"
      v-if="userLoggedIn && userLoggedIn.stripeId && !userLoggedIn.paypalId"
    >
      <q-btn
        class="payment-btn"
        color="primary"
        @click="portalLink"
        :disable="!linkToStripe"
        icon-right="fa-brands fa-stripe"
        label="Manage Subscription"
      >
        <q-tooltip
          >Update payment details and manage your subscription</q-tooltip
        >
      </q-btn>
    </div>
  </q-card>
</template>

<script setup lang="ts">
import { goToPortalLink } from 'src/entities/package';
import { userLoggedIn } from 'src/entities/user';
import { onMounted, ref } from 'vue';
import { formatDate, getHumanReadableDate } from 'src/entities/setting';
import { useQuasar } from 'quasar';

// Define component props
defineProps<{ user: object | null }>();

// Reference to store the Stripe link
const linkToStripe = ref<string>('');
const $q = useQuasar();

/**
 * Lifecycle hook that runs once the component is mounted.
 */
onMounted(async () => {
  // additional await to ensure portal link will generate
  await new Promise((resolve) => setTimeout(resolve, 1000));
  if (userLoggedIn.value?.stripeId) {
    linkToStripe.value = await goToPortalLink(userLoggedIn.value?.stripeId);
  }
});

/**
 * Fetches and opens the portal link in a new tab.
 * @returns {Promise<void>}
 */
const portalLink = async (): Promise<void> => {
  linkToStripe.value = await goToPortalLink(userLoggedIn.value?.stripeId);
  window.open(linkToStripe.value, '_blank');
};
</script>

<style lang="scss" scoped>
.user-profile-card {
  border-radius: 20px;
  overflow: hidden;
  background: white;
  box-shadow: none;
  transition: all 0.3s ease;

  // Dark mode styling
  &--dark {
    background: var(--q-dark);
  }

  .profile-header {
    height: 100px;
    background: linear-gradient(
      135deg,
      var(--q-primary),
      var(--q-primary-dark)
    );
    position: relative;

    body.body--dark & {
      background: linear-gradient(
        135deg,
        var(--q-primary-light),
        var(--q-primary)
      );
    }
  }

  .avatar-container {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 40px;

    .user-avatar {
      border: 5px solid white;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);

      body.body--dark & {
        border-color: var(--q-dark);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
      }
    }

    .avatar-badge {
      position: absolute;
      bottom: 5px;
      right: 5px;
      background: var(--q-positive);
      border-radius: 50%;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 3px solid white;
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);

      body.body--dark & {
        border-color: var(--q-dark);
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
      }
    }
  }

  .profile-content {
    margin-top: 70px;
    padding: 20px 30px;
    text-align: center;

    .user-name {
      font-size: 24px;
      font-weight: 600;
      color: var(--q-dark);
      margin: 0 0 5px;

      body.body--dark & {
        color: var(--q-text-color);
      }
    }

    .user-email {
      font-size: 16px;
      color: var(--q-text-color);
      opacity: 0.7;
      margin: 0 0 15px;

      body.body--dark & {
        opacity: 0.8;
      }
    }

    .user-status {
      margin-bottom: 15px;

      .status-badge {
        font-size: 14px;
        padding: 5px 12px;
        border-radius: 20px;
      }
    }

    .account-details {
      background: rgba(var(--q-primary-rgb), 0.03);
      border-radius: 12px;
      padding: 15px;
      border: 1px solid rgba(var(--q-primary-rgb), 0.1);

      body.body--dark & {
        background: rgba(var(--q-primary-rgb), 0.1);
        border-color: rgba(var(--q-primary-rgb), 0.2);
      }

      .detail-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;

        &:last-child {
          margin-bottom: 0;
        }

        .detail-label {
          font-weight: 500;
          color: var(--q-text-color);
          opacity: 0.7;
        }

        .detail-value {
          font-weight: 500;
          color: var(--q-text-color);
        }
      }
    }
  }

  .profile-actions {
    padding: 0 30px 30px;
    text-align: center;

    .payment-btn {
      border-radius: 12px;
      padding: 10px 20px;
      font-weight: 500;
      background: linear-gradient(
        135deg,
        var(--q-primary),
        var(--q-primary-dark)
      );
      box-shadow: 0 4px 15px rgba(var(--q-primary-rgb), 0.25);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(var(--q-primary-rgb), 0.35);
      }

      body.body--dark & {
        background: linear-gradient(
          135deg,
          var(--q-primary-light),
          var(--q-primary)
        );
      }
    }
  }

  // Separator styling for dark mode
  .q-separator {
    background: rgba(var(--q-text-color-rgb), 0.12);

    body.body--dark & {
      background: rgba(var(--q-text-color-rgb), 0.2);
    }
  }
}
</style>
