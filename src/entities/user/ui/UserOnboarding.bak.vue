<template>
  <q-dialog
    v-model="showDialog"
    persistent
    maximized
    transition-show="slide-up"
    transition-hide="slide-down"
  >
    <q-card class="onboarding-card">
      <!-- Progress bar at top -->
      <div class="progress-container">
        <div
          class="progress-bar"
          :style="{ width: `${(currentStep / totalSteps) * 100}%` }"
        ></div>
      </div>

      <q-card-section class="q-pa-none">
        <div class="onboarding-content">
          <!-- Mobile header with step indicator -->
          <div class="mobile-header q-pa-md q-pb-none">
            <div class="row items-center justify-between q-gutter-sm">
              <div class="text-subtitle1 text-weight-medium">
                Step {{ currentStep }} of {{ totalSteps }}
              </div>
              <div class="text-caption">{{ stepTitles[currentStep] }}</div>
            </div>
          </div>

          <!-- Centered content area -->
          <div class="content-area">
            <div class="centered-container">
              <!-- Step title and description -->
              <div class="step-header q-mb-lg">
                <h1 class="step-title">{{ stepTitles[currentStep] }}</h1>
                <p class="step-description">
                  {{ stepDescriptions[currentStep] }}
                </p>
              </div>

              <!-- Form content -->
              <div class="form-container">
                <q-card flat class="form-card bg-transparent">
                  <q-card-section>
                    <div v-if="currentStep === 1">
                      <!-- Step 1: Name -->
                      <h2 class="form-section-title">Personal Information</h2>
                      <div class="form-content">
                        <div class="row q-col-gutter-md">
                          <div class="col-12 col-md-6">
                            <q-input
                              v-model="userInfo.firstName"
                              label="First Name"
                              outlined
                              class="modern-input"
                              :rules="[
                                (val) => !!val || 'First name is required',
                              ]"
                            >
                              <template v-slot:prepend>
                                <q-icon name="person" />
                              </template>
                            </q-input>
                          </div>
                          <div class="col-12 col-md-6">
                            <q-input
                              v-model="userInfo.lastName"
                              label="Last Name"
                              outlined
                              class="modern-input"
                              :rules="[
                                (val) => !!val || 'Last name is required',
                              ]"
                            >
                              <template v-slot:prepend>
                                <q-icon name="person" />
                              </template>
                            </q-input>
                          </div>
                          <div class="col-12">
                            <q-input
                              v-model="userInfo.penName"
                              label="Pen Name (optional)"
                              outlined
                              class="modern-input"
                              hint="The name you want to publish under"
                            >
                              <template v-slot:prepend>
                                <q-icon name="edit" />
                              </template>
                            </q-input>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div v-else-if="currentStep === 2">
                      <!-- Step 2: Email -->
                      <h2 class="form-section-title">Contact Information</h2>
                      <div class="form-content">
                        <div class="row q-col-gutter-md">
                          <div class="col-12">
                            <q-input
                              v-model="userInfo.email"
                              label="Email"
                              outlined
                              class="modern-input"
                              :readonly="isStepDisabled === false"
                              type="email"
                              :rules="[
                                (val) => !!val || 'Email is required',
                                (val) =>
                                  /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(
                                    val,
                                  ) || 'Please enter a valid email',
                              ]"
                            >
                              <template v-slot:prepend>
                                <q-icon name="email" />
                              </template>
                              <template
                                v-slot:hint
                                v-if="isStepDisabled === false"
                              >
                                <div
                                  class="text-caption text-info flex items-center q-col-gutter-xs"
                                >
                                  <q-icon name="info" />
                                  <span
                                    >Registered email could not be changed.
                                    Please proceed to the next step to
                                    continue.</span
                                  >
                                </div>
                              </template>
                            </q-input>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div v-else-if="currentStep === 3">
                      <!-- Step 3: Publishing Experience -->
                      <h2 class="form-section-title">Publishing Experience</h2>
                      <div class="form-content">
                        <div class="experience-options">
                          <q-card
                            v-for="option in publishingOptions"
                            :key="option.value"
                            :class="[
                              'option-card',
                              userInfo.publishingExperience === option.value
                                ? 'selected-card'
                                : '',
                            ]"
                            clickable
                            @click="
                              userInfo.publishingExperience = option.value
                            "
                          >
                            <q-card-section class="row items-center no-wrap">
                              <div class="col-auto">
                                <q-avatar
                                  color="primary"
                                  text-color="white"
                                  size="42px"
                                >
                                  <q-icon :name="option.icon" size="24px" />
                                </q-avatar>
                              </div>
                              <div class="col q-ml-md">
                                <div class="text-subtitle1 text-weight-medium">
                                  {{ option.label }}
                                </div>
                                <div class="text-caption">
                                  {{ option.description }}
                                </div>
                              </div>
                              <div class="col-auto">
                                <q-radio
                                  v-model="userInfo.publishingExperience"
                                  :val="option.value"
                                  color="primary"
                                />
                              </div>
                            </q-card-section>
                          </q-card>
                        </div>
                      </div>
                    </div>

                    <div v-else-if="currentStep === 4">
                      <!-- Step 4: Writing Goals -->
                      <h2 class="form-section-title">Writing Goals</h2>
                      <div class="form-content">
                        <div class="goals-options">
                          <q-card
                            v-for="option in writingGoalOptions"
                            :key="option.value"
                            :class="[
                              'option-card',
                              userInfo.writingGoals[option.value]
                                ? 'selected-card'
                                : '',
                            ]"
                            clickable
                            @click="
                              userInfo.writingGoals[option.value] =
                                !userInfo.writingGoals[option.value]
                            "
                          >
                            <q-card-section class="row items-center no-wrap">
                              <div class="col-auto">
                                <q-avatar
                                  color="primary"
                                  text-color="white"
                                  size="42px"
                                >
                                  <q-icon :name="option.icon" size="24px" />
                                </q-avatar>
                              </div>
                              <div class="col q-ml-md">
                                <div class="text-subtitle1 text-weight-medium">
                                  {{ option.label }}
                                </div>
                                <div class="text-caption">
                                  {{ option.description }}
                                </div>
                              </div>
                              <div class="col-auto">
                                <q-checkbox
                                  v-model="userInfo.writingGoals[option.value]"
                                  color="primary"
                                />
                              </div>
                            </q-card-section>
                          </q-card>
                        </div>
                      </div>
                    </div>

                    <div v-else-if="currentStep === 5">
                      <!-- Step 5: Genres -->
                      <h2 class="form-section-title">Preferred Genres</h2>
                      <div class="form-content">
                        <div class="genres-grid row q-col-gutter-md">
                          <div v-for="genre in genreOptions" :key="genre.value">
                            <q-card
                              :class="[
                                'option-card',
                                userInfo.genres[genre.value]
                                  ? 'selected-card'
                                  : '',
                              ]"
                              clickable
                              @click="
                                userInfo.genres[genre.value] =
                                  !userInfo.genres[genre.value]
                              "
                            >
                              <q-card-section class="row items-center no-wrap">
                                <div class="col-auto">
                                  <q-avatar
                                    color="primary"
                                    text-color="white"
                                    size="42px"
                                  >
                                    <q-icon :name="genre.icon" size="24px" />
                                  </q-avatar>
                                </div>
                                <div class="col q-ml-md">
                                  <div
                                    class="text-subtitle1 text-weight-medium"
                                  >
                                    {{ genre.label }}
                                  </div>
                                  <div class="text-caption">
                                    {{ genre.description }}
                                  </div>
                                </div>
                                <div class="col-auto">
                                  <q-checkbox
                                    v-model="userInfo.genres[genre.value]"
                                    color="primary"
                                  />
                                </div>
                              </q-card-section>
                            </q-card>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div v-else-if="currentStep === 6">
                      <!-- Step 6: Address -->
                      <h2 class="form-section-title">Your Address</h2>
                      <div class="form-content">
                        <div class="row q-col-gutter-sm">
                          <div class="col-12">
                            <q-input
                              v-model="userInfo.address.line1"
                              label="Address Line 1"
                              outlined
                              class="modern-input"
                              :rules="[(val) => !!val || 'Address is required']"
                            >
                              <template v-slot:prepend>
                                <q-icon name="home" />
                              </template>
                            </q-input>
                          </div>
                          <div class="col-12">
                            <q-input
                              v-model="userInfo.address.line2"
                              label="Address Line 2 (optional)"
                              outlined
                              class="modern-input"
                            >
                              <template v-slot:prepend>
                                <q-icon name="home" />
                              </template>
                            </q-input>
                          </div>
                          <div class="col-12 col-md-6">
                            <q-input
                              v-model="userInfo.address.city"
                              label="City"
                              outlined
                              class="modern-input"
                              :rules="[(val) => !!val || 'City is required']"
                            >
                              <template v-slot:prepend>
                                <q-icon name="location_city" />
                              </template>
                            </q-input>
                          </div>
                          <div class="col-12 col-md-6">
                            <q-input
                              v-model="userInfo.address.state"
                              label="State/Province"
                              outlined
                              class="modern-input"
                              :rules="[(val) => !!val || 'State is required']"
                            >
                              <template v-slot:prepend>
                                <q-icon name="map" />
                              </template>
                            </q-input>
                          </div>
                          <div class="col-12 col-md-6">
                            <q-input
                              v-model="userInfo.address.zip"
                              label="ZIP/Postal Code"
                              outlined
                              class="modern-input"
                              :rules="[
                                (val) => !!val || 'ZIP code is required',
                              ]"
                            >
                              <template v-slot:prepend>
                                <q-icon name="markunread_mailbox" />
                              </template>
                            </q-input>
                          </div>
                          <div class="col-12 col-md-6">
                            <q-select
                              v-model="userInfo.address.country"
                              :options="countryOptions"
                              label="Country"
                              outlined
                              class="modern-input"
                              :rules="[(val) => !!val || 'Country is required']"
                            >
                              <template v-slot:prepend>
                                <q-icon name="flag" />
                              </template>
                            </q-select>
                          </div>
                        </div>
                      </div>
                    </div>
                  </q-card-section>
                </q-card>
              </div>

              <!-- Navigation buttons -->
              <div class="navigation-buttons">
                <div class="button-group">
                  <q-btn
                    v-if="currentStep > 1"
                    flat
                    color="white"
                    icon="arrow_back"
                    label="Back"
                    @click="prevStep"
                    class="nav-btn"
                  />
                  <q-btn
                    v-else
                    flat
                    color="white"
                    icon="arrow_back"
                    label="Exit"
                    @click="showDialog = false"
                    class="nav-btn"
                  />

                  <q-btn
                    v-if="currentStep < totalSteps"
                    unelevated
                    color="primary"
                    :label="
                      currentStep === totalSteps - 1 ? 'Final Step' : 'Continue'
                    "
                    icon-right="arrow_forward"
                    @click="nextStep"
                    :disable="isStepDisabled"
                    class="nav-btn primary-btn"
                  />
                  <q-btn
                    v-else
                    unelevated
                    color="primary"
                    label="Complete Setup"
                    icon-right="check"
                    @click="completeOnboarding"
                    :disable="isStepDisabled"
                    class="nav-btn primary-btn"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
// Script remains exactly the same as before
import {
  ref,
  reactive,
  defineEmits,
  defineProps,
  watch,
  computed,
  onMounted,
  onUnmounted,
} from 'vue';
import { useQuasar } from 'quasar';
import { useRouter } from 'vue-router';

const props = withDefaults(
  defineProps<{
    show?: boolean;
    user?: object | null;
  }>(),
  {
    show: true,
    user: null,
  },
);

const emit = defineEmits(['update:show', 'complete']);
const $q = useQuasar();
const router = useRouter();

// Reactive state
const showDialog = ref(props.show);
const currentStep = ref(1);
const totalSteps = 6;
const hasEmail = ref(props.user?.email);

// Step titles and descriptions
const stepTitles = {
  1: "Let's Get Started",
  2: 'Stay Connected',
  3: 'Your Experience Matters',
  4: 'What Are Your Goals?',
  5: 'Find Your Genre',
  6: 'Almost There!',
};

const stepDescriptions = {
  1: "Let's start with your name so we can address you properly throughout your writing journey.",
  2: "Let's make sure we can reach you. Your email will be used for important notifications about your books and account.",
  3: 'Tell us about your publishing background. This helps us tailor the experience to your level of expertise.',
  4: 'What do you hope to achieve with your writing? Understanding your goals helps us provide the right guidance and tools.',
  5: "Different genres require different approaches. Let us know which types of books you're interested in writing.",
  6: "Finally, let's collect your address information. This helps with regional settings and any physical materials we might send.",
};

// Publishing experience options
const publishingOptions = [
  {
    value: 'never',
    label: "I've never published a book",
    description: "You're just starting your publishing journey",
    icon: 'new_releases',
  },
  {
    value: 'one',
    label: "I've published 1 book",
    description: 'You have some experience with the publishing process',
    icon: 'menu_book',
  },
  {
    value: 'multiple',
    label: "I've published 2 or more books",
    description: "You're an experienced author with multiple publications",
    icon: 'auto_stories',
  },
];

// Writing goals options
const writingGoalOptions = [
  {
    value: 'ownBooks',
    label: 'I want to write my own books',
    description: 'Create and publish books under your own name',
    icon: 'edit',
  },
  {
    value: 'forOthers',
    label: 'I want to write books for others',
    description: 'Ghostwriting or collaborative writing projects',
    icon: 'people',
  },
];

// Genre options
const genreOptions = [
  {
    value: 'nonFiction',
    label: 'Non-Fiction',
    description: 'Educational, self-help, business, or informational books',
    icon: 'school',
  },
  {
    value: 'autobiography',
    label: 'Autobiography/Memoir',
    description: 'Personal stories and life experiences',
    icon: 'history_edu',
  },
  {
    value: 'fiction',
    label: 'Fiction',
    description: 'Novels, short stories, and imaginative narratives',
    icon: 'auto_stories',
  },
  {
    value: 'romance',
    label: 'Romance',
    description: 'Love stories and romantic relationships',
    icon: 'favorite',
  },
];

// User information object
const userInfo = reactive({
  firstName: '',
  lastName: '',
  penName: '',
  email: '',
  publishingExperience: '',
  writingGoals: {
    ownBooks: false,
    forOthers: false,
  },
  genres: {
    nonFiction: false,
    autobiography: false,
    fiction: false,
    romance: false,
  },
  address: {
    line1: '',
    line2: '',
    city: '',
    state: '',
    zip: '',
    country: '',
  },
});

// Initialize with props.user if available
watch(
  () => props.user,
  (newUser) => {
    if (newUser) {
      Object.assign(userInfo, newUser);
    }
  },
  { immediate: true },
);

// Country options for dropdown
const countryOptions = [
  'US',
  'USA',
  'United States',
  'United States of America',
  'Afghanistan',
  'Albania',
  'Algeria',
  'Andorra',
  'Angola',
  'Antigua and Barbuda',
  'Argentina',
  'Armenia',
  'Australia',
  'Austria',
  'Azerbaijan',
  'Bahamas',
  'Bahrain',
  'Bangladesh',
  'Barbados',
  'Belarus',
  'Belgium',
  'Belize',
  'Benin',
  'Bhutan',
  'Bolivia',
  'Bosnia and Herzegovina',
  'Botswana',
  'Brazil',
  'Brunei',
  'Bulgaria',
  'Burkina Faso',
  'Burundi',
  'Cabo Verde',
  'Cambodia',
  'Cameroon',
  'Canada',
  'Central African Republic',
  'Chad',
  'Chile',
  'China',
  'Colombia',
  'Comoros',
  'Congo (Congo-Brazzaville)',
  'Costa Rica',
  'Croatia',
  'Cuba',
  'Cyprus',
  'Czechia (Czech Republic)',
  'Democratic Republic of the Congo',
  'Denmark',
  'Djibouti',
  'Dominica',
  'Dominican Republic',
  'Ecuador',
  'Egypt',
  'El Salvador',
  'Equatorial Guinea',
  'Eritrea',
  'Estonia',
  'Eswatini (fmr. ' + 'Swaziland)',
  'Ethiopia',
  'Fiji',
  'Finland',
  'France',
  'Gabon',
  'Gambia',
  'Georgia',
  'Germany',
  'Ghana',
  'Greece',
  'Grenada',
  'Guatemala',
  'Guinea',
  'Guinea-Bissau',
  'Guyana',
  'Haiti',
  'Honduras',
  'Hungary',
  'Iceland',
  'India',
  'Indonesia',
  'Iran',
  'Iraq',
  'Ireland',
  'Israel',
  'Italy',
  'Jamaica',
  'Japan',
  'Jordan',
  'Kazakhstan',
  'Kenya',
  'Kiribati',
  'Kuwait',
  'Kyrgyzstan',
  'Laos',
  'Latvia',
  'Lebanon',
  'Lesotho',
  'Liberia',
  'Libya',
  'Liechtenstein',
  'Lithuania',
  'Luxembourg',
  'Madagascar',
  'Malawi',
  'Malaysia',
  'Maldives',
  'Mali',
  'Malta',
  'Marshall Islands',
  'Mauritania',
  'Mauritius',
  'Mexico',
  'Micronesia',
  'Moldova',
  'Monaco',
  'Mongolia',
  'Montenegro',
  'Morocco',
  'Mozambique',
  'Myanmar (formerly Burma)',
  'Namibia',
  'Nauru',
  'Nepal',
  'Netherlands',
  'New Zealand',
  'Nicaragua',
  'Niger',
  'Nigeria',
  'North Korea',
  'North Macedonia',
  'Norway',
  'Oman',
  'Pakistan',
  'Palau',
  'Palestine State',
  'Panama',
  'Papua New Guinea',
  'Paraguay',
  'Peru',
  'Philippines',
  'Poland',
  'Portugal',
  'Qatar',
  'Romania',
  'Russia',
  'Rwanda',
  'Saint Kitts and Nevis',
  'Saint Lucia',
  'Saint Vincent and the Grenadines',
  'Samoa',
  'San Marino',
  'Sao Tome and Principe',
  'Saudi Arabia',
  'Senegal',
  'Serbia',
  'Seychelles',
  'Sierra Leone',
  'Singapore',
  'Slovakia',
  'Slovenia',
  'Solomon Islands',
  'Somalia',
  'South Africa',
  'South Korea',
  'South Sudan',
  'Spain',
  'Sri Lanka',
  'Sudan',
  'Suriname',
  'Sweden',
  'Switzerland',
  'Syria',
  'Tajikistan',
  'Tanzania',
  'Thailand',
  'Timor-Leste',
  'Togo',
  'Tonga',
  'Trinidad and Tobago',
  'Tunisia',
  'Turkey',
  'Turkmenistan',
  'Tuvalu',
  'Uganda',
  'Ukraine',
  'United Arab Emirates',
  'United Kingdom',
  'Uruguay',
  'Uzbekistan',
  'Vanuatu',
  'Vatican City',
  'Venezuela',
  'Vietnam',
  'Yemen',
  'Zambia',
  'Zimbabwe',
];

// Computed property to check if the current step is disabled
const isStepDisabled = computed(() => {
  switch (currentStep.value) {
    case 1:
      return !userInfo.firstName || !userInfo.lastName;
    case 2:
      return (
        !userInfo.email ||
        !/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(userInfo.email)
      );
    case 3:
      return !userInfo.publishingExperience;
    case 4:
      return (
        !userInfo.writingGoals.ownBooks && !userInfo.writingGoals.forOthers
      );
    case 5:
      return (
        !userInfo.genres.nonFiction &&
        !userInfo.genres.autobiography &&
        !userInfo.genres.fiction &&
        !userInfo.genres.romance
      );
    case 6:
      return (
        !userInfo.address.line1 ||
        !userInfo.address.city ||
        !userInfo.address.state ||
        !userInfo.address.zip ||
        !userInfo.address.country
      );
    default:
      return false;
  }
});

// Navigation methods
const nextStep = () => {
  currentStep.value++;
};

const handleKeyDown = (e: KeyboardEvent) => {
  if (e.key === 'Enter' && !isStepDisabled.value) {
    nextStep();
  }
};

onMounted(() => {
  window.addEventListener('keydown', handleKeyDown);
});

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeyDown);
});

const prevStep = () => {
  currentStep.value--;
};

// Complete onboarding and save user data
const completeOnboarding = async () => {
  try {
    // Convert the reactive object to a plain object for submission
    const userInfoData = {
      firstName: userInfo.firstName,
      lastName: userInfo.lastName,
      penName: userInfo.penName || null,
      email: userInfo.email,
      publishingExperience: userInfo.publishingExperience,
      writingGoals: {
        ownBooks: userInfo.writingGoals.ownBooks,
        forOthers: userInfo.writingGoals.forOthers,
      },
      genres: {
        nonFiction: userInfo.genres.nonFiction,
        autobiography: userInfo.genres.autobiography,
        fiction: userInfo.genres.fiction,
        romance: userInfo.genres.romance,
      },
      address: {
        line1: userInfo.address.line1,
        line2: userInfo.address.line2 || null,
        city: userInfo.address.city,
        state: userInfo.address.state,
        zip: userInfo.address.zip,
        country: userInfo.address.country,
      },
      onboardingCompleted: true,
    };

    // Emit the complete event with the user data
    emit('complete', userInfoData);

    // Close the dialog
    showDialog.value = false;
    emit('update:show', false);

    // Show success notification
    $q.notify({
      color: 'positive',
      message: 'Profile setup complete! Welcome to Manuscriptr.',
      icon: 'check_circle',
    });
  } catch (error) {
    console.error('Error saving user information:', error);
    $q.notify({
      color: 'negative',
      message: 'There was an error saving your information. Please try again.',
      icon: 'error',
    });
  }
};

// Watch for changes in the show prop
watch(
  () => props.show,
  (newVal) => {
    showDialog.value = newVal;
  },
);

// Watch for changes in the dialog state
watch(showDialog, (newVal) => {
  emit('update:show', newVal);
});
</script>

<style lang="scss" scoped>
// Main container styling with dark background
.onboarding-card {
  border-radius: 0;
  width: 100vw;
  height: 90vh;
  display: flex;
  flex-direction: column;
  background-image: url('/images/ui/body-bg.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  overflow-x: hidden;
  position: relative;

  // Dark overlay for better text readability
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1;
  }

  // Ensure content appears above overlay
  > * {
    position: relative;
    z-index: 2;
  }

  .text-caption {
    color: white !important;
  }
}

// Progress bar at top - enhanced visibility on dark background
.progress-container {
  height: 4px;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 10;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, $primary 0%, lighten($primary, 15%) 100%);
  transition: width 0.3s ease;
  border-radius: 0 2px 2px 0;
  box-shadow: 0 0 8px rgba($primary, 0.5);
}

// Content layout
.onboarding-content {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 4px);
}

// Mobile header - light text for dark background
.mobile-header {
  display: flex;
  align-items: center;
  padding: 16px 20px;

  .text-subtitle1 {
    font-weight: 600;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }

  .text-caption {
    color: white;
    font-size: 0.9rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }
}

// Centered content area
.content-area {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
  overflow-y: auto;

  @media (min-width: 768px) {
    align-items: center;
    padding: 40px;
  }
}

.centered-container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

// Step header styling - white text for dark background
.step-header {
  text-align: center;
  margin-bottom: 32px;
  width: 100%;

  @media (min-width: 768px) {
    margin-bottom: 48px;
  }
}

.step-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: white;
  margin: 0 0 16px 0;
  line-height: 1.3;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);

  @media (min-width: 768px) {
    font-size: 2rem;
    margin-bottom: 20px;
  }
}

.step-description {
  font-size: 1rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);

  @media (min-width: 768px) {
    font-size: 1.1rem;
    line-height: 1.7;
  }
}

// Form container - centered and styled for dark theme
.form-container {
  width: 100%;
  display: flex;
  justify-content: center;
}

.form-card {
  width: 100%;

  .q-card__section {
    padding: 24px;

    @media (min-width: 768px) {
      padding: 32px;
    }
  }
}

// Form content centering
.form-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;

  .row {
    width: 100%;
    justify-content: center;
  }
}

.form-section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
  margin: 0 0 24px 0;
  line-height: 1.4;
  text-align: center;
  width: 100%;

  @media (min-width: 768px) {
    font-size: 1.375rem;
    margin-bottom: 28px;
  }
}

// Option cards - centered layout
.experience-options,
.goals-options {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.option-card {
  width: 100%;
  max-width: 400px;
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  }

  .q-card__section {
    padding: 16px;
  }

  .text-subtitle1 {
    font-size: 1rem;
    line-height: 1.4;
  }

  .text-caption {
    color: rgba(0, 0, 0, 0.6);
    line-height: 1.4;
    color: white;
  }
}

// Selected card styling
.selected-card {
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  color: white;
  &:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
  }
  .text-caption {
    color: white;
  }
  .text-subtitle1 {
    color: white;
  }
}

// Navigation buttons - centered below form
.navigation-buttons {
  padding: 24px 20px;
  display: flex;
  justify-content: center;
  width: 100%;

  @media (min-width: 768px) {
    padding: 32px 40px;
  }
}

.button-group {
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: center;

  @media (max-width: 600px) {
    flex-direction: column;
    gap: 12px;
    width: 100%;
  }
}

.nav-btn {
  min-width: 120px;
  height: 44px;
  font-weight: 600;
  border-radius: 8px;

  @media (max-width: 600px) {
    width: 100%;
    max-width: 280px;
  }

  &.primary-btn {
    background: linear-gradient(
      90deg,
      $primary 0%,
      lighten($primary, 10%) 100%
    );
    box-shadow: 0 4px 12px rgba($primary, 0.3);
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba($primary, 0.4);
    }
  }

  &:not(.primary-btn) {
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.5);
    }
  }
}

// Grid layouts for genres - centered
.genres-grid {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 16px;

  .col-12,
  .col-sm-6 {
    flex: 0 0 auto;
    width: 100%;
    max-width: 400px;

    @media (min-width: 600px) {
      width: calc(50% - 8px);
      max-width: none;
    }
  }
}

// Animations
.q-dialog__inner--minimized > div {
  animation: fade-in-up 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Custom scrollbar for dark theme
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

// Responsive adjustments
@media (max-width: 767px) {
  .step-header {
    margin-bottom: 24px;
  }

  .step-title {
    font-size: 1.5rem;
  }

  .step-description {
    font-size: 0.95rem;
  }

  .form-card .q-card__section {
    padding: 20px;
  }

  .form-section-title {
    font-size: 1.125rem;
    margin-bottom: 20px;
  }

  .navigation-buttons {
    padding: 16px 20px;
  }

  .content-area {
    padding: 16px;
  }

  .centered-container {
    max-width: 100%;
  }
}

// Avatar styling
.q-avatar {
  background: linear-gradient(135deg, $primary 0%, darken($primary, 15%) 100%);
  box-shadow: 0 4px 8px rgba($primary, 0.2);
}

// Checkbox and radio styling
:deep(.q-checkbox__inner),
:deep(.q-radio__inner) {
  color: $primary;

  .q-checkbox__bg,
  .q-radio__point {
    border-width: 2px;
  }
}

// Modern input styling
.modern-input {
  .q-field__control {
    border-radius: 8px;
  }
}

// Enhanced form inputs for better visibility
:deep(.q-field--outlined .q-field__control) {
  background: rgba(255, 255, 255, 0.9);
}

:deep(.q-field--outlined .q-field__control:hover) {
  background: rgba(255, 255, 255, 1);
}

// Better contrast for form labels
:deep(.q-field__label) {
  color: rgba(0, 0, 0, 0.8);
}

// Improved hint text visibility
:deep(.q-field__messages) {
  color: white;
}
</style>
