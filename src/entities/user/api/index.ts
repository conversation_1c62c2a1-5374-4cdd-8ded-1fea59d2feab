import { user } from 'src/entities/user';

/**
 * Generates a random ID string using alphanumeric characters.
 * @param {number} length - The length of the ID to generate. Defaults to 20.
 * @returns {string} The generated random ID.
 */
export const generateRandomId = (length: number = 20): string => {
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'; // Letters + Numbers
  let result = '';
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
};

/**
 * Generates user details for onboarding
 * @param userLogged
 * @returns {firstName: any, lastName: any, penName: any, email: any, publishingExperience: any, writingGoals: any, genres: any, address: {line1: any, line2: any, city: any, state: any, zip: any, country: any}
 */
export const getUserDetailsForOnboarding = (userLogged: any) => {
  return {
    displayName:
      userLogged?.firstName && userLogged?.lastName
        ? `${userLogged?.firstName}  ${userLogged?.lastName}`
        : user.value?.displayName ?? '',
    firstName:
      userLogged?.firstName?.length > 0
        ? userLogged?.firstName
        : user.value?.displayName?.split(' ')[0] ?? '',
    lastName:
      userLogged?.lastName?.length > 0
        ? userLogged?.lastName
        : user.value?.displayName?.split(' ')[1] ?? '',
    penName: userLogged?.penName ?? '',
    email: userLogged?.email ?? user.value?.email ?? '',
    publishingExperience: userLogged?.publishingExperience ?? '',
    writingGoals: {
      ownBooks: userLogged?.writingGoals?.ownBooks ?? false,
      forOthers: userLogged?.writingGoals?.forOthers ?? false,
    },
    genres: {
      nonFiction: userLogged?.genres?.nonFiction ?? false,
      autobiography: userLogged?.genres?.autobiography ?? false,
      fiction: userLogged?.genres?.fiction ?? false,
      romance: userLogged?.genres?.romance ?? false,
    },
    address: {
      line1: userLogged?.address?.line1 ?? '',
      line2: userLogged?.address?.line2 ?? '',
      city: userLogged?.address?.city ?? '',
      state: userLogged?.address?.state ?? '',
      zip: userLogged?.address?.zip ?? userLogged?.address?.postal_code ?? '',
      country: userLogged?.address?.country ?? '',
    },
  };
};
