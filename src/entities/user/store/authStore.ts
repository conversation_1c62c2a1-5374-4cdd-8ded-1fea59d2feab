import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { onAuthStateChanged, signOut, User } from 'firebase/auth';
import { auth } from 'src/firebase';
import { computedAsync } from '@vueuse/core';
import { useAuthorStore, useSubscriptionStore } from 'src/entities/user/store';
import { useRouter } from 'vue-router';

// List of possible subscriptions
export const subscriptionsPackage = ['basic', 'plus', 'premium'] as const;
type Subscription = (typeof subscriptionsPackage)[number];

/**
 * Use Auth Store
 * Manages authentication state and related user information.
 */
export const useAuthStore = defineStore('auth', () => {
  // Stores (passed as dependencies)
  const authorStore = useAuthorStore();
  const subscriptionStore = useSubscriptionStore();

  // State
  const user = ref<User | null>(null);
  const loadingState = ref(true);
  const isReviewMode = ref('');
  const loginAs = ref('');
  const isUsersListLoaded = ref(false);
  const router = useRouter();

  // Computed properties
  const isLoggedIn = computed(() => user.value !== null);

  /**
   * Retrieves token claims asynchronously.
   */
  const token = computedAsync(async () => {
    if (!user.value) return null;
    const token = await user.value.getIdTokenResult(true);
    return token.claims;
  }, null);

  /**
   * Retrieves user login information asynchronously.
   */
  const userLoggedIn = computedAsync(async () => {
    if (!user.value) return null;
    return await authorStore.getAuthor(user.value.uid);
  }, null);

  /**
   * Computes the user's stripe role.
   */
  const stripeRole = computed(() => {
    return (token.value?.stripeRole ?? null) as Subscription | null;
  });

  /**
   * Determines if the user is an admin.
   */
  const isAdmin = computed(() => token.value?.isAdmin === true);

  /**
   * Determines if the user is a reviewer.
   */
  const isReviewer = computed(() => token.value?.isReviewer === true);

  /**
   * Determines if the user is an author.
   */
  const isAuthor = computed(() => {
    return (
      token.value?.paidViaPaypal === true ||
      token.value?.isOldAuthor === true ||
      subscriptionStore.isSubscribe === true ||
      subscriptionsPackage.includes(stripeRole.value as any)
    );
  });

  const getCurrentUser = () => {
    return new Promise((resolve, reject) => {
      const unsubscribe = onAuthStateChanged(
        auth,
        (u) => {
          user.value = u;
          unsubscribe();
          resolve(u);
          loadingState.value = false;
        },
        reject,
      );
    });
  };

  /** Log out the current user. */
  const logOut = async () => {
    try {
      await signOut(auth);
      router.push({ path: '/login' });
    } catch (err) {
      console.log(err);
    }
  };

  // Return state, computed properties, and actions
  return {
    user,
    isLoggedIn,
    loadingState,
    isReviewMode,
    loginAs,
    isUsersListLoaded,
    token,
    userLoggedIn,
    stripeRole,
    isAdmin,
    isReviewer,
    isAuthor,
    logOut,
    getCurrentUser,
  };
});
