// subscriptionStore.ts
import { defineStore } from 'pinia';
import { ref, Ref } from 'vue';
import {
  collection,
  doc,
  query,
  where,
  limit,
  onSnapshot,
  DocumentReference,
} from 'firebase/firestore';
import { firestore } from 'src/firebase';
import { user } from 'src/entities/user';
import { Unsubscribe } from '@firebase/firestore';

/**
 * Define the subscription store using Pinia.
 * @returns The subscription store with state and actions.
 */
export const useSubscriptionStore = defineStore('subscription', () => {
  // State
  const isSubscribe: Ref<boolean> = ref<boolean>(false); // Indicates if the user is subscribed
  const newSubscription: Ref<string> = ref<string>(''); // Stores new subscription ID
  let productUnSub: Unsubscribe; // Unsubscribe function for product listener
  let userUnSub: Unsubscribe; // Unsubscribe function for user subscription listener

  /**
   * Sets up a listener on product and user subscriptions.
   */
  const setupSubscriptionListener = (): void => {
    const productsQuery = query(
      collection(firestore, 'products'),
      where('metadata.mannySubscription', '==', 'true'),
      limit(5),
    );

    let products: DocumentReference[] = [];

    productUnSub = onSnapshot(productsQuery, async (querySnapshot) => {
      products = querySnapshot
        .docChanges()
        .map((docRef) => doc(firestore, 'products', docRef.doc.id));

      const colRef = query(
        collection(
          firestore,
          'users',
          user.value?.uid as string,
          'subscriptions',
        ),
        where('status', 'in', ['active', 'trialing']),
        where('product', 'in', products),
      );

      userUnSub = onSnapshot(colRef, (querySnapshot) => {
        isSubscribe.value = querySnapshot.size > 0;
        if (!querySnapshot.size) {
          newSubscription.value = '';
        }
        querySnapshot.docChanges().forEach((change) => {
          if (change.type === 'added') {
            newSubscription.value = change.doc.id;
          }
        });
      });
    });
  };

  /**
   * Cleans up the listeners when the store is destroyed.
   */
  const cleanupListeners = (): void => {
    if (productUnSub) productUnSub();
    if (userUnSub) userUnSub();
  };

  // Return state and actions
  return {
    isSubscribe,
    newSubscription,
    setupSubscriptionListener,
    cleanupListeners,
  };
});
