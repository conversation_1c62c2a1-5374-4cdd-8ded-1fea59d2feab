<template>
  <q-item class="q-item col-md-4 col-sm-4 col-xs-3 q-pa-none">
    <div class="image-icons flex items-center justify-center q-pa-sm">
      <q-icon class="view-icon" name="search" @click="openViewer">
        <q-tooltip anchor="top middle" self="bottom middle" :offset="[0, 4]">
          View Image
        </q-tooltip>
      </q-icon>
      <q-icon class="insert-icon" name="add" @click="$emit('insertImage')">
        <q-tooltip anchor="top middle" self="bottom middle" :offset="[0, 4]">
          Insert Image
        </q-tooltip>
      </q-icon>
      <q-icon
        v-if="image?.id"
        class="delete-icon"
        name="delete"
        @click="$emit('deleteImage')"
      >
        <q-tooltip anchor="top middle" self="bottom middle" :offset="[0, 4]">
          Delete Image
        </q-tooltip>
      </q-icon>
    </div>
    <q-item-section class="q-pa-sm">
      <q-img :src="image.url" :ratio="1" spinner-color="primary" />
      <div class="filename">
        {{ trimText(image.filename, 7) }}
        <q-tooltip>{{ image.filename }}</q-tooltip>
      </div>
    </q-item-section>
  </q-item>

  <q-dialog v-model="imageViewer">
    <q-card class="row no-wrap" style="width: 100%; height: 90%">
      <q-img :src="currentImage.url" no-native-menu class="image-container">
        <q-card-section class="absolute all-pointer-events modal-icons">
          <q-icon
            size="32px"
            name="add"
            color="white"
            @click="$emit('insertImage')"
          >
            <q-tooltip> Insert </q-tooltip>
          </q-icon>
          <q-icon
            name="delete"
            size="32px"
            color="white"
            class="q-ml-xs"
            v-if="currentImage?.id"
            @click="$emit('deleteImage')"
          >
            <q-tooltip> Delete </q-tooltip>
          </q-icon>
        </q-card-section>
        <q-card-section
          class="absolute-top-right all-pointer-events modal-icons"
        >
          <q-icon
            size="32px"
            name="close"
            color="white"
            @click="imageViewer = false"
          >
            <q-tooltip> Close </q-tooltip>
          </q-icon>
        </q-card-section>
        <div class="absolute-bottom text-center">
          {{ currentImage.filename }}
        </div>
        <!-- Navigation Buttons -->
        <div
          v-if="groupedImages.length > 1"
          class="absolute-center-left modal-icons"
        >
          <q-icon
            name="chevron_left"
            size="32px"
            color="white"
            @click="prevImage"
          >
            <q-tooltip> Previous </q-tooltip>
          </q-icon>
        </div>
        <div
          v-if="groupedImages.length > 1"
          class="absolute-center-right modal-icons"
        >
          <q-icon
            name="chevron_right"
            size="32px"
            color="white"
            @click="nextImage"
          >
            <q-tooltip> Next </q-tooltip>
          </q-icon>
        </div>
      </q-img>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import type { Image } from '../model';
import { ref, computed } from 'vue';

interface Image {
  src: string; // Source URL of the image
  alt?: string; // Alternative text for the image (optional)
}

// Define component properties
const props = defineProps<{
  image: Image; // Single image object
  groupedImages: Image[]; // Array of image objects
}>();

// Define component events
defineEmits<{
  deleteImage: []; // Event for deleting an image
  replaceImage: []; // Event for replacing an image
  insertImage: []; // Event for inserting a new image
}>();

// State to control the visibility of the image viewer
const imageViewer = ref(false);
// Index of the currently displayed image
const currentIndex = ref(props.groupedImages.indexOf(props.image));

/**
 * Computed property to get the current image.
 * @returns {Image} The current image.
 */
const currentImage = computed(
  (): Image => props.groupedImages[currentIndex.value],
);

/**
 * Opens the image viewer and sets the current index to the index of the image.
 */
const openViewer = (): void => {
  imageViewer.value = true;
  currentIndex.value = props.groupedImages.indexOf(props.image);
};

/**
 * Moves to the next image in the gallery.
 */
const nextImage = (): void => {
  currentIndex.value = (currentIndex.value + 1) % props.groupedImages.length;
};

/**
 * Moves to the previous image in the gallery.
 */
const prevImage = (): void => {
  currentIndex.value =
    (currentIndex.value - 1 + props.groupedImages.length) %
    props.groupedImages.length;
};

/**
 * Trims text to a specified maximum length and adds ellipsis if necessary.
 * @param {string} text - The text to trim.
 * @param {number} maxLength - The maximum length of the text.
 * @returns {string} The trimmed text.
 */
const trimText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) {
    return text;
  }
  return text.slice(0, maxLength) + '...';
};
</script>

<style scoped lang="scss">
.image-container {
  .absolute-center-left,
  .absolute-center-right {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
  }

  .absolute-center-left {
    left: 0;
  }

  .absolute-center-right {
    right: 0;
  }

  &:hover {
    .modal-icons {
      opacity: 1;
      visibility: visible;
    }
  }

  .modal-icons {
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    transition:
      opacity 0.3s ease,
      visibility 0.3s ease;
  }
}
.image-icons {
  position: absolute;
  opacity: 0;
  width: 100%;
  z-index: 1;
  height: 100%;
  left: 0;
  top: 0;
  background: #fffffff2;
  transition: all 0.5s;
}

.view-icon,
.insert-icon {
  font-size: 1.5em;
  color: $primary;
}
.delete-icon {
  font-size: 1.5em;
  color: $negative;
}
.q-item:hover .image-icons {
  opacity: 1;
}
.filename {
  position: absolute;
  padding: 8px;
  color: #fff;
  background: rgba(0, 0, 0, 0.47);
  right: 0;
  bottom: 0;
  left: 0;
  margin: 8px;
  text-align: center;
}
</style>
