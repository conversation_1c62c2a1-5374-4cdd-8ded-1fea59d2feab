<template>
  <q-item class="q-item col-md-4 col-sm-4 col-xs-3 q-pa-none">
    <div class="image-icons flex items-center justify-center q-pa-sm">
      <q-icon class="view-icon" name="search" @click="openViewer">
        <q-tooltip anchor="top middle" self="bottom middle" :offset="[0, 4]">
          View Transcription
        </q-tooltip>
      </q-icon>
      <q-icon
        v-if="transcript?.id"
        class="delete-icon"
        name="delete"
        @click="$emit('deleteTranscription')"
      >
        <q-tooltip anchor="top middle" self="bottom middle" :offset="[0, 4]">
          Delete Transcript
        </q-tooltip>
      </q-icon>
    </div>
    <q-item-section class="q-pa-sm">
      <q-icon class="self-center flex-center" name="transcribe" size="5em" />
      <div class="filename">
        {{ trimText(transcript.fileName, 7) }}
        <q-tooltip>{{ transcript.fileName }}</q-tooltip>
      </div>
    </q-item-section>
  </q-item>

  <q-dialog
    v-model="transcriptViewer"
    persistent
    transition-show="scale"
    transition-hide="scale"
    :maximized="maximizedToggle"
  >
    <q-card style="min-width: 60vw">
      <q-card-section class="q-pa-none">
        <q-bar class="bg-primary text-white">
          <q-icon name="img:robot.png" />

          <div>{{ currentTranscript.fileName }}</div>

          <q-space />

          <q-btn
            dense
            flat
            :icon="!maximizedToggle ? 'fullscreen' : 'fullscreen_exit'"
            @click="maximizedToggle = !maximizedToggle"
          >
            <q-tooltip v-if="!maximizedToggle" class="bg-white text-primary"
              >Maximize</q-tooltip
            >
          </q-btn>
          <q-btn dense flat icon="close" v-close-popup>
            <q-tooltip class="bg-white text-primary">Close</q-tooltip>
          </q-btn>
        </q-bar>
      </q-card-section>

      <q-card-section class="q-py-none" style="height: calc(100vh - 250px)">
        <div class="q-pa-md splitter-container">
          <div class="text-h6 sticky-header">
            <q-img src="robot.png" width="30px" /> Generated Result
          </div>
          <q-separator />
          <q-editor
            paragraph-tag="p"
            v-model="currentTranscript.transcription"
            :toolbar="toolbarItems"
            min-height="15em"
            height="calc(100vh - 370px)"
          />
        </div>
      </q-card-section>
      <q-card-actions
        align="left"
        :class="maximizedToggle ? 'absolute-bottom' : ''"
      >
        <q-btn
          flat
          label="Delete"
          icon="delete"
          color="danger"
          v-if="currentTranscript?.id"
          @click="$emit('deleteTranscription')"
        />
        <q-btn flat icon="close" label="Close" color="danger" v-close-popup />
        <q-space />
        <q-btn
          flat
          label="Insert After Selection"
          color="primary"
          icon="add"
          @click="onInsertContent(true, currentTranscript.transcription)"
          v-close-popup
        >
          <q-tooltip
            >Insert text immediately after the selected area.</q-tooltip
          >
        </q-btn>
        <q-btn
          flat
          label="Replace in Selection"
          color="primary"
          icon="cached"
          @click="onInsertContent(false, currentTranscript.transcription)"
          v-close-popup
        >
          <q-tooltip
            >Replace text within the currently selected area.</q-tooltip
          >
        </q-btn>
        <q-space />
        <q-btn
          flat
          dense
          v-if="transcripts.length > 1"
          icon="chevron_left"
          color="black"
          @click="prevTranscript"
        >
          <q-tooltip>Previous</q-tooltip>
        </q-btn>
        <q-btn
          flat
          dense
          v-if="transcripts.length > 1"
          icon="chevron_right"
          color="black"
          @click="nextTranscript"
        >
          <q-tooltip>Next</q-tooltip>
        </q-btn>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ChapterMedia } from 'src/entities/book';
import { onInsertContent } from 'src/features/book-transcribe';

// Define component props
const props = defineProps<{
  transcript: ChapterMedia;
  transcripts: ChapterMedia[];
}>();

// Define component emits
defineEmits<{
  deleteTranscription: (
    chaptermedia: ChapterMedia,
    selectedChapterId: string,
  ) => void;
}>();

// State to control the visibility of the transcript viewer
const transcriptViewer = ref(false);
// State to control the maximized view toggle
const maximizedToggle = ref(false);
// Current index of the transcript in the array
const currentIndex = ref(props.transcripts.indexOf(props.transcript));
// Toolbar items for the editor
const toolbarItems = [
  [
    'bold',
    'italic',
    'underline',
    'unordered',
    'outdent',
    'indent',
    'undo',
    'redo',
  ],
];

/**
 * Computes the current transcript based on the currentIndex
 * @returns {ChapterMedia} The current transcript
 */
const currentTranscript = computed(() => props.transcripts[currentIndex.value]);

/**
 * Opens the transcript viewer and sets the current index
 */
const openViewer = (): void => {
  transcriptViewer.value = true;
  currentIndex.value = props.transcripts.indexOf(props.transcript);
};

/**
 * Moves to the next transcript in the list
 */
const nextTranscript = (): void => {
  currentIndex.value = (currentIndex.value + 1) % props.transcripts.length;
};

/**
 * Moves to the previous transcript in the list
 */
const prevTranscript = (): void => {
  currentIndex.value =
    (currentIndex.value - 1 + props.transcripts.length) %
    props.transcripts.length;
};

/**
 * Trims the text to a specified maximum length and adds ellipsis if needed
 * @param {string} text - The text to trim
 * @param {number} maxLength - The maximum length of the text
 * @returns {string} The trimmed text
 */
const trimText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) {
    return text;
  }
  return text.slice(0, maxLength) + '...';
};
</script>

<style scoped lang="scss">
.image-container {
  .absolute-center-left,
  .absolute-center-right {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
  }

  .absolute-center-left {
    left: 0;
  }

  .absolute-center-right {
    right: 0;
  }

  &:hover {
    .modal-icons {
      opacity: 1;
      visibility: visible;
    }
  }

  .modal-icons {
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    transition:
      opacity 0.3s ease,
      visibility 0.3s ease;
  }
}
.image-icons {
  position: absolute;
  opacity: 0;
  width: 100%;
  z-index: 1;
  height: 100%;
  left: 0;
  top: 0;
  background: #fffffff2;
  transition: all 0.5s;
}

.view-icon,
.insert-icon {
  font-size: 1.5em;
  color: $primary;
}
.delete-icon {
  font-size: 1.5em;
  color: $negative;
}
.q-item:hover .image-icons {
  opacity: 1;
}
.filename {
  position: absolute;
  padding: 8px;
  color: #fff;
  background: rgba(0, 0, 0, 0.47);
  right: 0;
  bottom: 0;
  left: 0;
  margin: 8px;
  text-align: center;
}
</style>
