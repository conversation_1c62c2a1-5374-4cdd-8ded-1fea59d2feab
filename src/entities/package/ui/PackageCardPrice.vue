<template>
  <q-card-section>
    <p>Buy now for {{ priceAmount }}</p>
    <p>
      Billed every {{ price.interval_count }} {{ price.interval }}(s)
      <span v-if="price.trial_period_days">
        after a trial period of {{ price.trial_period_days }} days
      </span>
    </p>
  </q-card-section>
  <q-separator />
  <q-card-actions class="bg-primary">
    <q-btn
      flat
      color="red"
      icon="favorite"
      :disable="isLoading"
      :loading="isLoading"
      @click="buy"
      label="Buy Now!"
      text-color="white"
    >
      <q-tooltip> Buy now for {{ priceAmount }}</q-tooltip>
    </q-btn>
    <q-btn
      flat
      text-color="white"
      color="accent"
      to="/help"
      icon="info"
      label="More Info"
    >
      <q-tooltip> Ask for more info.</q-tooltip>
    </q-btn>
  </q-card-actions>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { Price } from '@stripe/firestore-stripe-payments';
import { goToPaymentPage } from '../api';

// Define component props with type annotation
const props = defineProps<{ price: Price }>();

/**
 * Computes the formatted price amount.
 * @returns {string} The formatted price amount.
 */
const priceAmount = computed((): string =>
  Intl.NumberFormat(undefined, {
    style: 'currency',
    currency: props.price.currency,
  }).format(props.price.unit_amount! / 100),
);

// Ref to track loading state
const isLoading = ref(false);

/**
 * Initiates the payment process.
 */
const buy = async (): Promise<void> => {
  isLoading.value = true;
  await goToPaymentPage(props.price);
  isLoading.value = false;
};
</script>
