<template>
  <div
    class="packages-page"
    :class="{ 'packages-page--dark': $q.dark.isActive }"
  >
    <!-- Hero Section -->
    <div class="hero-section">
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">Transform Your Ideas Into Published Books</h1>
          <p class="hero-subtitle text-primary">
            Join thousands of authors using Manuscriptr to write, edit, and
            publish their books with AI-powered assistance.
          </p>
        </div>
        <div class="hero-image">
          <q-img
            src="robot.png"
            alt="Manuscriptr AI Assistant"
            class="manny-hero"
            width="200px"
          />
        </div>
      </div>
    </div>

    <!-- Packages Section -->
    <div class="packages-section">
      <div class="packages-container">
        <div class="section-header">
          <h2 class="section-title">Choose Your Plan</h2>
          <p class="section-description">
            Start your writing journey today with our flexible subscription
            plans
          </p>
        </div>

        <!-- Loading State -->
        <div v-if="isLoading" class="packages-loading">
          <div class="loading-grid">
            <div v-for="n in 3" :key="n" class="loading-card">
              <q-skeleton type="rect" height="200px" class="q-mb-md" />
              <q-skeleton type="text" width="70%" class="q-mb-sm" />
              <q-skeleton type="text" width="90%" class="q-mb-md" />
              <q-skeleton type="rect" height="40px" />
            </div>
          </div>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="packages-error">
          <q-icon name="error" size="64px" color="negative" />
          <h3 class="error-title">Unable to Load Packages</h3>
          <p class="error-message">{{ error }}</p>
          <q-btn
            color="primary"
            label="Try Again"
            icon="refresh"
            @click="execute"
            unelevated
            class="retry-btn"
          />
        </div>

        <!-- Packages Grid -->
        <div v-else-if="packages && packages.length > 0" class="packages-grid">
          <div
            v-for="packageItem in packages"
            :key="packageItem.id"
            class="package-wrapper"
          >
            <PackageCard :package="packageItem" />
          </div>
        </div>

        <!-- No Packages State -->
        <div v-else class="no-packages">
          <q-icon
            name="inventory_2"
            size="64px"
            :color="$q.dark.isActive ? 'grey-6' : 'grey-4'"
          />
          <h3 class="no-packages-title">No Packages Available</h3>
          <p class="no-packages-message">
            We're working on bringing you the best subscription options. Check
            back soon!
          </p>
        </div>
      </div>
    </div>

    <!-- Features Section -->
    <div class="features-section">
      <div class="features-container">
        <h2 class="features-title">Why Choose Manuscriptr?</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">
              <q-icon name="auto_awesome" size="48px" color="primary" />
            </div>
            <h3 class="feature-title">AI-Powered Writing</h3>
            <p class="feature-description">
              Get help with outlines, content generation, and editing from our
              AI assistant Manny.
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <q-icon name="groups" size="48px" color="primary" />
            </div>
            <h3 class="feature-title">Collaborative Tools</h3>
            <p class="feature-description">
              Work with editors, reviewers, and other authors in our
              collaborative writing environment.
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <q-icon name="publish" size="48px" color="primary" />
            </div>
            <h3 class="feature-title">Easy Publishing</h3>
            <p class="feature-description">
              Export your finished book in multiple formats ready for publishing
              on any platform.
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- CTA Section -->
    <div class="cta-section">
      <div class="cta-content">
        <h2 class="cta-title">Ready to Start Writing?</h2>
        <p class="cta-description text-primary">
          Join our community of authors and bring your book ideas to life with
          Manuscriptr.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAsyncState } from '@vueuse/core';
import { useQuasar } from 'quasar';
import PackageCard from './PackageCard.vue';
import { getPackages } from '../api';

const $q = useQuasar();

// Async state for packages
const {
  state: packages,
  isLoading,
  error,
  execute,
} = useAsyncState(getPackages(), []);
</script>

<style lang="scss" scoped>
.packages-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

  &--dark {
    background: linear-gradient(
      90deg,
      var(--q-dark-page) 0%,
      color-mix(in srgb, var(--q-dark-page) 90%, white 10%) 100%
    );
  }
}

// Hero Section
.hero-section {
  padding: 80px 20px 60px;
  background: linear-gradient(
    135deg,
    var(--q-primary) 0%,
    var(--q-primary-dark) 100%
  );
  color: white;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(
      circle,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 70%
    );
    animation: float 20s infinite linear;
  }

  body.body--dark & {
    background: linear-gradient(
      135deg,
      var(--q-primary-light) 0%,
      var(--q-primary) 100%
    );
  }
}

@keyframes float {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 40px;
  align-items: center;
  position: relative;
  z-index: 2;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 30px;
  }
}

.hero-title {
  font-size: 3rem;
  font-weight: 700;
  margin: 0 0 20px;
  line-height: 1.2;

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }

  @media (max-width: 480px) {
    font-size: 2rem;
  }
}

.hero-subtitle {
  font-size: 1.25rem;
  opacity: 0.9;
  line-height: 1.6;
  margin: 0;

  @media (max-width: 768px) {
    font-size: 1.1rem;
  }
}

.manny-hero {
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  transform: perspective(1000px) rotateY(-5deg);
  transition: transform 0.3s ease;

  &:hover {
    transform: perspective(1000px) rotateY(0deg) scale(1.05);
  }

  @media (max-width: 768px) {
    width: 150px;
  }
}

// Packages Section
.packages-section {
  padding: 80px 20px;
  background: white;

  body.body--dark & {
    background: var(--q-dark);
  }
}

.packages-container {
  max-width: 1200px;
  margin: 0 auto;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 600;
  color: var(--q-dark);
  margin: 0 0 16px;

  body.body--dark & {
    color: var(--q-text-color);
  }

  @media (max-width: 768px) {
    font-size: 2rem;
  }
}

.section-description {
  font-size: 1.2rem;
  color: rgba(var(--q-text-color-rgb), 0.7);
  margin: 0;

  body.body--dark & {
    color: rgba(var(--q-text-color-rgb), 0.8);
  }
}

.packages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 30px;
  margin: 0 auto;
  max-width: 960px;
}

.package-wrapper {
  display: flex;
  flex-direction: column;
}

// Loading States
.packages-loading {
  .loading-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 30px;
    margin: 0 auto;
    max-width: 960px;
  }

  .loading-card {
    padding: 24px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);

    body.body--dark & {
      background: var(--q-dark);
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
    }
  }
}

.packages-error,
.no-packages {
  text-align: center;
  padding: 60px 20px;

  .error-title,
  .no-packages-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--q-dark);
    margin: 16px 0 8px;

    body.body--dark & {
      color: var(--q-text-color);
    }
  }

  .error-message,
  .no-packages-message {
    color: rgba(var(--q-text-color-rgb), 0.7);
    margin-bottom: 24px;

    body.body--dark & {
      color: rgba(var(--q-text-color-rgb), 0.8);
    }
  }

  .retry-btn {
    border-radius: 8px;
    padding: 8px 24px;
    font-weight: 500;
  }
}

// Features Section
.features-section {
  padding: 80px 20px;
  background: rgba(var(--q-primary-rgb), 0.03);

  body.body--dark & {
    background: rgba(var(--q-primary-rgb), 0.05);
  }
}

.features-container {
  max-width: 1200px;
  margin: 0 auto;
}

.features-title {
  font-size: 2.5rem;
  font-weight: 600;
  text-align: center;
  color: var(--q-dark);
  margin: 0 0 60px;

  body.body--dark & {
    color: var(--q-text-color);
  }

  @media (max-width: 768px) {
    font-size: 2rem;
    margin-bottom: 40px;
  }
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
}

.feature-card {
  background: white;
  padding: 40px 30px;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
  }

  body.body--dark & {
    background: var(--q-dark);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);

    &:hover {
      box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
    }
  }

  .feature-icon {
    margin-bottom: 20px;
  }

  .feature-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--q-dark);
    margin: 0 0 16px;

    body.body--dark & {
      color: var(--q-text-color);
    }
  }

  .feature-description {
    color: rgba(var(--q-text-color-rgb), 0.7);
    line-height: 1.6;
    margin: 0;

    body.body--dark & {
      color: rgba(var(--q-text-color-rgb), 0.8);
    }
  }
}

// CTA Section
.cta-section {
  padding: 80px 20px;
  background: linear-gradient(
    135deg,
    var(--q-primary) 0%,
    var(--q-primary-dark) 100%
  );
  color: white;
  text-align: center;

  body.body--dark & {
    background: linear-gradient(
      135deg,
      var(--q-primary-light) 0%,
      var(--q-primary) 100%
    );
  }
}

.cta-content {
  max-width: 600px;
  margin: 0 auto;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 600;
  margin: 0 0 16px;

  @media (max-width: 768px) {
    font-size: 2rem;
  }
}

.cta-description {
  font-size: 1.2rem;
  opacity: 0.9;
  line-height: 1.6;
  margin: 0 0 32px;

  @media (max-width: 768px) {
    font-size: 1.1rem;
  }
}

.cta-btn {
  border-radius: 12px;
  padding: 12px 32px;
  font-weight: 600;
  font-size: 1.1rem;
  background: white;
  color: var(--q-primary);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3);
  }

  body.body--dark & {
    background: rgba(255, 255, 255, 0.9);
    color: var(--q-primary-dark);
  }
}
</style>
