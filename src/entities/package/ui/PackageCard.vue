<template>
  <q-card
    flat
    bordered
    class="package-card column"
    :class="{ 'package-card--dark': $q.dark.isActive }"
  >
    <q-card-section horizontal class="justify-between">
      <q-card-section class="q-pt-xs">
        <div class="text-overline package-overline">Buy Now!</div>
        <div class="text-h5 q-mt-sm q-mb-xs package-title">
          {{ package.name }}
        </div>
        <div class="text-caption package-description">
          {{ package.description }}
        </div>
      </q-card-section>
      <q-card-section>
        <q-img
          class="rounded-borders robot-image"
          src="robot.png"
          width="62px"
        />
      </q-card-section>
    </q-card-section>

    <q-separator />
    <div v-for="price in package.prices" :key="price.id">
      <PackageCardPrice :price="price" v-if="price.type === 'recurring'" />
    </div>
  </q-card>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import type { Package } from '../model/package';
import PackageCardPrice from './PackageCardPrice.vue';

defineProps<{ package: Package }>();

const $q = useQuasar();
</script>

<style lang="scss" scoped>
.package-card {
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.08);
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }

  &--dark {
    background: var(--q-dark);
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

    &:hover {
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
    }
  }

  .package-overline {
    color: var(--q-primary);
    font-weight: 600;
    letter-spacing: 0.5px;

    body.body--dark & {
      color: var(--q-primary-light);
    }
  }

  .package-title {
    color: var(--q-dark);
    font-weight: 600;

    body.body--dark & {
      color: var(--q-text-color);
    }
  }

  .package-description {
    color: rgba(var(--q-text-color-rgb), 0.7);

    body.body--dark & {
      color: rgba(var(--q-text-color-rgb), 0.8);
    }
  }

  .robot-image {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(var(--q-primary-rgb), 0.2);
  }

  // Separator styling for dark mode
  .q-separator {
    background: rgba(var(--q-text-color-rgb), 0.12);

    body.body--dark & {
      background: rgba(var(--q-text-color-rgb), 0.2);
    }
  }
}
</style>
