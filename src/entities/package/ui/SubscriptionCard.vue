<template>
  <!-- Loading State -->
  <div
    v-if="isLoadingProduct"
    class="subscription-loading"
    :class="{ 'subscription-loading--dark': $q.dark.isActive }"
  >
    <div class="loading-header">
      <q-skeleton type="rect" height="80px" class="q-mb-md" />
    </div>
    <div class="loading-content">
      <q-skeleton type="text" width="60%" class="q-mb-sm" />
      <q-skeleton type="text" width="80%" class="q-mb-lg" />
      <q-skeleton type="text" width="40%" class="q-mb-sm" />
      <q-skeleton type="text" width="70%" class="q-mb-sm" />
      <q-skeleton type="text" width="50%" />
    </div>
  </div>

  <!-- Error State -->
  <div
    v-else-if="errorProduct"
    class="subscription-error"
    :class="{ 'subscription-error--dark': $q.dark.isActive }"
  >
    <div class="error-content">
      <q-icon name="error" color="negative" size="48px" class="error-icon" />
      <h3 class="error-title">Something went wrong</h3>
      <p class="error-message">{{ errorProduct }}</p>
      <q-btn
        color="primary"
        label="Try Again"
        icon="refresh"
        @click="retryFetch"
        unelevated
        class="retry-btn"
      />
    </div>
  </div>

  <!-- Subscription Card -->
  <q-card
    v-else-if="product != null"
    class="subscription-card"
    :class="{ 'subscription-card--dark': $q.dark.isActive }"
  >
    <!-- Card Header with Status -->
    <div
      class="subscription-header"
      :class="[
        `status-${subscription.status}`,
        { 'subscription-header--dark': $q.dark.isActive },
      ]"
    >
      <div class="header-overlay"></div>
      <div class="header-content">
        <div class="product-info">
          <div class="product-icon">
            <q-avatar size="50px" class="product-avatar">
              <q-img src="robot.png" />
            </q-avatar>
          </div>
          <div class="product-details">
            <h3 class="product-name">
              {{ product.metadata.appName ?? product.name }}
            </h3>
            <p class="product-subtitle">Premium Subscription</p>
          </div>
        </div>
        <div class="status-section">
          <q-badge
            :color="statusColor"
            class="status-badge"
            :class="{ 'status-badge--dark': $q.dark.isActive }"
          >
            <q-icon :name="statusIcon" size="xs" class="q-mr-xs" />
            {{ formatStatus(subscription.status) }}
          </q-badge>
        </div>
      </div>
    </div>

    <!-- Card Content -->
    <div class="subscription-content">
      <div class="description-section">
        <p class="product-description">
          {{ product.metadata.subscriptionDetails ?? product.description }}
        </p>
      </div>

      <div class="subscription-details">
        <!-- Started Date -->
        <div class="detail-item">
          <div class="detail-icon-wrapper">
            <q-icon
              name="event"
              size="20px"
              :color="$q.dark.isActive ? 'primary-light' : 'primary'"
            />
          </div>
          <div class="detail-content">
            <span class="detail-label">Subscription Started</span>
            <span class="detail-value">
              {{ formatDate(subscription.current_period_start) }}
            </span>
          </div>
        </div>

        <!-- Current Billing Period -->
        <div
          class="detail-item"
          v-if="userLoggedIn && userLoggedIn.stripeId && !userLoggedIn.paypalId"
        >
          <div class="detail-icon-wrapper">
            <q-icon
              name="date_range"
              size="20px"
              :color="$q.dark.isActive ? 'primary-light' : 'primary'"
            />
          </div>
          <div class="detail-content">
            <span class="detail-label">Current Billing Period</span>
            <span class="detail-value">
              {{ formatDate(subscription.current_period_start) }} &ndash;
              {{ formatDate(subscription.current_period_end) }}
            </span>
          </div>
        </div>

        <!-- Billing Information -->
        <div class="detail-item" v-if="price">
          <div class="detail-icon-wrapper">
            <q-icon
              name="payments"
              size="20px"
              :color="$q.dark.isActive ? 'primary-light' : 'primary'"
            />
          </div>
          <div class="detail-content">
            <span class="detail-label">Billing Details</span>
            <div class="detail-value billing-info" v-html="billingAmount" />
          </div>
        </div>

        <!-- Next Billing Date -->
        <div
          class="detail-item"
          v-if="
            subscription.status === 'active' && subscription.current_period_end
          "
        >
          <div class="detail-icon-wrapper">
            <q-icon
              name="schedule"
              size="20px"
              :color="$q.dark.isActive ? 'primary-light' : 'primary'"
            />
          </div>
          <div class="detail-content">
            <span class="detail-label">Next Billing Date</span>
            <span class="detail-value next-billing">
              {{ formatDate(subscription.current_period_end) }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Card Actions -->
    <div class="subscription-actions">
      <div class="actions-container">
        <!-- Cancel Subscription Button -->
        <q-btn
          v-if="!invoiceDetails.isLoading && invoiceDetails?.showCancelButton"
          class="action-btn cancel-btn"
          :class="{ 'cancel-btn--dark': $q.dark.isActive }"
          icon="cancel"
          label="Cancel Subscription"
          @click="cancel"
          :disable="cancelSubscriptionAction[subscription.id]"
          :loading="cancelSubscriptionAction[subscription.id]"
          unelevated
        >
          <q-tooltip class="bg-negative text-white">
            Cancel your subscription to Manuscriptr
          </q-tooltip>
        </q-btn>

        <!-- Manage Payment Button -->
        <q-btn
          v-if="userLoggedIn.stripeId"
          class="action-btn payment-btn"
          :class="{ 'payment-btn--dark': $q.dark.isActive }"
          icon="fa-brands fa-stripe"
          label="Manage Payment"
          @click="portalLink"
          :disable="!linkToStripe"
          unelevated
        >
          <q-tooltip class="bg-primary text-white">
            Update payment methods and billing information
          </q-tooltip>
        </q-btn>
      </div>
    </div>

    <!-- Loading Overlay -->
    <q-inner-loading
      :showing="isLoading"
      :color="$q.dark.isActive ? 'white' : 'primary'"
      label="Processing..."
      label-class="text-weight-medium loading-label"
      label-style="font-size: 1.1em"
    >
      <div class="loading-overlay-content">
        <q-spinner-dots
          :color="$q.dark.isActive ? 'white' : 'primary'"
          size="3em"
        />
        <div class="loading-info">
          <q-avatar size="32px" class="q-mr-sm">
            <q-img src="robot.png" />
          </q-avatar>
          <span class="loading-text">Processing your request...</span>
        </div>
      </div>
    </q-inner-loading>
  </q-card>
</template>

<script setup lang="ts">
import { useAsyncState } from '@vueuse/core';
import {
  cancelSubscription,
  getPackage,
  getPriceInfo,
  getSubscriptionInvoiceDetails,
  goToPortalLink,
} from '../api';
import { type Price, Subscription } from '@stripe/firestore-stripe-payments';
import { computed, ref, watch, onMounted } from 'vue';
import {
  confirmDeletion,
  confirmOverrideText,
  showError,
} from 'src/shared/lib/quasar-dialogs';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { cancelSubscriptionAction } from 'src/entities/package';
import { userLoggedIn } from 'src/entities/user';

const emits = defineEmits<{
  cancelled: [];
}>();

// Define component props with a type
const props = defineProps<{ subscription: Subscription }>();
// Reactive reference for loading state
const isLoading = ref(false);
// Access to the Vue Router instance
const router = useRouter();
// Access to Quasar framework instance
const $q = useQuasar();
// Reactive reference for link storage
const linkToStripe = ref('');

// Async state for fetching product details
const {
  state: product,
  isLoading: isLoadingProduct,
  error: errorProduct,
  execute: retryFetch,
} = useAsyncState(getPackage(props.subscription.product), null);

// Async state for fetching price information
const {
  state: price,
  isLoading: isLoadingPrice,
  error: errorPrice,
} = useAsyncState(
  getPriceInfo(props.subscription.product, props.subscription.price),
  null,
);

// Invoice details state
const invoiceDetails = ref({
  showCancelButton: true,
  amountToPay: price?.unit_amount!,
  isLoading: false,
});

// Computed property to determine the status color based on subscription status
const statusColor = computed(() => {
  const { status } = props.subscription;
  switch (status) {
    case 'active':
      return 'positive';
    case 'trialing':
      return 'warning';
    case 'canceled':
    case 'incomplete':
    case 'incomplete_expired':
    case 'past_due':
    case 'unpaid':
      return 'negative';
    default:
      return 'grey-6';
  }
});

// Computed property for status icon
const statusIcon = computed(() => {
  const { status } = props.subscription;
  switch (status) {
    case 'active':
      return 'check_circle';
    case 'trialing':
      return 'schedule';
    case 'canceled':
      return 'cancel';
    case 'incomplete':
    case 'incomplete_expired':
      return 'warning';
    case 'past_due':
    case 'unpaid':
      return 'error';
    default:
      return 'help';
  }
});

// Helper function to format status text
const formatStatus = (status: string): string => {
  return status
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

// Helper function to format dates
const formatDate = (timestamp: string): string => {
  return new Date(timestamp).toLocaleDateString(undefined, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

/**
 * Opens a new tab with the Stripe portal link.
 */
const portalLink = async (): Promise<void> => {
  if (linkToStripe.value) {
    window.open(linkToStripe.value, '_blank');
  }
};

// Fetch the Stripe portal link on component mount
onMounted(async () => {
  if (userLoggedIn?.value?.stripeId) {
    try {
      linkToStripe.value = await goToPortalLink(userLoggedIn?.value?.stripeId);
    } catch (error) {
      console.error('Error fetching portal link:', error);
    }
  }
});

/**
 * Converts a timestamp string to a locale date string.
 * @param timestamp - The timestamp to convert.
 * @returns The locale date string.
 */
const toLocaleDate = (timestamp: string): string => {
  return new Date(timestamp).toLocaleDateString();
};

// Watch for price loading changes to update invoice details
watch(isLoadingPrice, async () => {
  if (
    !isLoadingPrice.value &&
    (price.value?.metadata?.allowCancelWhenInvoicePaid ||
      !price.value?.unit_amount)
  ) {
    try {
      invoiceDetails.value = await getSubscriptionInvoiceDetails(
        props.subscription,
        price.value as Price,
      );
      if (invoiceDetails.value.amountToPay) {
        price.value.unit_amount = invoiceDetails.value.amountToPay;
      }
    } catch (e) {
      console.log('error retrieving invoices');
    }
  }
});

// Computed property for formatted price amount
const priceAmount = computed(() => {
  return Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'usd',
  }).format(
    (price.value?.unit_amount || invoiceDetails.value.amountToPay || 0) / 100,
  );
});

const billingAmount = computed(() => {
  if (props.subscription.status === 'trialing') {
    return `<span class="trial-notice">Currently in trial period</span>`;
  } else {
    return `<strong>Billed at</strong> <span class="price-highlight">${
      priceAmount.value
    }</span> every ${price.value?.interval_count} ${price.value?.interval}${
      price.value?.interval_count > 1 ? 's' : ''
    }`;
  }
});

/**
 * Cancels the subscription and handles UI updates.
 */
const cancel = async (): Promise<void> => {
  isLoading.value = true;

  try {
    const confirmed = await confirmOverrideText($q, {
      title: 'Cancel Subscription',
      message: `Are you sure you want to cancel your subscription to Manuscriptr? This action cannot be undone.`,
    });

    if (confirmed) {
      cancelSubscriptionAction.value[props.subscription.id] = true;

      $q.notify({
        message: 'Processing your cancellation request. Please wait...',
        color: 'info',
        multiLine: true,
        avatar: 'robot.png',
        timeout: 3000,
      });

      const isCancelled = await cancelSubscription(
        userLoggedIn.value?.stripeId,
      );

      if (!isCancelled) {
        await showError(
          $q,
          'Cancellation could not be processed. Please try again or contact support.',
        );
        delete cancelSubscriptionAction.value[props.subscription.id];
      } else {
        $q.dialog({
          title: 'Cancellation Successful',
          message:
            'Your subscription to Manuscriptr has been cancelled. You will continue to have access until the end of your current billing period.',
          persistent: true,
          ok: {
            color: 'primary',
            label: 'Got it',
          },
        }).onOk(() => {
          location.reload();
        });

        emits('cancelled');
      }
    }
  } catch (error) {
    console.error('Error canceling subscription:', error);
    $q.notify({
      message:
        'An error occurred while canceling your subscription. Please try again.',
      color: 'negative',
      icon: 'error',
    });
    delete cancelSubscriptionAction.value[props.subscription.id];
  } finally {
    isLoading.value = false;
  }
};
</script>

<style lang="scss" scoped>
// Loading State Styles
.subscription-loading {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 35px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.06);

  &--dark {
    background: var(--q-dark);
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.4);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .loading-header {
    padding: 20px 24px 0;
  }

  .loading-content {
    padding: 0 24px 24px;
  }
}

// Error State Styles
.subscription-error {
  background: white;
  border-radius: 20px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 10px 35px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(var(--q-negative-rgb), 0.2);

  &--dark {
    background: var(--q-dark);
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.4);
    border-color: rgba(var(--q-negative-rgb), 0.3);
  }

  .error-content {
    max-width: 300px;
    margin: 0 auto;
  }

  .error-icon {
    margin-bottom: 16px;
    opacity: 0.8;
  }

  .error-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--q-negative);
    margin: 0 0 8px;
  }

  .error-message {
    color: rgba(var(--q-text-color-rgb), 0.7);
    margin: 0 0 24px;
    line-height: 1.5;

    body.body--dark & {
      color: rgba(var(--q-text-color-rgb), 0.8);
    }
  }

  .retry-btn {
    border-radius: 12px;
    font-weight: 500;
    padding: 8px 20px;
  }
}

// Main Subscription Card Styles
.subscription-card {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  margin-bottom: 24px;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.06);
  position: relative;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.12);
  }

  &--dark {
    background: var(--q-dark);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
    border-color: rgba(255, 255, 255, 0.1);

    &:hover {
      box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
    }
  }
}

// Header Styles
.subscription-header {
  padding: 30px 32px;
  position: relative;
  overflow: hidden;
  background: linear-gradient(
    135deg,
    var(--q-primary) 0%,
    var(--q-primary-dark) 100%
  );

  &--dark {
    background: linear-gradient(
      135deg,
      var(--q-primary-light) 0%,
      var(--q-primary) 100%
    );
  }

  .header-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      circle at top right,
      rgba(255, 255, 255, 0.2) 0%,
      transparent 60%
    );
    pointer-events: none;
  }

  // Status-specific styling
  &.status-active {
    background: linear-gradient(
      135deg,
      var(--q-positive) 0%,
      color-mix(in srgb, var(--q-positive) 85%, white 15%) 100%
    );
  }

  &.status-trialing {
    background: linear-gradient(
      135deg,
      var(--q-warning) 0%,
      color-mix(in srgb, var(--q-warning) 85%, white 15%) 100%
    );
  }

  &.status-canceled,
  &.status-incomplete,
  &.status-incomplete_expired,
  &.status-past_due,
  &.status-unpaid {
    background: linear-gradient(
      135deg,
      var(--q-negative) 0%,
      color-mix(in srgb, var(--q-negative) 85%, white 15%) 100%
    );
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;

    @media (max-width: 600px) {
      flex-direction: column;
      gap: 16px;
      text-align: center;
    }
  }

  .product-info {
    display: flex;
    align-items: center;
    gap: 16px;

    @media (max-width: 600px) {
      flex-direction: column;
      gap: 12px;
    }
  }

  .product-avatar {
    background: rgba(255, 255, 255, 0.2);
    border: 3px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  }

  .product-name {
    margin: 0 0 4px;
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

    @media (max-width: 600px) {
      font-size: 1.25rem;
    }
  }

  .product-subtitle {
    margin: 0;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }

  .status-badge {
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);

    &--dark {
      background: rgba(255, 255, 255, 0.15);
      border-color: rgba(255, 255, 255, 0.25);
    }
  }
}

// Content Styles
.subscription-content {
  padding: 32px;

  @media (max-width: 600px) {
    padding: 24px;
  }

  .description-section {
    margin-bottom: 28px;
  }

  .product-description {
    color: rgba(var(--q-text-color-rgb), 0.7);
    margin: 0;
    line-height: 1.6;
    font-size: 1rem;

    body.body--dark & {
      color: rgba(var(--q-text-color-rgb), 0.85);
    }
  }

  .subscription-details {
    .detail-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 20px;
      padding: 16px;
      background: rgba(var(--q-primary-rgb), 0.03);
      border-radius: 12px;
      border: 1px solid rgba(var(--q-primary-rgb), 0.08);
      transition: all 0.2s ease;

      &:hover {
        background: rgba(var(--q-primary-rgb), 0.06);
        border-color: rgba(var(--q-primary-rgb), 0.15);
      }

      &:last-child {
        margin-bottom: 0;
      }

      body.body--dark & {
        background: rgba(var(--q-primary-rgb), 0.08);
        border-color: rgba(var(--q-primary-rgb), 0.15);

        &:hover {
          background: rgba(var(--q-primary-rgb), 0.12);
          border-color: rgba(var(--q-primary-rgb), 0.25);
        }
      }

      .detail-icon-wrapper {
        margin-right: 16px;
        padding: 8px;
        background: rgba(var(--q-primary-rgb), 0.1);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;

        body.body--dark & {
          background: rgba(var(--q-primary-rgb), 0.2);
        }
      }

      .detail-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 4px;

        .detail-label {
          font-size: 0.85rem;
          font-weight: 600;
          color: var(--q-primary);
          text-transform: uppercase;
          letter-spacing: 0.5px;

          body.body--dark & {
            color: var(--q-primary-light);
          }
        }

        .detail-value {
          font-weight: 500;
          color: var(--q-text-color);
          line-height: 1.4;

          &.billing-info {
            :deep(.price-highlight) {
              color: var(--q-positive);
              font-weight: 700;
              font-size: 1.1em;
            }

            :deep(.trial-notice) {
              color: var(--q-warning);
              font-weight: 600;
              font-style: italic;
            }
          }

          &.next-billing {
            color: var(--q-info);
            font-weight: 600;
          }
        }
      }
    }
  }
}

// Actions Styles
.subscription-actions {
  background: linear-gradient(
    135deg,
    rgba(var(--q-primary-rgb), 0.05) 0%,
    rgba(var(--q-primary-rgb), 0.1) 100%
  );
  padding: 24px 32px;
  border-top: 1px solid rgba(var(--q-primary-rgb), 0.12);

  body.body--dark & {
    background: linear-gradient(
      135deg,
      rgba(var(--q-primary-rgb), 0.08) 0%,
      rgba(var(--q-primary-rgb), 0.15) 100%
    );
    border-top-color: rgba(var(--q-primary-rgb), 0.2);
  }

  @media (max-width: 600px) {
    padding: 20px 24px;
  }

  .actions-container {
    display: flex;
    justify-content: flex-end;
    gap: 16px;

    @media (max-width: 600px) {
      flex-direction: column;
      gap: 12px;
    }
  }

  .action-btn {
    border-radius: 12px;
    padding: 12px 24px;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &:before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
      );
      transition: left 0.5s ease;
    }

    &:hover:before {
      left: 100%;
    }

    &:hover {
      transform: translateY(-2px);
    }

    @media (max-width: 600px) {
      width: 100%;
      justify-content: center;
    }

    .q-icon {
      margin-right: 8px;
    }
  }

  .cancel-btn {
    background: linear-gradient(
      135deg,
      var(--q-negative) 0%,
      color-mix(in srgb, var(--q-negative) 85%, white 15%) 100%
    );
    color: white;
    box-shadow: 0 6px 20px rgba(var(--q-negative-rgb), 0.25);

    &:hover {
      box-shadow: 0 10px 30px rgba(var(--q-negative-rgb), 0.4);
    }

    &--dark {
      background: linear-gradient(
        135deg,
        var(--q-negative) 0%,
        color-mix(in srgb, var(--q-negative) 90%, white 10%) 100%
      );
    }
  }

  .payment-btn {
    background: linear-gradient(
      135deg,
      var(--q-primary) 0%,
      color-mix(in srgb, var(--q-primary) 85%, white 15%) 100%
    );
    color: white;
    box-shadow: 0 6px 20px rgba(var(--q-primary-rgb), 0.25);

    &:hover {
      box-shadow: 0 10px 30px rgba(var(--q-primary-rgb), 0.4);
    }

    &--dark {
      background: linear-gradient(
        135deg,
        var(--q-primary-light) 0%,
        var(--q-primary) 100%
      );
    }
  }
}

// Loading Overlay Styles
:deep(.q-inner-loading) {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);

  body.body--dark & {
    background: rgba(var(--q-dark-rgb), 0.95);
  }

  .loading-overlay-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  .loading-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .loading-text {
      color: var(--q-primary);
      font-weight: 500;

      body.body--dark & {
        color: var(--q-primary-light);
      }
    }
  }

  .loading-label {
    color: var(--q-primary) !important;

    body.body--dark & {
      color: var(--q-primary-light) !important;
    }
  }
}

// Responsive adjustments
@media (max-width: 600px) {
  .subscription-card {
    border-radius: 16px;
    margin-bottom: 16px;

    &:hover {
      transform: translateY(-4px);
    }
  }

  .subscription-header {
    padding: 24px 20px;

    .product-name {
      font-size: 1.25rem;
    }

    .product-subtitle {
      font-size: 0.85rem;
    }

    .status-badge {
      padding: 6px 12px;
      font-size: 0.75rem;
    }
  }

  .subscription-content {
    padding: 20px;

    .subscription-details {
      .detail-item {
        padding: 12px;
        margin-bottom: 16px;

        .detail-icon-wrapper {
          margin-right: 12px;
          padding: 6px;
        }

        .detail-content {
          .detail-label {
            font-size: 0.8rem;
          }

          .detail-value {
            font-size: 0.9rem;
          }
        }
      }
    }
  }

  .subscription-actions {
    padding: 16px 20px;

    .action-btn {
      padding: 10px 20px;
      font-size: 0.85rem;
    }
  }
}

// Animation keyframes
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Apply fade in animation to the card
.subscription-card {
  animation: fadeInUp 0.6s ease-out;
}

// Enhanced skeleton loading animation
:deep(.q-skeleton) {
  background: linear-gradient(
    90deg,
    rgba(var(--q-primary-rgb), 0.1) 0%,
    rgba(var(--q-primary-rgb), 0.2) 50%,
    rgba(var(--q-primary-rgb), 0.1) 100%
  );
  background-size: 200px 100%;
  animation: shimmer 1.2s ease-in-out infinite;

  body.body--dark & {
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0.05) 0%,
      rgba(255, 255, 255, 0.1) 50%,
      rgba(255, 255, 255, 0.05) 100%
    );
  }
}

// Tooltip customization
:deep(.q-tooltip) {
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.85rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

  &.bg-negative {
    background: var(--q-negative) !important;
  }

  &.bg-primary {
    background: var(--q-primary) !important;
  }
}

// Enhanced focus states for accessibility
.action-btn:focus-visible {
  outline: 3px solid rgba(var(--q-primary-rgb), 0.5);
  outline-offset: 2px;
}

.cancel-btn:focus-visible {
  outline-color: rgba(var(--q-negative-rgb), 0.5);
}

// Print styles
@media print {
  .subscription-card {
    box-shadow: none;
    border: 1px solid #ddd;
    break-inside: avoid;
  }

  .subscription-actions {
    display: none;
  }

  .subscription-header {
    background: #f5f5f5 !important;
    color: #333 !important;

    * {
      color: #333 !important;
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .subscription-card {
    border: 2px solid var(--q-primary);
  }

  .subscription-header {
    background: var(--q-primary) !important;
  }

  .detail-item {
    border: 1px solid var(--q-primary) !important;
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .subscription-card {
    transition: none;
    animation: none;
  }

  .action-btn {
    transition: none;

    &:before {
      transition: none;
    }

    &:hover {
      transform: none;
    }
  }

  :deep(.q-skeleton) {
    animation: none;
  }
}

// Dark mode specific enhancements
body.body--dark {
  .subscription-card {
    // Enhanced shadow for dark mode
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.4),
      0 0 0 1px rgba(255, 255, 255, 0.05);

    &:hover {
      box-shadow:
        0 20px 50px rgba(0, 0, 0, 0.5),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    }
  }

  // Enhanced text contrast for dark mode
  .product-description {
    color: rgba(255, 255, 255, 0.85);
  }

  .detail-label {
    color: var(--q-primary-light);
  }

  .detail-value {
    color: rgba(255, 255, 255, 0.95);
  }
}
</style>
