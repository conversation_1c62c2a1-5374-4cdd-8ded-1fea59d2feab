<template>
  <q-banner dense inline-actions class="text-white bg-primary">
    <template v-slot:avatar>
      <q-icon name="img:robot.png" size="26px" color="white" />
    </template>
    {{ unsubscribeMessage }}
    <!--
    <template v-slot:action>
      <q-btn flat color="white" label="Review Now!" to="/reviewing" />
    </template>
    -->
  </q-banner>
</template>
<script setup lang="ts">
import { ref } from 'vue';

// Define component props with types
const props = defineProps<{
  message: string; // Prop for message of type string
}>();

// Reactive reference for message, initialized with the prop value
const unsubscribeMessage = ref(props.message);
</script>

<style scoped></style>
