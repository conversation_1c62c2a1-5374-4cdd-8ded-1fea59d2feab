<template>
  <q-banner dense inline-actions class="text-white bg-red">
    <template v-slot:avatar>
      <q-icon name="img:robot.png" size="26px" color="white" />
    </template>
    {{ unsubscribeMessage }}
    <template v-slot:action>
      <q-btn flat color="white" label="Subscribe Now!" to="/packages" />
    </template>
  </q-banner>
</template>

<script setup lang="ts">
import { ref } from 'vue';

// Define component properties
const props = defineProps<{
  message: string;
}>();

// Reference to store the message property
const unsubscribeMessage = ref(props.message);
</script>

<style scoped></style>
