import {
  type Price,
  type Subscription,
  getProducts,
  getCurrentUserSubscriptions,
  createCheckoutSession,
  getProduct,
  getPrice,
} from '@stripe/firestore-stripe-payments';
import {
  cancelStripeSubscription,
  createStripePortalLink,
  getSubscriptionInvoicesPaid,
  payments,
} from 'src/shared/api/stripe';
import type { Package } from '../model/package';
import { firestore, functions } from 'src/firebase';
import { doc, getDoc } from 'firebase/firestore';
import { cancelPaypalSubscription } from 'src/shared/api/paypal';

/**
 * Fetches all packages available for purchase.
 * @returns {Promise<Package[]>} An array of packages.
 */
export const getPackages = async (): Promise<Package[]> => {
  const products = await getProducts(payments, {
    includePrices: true,
    activeOnly: true,
    where: [['metadata.showOnApp', '==', 'true']],
  });
  return products;
};

/**
 * Fetches a single package by its ID.
 * @param {string} id - The ID of the package.
 * @returns {Promise<Package>} The requested package.
 */
export const getPackage = async (id: string): Promise<Package> => {
  const product = await getProduct(payments, id, {
    includePrices: true,
  });
  return product;
};

/**
 * Retrieves pricing information for a specific product and price ID.
 * @param {string} productId - The product ID.
 * @param {string} priceId - The price ID.
 * @returns {Promise<Price>} The price details.
 */
export const getPriceInfo = async (
  productId: string,
  priceId: string,
): Promise<Price> => {
  const price = await getPrice(payments, productId, priceId);
  return price;
};

/**
 * Redirects the user to the payment page.
 * @param {Price} price - The price object containing the ID.
 */
export const goToPaymentPage = async (price: Price) => {
  const url = new URL(window.location.href);
  const session = await createCheckoutSession(payments, {
    price: price.id,
    success_url: `${url.origin}/books`,
    cancel_url: window.location.href,
  });
  window.location.assign(session.url);
};

/**
 * Generates a portal link for managing subscriptions.
 * @param {string} customer - The customer ID.
 * @returns {Promise<string>} The URL to the customer portal.
 */
export const goToPortalLink = async (customer: string): Promise<string> => {
  const { url } = await createStripePortalLink(customer);
  return url;
};

/**
 * Cancels a subscription.
 * @param {string | any} subscription - The subscription to cancel.
 * @returns {Promise<void>} A promise that resolves when the operation is complete.
 */
export const cancelSubscription = async (subscription: any) => {
  return subscription.includes('cus_')
    ? await cancelStripeSubscription(subscription)
    : await cancelPaypalSubscription(subscription.id);
};

/**
 * Retrieves subscription invoice details and determines if cancellation is allowed.
 * @param {Subscription} subscription - The subscription object.
 * @param {Price} price - The associated price object.
 * @returns {Promise<object>} An object containing invoice details.
 */
export const getSubscriptionInvoiceDetails = async (
  subscription: Subscription,
  price: Price,
) => {
  const noOfInvoicePaid = await getSubscriptionInvoicesPaid(subscription.id);
  const response = {
    showCancelButton: true,
    amountToPay: price.unit_amount!,
    isLoading: false,
  };
  if (price.metadata?.allowCancelWhenInvoicePaid) {
    response.showCancelButton =
      noOfInvoicePaid >= parseInt(price.metadata?.allowCancelWhenInvoicePaid);
  }
  if (!price.unit_amount && price.tiers.length > 0) {
    const graduated_price = price.tiers.find((item: any) => {
      return item.up_to && noOfInvoicePaid < item.up_to;
    });
    response.amountToPay = !graduated_price
      ? price.tiers[price.tiers.length - 1].unit_amount
      : graduated_price.unit_amount;
  }
  return response;
};

/**
 * Fetches the current user's subscriptions.
 * @returns {Promise<Subscription[]>} An array of subscriptions.
 */
export const getMySubscriptions = async (): Promise<Subscription[]> => {
  const subs = await getCurrentUserSubscriptions(payments, {
    status: ['active', 'trialing'],
  });
  return subs;
};

/**
 * Filters subscriptions to show only those relevant for the user's profile.
 * @returns {Promise<Subscription[]>} An array of filtered subscriptions.
 */
export const getMyProfileSubscriptions = async (): Promise<Subscription[]> => {
  const subs = await getMySubscriptions();
  const subsFiltered = await Promise.all(
    subs.map(async (sub) => {
      const docRef = doc(firestore, 'products', sub.product);
      const snapshot = await getDoc(docRef);
      const product = snapshot.data()!;
      return product.metadata?.showOnProfile == 'true' &&
        product.metadata?.mannySubscription == 'true'
        ? sub
        : null;
    }),
  );
  return subsFiltered.filter((sub) => sub !== null) as Subscription[];
};
