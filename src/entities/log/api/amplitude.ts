import { recordEventByApi } from 'src/shared/api/amplitude';

/**
 * Logs an event to Amplitude analytics.
 * @param {string} userId - The unique identifier for the user.
 * @param {string} eventType - The type of event being recorded.
 * @param {string} page - The page where the event occurred.
 */
export const logAmplitudeEvent = (
  userId: string,
  eventType: string,
  page: string,
): void => {
  recordEventByApi(userId, eventType, page);
};
