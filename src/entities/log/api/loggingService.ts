import { firestore } from 'src/firebase';
import {
  collection,
  addDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  doc,
  query,
  where,
  limit,
  startAfter,
  Timestamp,
} from 'firebase/firestore';
import { user } from 'src/entities/user';
import { LogData } from '../model';

/**
 * LoggingService class to handle CRUD operations for logs.
 */
class LoggingService {
  // Reference to the logs collection in Firestore
  private logsCollection = collection(firestore, 'logs');

  /**
   * Logs an action.
   * @param page - The page of the component where the action taken.
   * @param action - The action taken.
   * @param description - Description of the action.
   * @param module - Module or component name where the action occurred.
   * @param idReference - ID reference from the firestore database.
   * @returns A promise that resolves when the log is created.
   * @throws Error if no user is logged in.
   */
  public logAction = async (
    page: string,
    action: string,
    description: string,
    module: string,
    idReference: string,
  ): Promise<void> => {
    if (!user) throw new Error('No user is logged in');
    const logData: LogData = {
      id: '',
      authorId: user.value?.uid as string,
      userEmail: user.value?.email as string,
      userDisplayName:
        (user.value?.displayName as string) || (user.value?.email as string),
      action,
      description,
      timestamp: Timestamp.fromDate(new Date()),
      module,
      idReference,
    };

    const docRef = await addDoc(this.logsCollection, logData);
    const id = docRef.id;
    await updateDoc(docRef, { id });
  };

  /**
   * Retrieves a specific log by ID.
   * @param id - The ID of the log document.
   * @returns A promise that resolves to the log data or null if not found.
   */
  public getLog = async (id: string): Promise<LogData | null> => {
    const logDoc = doc(this.logsCollection, id);
    const docSnap = await getDoc(logDoc);

    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() } as LogData;
    } else {
      return null;
    }
  };

  /**
   * Retrieves logs with pagination and search functionality.
   * @param params - Parameters for pagination and search.
   * @returns A promise that resolves to an array of log documents.
   */
  public getLogs = async (params: {
    pageSize: number;
    lastDocId?: string;
    searchField?: string;
    searchValue?: string;
  }): Promise<any[]> => {
    const { pageSize, lastDocId, searchField, searchValue } = params;

    let q = query(this.logsCollection, limit(pageSize));

    if (searchField && searchValue) {
      q = query(q, where(searchField, '==', searchValue));
    }

    if (lastDocId) {
      const lastDocRef = doc(this.logsCollection, lastDocId);
      const lastDocSnap = await getDoc(lastDocRef);

      if (lastDocSnap.exists()) {
        q = query(q, startAfter(lastDocSnap));
      }
    }

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
  };

  /**
   * Updates a log.
   * @param id - The ID of the log document.
   * @param updateData - Data to update.
   * @returns A promise that resolves when the log is updated.
   */
  public updateLog = async (
    id: string,
    updateData: Partial<LogData>,
  ): Promise<void> => {
    const logDoc = doc(this.logsCollection, id);
    await updateDoc(logDoc, updateData);
  };

  /**
   * Deletes a log.
   * @param id - The ID of the log document.
   * @returns A promise that resolves when the log is deleted.
   */
  public deleteLog = async (id: string): Promise<void> => {
    const logDoc = doc(this.logsCollection, id);
    await deleteDoc(logDoc);
  };
}

// Singleton instance of LoggingService
export const loggingService = new LoggingService();
