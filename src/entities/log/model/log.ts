import { Timestamp } from 'firebase/firestore';

/**
 * Constants for actions
 */
export const ACTIONS = {
  CREATE: 'create', // Action for creating an item
  CLICK: 'click', // Action for clicking an item
  DELETE: 'delete', // Action for deleting an item
  UPDATE: 'update', // Action for updating an item
  EXPORT: 'export', // Action for exporting data
};

/**
 * Constants for page ui
 */
export const PAGES = {
  PROFILE: 'profile', // Page for listing books
  BOOKLIST: 'list', // Page for listing books
  BOOKMISSIONSTATEMENT: 'mission-statement', // Page for the book mission statement
  BOOKDETAIL: 'book-detail', // Page for book details
  BOOKPROMPTS: 'book-prompts', // Page for book prompts
};

/**
 * Interface for Log data
 */
export interface LogData {
  id: string; // Unique identifier for the log entry
  authorId: string; // Identifier for the author of the log entry
  userEmail: string; // Email of the user who performed the action
  userDisplayName: string; // Display name of the user who performed the action
  action: string; // Type of action performed
  description: string; // Description of the action performed
  timestamp: Timestamp; // Timestamp when the action was performed
  module: string; // Module where the action was performed
  idReference: string; // Reference ID related to the action
}
