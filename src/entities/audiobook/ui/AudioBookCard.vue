<template>
  <q-card>
    <q-card-section v-if="audioUrl" class="q-pa-none">
      <AudioDialogPlayer :tracks="track" />
    </q-card-section>
    <q-card-section class="q-pa-none bg-grey-4">
      <q-card-actions align="center">
        <q-btn
          flat
          :icon="audioUrl.audio ? 'sync' : 'add'"
          :label="audioUrl.audio ? 'Refresh' : 'New'"
          @click="$emit('refreshAudio')"
        >
          <q-tooltip anchor="top middle" self="bottom middle" :offset="[0, 4]">
            Regenerate Audio
          </q-tooltip>
        </q-btn>
        <q-btn
          v-if="audioUrl.audio"
          icon="delete"
          @click="$emit('deleteAudio')"
          label="Delete"
          color="danger"
          flat
        >
          <q-tooltip anchor="top middle" self="bottom middle" :offset="[0, 4]">
            Delete Audio
          </q-tooltip>
        </q-btn>
        <q-btn
          v-if="audioUrl.audio"
          icon="download"
          @click="downloadTrack"
          flat
        >
          <q-tooltip anchor="top middle" self="bottom middle" :offset="[0, 4]">
            Download Audio
          </q-tooltip>
        </q-btn>
      </q-card-actions>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import AudioDialogPlayer from 'src/entities/audiobook/ui/AudioDialogPlayer.vue';
import { computed } from 'vue';
import { user } from 'src/entities/user';
import { ChapterOutline } from 'src/entities/book';

// Define component props
const props = defineProps<{
  audioUrl: ChapterOutline;
}>();

// Define component emits
defineEmits<{
  deleteAudio: [];
  refreshAudio: [];
}>();

// Computed property to create track details
const track = computed(() => {
  return [
    {
      id: props.audioUrl.id,
      title: props.audioUrl.title,
      artist: user.value.displayName || user.value.email,
      src: props.audioUrl?.audio ?? '',
    },
  ];
});

/**
 * Downloads the audio track from the provided URL.
 */
const downloadTrack = (): void => {
  if (props.audioUrl?.audio) {
    fetch(props.audioUrl.audio)
      .then((response: Response) => response.blob())
      .then((blob: Blob) => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `${props.audioUrl.title}.mp3`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      })
      .catch((error: Error) => {
        console.error('Error downloading audio:', error);
        // Handle error if needed
      });
  }
};
</script>

<style scoped lang="scss">
.replace-icon {
  font-size: 24px;
  color: $primary;
}
.delete-icon {
  font-size: 24px;
  color: $negative;
}
</style>
