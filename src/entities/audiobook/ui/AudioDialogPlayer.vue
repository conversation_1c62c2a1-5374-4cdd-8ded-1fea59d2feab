<template>
  <q-card
    :style="
      currentTrack.minWidth ? { minWidth: currentTrack.minWidth + 'px' } : {}
    "
    class="q-pa-none no-box-shadow"
  >
    <q-card-section class="text-h7 text-right" v-if="tracks.length !== 1">
      showing {{ currentTrackIndex + 1 }} of {{ tracks.length }}
    </q-card-section>
    <q-card-section class="q-pa-none">
      <q-card
        :style="
          currentTrack.minWidth
            ? { minWidth: currentTrack.minWidth + 'px' }
            : { minWidth: '' }
        "
        v-model="dialog"
      >
        <q-linear-progress :value="progress" color="pink" />

        <q-card-section class="row items-center no-wrap">
          <div v-if="currentTrack.src || tracks.length === 1">
            <div class="text-weight-bold">{{ currentTrack.title }}</div>
            <div class="text-grey">{{ currentTrack.artist }}</div>
          </div>
          <div v-else-if="!currentTrack.src && tracks.length !== 1">
            <div class="text-weight-bold">
              (Uploading...) {{ currentTrack.title }}
            </div>
            <div class="text-grey"><q-skeleton type="text" /></div>
          </div>

          <q-space />

          <q-btn
            flat
            round
            icon="fast_rewind"
            v-if="tracks.length !== 1"
            @click="playPrevious"
            :disable="disableButtons"
          />
          <q-btn
            flat
            round
            :icon="isPlaying === true ? 'pause' : 'play_arrow'"
            @click="togglePlayPause(isPlaying)"
            v-if="currentTrack.src"
            :disable="disableButtons"
          />
          <q-spinner-bars
            v-else-if="!currentTrack.src && currentTrack.minWidth"
            color="primary"
            size="1.5em"
          />
          <q-btn
            flat
            round
            icon="fast_forward"
            :disable="disableButtons"
            v-if="tracks.length !== 1"
            @click="playNext"
          />

          <!-- Download button -->
          <q-btn
            flat
            round
            icon="cloud_download"
            v-if="currentTrack.src"
            @click="downloadTrack"
          />
        </q-card-section>
      </q-card>
    </q-card-section>
    <audio
      v-if="currentTrack.src"
      ref="audio"
      :src="currentTrack.src"
      @timeupdate="updateProgress"
    ></audio>
  </q-card>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

const props = defineProps({
  tracks: Array, // Define the type of elements in the array if known
});

// State variables
const dialog = ref<boolean>(true); // Ensure dialog is open by default
const disableButtons = ref<boolean>(false);
const isPlaying = ref<boolean>(false);
const progress = ref<number>(0);
const currentTrackIndex = ref<number>(0);
const currentTrack = ref<any>(props.tracks[currentTrackIndex.value]); // Define the type of track if known
const audio = ref<HTMLAudioElement | null>(null);

/**
 * Toggles play or pause state of the audio.
 * @param playing - Current playing state.
 */
const togglePlayPause = (playing: boolean): void => {
  if (audio.value) {
    if (playing) {
      audio.value.pause();
      isPlaying.value = false;
    } else {
      audio.value.play();
      isPlaying.value = true;
    }
  }
};

/**
 * Plays the previous track in the list.
 */
const playPrevious = (): void => {
  if (currentTrackIndex.value > 0) {
    currentTrackIndex.value--;
  } else {
    currentTrackIndex.value = props.tracks.length - 1;
  }
  loadTrack();
  playAudio(true);
};

/**
 * Plays the next track in the list.
 */
const playNext = (): void => {
  if (currentTrackIndex.value < props.tracks.length - 1) {
    currentTrackIndex.value++;
  } else {
    currentTrackIndex.value = 0;
  }
  loadTrack();
  playAudio(true);
};

/**
 * Loads the current track.
 */
const loadTrack = (): void => {
  currentTrack.value = props.tracks[currentTrackIndex.value];
  if (audio.value) {
    disableButtons.value = true;
    progress.value = 0;
    audio.value.load();
    isPlaying.value = false;
  }
};

/**
 * Plays the audio with an option to autoplay.
 * @param autoplay - Whether to start playing automatically.
 */
const playAudio = (autoplay: boolean = false): void => {
  if (audio.value) {
    if (autoplay) {
      setTimeout(() => {
        disableButtons.value = false;
        audio.value.play();
        isPlaying.value = true;
      }, 200);
    }
  }
};

/**
 * Updates the progress of the audio playback.
 */
const updateProgress = (): void => {
  if (audio.value) {
    progress.value = audio.value.currentTime / audio.value.duration;
  }
};

/**
 * Downloads the current track.
 */
const downloadTrack = (): void => {
  if (currentTrack.value.src) {
    fetch(currentTrack.value.src)
      .then((response) => response.blob())
      .then((blob) => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `${currentTrack.value.title}.mp3`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      })
      .catch((error) => {
        console.error('Error downloading audio:', error);
        // Handle error if needed
      });
  }
};

// Watch for changes in props.tracks and reset currentTrackIndex and currentTrack
watch(
  () => props.tracks,
  (newTracks) => {
    currentTrackIndex.value = 0;
    currentTrack.value = newTracks[currentTrackIndex.value];
  },
);
</script>

<style scoped>
/* Optional styling for your component */
</style>
