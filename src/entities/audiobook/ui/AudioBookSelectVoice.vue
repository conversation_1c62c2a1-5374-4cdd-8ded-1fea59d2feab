<template>
  <q-card>
    <q-card-section class="q-pa-none">
      <q-bar class="bg-primary text-white">
        <q-icon name="img:robot.png" />

        <div>Generate AudioBook</div>

        <q-space />

        <q-btn
          dense
          flat
          icon="close"
          @click="cancelCreation"
          v-close-popup
          v-if="!loadingAudioBook"
        >
          <q-tooltip class="bg-white text-primary">Close</q-tooltip>
        </q-btn>
      </q-bar>
    </q-card-section>
    <!-- Voice Selection Section -->

    <q-separator></q-separator>
    <q-card-section>
      <q-select
        v-model="selectedVoice.voice"
        :options="voices"
        label="Select Voice"
        map-options
        use-input
        @filter="filterFn"
        :loading="loadingVoices"
        @update:model-value="previewVoice"
        style="min-width: 400px"
      >
        <template v-slot:append>
          <q-icon
            name="close"
            @click.stop.prevent="selectedVoice.voice = ''"
            class="cursor-pointer"
          />
        </template>
        <template #loading>
          <q-spinner-hourglass size="24px" color="primary" />
        </template>
        <template v-slot:option="scope">
          <q-item v-if="!scope.opt.group" v-bind="scope.itemProps">
            <q-item-section avatar>
              <q-icon :name="scope.opt.icon"></q-icon>
            </q-item-section>
            <q-item-section>
              <q-item-label v-html="scope.opt.label"></q-item-label>
              <q-item-label caption>{{ scope.opt.description }}</q-item-label>
            </q-item-section>
          </q-item>
          <q-item v-if="scope.opt.group" v-bind="scope.itemProps">
            <q-item-section avatar>
              <q-icon :name="scope.opt.icon"></q-icon>
            </q-item-section>
            <q-item-label header>{{ scope.opt.group }}</q-item-label>
          </q-item>
        </template>
      </q-select>

      <q-select
        v-model="selectedVoice.gender"
        :options="[
          { label: 'All', value: '' },
          { label: 'Male', value: 'boy' },
          { label: 'Female', value: 'girl' },
        ]"
        debounce="300"
        label="Select Gender"
        map-options
        emit-value
        @update:model-value="filterByGender"
      >
        <template v-slot:append>
          <q-icon
            name="close"
            @click.stop.prevent="selectedVoice.gender = ''"
            class="cursor-pointer"
          />
        </template>
      </q-select>
    </q-card-section>

    <q-separator></q-separator>

    <!-- Preview Audio Section -->
    <q-card-section v-if="previewUrl.length">
      <div class="text-h7">Voice Preview</div>
      <AudioDialogPlayer :tracks="previewUrl" />
    </q-card-section>
    <q-card-section v-else>
      <div class="text-h7">Select a Voice</div>
      <AudioDialogPlayer :tracks="previewUrlMock" />
    </q-card-section>

    <q-separator></q-separator>
    <!-- Preview Audio Section -->
    <q-card-section v-if="trackUrls.length">
      <AudioDialogPlayer :tracks="trackUrls" />
    </q-card-section>

    <q-separator></q-separator>
    <!-- Action Buttons Section -->
    <q-card-actions align="right">
      <q-btn
        flat
        label="Close"
        v-close-popup
        v-if="!loadingAudioBook"
        @click="cancelCreation"
      />
      <q-btn
        v-if="previewUrl.length"
        flat
        icon="add"
        :label="previewUrls.length ? 'Update AudioBook' : 'Create AudioBook'"
        @click="createAudioBook"
        :loading="loadingAudioBook"
        color="primary"
      />
    </q-card-actions>
  </q-card>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import {
  Book,
  BookOutlines,
  ChapterOutline,
  listChapters,
  setOutlineAudioBook,
  updateOutline,
} from 'src/entities/book';
import { confirmOverrideText } from 'src/shared/lib/quasar-dialogs';

import { useVModels } from '@vueuse/core';
import {
  fetchPreviewAudio,
  storeChapterAudioBook,
  voicesList,
} from 'src/entities/audiobook';
import { user } from 'src/entities/user';
import AudioDialogPlayer from './AudioDialogPlayer.vue';

// Define component props
const props = defineProps<{
  outlines: BookOutlines;
  book: Book;
}>();

// Define component emits
const emit = defineEmits<{
  'update:book': (book: Book) => void;
  'update:outlines': (outlines: BookOutlines) => void;
}>();

// Use v-models with props and emit
const { book, outlines } = useVModels(props, emit);
// Use Quasar plugin
const $q = useQuasar();
// Refs for voices and selected voice
const voices = ref(voicesList);
const selectedVoice = ref({
  text: 'Listen to a sneak peek of your next adventure with Manny, the ultimate audio book companion!',
  gender: '',
  voice: {
    label: book.value.voice?.label ?? '',
    voiceName: book.value.voice?.voiceName ?? '',
    value: book.value.voice?.languageCode ?? '',
    languageCode: book.value.voice?.languageCode ?? '',
  },
});
// Player width and preview URLs
const playerWidth = ref(400);
const previewUrlMock = ref([
  {
    id: 'SelectVoice',
    title: 'Select a Voice',
    artist: user.value.displayName || user.value.email,
    src: ``,
    minWidth: playerWidth.value,
  },
]);
const previewUrl = ref([]);
const previewUrls = ref([]);
const trackUrls = computed(() => previewUrls.value);

// Loading states
const loadingVoices = ref(false);
const loadingAudioBook = ref(false);

/**
 * Fetch initial previews for the audio book.
 */
const fetchInitialPreviews = (): void => {
  if (outlines.value.introduction.audio) {
    previewUrls.value.push({
      id: outlines.value.introduction.id,
      title: outlines.value.introduction.title,
      artist: user.value.displayName || user.value.email,
      src: outlines.value.introduction.audio,
      minWidth: playerWidth.value,
    });
  }
  outlines.value.outlines.forEach((outline) => {
    if (outline.audio) {
      previewUrls.value.push({
        id: outline.id,
        title: outline.title,
        artist: user.value.displayName || user.value.email,
        src: outline.audio,
        minWidth: playerWidth.value,
      });
    }
  });
  if (outlines.value.conclusion.audio) {
    previewUrls.value.push({
      id: outlines.value.conclusion.id,
      title: outlines.value.conclusion.title,
      artist: user.value.displayName || user.value.email,
      src: outlines.value.conclusion.audio,
      minWidth: playerWidth.value,
    });
  }
};

/**
 * Preview the selected voice by fetching and playing audio.
 * Sets the `previewUrl` ref to the audio URL.
 */
const previewVoice = async (): Promise<void> => {
  try {
    previewUrl.value = [];
    if (
      !selectedVoice.value.voice.voiceName ||
      !selectedVoice.value.voice.languageCode
    ) {
      return;
    }

    const response = await fetchPreviewAudio(
      selectedVoice.value.text,
      selectedVoice.value.voice.voiceName ?? '',
      selectedVoice.value.voice.languageCode ?? '',
    );

    previewUrl.value.push({
      id: 'Preview',
      title: response ? 'Preview' : 'Error previewing voice. Choose another',
      artist: selectedVoice.value.voice.label,
      src: response ? `data:audio/wav;base64, ${response}` : '',
      minWidth: playerWidth.value,
    });
  } catch (error) {
    console.error('Error previewing voice:', error);
    previewUrl.value = [];
  }
};

fetchInitialPreviews(); // Call function to fetch initial previews
onMounted(async () => {
  // When component is mounted
  await previewVoice(); // Asynchronously preview voice
});

/**
 * Converts the selected outlines to an audio book.
 * Handles converting text to speech, uploading to Firebase, and updating outlines.
 */
const createAudioBook = async (): Promise<void> => {
  try {
    // Count existing audios in outlines
    let audiosCount = 0;
    if (outlines.value.introduction?.audio) audiosCount++;
    if (outlines.value.conclusion?.audio) audiosCount++;
    audiosCount += outlines.value.outlines.filter(
      (outline) => 'audio' in outline,
    ).length;

    if (audiosCount) {
      const shouldDelete = await confirmOverrideText($q, {
        message: 'This will delete all the current audio book. Are you sure?',
      });

      if (!shouldDelete) {
        return;
      }
    }

    loadingAudioBook.value = true;
    // Set voice for the book
    // Assuming `voice.country` is also saved with `voice.label` and `voice.value`
    previewUrls.value = [];

    book.value.voice = {
      voiceName: selectedVoice.value.voice.voiceName,
      languageCode: selectedVoice.value.voice.languageCode,
      label: selectedVoice.value.voice.label,
      icon: selectedVoice.value.voice.icon,
    };

    // Fetch chapters and convert each to audio
    const chapters = await listChapters(book.value.id);

    await saveChapterAudio(
      outlines.value.introduction,
      chapters.value.introduction.content,
    );
    await saveChapterAudio(
      outlines.value.conclusion,
      chapters.value.conclusion.content,
    );

    for (const chapter of chapters.value.chapters ?? []) {
      const outline = outlines.value.outlines.find(
        (outline) => outline.id === chapter.id,
      );
      if (outline) {
        await saveChapterAudio(outline, chapter.content);
      }
    }
    if (!previewUrls.value.length) {
      console.error('Error creating audio book:', error);
      $q.notify({
        type: 'negative',
        message: 'Error creating audio book',
      });
    } else {
      $q.notify({
        type: 'positive',
        message: 'Audio book created successfully!',
      });
    }
  } catch (error) {
    console.error('Error creating audio book:', error);
    $q.notify({
      type: 'negative',
      message: 'Error creating audio book',
    });
  } finally {
    loadingAudioBook.value = false;
  }
};

/**
 * Saves the audio for a chapter to Firebase Storage and updates the outlines.
 * @param {ChapterOutline} selectedOutline - The selected outline to save audio for.
 * @param {string} chapterContent - The content of the chapter to convert to audio.
 */
const saveChapterAudio = async (
  selectedOutline: ChapterOutline,
  chapterContent: string,
): Promise<void> => {
  try {
    const initialLength = previewUrls.value.length;
    previewUrls.value.push({
      id: selectedOutline.id,
      title: `${selectedOutline.title}`,
      artist: user.value.displayName || user.value.email,
      src: '',
      minWidth: playerWidth.value,
    });

    const downloadUrl = await storeChapterAudioBook(
      book.value,
      selectedOutline,
      `${selectedOutline.title}. ${chapterContent}`,
    );
    if (downloadUrl) {
      await updateOutline(book.value.id, selectedOutline.id, {
        audio: downloadUrl,
      });
      previewUrls.value[initialLength].src = downloadUrl;

      // Update outlines with audio URL
      outlines.value = setOutlineAudioBook(
        book.value.id,
        outlines,
        selectedOutline.id,
        downloadUrl,
      );
    } else {
      $q.notify({
        type: 'negative',
        message: 'Error saving chapter audio. Please try again.',
      });
    }
  } catch (error) {
    console.error('Error saving chapter audio:', error);
    $q.notify({
      type: 'negative',
      message: 'Error saving chapter audio',
    });
  }
};

/**
 * Cancel the creation of the audio book and reset loading state.
 */
const cancelCreation = (): void => {
  loadingAudioBook.value = false;
};

/**
 * Custom filter function for the q-select to search by voice name.
 * @param {string} term - The search term entered by the user.
 * @param {any} option - The current option object being filtered.
 * @returns {boolean} - Whether the option matches the search term.
 */
const filterFn = (
  term: string,
  update: (callback: () => void) => void,
): void => {
  if (term === '') {
    update(() => {
      voices.value = voicesList;
    });
    if (selectedVoice.value.gender) filterByGender();
    previewUrl.value = [];
    return;
  }

  update(() => {
    const needle = term.toLowerCase();
    voices.value = voices.value.filter(
      (v) => v.label.toLowerCase().indexOf(needle) > -1,
    );
    if (selectedVoice.value.gender) filterByGender();
    if (!voices.value.length) {
      previewUrl.value = [];
    }
    return;
  });
};

/**
 * Filters voices by gender.
 */
const filterByGender = (): void => {
  if (selectedVoice.value.gender) {
    voices.value = voices.value.filter(
      (v) => v.icon === selectedVoice.value.gender,
    );
  } else {
    voices.value = voicesList;
  }
};
</script>

<style scoped lang="scss">
/* Add any scoped styles here */
</style>
