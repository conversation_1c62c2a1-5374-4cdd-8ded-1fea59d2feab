import { countryNames } from 'src/entities/audiobook';
import { Book, ChapterOutline } from 'src/entities/book';
import { user } from 'src/entities/user';
import { storage } from 'src/firebase';
import {
  ref as storageRef,
  getDownloadURL,
  uploadString,
  deleteObject,
} from 'firebase/storage';
import {
  convertTextToSpeech,
  convertTextToSpeechLongAudio,
  fetchVoices,
} from 'src/shared/api/tts';
// @ts-ignore
import * as sanitizeHtml from 'sanitize-html';

/**
 * Retrieves the full country name based on the ISO 3166-1 alpha-2 country code.
 * @param code The ISO 3166-1 alpha-2 country code.
 * @returns The full country name corresponding to the given code, or 'Unknown' if not found.
 */
export const getCountryFullName = (code: string): string => {
  return countryNames[code] || 'Unknown';
};

/**
 * Extracts the country code from the voice name.
 * Assumes the country code is a 2-character code at the end of the name.
 * @param name - The voice name.
 * @returns - The extracted country code.
 */
export const extractCountryFromName = (name: string): string => {
  const match = name.match(/\b[A-Z]{2}\b/);
  return match ? getCountryFullName(match[0]) : 'Other';
};

// utils/removeHtmlTags.ts
/**
 * Removes HTML tags from the provided content.
 * @param content The HTML content to sanitize.
 * @returns The sanitized content without HTML tags.
 */
export const removeHtmlTags = (content: string): string => {
  // Specify the allowed tags and their attributes (if any)
  const clean = sanitizeHtml(content, {
    allowedTags: [],
    allowedAttributes: {},
  });

  return clean;
};

/**
 * Saves the audio for a chapter to Firebase Storage and updates the outlines.
 * @param book - The book to select the voice.
 * @param selectedOutline - The selected outline to save audio for.
 * @param chapterContent - The content of the chapter to convert to audio.
 * @returns A promise that resolves when the operation is complete.
 */
export const storeChapterAudioBook = async (
  book: Book,
  selectedOutline: ChapterOutline,
  chapterContent: string,
): Promise<string> => {
  try {
    const content = removeHtmlTags(chapterContent);
    if (!content) throw new Error('Error saving chapter audio');

    console.log(getByteLength(content));
    if (getByteLength(content) > 5000) {
      const storageReferenceLongAudio = storageRef(
        storage,
        `${user.value?.uid}/audiobooks/${book.id}/${selectedOutline.id}.wav`,
      );
      try {
        await deleteObject(storageReferenceLongAudio);
      } catch (errorDelete) {
        console.error(
          'Error deleting chapter audio. It does not exists:',
          errorDelete,
        );
      }
      let bucketGS = '';
      let projectId = '';
      switch (process.env.FIREBASE_APP) {
        case 'prod': {
          bucketGS = 'gs://bspub-storage';
          projectId = '************';
          break;
        }
        case 'books': {
          bucketGS = 'gs://manuscriptr-dev.appspot.com';
          projectId = '************';
          break;
        }
        default: {
          bucketGS = 'gs://manuscriptr-dev.appspot.com';
          projectId = '127339513256';
          break;
        }
      }
      const storageLocation = `${bucketGS}/${user.value?.uid}/audiobooks/${book.id}`;
      const filename = selectedOutline.id;
      const response = await fetchPreviewLongAudio(
        content,
        book.voice.voiceName,
        book.voice.languageCode,
        storageLocation,
        filename,
        projectId,
      );
      if (!response) throw new Error('Error saving chapter audio');
      return await getDownloadURL(storageReferenceLongAudio);
    } else {
      const storageReference = storageRef(
        storage,
        `${user.value?.uid}/audiobooks/${book.id}/${selectedOutline.id}.mp3`,
      );

      const response = await fetchPreviewAudio(
        content,
        book.voice.voiceName,
        book.voice.languageCode,
      );
      if (!response) throw new Error('Error saving chapter audio');

      const base64AudioString = `data:audio/mp3;base64, ${response}`;
      // Upload to Firebase Storage

      await uploadString(storageReference, base64AudioString, 'data_url');
      return await getDownloadURL(storageReference);
    }
  } catch (error) {
    console.error('Error saving chapter audio:', error);
    return '';
  }
};

/**
 * Fetches preview audio from the backend service.
 * @param text - The text to convert to speech.
 * @param voiceName - The name of the voice to use for conversion.
 * @param languageCode - The language code of the voice.
 * @returns A promise that resolves with the audio data as ArrayBuffer.
 */
export const fetchPreviewAudio = async (
  text: string,
  voiceName: string,
  languageCode: string,
): Promise<any> => {
  try {
    return await convertTextToSpeech(text, voiceName, languageCode);
  } catch (error) {
    console.error('Error fetching preview audio:', error);
    throw error;
  }
};

/**
 * Fetches preview long audio from the backend service.
 * @param text - The text to convert to speech.
 * @param voiceName - The name of the voice to use for conversion.
 * @param languageCode - The language code of the voice.
 * @param storageLocation - The GS Bucket storageLocation.
 * @param filename - The filename without extension.
 * @param projectId - The project ID for the Firebase project.
 * @returns A promise that resolves with the audio data as ArrayBuffer.
 */
export const fetchPreviewLongAudio = async (
  text: string,
  voiceName: string,
  languageCode: string,
  storageLocation: string,
  filename: string,
  projectId: string,
): Promise<any> => {
  try {
    return await convertTextToSpeechLongAudio(
      text,
      voiceName,
      languageCode,
      storageLocation,
      filename,
      projectId,
    );
  } catch (error) {
    console.error('Error fetching preview audio:', error);
    throw error;
  }
};

/**
 * Deletes the audio book chapter from Firebase Storage.
 * @param book - The book containing the chapter.
 * @param selectedOutline - The chapter outline to delete.
 * @returns A promise that resolves when the operation is complete.
 */
export const deleteChapterAudioBook = async (
  book: Book,
  selectedOutline: ChapterOutline,
): Promise<void> => {
  try {
    const storageReference = storageRef(
      storage,
      `${user.value?.uid}/audiobooks/${book.id}/${selectedOutline.id}.mp3`,
    );

    await deleteObject(storageReference);
  } catch (error) {
    console.error('Error deleting chapter audio:', error);
  }
};

// Function to calculate byte length of a string
/**
 * Calculates the byte length of a string.
 * @param str The string to measure.
 * @returns The byte length of the string.
 */
const getByteLength = (str: string): number => {
  return new TextEncoder().encode(str).length;
};

/**
 * Converts fetched voices to a JSON format suitable for UI display.
 * @returns A promise that resolves when the operation is complete.
 */
export const convertVoicesToJSON = async (): Promise<void> => {
  const voicesData = (await fetchVoices()) ?? [];
  const voices = voicesData.data.map((voice) => ({
    gender: voice.ssmlGender,
    label: `${voice.name} (${voice.ssmlGender})`,
    voiceName: voice.name,
    languageCode: voice.languageCodes ? voice.languageCodes[0] : null,
    value: `${voice.name} (${voice.ssmlGender})`,
    country: extractCountryFromName(voice.name as string), // Extract country code from voice name
  }));

  const grouped: Array<{
    label: string;
    voiceName?: string;
    value?: string;
    languageCode?: string;
    icon: string;
    disable?: boolean;
    group?: string;
  }> = [];
  const exists: Record<string, string> = {};
  voices.forEach((voice) => {
    const country = voice.country; // Extract country from voice name
    if (!exists[country]) {
      grouped.push({
        disable: true,
        group: country,
        icon: 'flag',
        label: country,
      });
      exists[country] = country;
    }
    grouped.push({
      label: voice.label as string,
      voiceName: voice.voiceName as string,
      value: voice.value as string,
      icon: voice.gender === 'MALE' ? 'boy' : 'girl',
      languageCode: voice.languageCode as string,
    });
  });
  const jsonData = JSON.stringify(grouped, null, 2);
  console.log(jsonData);
};
