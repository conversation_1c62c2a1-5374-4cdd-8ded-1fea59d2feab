export let voicesList = [
  {
    disable: true,
    group: 'Australia',
    icon: 'flag',
    label: 'Australia',
  },
  {
    label: 'en-AU-Neural2-A (FEMALE)',
    voiceName: 'en-AU-Neural2-A',
    value: 'en-AU-Neural2-A (FEMALE)',
    icon: 'girl',
    languageCode: 'en-AU',
  },
  {
    label: 'en-AU-Neural2-B (MALE)',
    voiceName: 'en-AU-Neural2-B',
    value: 'en-AU-Neural2-B (MALE)',
    icon: 'boy',
    languageCode: 'en-AU',
  },
  {
    label: 'en-AU-Neural2-C (FEMALE)',
    voiceName: 'en-AU-Neural2-C',
    value: 'en-AU-Neural2-C (FEMALE)',
    icon: 'girl',
    languageCode: 'en-AU',
  },
  {
    label: 'en-AU-Neural2-D (MALE)',
    voiceName: 'en-AU-Neural2-D',
    value: 'en-AU-Neural2-D (MALE)',
    icon: 'boy',
    languageCode: 'en-AU',
  },
  {
    label: 'en-AU-News-E (FEMALE)',
    voiceName: 'en-AU-News-E',
    value: 'en-AU-News-E (FEMALE)',
    icon: 'girl',
    languageCode: 'en-AU',
  },
  {
    label: 'en-AU-News-F (FEMALE)',
    voiceName: 'en-AU-News-F',
    value: 'en-AU-News-F (FEMALE)',
    icon: 'girl',
    languageCode: 'en-AU',
  },
  {
    label: 'en-AU-News-G (MALE)',
    voiceName: 'en-AU-News-G',
    value: 'en-AU-News-G (MALE)',
    icon: 'boy',
    languageCode: 'en-AU',
  },
  {
    label: 'en-AU-Polyglot-1 (MALE)',
    voiceName: 'en-AU-Polyglot-1',
    value: 'en-AU-Polyglot-1 (MALE)',
    icon: 'boy',
    languageCode: 'en-AU',
  },
  {
    label: 'en-AU-Standard-A (FEMALE)',
    voiceName: 'en-AU-Standard-A',
    value: 'en-AU-Standard-A (FEMALE)',
    icon: 'girl',
    languageCode: 'en-AU',
  },
  {
    label: 'en-AU-Standard-B (MALE)',
    voiceName: 'en-AU-Standard-B',
    value: 'en-AU-Standard-B (MALE)',
    icon: 'boy',
    languageCode: 'en-AU',
  },
  {
    label: 'en-AU-Standard-C (FEMALE)',
    voiceName: 'en-AU-Standard-C',
    value: 'en-AU-Standard-C (FEMALE)',
    icon: 'girl',
    languageCode: 'en-AU',
  },
  {
    label: 'en-AU-Standard-D (MALE)',
    voiceName: 'en-AU-Standard-D',
    value: 'en-AU-Standard-D (MALE)',
    icon: 'boy',
    languageCode: 'en-AU',
  },
  {
    label: 'en-AU-Wavenet-A (FEMALE)',
    voiceName: 'en-AU-Wavenet-A',
    value: 'en-AU-Wavenet-A (FEMALE)',
    icon: 'girl',
    languageCode: 'en-AU',
  },
  {
    label: 'en-AU-Wavenet-B (MALE)',
    voiceName: 'en-AU-Wavenet-B',
    value: 'en-AU-Wavenet-B (MALE)',
    icon: 'boy',
    languageCode: 'en-AU',
  },
  {
    label: 'en-AU-Wavenet-C (FEMALE)',
    voiceName: 'en-AU-Wavenet-C',
    value: 'en-AU-Wavenet-C (FEMALE)',
    icon: 'girl',
    languageCode: 'en-AU',
  },
  {
    label: 'en-AU-Wavenet-D (MALE)',
    voiceName: 'en-AU-Wavenet-D',
    value: 'en-AU-Wavenet-D (MALE)',
    icon: 'boy',
    languageCode: 'en-AU',
  },
  {
    disable: true,
    group: 'Uk',
    icon: 'flag',
    label: 'Uk',
  },
  {
    label: 'en-GB-Neural2-A (FEMALE)',
    voiceName: 'en-GB-Neural2-A',
    value: 'en-GB-Neural2-A (FEMALE)',
    icon: 'girl',
    languageCode: 'en-GB',
  },
  {
    label: 'en-GB-Neural2-B (MALE)',
    voiceName: 'en-GB-Neural2-B',
    value: 'en-GB-Neural2-B (MALE)',
    icon: 'boy',
    languageCode: 'en-GB',
  },
  {
    label: 'en-GB-Neural2-C (FEMALE)',
    voiceName: 'en-GB-Neural2-C',
    value: 'en-GB-Neural2-C (FEMALE)',
    icon: 'girl',
    languageCode: 'en-GB',
  },
  {
    label: 'en-GB-Neural2-D (MALE)',
    voiceName: 'en-GB-Neural2-D',
    value: 'en-GB-Neural2-D (MALE)',
    icon: 'boy',
    languageCode: 'en-GB',
  },
  {
    label: 'en-GB-Neural2-F (FEMALE)',
    voiceName: 'en-GB-Neural2-F',
    value: 'en-GB-Neural2-F (FEMALE)',
    icon: 'girl',
    languageCode: 'en-GB',
  },
  {
    label: 'en-GB-News-G (FEMALE)',
    voiceName: 'en-GB-News-G',
    value: 'en-GB-News-G (FEMALE)',
    icon: 'girl',
    languageCode: 'en-GB',
  },
  {
    label: 'en-GB-News-H (FEMALE)',
    voiceName: 'en-GB-News-H',
    value: 'en-GB-News-H (FEMALE)',
    icon: 'girl',
    languageCode: 'en-GB',
  },
  {
    label: 'en-GB-News-I (FEMALE)',
    voiceName: 'en-GB-News-I',
    value: 'en-GB-News-I (FEMALE)',
    icon: 'girl',
    languageCode: 'en-GB',
  },
  {
    label: 'en-GB-News-J (MALE)',
    voiceName: 'en-GB-News-J',
    value: 'en-GB-News-J (MALE)',
    icon: 'boy',
    languageCode: 'en-GB',
  },
  {
    label: 'en-GB-News-K (MALE)',
    voiceName: 'en-GB-News-K',
    value: 'en-GB-News-K (MALE)',
    icon: 'boy',
    languageCode: 'en-GB',
  },
  {
    label: 'en-GB-News-L (MALE)',
    voiceName: 'en-GB-News-L',
    value: 'en-GB-News-L (MALE)',
    icon: 'boy',
    languageCode: 'en-GB',
  },
  {
    label: 'en-GB-News-M (MALE)',
    voiceName: 'en-GB-News-M',
    value: 'en-GB-News-M (MALE)',
    icon: 'boy',
    languageCode: 'en-GB',
  },
  {
    label: 'en-GB-Standard-A (FEMALE)',
    voiceName: 'en-GB-Standard-A',
    value: 'en-GB-Standard-A (FEMALE)',
    icon: 'girl',
    languageCode: 'en-GB',
  },
  {
    label: 'en-GB-Standard-B (MALE)',
    voiceName: 'en-GB-Standard-B',
    value: 'en-GB-Standard-B (MALE)',
    icon: 'boy',
    languageCode: 'en-GB',
  },
  {
    label: 'en-GB-Standard-C (FEMALE)',
    voiceName: 'en-GB-Standard-C',
    value: 'en-GB-Standard-C (FEMALE)',
    icon: 'girl',
    languageCode: 'en-GB',
  },
  {
    label: 'en-GB-Standard-D (MALE)',
    voiceName: 'en-GB-Standard-D',
    value: 'en-GB-Standard-D (MALE)',
    icon: 'boy',
    languageCode: 'en-GB',
  },
  {
    label: 'en-GB-Standard-F (FEMALE)',
    voiceName: 'en-GB-Standard-F',
    value: 'en-GB-Standard-F (FEMALE)',
    icon: 'girl',
    languageCode: 'en-GB',
  },
  {
    label: 'en-GB-Studio-B (MALE)',
    voiceName: 'en-GB-Studio-B',
    value: 'en-GB-Studio-B (MALE)',
    icon: 'boy',
    languageCode: 'en-GB',
  },
  {
    label: 'en-GB-Studio-C (FEMALE)',
    voiceName: 'en-GB-Studio-C',
    value: 'en-GB-Studio-C (FEMALE)',
    icon: 'girl',
    languageCode: 'en-GB',
  },
  {
    label: 'en-GB-Wavenet-A (FEMALE)',
    voiceName: 'en-GB-Wavenet-A',
    value: 'en-GB-Wavenet-A (FEMALE)',
    icon: 'girl',
    languageCode: 'en-GB',
  },
  {
    label: 'en-GB-Wavenet-B (MALE)',
    voiceName: 'en-GB-Wavenet-B',
    value: 'en-GB-Wavenet-B (MALE)',
    icon: 'boy',
    languageCode: 'en-GB',
  },
  {
    label: 'en-GB-Wavenet-C (FEMALE)',
    voiceName: 'en-GB-Wavenet-C',
    value: 'en-GB-Wavenet-C (FEMALE)',
    icon: 'girl',
    languageCode: 'en-GB',
  },
  {
    label: 'en-GB-Wavenet-D (MALE)',
    voiceName: 'en-GB-Wavenet-D',
    value: 'en-GB-Wavenet-D (MALE)',
    icon: 'boy',
    languageCode: 'en-GB',
  },
  {
    label: 'en-GB-Wavenet-F (FEMALE)',
    voiceName: 'en-GB-Wavenet-F',
    value: 'en-GB-Wavenet-F (FEMALE)',
    icon: 'girl',
    languageCode: 'en-GB',
  },
  {
    disable: true,
    group: 'USA',
    icon: 'flag',
    label: 'USA',
  },
  {
    label: 'en-US-Casual-K (MALE)',
    voiceName: 'en-US-Casual-K',
    value: 'en-US-Casual-K (MALE)',
    icon: 'boy',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Journey-D (MALE)',
    voiceName: 'en-US-Journey-D',
    value: 'en-US-Journey-D (MALE)',
    icon: 'boy',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Journey-F (FEMALE)',
    voiceName: 'en-US-Journey-F',
    value: 'en-US-Journey-F (FEMALE)',
    icon: 'girl',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Journey-O (FEMALE)',
    voiceName: 'en-US-Journey-O',
    value: 'en-US-Journey-O (FEMALE)',
    icon: 'girl',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Neural2-A (MALE)',
    voiceName: 'en-US-Neural2-A',
    value: 'en-US-Neural2-A (MALE)',
    icon: 'boy',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Neural2-C (FEMALE)',
    voiceName: 'en-US-Neural2-C',
    value: 'en-US-Neural2-C (FEMALE)',
    icon: 'girl',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Neural2-D (MALE)',
    voiceName: 'en-US-Neural2-D',
    value: 'en-US-Neural2-D (MALE)',
    icon: 'boy',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Neural2-E (FEMALE)',
    voiceName: 'en-US-Neural2-E',
    value: 'en-US-Neural2-E (FEMALE)',
    icon: 'girl',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Neural2-F (FEMALE)',
    voiceName: 'en-US-Neural2-F',
    value: 'en-US-Neural2-F (FEMALE)',
    icon: 'girl',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Neural2-G (FEMALE)',
    voiceName: 'en-US-Neural2-G',
    value: 'en-US-Neural2-G (FEMALE)',
    icon: 'girl',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Neural2-H (FEMALE)',
    voiceName: 'en-US-Neural2-H',
    value: 'en-US-Neural2-H (FEMALE)',
    icon: 'girl',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Neural2-I (MALE)',
    voiceName: 'en-US-Neural2-I',
    value: 'en-US-Neural2-I (MALE)',
    icon: 'boy',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Neural2-J (MALE)',
    voiceName: 'en-US-Neural2-J',
    value: 'en-US-Neural2-J (MALE)',
    icon: 'boy',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-News-K (FEMALE)',
    voiceName: 'en-US-News-K',
    value: 'en-US-News-K (FEMALE)',
    icon: 'girl',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-News-L (FEMALE)',
    voiceName: 'en-US-News-L',
    value: 'en-US-News-L (FEMALE)',
    icon: 'girl',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-News-N (MALE)',
    voiceName: 'en-US-News-N',
    value: 'en-US-News-N (MALE)',
    icon: 'boy',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Polyglot-1 (MALE)',
    voiceName: 'en-US-Polyglot-1',
    value: 'en-US-Polyglot-1 (MALE)',
    icon: 'boy',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Standard-A (MALE)',
    voiceName: 'en-US-Standard-A',
    value: 'en-US-Standard-A (MALE)',
    icon: 'boy',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Standard-B (MALE)',
    voiceName: 'en-US-Standard-B',
    value: 'en-US-Standard-B (MALE)',
    icon: 'boy',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Standard-C (FEMALE)',
    voiceName: 'en-US-Standard-C',
    value: 'en-US-Standard-C (FEMALE)',
    icon: 'girl',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Standard-D (MALE)',
    voiceName: 'en-US-Standard-D',
    value: 'en-US-Standard-D (MALE)',
    icon: 'boy',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Standard-E (FEMALE)',
    voiceName: 'en-US-Standard-E',
    value: 'en-US-Standard-E (FEMALE)',
    icon: 'girl',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Standard-F (FEMALE)',
    voiceName: 'en-US-Standard-F',
    value: 'en-US-Standard-F (FEMALE)',
    icon: 'girl',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Standard-G (FEMALE)',
    voiceName: 'en-US-Standard-G',
    value: 'en-US-Standard-G (FEMALE)',
    icon: 'girl',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Standard-H (FEMALE)',
    voiceName: 'en-US-Standard-H',
    value: 'en-US-Standard-H (FEMALE)',
    icon: 'girl',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Standard-I (MALE)',
    voiceName: 'en-US-Standard-I',
    value: 'en-US-Standard-I (MALE)',
    icon: 'boy',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Standard-J (MALE)',
    voiceName: 'en-US-Standard-J',
    value: 'en-US-Standard-J (MALE)',
    icon: 'boy',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Studio-O (FEMALE)',
    voiceName: 'en-US-Studio-O',
    value: 'en-US-Studio-O (FEMALE)',
    icon: 'girl',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Studio-Q (MALE)',
    voiceName: 'en-US-Studio-Q',
    value: 'en-US-Studio-Q (MALE)',
    icon: 'boy',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Wavenet-A (MALE)',
    voiceName: 'en-US-Wavenet-A',
    value: 'en-US-Wavenet-A (MALE)',
    icon: 'boy',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Wavenet-B (MALE)',
    voiceName: 'en-US-Wavenet-B',
    value: 'en-US-Wavenet-B (MALE)',
    icon: 'boy',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Wavenet-C (FEMALE)',
    voiceName: 'en-US-Wavenet-C',
    value: 'en-US-Wavenet-C (FEMALE)',
    icon: 'girl',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Wavenet-D (MALE)',
    voiceName: 'en-US-Wavenet-D',
    value: 'en-US-Wavenet-D (MALE)',
    icon: 'boy',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Wavenet-E (FEMALE)',
    voiceName: 'en-US-Wavenet-E',
    value: 'en-US-Wavenet-E (FEMALE)',
    icon: 'girl',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Wavenet-F (FEMALE)',
    voiceName: 'en-US-Wavenet-F',
    value: 'en-US-Wavenet-F (FEMALE)',
    icon: 'girl',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Wavenet-G (FEMALE)',
    voiceName: 'en-US-Wavenet-G',
    value: 'en-US-Wavenet-G (FEMALE)',
    icon: 'girl',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Wavenet-H (FEMALE)',
    voiceName: 'en-US-Wavenet-H',
    value: 'en-US-Wavenet-H (FEMALE)',
    icon: 'girl',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Wavenet-I (MALE)',
    voiceName: 'en-US-Wavenet-I',
    value: 'en-US-Wavenet-I (MALE)',
    icon: 'boy',
    languageCode: 'en-US',
  },
  {
    label: 'en-US-Wavenet-J (MALE)',
    voiceName: 'en-US-Wavenet-J',
    value: 'en-US-Wavenet-J (MALE)',
    icon: 'boy',
    languageCode: 'en-US',
  },
];
voicesList = voicesList.filter((v) => v.icon !== 'flag');
