// src/entities/chapterchat/store/openAIStore.ts

import { defineStore, Store } from 'pinia';
import { computed, ref, Ref } from 'vue';
import {
  requestChatWithManny,
  requestProcessLongTextForAI,
} from 'src/shared/api/bookChat';
import { useChatDBStore } from 'src/entities/chapterchat';
import { maxChapterAllowedChats, maxChatsPerDay } from 'src/entities/setting';

/**
 * Pinia store for managing OpenAI-related state and actions.
 */
export const useOpenAIStore = defineStore('openAI', () => {
  const bookId = ref<string>();
  const chapterId = ref<string>();
  const docId = ref<string>();
  const chatId = ref<string>();

  const chatDBStore = useChatDBStore();

  const isNoAbort = ref<boolean>(false);

  let abortController: AbortController;
  const isLoading = ref(false);
  const canStillChat: Ref<boolean> = computed(() => {
    return (chatDBStore.countChats || 0) < maxChapterAllowedChats.value;
  });

  const canStillMessage: Ref<boolean> = computed(() => {
    return (chatDBStore.countMessagesInAChat || 0) < maxChatsPerDay.value;
  });

  // Send a new message to the OpenAI API and handle the response stream
  /**
   * Sends a message to the OpenAI API and handles the response stream.
   * @param newMessage - The new message to send.
   * @returns {Promise<boolean>} - A promise that resolves when the message has been sent and the response has been processed.
   */
  const loadChapterContentForChat = (content: string): Promise<boolean> => {
    docId.value = `${bookId.value}-${chapterId.value}`;

    return requestProcessLongTextForAI({
      longText: content,
      docId: docId.value as string,
    });
  };
  /**
   * Sends a message to the OpenAI API and handles the response stream.
   * @param newMessage - The new message to send.
   * @returns {Promise<void>} - A promise that resolves when the message has been sent and the response has been processed.
   */
  const sendMessage = async (newMessage: string): Promise<void> => {
    if (!newMessage || isLoading.value || !bookId.value || !chapterId.value) {
      return;
    }

    // Update the time before making the API call
    chatDBStore.chatDb.lastApiCallTime = new Date();
    if (!chatDBStore.chatDb.messages) {
      chatDBStore.chatDb.messages = [];
    }
    // Add the new message to the messages array with role 0 (user)
    chatDBStore.chatDb.messages.push({
      role: 'user',
      text: newMessage,
    });

    abortController = new AbortController();
    chatDBStore.bookId = bookId.value;
    chatDBStore.chapterId = chapterId.value;
    isNoAbort.value = true;

    // Create or use selected chat ID
    chatId.value = chatDBStore.chatDb.selectedChatId as string;

    isNoAbort.value = false;

    isLoading.value = true;

    let isError = false;
    docId.value = `${bookId.value}-${chapterId.value}`;

    try {
      await new Promise((resolve) => setTimeout(resolve, 1500));
      chatDBStore.chatDb.messages?.push({
        text: 'Generating content',
        role: 'assistant',
        id: 'mannyTyping',
      });

      // Send messages to the OpenAI API and handle the response stream
      const textResponse = await requestChatWithManny({
        query: newMessage,
        docId: docId.value as string,
        messageHistory: chatDBStore.chatDb.messages.filter(
          (message) => message.role === 'user' || message.role === 'assistant',
        ),
      });

      chatDBStore.chatDb.messages = chatDBStore.chatDb.messages.filter(
        (message: any) => message?.id !== 'mannyTyping',
      );

      chatDBStore.chatDb.messages.push({
        text: textResponse as string,
        role: 'assistant',
      });
    } catch (err: any) {
      isError = true;
      chatDBStore.chatDb.messages = chatDBStore.chatDb.messages.filter(
        (message: any) => message?.id !== 'mannyTyping',
      );
      chatDBStore.chatDb.messages.push({
        text: 'Error: ' + err.message,
        role: 'error',
      });
    }

    // Save the chat if there was an error, otherwise process the response stream

    chatId.value = await chatDBStore.saveChat(
      chatId.value,
      chatDBStore.chatDb.messages,
    );

    isLoading.value = false;
  };
  /**
   * Aborts the current response stream if it exists.
   */
  const abortStream = (): void => {
    if (abortController) {
      abortController.abort();
    }
  };

  return {
    bookId,
    chapterId,
    isLoading,
    isNoAbort,
    loadChapterContentForChat,
    sendMessage,
    abortStream,
    canStillChat,
    canStillMessage,
  };
});
