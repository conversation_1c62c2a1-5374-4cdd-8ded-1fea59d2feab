import { defineStore } from 'pinia';
import { useOpenAIStore } from 'src/entities/chapterchat';
import { computed, Ref, ref, toRefs } from 'vue';
import {
  collection,
  doc,
  addDoc,
  getDocs,
  query,
  orderBy,
  updateDoc,
  deleteDoc,
  where,
  getDoc,
  limit,
  startAfter,
  CollectionReference,
  DocumentData,
} from 'firebase/firestore';
import { firestore } from 'src/firebase';
import { ChatMessage, Chat, File, Image, ChatDBState } from '../model';
import { maxChatsPerDay } from 'src/entities/setting';

export const useChatDBStore = defineStore('chatDB', () => {
  const openAIStore = useOpenAIStore();
  const bookId = ref<string>(openAIStore.bookId as string);
  const chapterId = ref<string>(openAIStore.chapterId as string);
  const userChatCollection = ref<CollectionReference<
    DocumentData,
    DocumentData
  > | null>(null);
  const initDb = () => {
    bookId.value = openAIStore.bookId as string;
    chapterId.value = openAIStore.chapterId as string;
    userChatCollection.value = collection(
      firestore,
      `books/${bookId.value}/chapters/${chapterId.value}/chats`,
    );
  };
  const chatDb = ref<ChatDBState>({
    chats: [],
    messages: [],
    selectedChatId: null,
    lastApiCallTime: null,
  });

  const countChats: Ref<number> = computed(
    () => chatDb.value?.chats?.length || 0,
  );
  const countMessagesInAChat: Ref<number> = computed(
    () => chatDb.value?.messages?.length || 0,
  );
  /**
   * Builds a query for fetching notifications with optional filters and pagination.
   * @param rowsPerPage - Number of rows per page
   * @param lastVisibleDoc - Last document from the previous query
   * @param filters - Filters to apply on the query
   */
  const buildFetchChats = async (
    rowsPerPage: number,
    lastVisibleDoc: any,
    filters: any,
  ): Promise<any> => {
    initDb();
    if (!userChatCollection.value) throw new Error('collection error');
    let q = query(userChatCollection.value);

    if (filters.search) {
      q = query(
        q,
        where('messages', '>=', filters.search),
        where('messages', '<=', filters.search + '\uf8ff'),
        orderBy('messages'),
        orderBy('createdAt', 'desc'),
        limit(rowsPerPage),
      );
    } else {
      q = query(q, orderBy('createdAt', 'desc'), limit(rowsPerPage));
    }

    if (lastVisibleDoc) {
      q = query(q, startAfter(lastVisibleDoc));
    }

    return await getDocs(q);
  };

  /**
   * Fetches all chats from the database, sorts them, and selects the most recent one.
   * @param {string} [filterWord] - Optional filter word to search within chat messages.
   * @returns {Promise<string|null>} - The ID of the most recent chat or null if no chats.
   */
  const fetchChats = async (filterWord?: string, lastVisibleDoc?: string) => {
    const limitRows = 10;

    const snapshot = await buildFetchChats(limitRows, '', {
      search: filterWord,
    });
    const querySnapshot = snapshot.docs;
    const loadedChats: Chat[] = [];
    querySnapshot.forEach((doc: any) => {
      loadedChats.push({
        id: doc.id,
        name: doc.data().name,
        messages: doc.data().messages,
        createdAt: doc.data().createdAt.toDate(),
      });
    });

    if (loadedChats.length === 0) {
      chatDb.value.chats = [];
      return null;
    }

    const filteredChats: Chat[] = loadedChats;

    const sortedChats = filteredChats.sort(
      (a, b) => b.createdAt.getTime() - a.createdAt.getTime(),
    );

    const mappedChats = sortedChats.map((chat) => ({
      id: chat.id,
      name: chat.name,
      createdAt: chat.createdAt,
    }));

    chatDb.value.chats = mappedChats;
    return chatDb.value.chats.length > 0 ? chatDb.value.chats[0].id : null;
  };

  /**
   * Creates a new chat and selects it.
   * @param {string} [incomingText] - Optional text to use as the chat name.
   * @returns {Promise<string>} - The ID of the newly created chat
   */
  const createChat = async (incomingText?: string): Promise<string> => {
    initDb();
    if (!userChatCollection.value) throw new Error('collection error');
    const name = incomingText
      ? incomingText?.trim().substring(0, 50)
      : 'New chat';

    const docRef = await addDoc(userChatCollection.value, {
      name: name,
      messages: [],
      createdAt: new Date(),
    });
    const chatId = docRef.id;
    chatDb.value.chats.push({
      id: chatId,
      name: name,
      createdAt: new Date(),
    });
    chatDb.value.selectedChatId = chatId;
    await selectChat(chatId);
    return chatId;
  };

  /**
   * Selects a chat by its ID and loads its messages.
   * @param {string|null} chatId - The ID of the chat to select, or null to clear the selection.
   * @returns {Promise<void>}
   */
  const selectChat = async (chatIdVal: string | null): Promise<void> => {
    initDb();

    if (!userChatCollection.value) throw new Error('collection error');

    if (chatDb.value.selectedChatId) {
      try {
        chatDb.value.messages = await loadChat(chatDb.value.selectedChatId);
      } catch (error) {
        console.error('Error loading chat:', error);
        chatDb.value.messages = [];
      }
    } else {
      chatDb.value.selectedChatId = null;
      chatDb.value.messages = [];
    }
  };

  /**
   * Clears all chats from the database and resets the chatDb.
   * @returns {Promise<void>}
   */
  const clearChats = async (): Promise<void> => {
    await selectChat(null);
    chatDb.value.chats = [];
    initDb();
    if (userChatCollection.value) {
      const { docs } = await getDocs(userChatCollection.value);
      docs.forEach((doc) => deleteDoc(doc.ref));
    }
    await createChat('null');
  };

  /**
   * Loads messages for a given chat ID.
   * @param {string} chatId - The ID of the chat to load messages for.
   * @returns {Promise<ChatMessage[]>} - An array of chat messages.
   */
  const loadChat = async (chatId: string): Promise<ChatMessage[]> => {
    if (chatId !== null) {
      initDb();
      if (!userChatCollection.value) throw new Error('collection error');
      const docRef = doc(userChatCollection.value, chatId);
      const docSnap = await getDoc(docRef);
      if (docSnap.exists()) {
        const selectedChat = docSnap.data();
        chatDb.value.selectedChatId = docSnap.ref.id;
        return selectedChat.messages;
      } else {
        console.log('No such document!');
        return [];
      }
    }
    return [];
  };

  /**
   * Saves new messages to a chat, updating its metadata.
   * @param {string} chatId - The ID of the chat to save messages to.
   * @param {ChatMessage[]} newMessages - The new messages to save.
   * @returns {Promise<void>}
   */
  const saveMessagesToChat = async (
    chatId: string,
    newMessages: ChatMessage[],
  ): Promise<void> => {
    initDb();
    if (!userChatCollection.value) throw new Error('collection error');
    const docRef = doc(userChatCollection.value, chatId);
    const chat = await getDoc(docRef);
    if (chat.exists()) {
      const filteredMessages = newMessages
        .filter(
          (message) => message.role !== 'error' && message.role !== 'system',
        )
        .map((message) => ({
          role: message.role,
          text: message.text,
          fileIds: message.fileIds ? Array.from(message.fileIds) : null,
          imageId: message.imageId ? message.imageId : null,
        }));
      await updateDoc(docRef, {
        messages: filteredMessages,
        createdAt: new Date(),
      });
    }
  };

  /**
   * Saves a chat and its messages, creating a new chat if necessary.
   * @param {string|null} chatId - The ID of the chat to save to, or null to create a new chat.
   * @param {ChatMessage[]} newMessages - The messages to save.
   * @returns {Promise<string>} - The ID of the chat that was saved to.
   */
  const saveChat = async (
    chatId: string | null,
    newMessages: ChatMessage[],
  ): Promise<string> => {
    if (!chatId) {
      chatId = await createChat();
    }
    newMessages.filter((message) => message.role !== 'error');

    await saveMessagesToChat(chatId, newMessages);
    await fetchChats();
    return chatId;
  };

  /**
   * Deletes a chat and its associated files and images.
   * @param {string} chatId - The ID of the chat to delete.
   * @returns {Promise<void>}
   */
  const deleteChat = async (chatId: string): Promise<void> => {
    if (chatId) {
      try {
        initDb();
        if (!userChatCollection.value) throw new Error('collection error');
        const docRef = doc(userChatCollection.value, chatId);
        await deleteDoc(docRef);
        await fetchChats();

        // Determine the next chat to select or create a new one
        const remainingChats = chatDb.value.chats;
        if (remainingChats.length > 0) {
          chatDb.value.selectedChatId = remainingChats[0].id as string; // Select the next available chat
          await selectChat(chatDb.value.selectedChatId);
        } else {
          // Create a new chat logic here
          await createChat('null');
          console.log('No chats available. Consider creating a new chat.');
        }
      } catch (error) {
        console.error('Error deleting chat:', error);
      }
    }
  };

  /**
   * Saves an image to the database.
   * @param {string} chatId - The ID of the chat the image belongs to.
   * @param {string} content - The base64 encoded image content.
   * @returns {Promise<number>} - The ID of the saved image.
   */
  const saveImage = async (
    chatId: string,
    content: string,
  ): Promise<number> => {
    try {
      const prefixedContent = `data:image/png;base64,${content}`;
      // const id = await db.images.add({ chatId, content: prefixedContent });
      // return id;
      console.log('saveImage not implemented yet');
      return 0;
    } catch (error) {
      console.error('Error saving image to the database:', error);
      throw error;
    }
  };

  /**
   * Gets an image by its ID.
   * @param {string} imageId - The ID of the image to get.
   * @returns {Promise<Image|null>} - The image object or null if not found.
   */
  const getImageById = async (imageId: string): Promise<Image | null> => {
    try {
      if (imageId) {
        // const imageRecord: Image | undefined = await db.images.get(imageId);
        // return imageRecord || null;
        return null;
      }
      return null;
    } catch (error) {
      console.error('Error retrieving image record from the database:', error);
      throw error;
    }
  };

  return {
    bookId,
    chapterId,
    chatDb,
    fetchChats,
    createChat,
    selectChat,
    clearChats,
    loadChat,
    saveMessagesToChat,
    saveChat,
    deleteChat,
    saveImage,
    getImageById,
    countChats,
    countMessagesInAChat,
  };
});
