export interface ChatMessage {
  id?: string;
  role: string;
  text: string;
  fileIds?: string[] | null;
  imageId?: string | null;
}

export interface Chat {
  id?: string;
  name: string;
  messages?: ChatMessage[];
  createdAt: Date;
}

export interface File {
  id?: string;
  chatId: string;
  name: string;
  content: string;
}

export interface Image {
  id?: string;
  chatId: string;
  content: string;
}

export interface ChatDBState {
  chats: Chat[];
  messages?: ChatMessage[];
  selectedChatId: string | null;
  lastApiCallTime: Date | null;
}
