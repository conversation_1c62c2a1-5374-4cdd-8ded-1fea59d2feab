// src/settings.js

import { ref, watch, Ref, toRef } from 'vue';
import { LocalStorage } from 'quasar';

interface Settings {
  apiKey: Ref<string>;
  models: Ref<string[]>;
  model: Ref<string>;
  temperature: Ref<number>;
  top_p: Ref<number>;
  frequency_penalty: Ref<number>;
  presence_penalty: Ref<number>;
  prompt: Ref<string>;
  sendOnEnter: Ref<boolean>;
  showMarkdown: Ref<boolean>;
  imageSize: Ref<string>;
  imageStyle: Ref<string>;
}

export const settings = {
  apiKey: ref(''),
  models: ref(['gpt-4o', 'gpt-4-turbo', 'gpt-4', 'gpt-3.5-turbo']),
  model: ref('gpt-4o'),
  temperature: ref(1.0),
  top_p: ref(1.0),
  frequency_penalty: ref(0.0),
  presence_penalty: ref(0.0),
  prompt: ref(''),
  sendOnEnter: ref(true),
  showMarkdown: ref(true),
  imageSize: ref('1024x1024'),
  imageStyle: ref('vivid'),
} as Settings;

export function saveSettings() {
  LocalStorage.set('apiKey', settings.apiKey.value);
  LocalStorage.set('models', JSON.stringify(settings.models.value));
  LocalStorage.set('model', settings.model.value);
  LocalStorage.set('temperature', settings.temperature.value.toString());
  LocalStorage.set('top_p', settings.top_p.value.toString());
  LocalStorage.set(
    'frequency_penalty',
    settings.frequency_penalty.value.toString(),
  );
  LocalStorage.set(
    'presence_penalty',
    settings.presence_penalty.value.toString(),
  );
  LocalStorage.set('prompt', settings.prompt.value);
  LocalStorage.set('sendOnEnter', settings.sendOnEnter.value.toString());
  LocalStorage.set('showMarkdown', settings.showMarkdown.value.toString());
  LocalStorage.set('imageSize', settings.imageSize.value);
  LocalStorage.set('imageStyle', settings.imageStyle.value);
}

export function loadSettings() {
  const keys = [
    'apiKey',
    'models',
    'model',
    'temperature',
    'top_p',
    'frequency_penalty',
    'presence_penalty',
    'prompt',
    'sendOnEnter',
    'showMarkdown',
    'imageSize',
    'imageStyle',
  ];

  keys.forEach((key) => {
    const value = LocalStorage.getItem(key);
    if (value !== null) {
      if (key === 'models') {
        settings.models.value = JSON.parse(value as any);
      } else if (
        [
          'temperature',
          'top_p',
          'frequency_penalty',
          'presence_penalty',
        ].includes(key)
      ) {
        (settings as any)[key].value = parseFloat(value as any);
      } else if (key === 'sendOnEnter' || key === 'showMarkdown') {
        (settings as any)[key].value = value === 'true';
      } else {
        (settings as any)[key].value = value;
      }
    }
  });
}

// Automatically save settings when they change
Object.keys(settings).forEach((key) => {
  watch(toRef(settings, key as keyof Settings), () => {
    saveSettings();
  });
});
