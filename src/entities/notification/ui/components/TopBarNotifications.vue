<template>
  <q-btn
    dense
    color="white"
    flat
    round
    icon="notifications"
    :class="{
      'animate-bell':
        newNotification && notificationStore.notifications.length > 0,
      'q-mr-xs': !isTopNavDefault,
    }"
    class="theme-toggle-btn"
    @click="clearNotificationAnimation"
  >
    <q-badge color="red" floating>{{ notificationStore.unreadCount }}</q-badge>
    <q-menu
      @focus="clearNotificationAnimation"
      :class="$q.dark.isActive ? 'bg-dark' : 'bg-white'"
    >
      <q-list
        :style="{
          maxWidth: `${$q.screen.sizes.sm}px !important`,
          minWidth: `${$q.screen.sizes.sm - 300}px !important`,
        }"
        :class="menuListClass"
      >
        <q-item
          v-close-popup
          v-for="(notification, index) in notificationStore.notifications"
          :key="notification.id"
          clickable
          v-ripple
          @click="notificationStore.goToNotificationUrl(notification)"
          v-if="notificationStore.notifications.length"
          :class="notificationItemClass"
        >
          <q-item-section>
            <q-item-label
              overline
              class="text-bold"
              lines="1"
              :class="titleTextClass"
            >
              {{ notification.title }}
            </q-item-label>
            <q-item-label
              caption
              lines="2"
              v-html="notification.notification"
              :class="captionTextClass"
            />
          </q-item-section>

          <q-item-section side top>
            <q-item-label caption :class="captionTextClass">
              {{ formatDate(notification.createdAt) }}
            </q-item-label>
            <q-icon
              :name="
                notification.isRead ? 'mark_email_read' : 'mark_email_unread'
              "
              color="primary"
            />
          </q-item-section>
        </q-item>

        <q-item v-else style="min-width: 20vw" :class="notificationItemClass">
          <q-item-section>
            <q-item-label
              overline
              class="text-bold self-center"
              :class="titleTextClass"
            >
              No notifications found!
            </q-item-label>
          </q-item-section>
        </q-item>

        <q-separator
          spaced
          :class="$q.dark.isActive ? 'bg-grey-7' : 'bg-grey-4'"
        />

        <q-item
          clickable
          @click="notificationStore.goToNotifications"
          v-close-popup
          :class="[notificationItemClass, 'hover-highlight']"
        >
          <q-item-section>
            <q-item-label class="text-primary self-center">
              View All
            </q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </q-menu>
  </q-btn>
</template>

<script setup lang="ts">
import { onUnmounted, ref, watch, computed } from 'vue';
import {
  useNotificationStore,
  UserNotification,
} from 'src/entities/notification';
import { user } from 'src/entities/user';
import { formatDate, isTopNavDefault } from 'src/entities/setting';
import { useQuasar } from 'quasar';

//  state
const $q = useQuasar();

/** Store for managing notifications */
const notificationStore = useNotificationStore();

/** List of user notifications */
const notifications = ref<UserNotification[]>([]);

/** Fetch notifications for the user */
(async () => {
  await notificationStore.fetchNotifications(user?.value?.uid as string, 5);
  notifications.value = notificationStore.notifications;
})();

/** Count of unread notifications */
const unreadCount = ref(0);

/** Fetch count of new user notifications */
(async () => {
  await notificationStore.countNewUserNotifications(user?.value?.uid as string);
  unreadCount.value = notificationStore.unreadCount;
})();

/** State to track if there is a new notification */
const newNotification = ref(unreadCount.value > 0);

/** Notification sound setup */
const notificationSound = new Audio('/audio/pop.mp3');
notificationSound.volume = 0;

// Theme-aware computed properties
const buttonColor = computed(() => {
  return $q.dark.isActive ? 'grey-3' : 'grey-8';
});

const menuListClass = computed(() => {
  return $q.dark.isActive ? 'bg-dark text-white' : 'bg-white text-dark';
});

const notificationItemClass = computed(() => {
  return $q.dark.isActive ? 'text-white hover-dark' : 'text-dark hover-light';
});

const titleTextClass = computed(() => {
  return $q.dark.isActive ? 'text-grey-3' : 'text-grey-8';
});

const captionTextClass = computed(() => {
  return $q.dark.isActive ? 'text-grey-5' : 'text-grey-6';
});

/**
 * Plays notification sound
 */
const playSound = () => {
  notificationSound
    .play()
    .catch((error: any) => console.error('Sound play error:', error));
};

/**
 * Clears notification animation and resets sound
 */
const clearNotificationAnimation = () => {
  playSound();
  notificationSound.volume = 0;
  newNotification.value = false;
};

/** Watch for changes in notification store */
watch(
  notificationStore,
  (newValue, oldValue) => {
    if (newValue.unreadCount > 0 && notificationSound) {
      notificationSound.volume = 1;
      newNotification.value = true;
      playSound();
    }
  },
  { immediate: true },
);

/** Cleanup function when component is unmounted */
onUnmounted(() => {
  notificationStore.stopListeningNewNotificationLimit();
  notificationStore.stopListeningNewNotification();
});
</script>

<style scoped lang="scss">
@keyframes bell-ring {
  0%,
  100% {
    transform: rotate(0);
  }
  25% {
    transform: rotate(-10deg);
  }
  50% {
    transform: rotate(10deg);
  }
  75% {
    transform: rotate(-10deg);
  }
}

.animate-bell {
  animation: bell-ring 0.5s ease-in-out infinite;
}

// Dark mode specific hover effects
.body--dark {
  .hover-dark:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .hover-highlight:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
}

// Light mode specific hover effects
.body--light {
  .hover-light:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .hover-highlight:hover {
    background-color: rgba(0, 0, 0, 0.03);
  }
}

// Enhanced notification button styling
.q-btn {
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.02);
  }
}

// Menu styling improvements
.q-menu {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

  .body--dark & {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  }
}

// Notification items styling
.q-item {
  transition: background-color 0.2s ease;
  border-radius: 4px;
  margin: 2px 4px;

  &:first-child {
    margin-top: 4px;
  }

  &:last-child {
    margin-bottom: 4px;
  }
}

// Badge styling enhancement
.q-badge {
  font-weight: 600;
  font-size: 0.75rem;
}

// Separator styling
.q-separator {
  margin: 8px 0;
  opacity: 0.6;

  .body--dark & {
    opacity: 0.4;
  }
}
</style>
