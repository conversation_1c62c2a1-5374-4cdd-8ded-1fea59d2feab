import {
  doc,
  getDoc,
  updateDoc,
  arrayUnion,
  arrayRemove,
} from 'firebase/firestore';
import {
  messaging,
  onMessage,
  getToken,
  vapidKey,
  firestore,
} from 'src/firebase';

export class PushNotificationService {
  /**
   * Requests notification permission and fetches the FCM token if needed.
   * Checks if the token needs to be refreshed based on the last refresh time.
   * If a refresh is needed, it removes old tokens and adds the new one.
   * Ensures token is stored in Firestore only if new or changed.
   * @param userId The user's unique identifier.
   */
  static requestPermission = async (userId: string): Promise<void> => {
    try {
      const userRef = doc(firestore, 'users', userId);
      const userSnap = await getDoc(userRef);
      const userData = userSnap.data();
      const deviceIdentifier = navigator.userAgent;
      const existingTokens = userData?.fcmTokens || [];
      const lastTokenRefresh = userData?.lastTokenRefresh
        ? new Date(userData.lastTokenRefresh.toDate())
        : null;

      const now = new Date();
      const oneMonthAgo = new Date(
        now.getFullYear(),
        now.getMonth() - 1,
        now.getDate(),
      );
      const shouldRefresh = !lastTokenRefresh || lastTokenRefresh < oneMonthAgo;

      Notification.requestPermission().then(async (permission) => {
        if (permission === 'granted') {
          await this.listenForTokenRefresh(
            userId,
            deviceIdentifier,
            existingTokens,
            shouldRefresh,
          );
        } else {
          console.warn('Notification permission denied.');
        }
      });
    } catch (error) {
      // console.error('Error getting FCM token:', error);
    }
  };

  private static listenForTokenRefresh = async (
    userId: string,
    deviceIdentifier: string,
    existingTokens: string[],
    shouldRefresh: boolean,
  ): Promise<void> => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker
        .register('./firebase-messaging-sw.js', { scope: './' })
        .then(async (registration) => {
          const userRef = doc(firestore, 'users', userId);
          try {
            const newToken = await getToken(messaging, {
              vapidKey: vapidKey,
            });

            if (newToken) {
              const existingTokenEntry: any = existingTokens.find(
                (entry: any) => entry?.deviceIdentifier === deviceIdentifier,
              );

              if (shouldRefresh) {
                if (existingTokenEntry) {
                  if (existingTokenEntry.token !== newToken) {
                    await updateDoc(userRef, {
                      fcmTokens: arrayRemove(existingTokenEntry),
                    });
                  }
                }
                await updateDoc(userRef, {
                  fcmTokens: arrayUnion({
                    token: newToken,
                    deviceIdentifier: deviceIdentifier,
                  }),
                  lastTokenRefresh: new Date(),
                });
              } else if (!existingTokenEntry) {
                await updateDoc(userRef, {
                  fcmTokens: arrayUnion({
                    token: newToken,
                    deviceIdentifier: deviceIdentifier,
                  }),
                });
              }
            }
          } catch (error) {
            // console.error('Error getting FCM token:', error);
          }
        })
        .catch((error) => {
          // console.error('Failed to register the Service Worker:', error);
        });
    }
  };

  /**
   * Listens for incoming push messages and displays them as notifications.
   */
  static listenForMessages = (): void => {
    onMessage(messaging, (payload) => {
      console.log('Message received:', payload);
      new Notification(payload.notification?.title || 'New Notification', {
        body: payload.notification?.body,
        icon: payload.notification?.image,
      });
    });
  };
}
