import { defineStore } from 'pinia';
import { ref, Ref } from 'vue';
import {
  addDoc,
  collection,
  doc,
  getDocs,
  limit,
  onSnapshot,
  orderBy,
  query,
  setDoc,
  startAfter,
  Unsubscribe,
  where,
} from 'firebase/firestore';
import { firestore } from 'src/firebase';
import { UserNotification } from 'src/entities/notification';
import { user } from 'src/entities/user';
import { useRoute, useRouter } from 'vue-router';

export const useNotificationStore = defineStore('notification', () => {
  /** State: Array of user notifications */
  const notifications: Ref<UserNotification[]> = ref([]);
  /** State: Count of unread notifications */
  const unreadCount: Ref<number> = ref(0);
  /** State: Count of notifications matching the search query */
  const countSearch: Ref<number> = ref(0);
  /** Unsubscribe function for notification limits */
  let newNotificationLimitUnSub: Unsubscribe;
  /** Unsubscribe function for new notifications */
  let newNotificationUnSub: Unsubscribe;
  /** Vue Router instance */
  const router = useRouter();
  const route = useRoute();

  /**
   * Builds a query for fetching notifications with optional filters and pagination.
   * @param rowsPerPage - Number of rows per page
   * @param lastVisibleDoc - Last document from the previous query
   * @param filters - Filters to apply on the query
   */
  const buildNotificationsQuery = async (
    rowsPerPage: number,
    lastVisibleDoc: any,
    filters: any,
  ): Promise<any> => {
    let q = query(
      collection(firestore, `users/${user?.value?.uid}/notifications`),
    );

    if (filters.search) {
      q = query(
        q,
        where('notification', '>=', filters.search),
        where('notification', '<=', filters.search + '\uf8ff'),
        orderBy('notification'),
        orderBy('createdAt', 'desc'),
        limit(rowsPerPage),
      );
    } else {
      q = query(q, orderBy('createdAt', 'desc'), limit(rowsPerPage));
    }

    if (lastVisibleDoc) {
      q = query(q, startAfter(lastVisibleDoc));
    }

    if (filters.isRead !== '') {
      q = query(q, where('isRead', '==', filters.isRead));
    }

    return await getDocs(q);
  };

  /**
   * Fetches the count of notifications based on search filters.
   * @param filters - Search filters
   */
  const getNotificationsQueryCountBySearch = async (
    filters: any,
  ): Promise<void> => {
    let q = query(
      collection(firestore, `users/${user?.value?.uid}/notifications`),
    );

    if (filters.search) {
      q = query(
        q,
        where('notification', '>=', filters.search),
        where('notification', '<=', filters.search + '\uf8ff'),
        orderBy('notification'),
      );
    }
    if (filters.isRead !== '') {
      q = query(q, where('isRead', '==', filters.isRead));
    }

    const { docs } = await getDocs(q);

    countSearch.value = docs.length;
  };

  /**
   * Creates a user notification document in Firestore.
   * @param {string} userId - The ID of the user.
   * @param {Partial<UserNotification>} newData - The notification data to be added.
   * @returns {Promise<void>} - A promise that resolves when the document is added.
   */
  const createUserNotification = (
    userId: string,
    newData: Partial<UserNotification>,
  ) => {
    addDoc(collection(firestore, 'users', userId, 'notifications'), {
      ...newData,
      isRead: false,
      createdAt: Date.now(),
    });
  };

  /**
   * Updates a specific user notification.
   * @param userId - User ID
   * @param id - Notification ID
   * @param newData - New data to update the notification with
   * @returns A promise that resolves when the document is updated
   */
  const updateUserNotification = async (
    userId: string,
    id: string,
    newData: Partial<UserNotification>,
  ): Promise<void> => {
    return await setDoc(
      doc(firestore, 'users', userId, 'notifications', id),
      newData,
      { merge: true },
    );
  };
  /**
   * Fetches notifications for a specific user with a limit on the number of entries.
   * @param userId - User ID
   * @param limitRows - Maximum number of notifications to fetch
   */
  const fetchNotifications = async (
    userId: string,
    limitRows: number,
  ): Promise<void> => {
    const q = query(
      collection(firestore, `users/${userId}/notifications`),
      orderBy('createdAt', 'desc'),
      where('isRead', '==', false),
      limit(limitRows),
    );
    newNotificationLimitUnSub = onSnapshot(q, (snapshot) => {
      const { docs } = snapshot;

      notifications.value = docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as UserNotification[];
    });
    const { docs } = await getDocs(q);

    notifications.value = docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as UserNotification[];
  };

  /**
   * Counts new notifications for a user that are unread.
   * @param userId - User ID
   */
  const countNewUserNotifications = async (userId: string): Promise<void> => {
    const q = query(
      collection(firestore, `users/${userId}/notifications`),
      orderBy('createdAt', 'desc'),
      where('isRead', '==', false),
    );

    newNotificationUnSub = onSnapshot(q, (snapshot) => {
      unreadCount.value = snapshot.docs.length;
    });

    const { docs } = await getDocs(q);
    unreadCount.value = docs.length;
  };

  /**
   * Go to notifications
   */
  const goToNotifications = () => {
    router.push({ path: '/notifications' }).then(() => {
      // Use router.replace instead of router.go(0)
      if (route.name === 'usersnotifications') {
        router.replace({
          path: '/notifications',
          query: { refresh: Date.now() },
        });
      }
    });
  };

  /**
   * Navigates to the notification URL or default notification page
   * @param notificationRecord - The notification record to process
   */
  const goToNotificationUrl = async (notificationRecord: UserNotification) => {
    await updateUserNotification(
      user?.value?.uid as string,
      notificationRecord.id,
      {
        isRead: true,
        readAt: Date.now(),
      },
    );
    goToNotifications();
    // if (!notificationRecord.url) goToNotifications();
    // else await router.push({ path: notificationRecord.url });
  };

  /** Stops the listener for new notification limits */
  const stopListeningNewNotificationLimit = (): void => {
    if (newNotificationLimitUnSub) {
      newNotificationLimitUnSub();
    }
  };

  /** Stops the listener for new notifications */
  const stopListeningNewNotification = (): void => {
    if (newNotificationUnSub) {
      newNotificationUnSub();
    }
  };

  // Return state and actions
  return {
    notifications,
    unreadCount,
    countSearch,
    stopListeningNewNotification,
    stopListeningNewNotificationLimit,
    buildNotificationsQuery,
    getNotificationsQueryCountBySearch,
    createUserNotification,
    updateUserNotification,
    fetchNotifications,
    countNewUserNotifications,
    goToNotificationUrl,
    goToNotifications,
  };
});
