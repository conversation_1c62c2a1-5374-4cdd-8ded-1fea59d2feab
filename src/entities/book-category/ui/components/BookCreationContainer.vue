<template>
  <q-dialog
    v-model="showDialog"
    persistent
    maximized
    transition-show="slide-up"
    transition-hide="slide-down"
  >
    <q-card class="book-creation-card">
      <!-- Progress bar at top -->
      <div class="progress-container">
        <div
          class="progress-bar"
          :style="{ width: `${(currentStepIndex / totalSteps) * 100}%` }"
        ></div>
      </div>

      <q-card-section class="q-pa-none">
        <div class="creation-content">
          <!-- Mobile header with step indicator -->
          <div class="mobile-header q-pa-md q-pb-none">
            <div class="row items-center justify-between">
              <div class="text-subtitle1 text-weight-medium">
                Step {{ currentStepIndex }} of {{ totalSteps }}
              </div>
              <div class="text-caption">{{ stepTitles[currentStep] }}</div>
            </div>
          </div>

          <!-- Content area -->
          <div class="content-area">
            <!-- Manny avatar and message - shown at top on mobile, left on desktop -->
            <div class="manny-container q-pa-md">
              <div class="manny-avatar">
                <q-img src="robot.png" width="80px" />
              </div>
              <div class="manny-message">
                <p>{{ stepDescriptions[currentStep] }}</p>
              </div>
            </div>

            <!-- Form content -->
            <div class="form-container q-pa-md">
              <q-card flat bordered class="form-card">
                <q-card-section>
                  <!-- Step content -->
                  <div v-if="currentStep === 'category'">
                    <CategorySelectionPage
                      @select-category="setCategory"
                      @continue="goToNextStep"
                    />
                  </div>

                  <div v-else-if="currentStep === 'details'">
                    <BookDetailsPage
                      :category="selectedCategory"
                      :title="title"
                      :subtitle="subtitle"
                      :author-name="authorName"
                      @update:title="title = $event"
                      @update:subtitle="subtitle = $event"
                      @update:author-name="authorName = $event"
                      @continue="goToNextStep"
                      @back="goToPreviousStep"
                    />
                  </div>

                  <div v-else-if="currentStep === 'subcategory'">
                    <SubcategorySelectionPage
                      :category-id="selectedCategory?.id || ''"
                      :subcategory-id="selectedSubcategory?.id"
                      :book-title="title"
                      :book-subtitle="subtitle"
                      :mission-questions="missionQuestions"
                      @select-subcategory="setSubcategory"
                      @update:mission-questions="missionQuestions = $event"
                      @continue="goToNextStep"
                      @back="goToPreviousStep"
                    />
                  </div>

                  <div v-else-if="currentStep === 'chapters'">
                    <ChaptersPage
                      :category="selectedCategory"
                      :subcategory="selectedSubcategory"
                      :book-title="title"
                      :book-subtitle="subtitle"
                      :mission-questions="missionQuestions"
                      :chapters="chapters"
                      @update:chapters="chapters = $event"
                      @continue="goToNextStep"
                      @back="goToPreviousStep"
                    />
                  </div>

                  <div v-else-if="currentStep === 'summary'">
                    <SummaryPage
                      :category="selectedCategory"
                      :subcategory="selectedSubcategory"
                      :title="title"
                      :subtitle="subtitle"
                      :author-name="authorName"
                      :mission-questions="missionQuestions"
                      :chapters="chapters"
                      @back="goToPreviousStep"
                      @save="saveBook"
                    />
                  </div>
                </q-card-section>
              </q-card>
            </div>
          </div>

          <!-- Navigation buttons -->
          <div class="navigation-buttons q-pa-md">
            <div class="row justify-between">
              <q-btn
                v-if="currentStepIndex > 1"
                flat
                color="primary"
                icon="arrow_back"
                label="Back"
                @click="goToPreviousStep"
              />
              <q-btn
                v-else
                flat
                color="primary"
                icon="arrow_back"
                label="Exit"
                @click="showDialog = false"
              />

              <q-btn
                v-if="currentStepIndex < totalSteps"
                unelevated
                color="primary"
                :label="
                  currentStepIndex === totalSteps - 1 ? 'Review' : 'Continue'
                "
                icon-right="arrow_forward"
                @click="goToNextStep"
                :disable="isStepDisabled"
              />
              <q-btn
                v-else
                unelevated
                color="primary"
                label="Create Book"
                icon-right="check"
                @click="saveBook"
                :disable="isStepDisabled"
              />
            </div>
          </div>
        </div>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, defineEmits, defineProps, watch } from 'vue';
import { useQuasar } from 'quasar';

import { createEmptyBook, NewChapterOutline } from 'src/entities/book';
import {
  BookCategory,
  Subcategory,
  ChaptersPage,
  SummaryPage,
  CategorySelectionPage,
  BookDetailsPage,
  SubcategorySelectionPage,
} from 'src/entities/book-category';
import type { NewBook } from 'src/entities/book/model/types';

const props = withDefaults(
  defineProps<{
    show?: boolean;
    initialBook?: Partial<NewBook>;
  }>(),
  {
    show: true,
    initialBook: () => ({}),
  },
);

const emit = defineEmits<{
  (e: 'update:show', value: boolean): void;
  (e: 'book-created', book: NewBook): void;
}>();

const $q = useQuasar();

// Dialog state
const showDialog = ref(props.show);

// Navigation state
const steps = [
  'category',
  'details',
  'subcategory',
  'chapters',
  'summary',
] as const;
type Step = (typeof steps)[number];
const currentStep = ref<Step>('category');
const totalSteps = steps.length;

// Step information
const stepTitles = {
  category: 'Choose Book Type',
  details: 'Book Details',
  subcategory: 'Book Focus',
  chapters: 'Create Chapters',
  summary: 'Book Summary',
};

const stepDescriptions = {
  category:
    "Hi, I'm Manny! I'll help you create your book. Let's start by choosing what type of book you want to write.",
  details:
    "Great choice! Now, let's give your book a title and subtitle. Don't worry, you can change these later.",
  subcategory:
    "Let's narrow down your book's focus. Select a subcategory that best matches your vision.",
  chapters:
    "Now for the exciting part! Let's outline your book's chapters. I can help generate ideas based on your book's purpose.",
  summary:
    'Almost done! Review all the details of your book before creating it. Everything look good?',
};

// Computed current step index (1-based for display)
const currentStepIndex = computed(() => {
  return steps.indexOf(currentStep.value) + 1;
});

// Book data
const selectedCategory = ref<BookCategory | null>(null);
const selectedSubcategory = ref<Subcategory | null>(null);
const title = ref(props.initialBook?.title || '');
const subtitle = ref(props.initialBook?.subtitle || '');
const authorId = ref(props.initialBook?.authorId || '');
const authorName = ref(props.initialBook?.authorName || '');
const missionQuestions = ref<Record<string, any>>(
  props.initialBook?.missionQuestions || {},
);
const chapters = ref<NewChapterOutline[]>(props.initialBook?.chapters || []);

// Computed book category for createEmptyBook
const bookCategory = computed(() => selectedCategory.value?.id || '');

// Complete book object
const book = computed(() => {
  const newBook: NewBook = {
    ...createEmptyBook(bookCategory.value),
    title: title.value,
    subtitle: subtitle.value,
    authorId: authorId.value,
    authorName: authorName.value,
    category: bookCategory.value,
    chapters: chapters.value,
    missionQuestions: missionQuestions.value,
  };

  return newBook;
});

// Computed property to check if the current step is disabled
const isStepDisabled = computed(() => {
  switch (currentStep.value) {
    case 'category':
      return !selectedCategory.value;
    case 'details':
      return !title.value || !authorName.value;
    case 'subcategory':
      return (
        !selectedSubcategory.value ||
        Object.keys(missionQuestions.value).length === 0
      );
    case 'chapters':
      return chapters.value.length === 0;
    case 'summary':
      return false; // Summary page is never disabled
    default:
      return false;
  }
});

// Navigation methods
function goToNextStep() {
  const currentIndex = steps.indexOf(currentStep.value);
  if (currentIndex < steps.length - 1) {
    currentStep.value = steps[currentIndex + 1];
  }
}

function goToPreviousStep() {
  const currentIndex = steps.indexOf(currentStep.value);
  if (currentIndex > 0) {
    currentStep.value = steps[currentIndex - 1];
  }
}

function setCategory(category: BookCategory) {
  selectedCategory.value = category;
}

function setSubcategory(subcategory: Subcategory) {
  selectedSubcategory.value = subcategory;
}

function saveBook() {
  // Validate book data
  if (!title.value) {
    $q.notify({
      color: 'negative',
      message: 'Book title is required',
      icon: 'error',
    });
    return;
  }

  if (!authorName.value) {
    $q.notify({
      color: 'negative',
      message: 'Author name is required',
      icon: 'error',
    });
    return;
  }

  if (!selectedCategory.value) {
    $q.notify({
      color: 'negative',
      message: 'Book category is required',
      icon: 'error',
    });
    return;
  }

  if (!selectedSubcategory.value) {
    $q.notify({
      color: 'negative',
      message: 'Book subcategory is required',
      icon: 'error',
    });
    return;
  }

  if (Object.keys(missionQuestions.value).length === 0) {
    $q.notify({
      color: 'negative',
      message: 'Please answer at least one mission question',
      icon: 'error',
    });
    return;
  }

  if (chapters.value.length === 0) {
    $q.notify({
      color: 'negative',
      message: 'Please add at least one chapter',
      icon: 'error',
    });
    return;
  }

  // Emit the created book
  emit('book-created', book.value);

  // Close the dialog
  showDialog.value = false;
  emit('update:show', false);

  // Show success notification
  $q.notify({
    color: 'positive',
    message: 'Book created successfully!',
    icon: 'check_circle',
  });
}

// Watch for changes in the show prop
watch(
  () => props.show,
  (newVal) => {
    showDialog.value = newVal;
  },
);

// Watch for changes in the dialog state
watch(showDialog, (newVal) => {
  emit('update:show', newVal);
});
</script>

<style lang="scss" scoped>
// Main container styling
.book-creation-card {
  border-radius: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f9fafb;
  overflow: hidden;
}

// Progress bar at top
.progress-container {
  height: 4px;
  width: 100%;
  background-color: rgba($primary, 0.1);
  position: relative;
  z-index: 10;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, $primary 0%, lighten($primary, 15%) 100%);
  transition: width 0.3s ease;
  border-radius: 0 2px 2px 0;
}

// Content layout
.creation-content {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 4px);
}

// Mobile header
.mobile-header {
  display: flex;
  align-items: center;
  padding: 16px 20px;

  .text-subtitle1 {
    font-weight: 600;
    color: $primary;
  }

  .text-caption {
    color: rgba(0, 0, 0, 0.6);
    font-size: 0.9rem;
  }
}

// Content area with Manny and form
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  @media (min-width: 768px) {
    flex-direction: row;
  }
}

// Manny container
.manny-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, $primary 0%, darken($primary, 15%) 100%);
  color: white;
  border-radius: 0 0 24px 24px;
  box-shadow: 0 4px 20px rgba($primary, 0.2);

  @media (min-width: 768px) {
    width: 30%;
    max-width: 320px;
    border-radius: 0;
    justify-content: center;
    box-shadow: 4px 0 20px rgba($primary, 0.1);
  }
}

.manny-avatar {
  background-color: white;
  border-radius: 50%;
  padding: 10px;
  margin-bottom: 20px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);

  img {
    border-radius: 50%;
  }
}

.manny-message {
  text-align: center;
  font-size: 1rem;
  line-height: 1.6;

  p {
    margin-bottom: 0;
  }
}

// Form container
.form-container {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;

  @media (min-width: 768px) {
    padding: 40px;
  }
}

.form-card {
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.05);
  border: none;
  flex: 1;

  .q-card__section {
    padding: 24px;

    @media (min-width: 768px) {
      padding: 32px;
    }
  }
}

// Navigation buttons
.navigation-buttons {
  padding: 16px 20px;
  background-color: white;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.03);

  .q-btn {
    min-width: 120px;
    height: 44px;

    &[unelevated] {
      background: linear-gradient(
        90deg,
        $primary 0%,
        lighten($primary, 10%) 100%
      );
      box-shadow: 0 4px 12px rgba($primary, 0.2);
      transition: all 0.2s ease;

      &:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba($primary, 0.3);
      }
    }

    &[flat] {
      color: rgba(0, 0, 0, 0.7);

      &:hover {
        background-color: rgba(0, 0, 0, 0.05);
      }
    }
  }
}

// Animations
.q-dialog__inner--minimized > div {
  animation: fade-in-up 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Custom scrollbar
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.03);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba($primary, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba($primary, 0.5);
}

// Responsive adjustments
@media (max-width: 767px) {
  .manny-container {
    padding: 16px;
    margin-bottom: 0;
  }

  .manny-avatar {
    margin-bottom: 12px;
  }

  .manny-message {
    font-size: 0.9rem;
  }

  .form-container {
    padding: 16px;
  }

  .form-card .q-card__section {
    padding: 16px;
  }

  .navigation-buttons {
    padding: 12px 16px;
  }

  .navigation-buttons .q-btn {
    min-width: 100px;
  }
}
</style>
