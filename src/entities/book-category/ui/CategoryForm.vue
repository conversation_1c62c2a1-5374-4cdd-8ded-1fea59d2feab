<template>
  <q-card class="category-form">
    <q-card-section>
      <div class="text-h6">Create Category</div>
    </q-card-section>

    <q-card-section>
      <q-form @submit.prevent="submit">
        <q-input v-model="category.title" label="Title" :rules="[required]" />

        <q-select
          v-model="category.icon"
          :options="iconOptions"
          label="Icon"
          :rules="[required]"
        >
          <template #selected-item="{ value }">
            <q-icon :name="value" />
          </template>
          <template #option="{ itemProps, label, value }">
            <q-item v-bind="itemProps">
              <q-item-section avatar>
                <q-icon :name="value" />
              </q-item-section>
              <q-item-section>{{ label }}</q-item-section>
            </q-item>
          </template>
        </q-select>

        <q-card-actions>
          <q-btn type="submit" color="primary" label="Save" />
        </q-card-actions>
      </q-form>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { BookCategory } from 'src/entities/book-category';

const props = defineProps<{
  modelValue: BookCategory;
}>();

const emit = defineEmits<{
  (e: 'submit', value: BookCategory): void;
}>();

const category = ref({ ...props.modelValue });

const iconOptions = [
  { label: 'Book', value: 'book' },
  { label: 'Science', value: 'science' },
  { label: 'Code', value: 'code' },
  { label: 'School', value: 'school' },
];

function required(value: string) {
  return !!value || 'Required';
}

function submit() {
  emit('submit', category.value);
}
</script>
