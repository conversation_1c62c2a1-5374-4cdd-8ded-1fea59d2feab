<template>
  <div class="category-selection q-pa-md">
    <div class="text-h5 q-mb-xl text-weight-bold text-primary text-center">
      Select Book Category
    </div>

    <div v-if="loading" class="text-center q-pa-xl">
      <q-spinner color="primary" size="3em" />
      <div class="q-mt-sm text-subtitle1">Loading categories...</div>
    </div>

    <div class="row justify-center q-col-gutter-lg" v-else>
      <div class="col-12 col-md-10 col-lg-8">
        <q-card
          v-for="category in categories"
          :key="category.id"
          :class="[
            'category-card q-mb-lg',
            selectedCategoryId === category.id ? 'selected-card' : '',
          ]"
          clickable
          @click="selectCategory(category)"
        >
          <q-card-section class="row items-center no-wrap q-py-md">
            <div class="col-auto">
              <q-avatar
                :color="
                  selectedCategoryId === category.id ? 'primary' : 'grey-3'
                "
                text-color="white"
                size="60px"
                class="category-avatar"
              >
                <q-icon :name="category.icon || 'category'" size="32px" />
              </q-avatar>
            </div>
            <div class="col q-ml-lg">
              <div class="text-h6 text-weight-medium">
                {{ category.title }}
              </div>
              <div class="text-body2 q-mt-sm text-grey-8">
                {{
                  category.description ||
                  `Create a ${category.title.toLowerCase()} book with our guided process`
                }}
              </div>
            </div>
            <div class="col-auto">
              <q-radio
                v-model="selectedCategoryId"
                :val="category.id"
                color="primary"
                size="lg"
              />
            </div>
          </q-card-section>
        </q-card>

        <div class="row justify-center q-mt-xl">
          <q-btn
            color="primary"
            label="Continue"
            :disable="!selectedCategoryId"
            @click="continueToDetails"
            class="premium-btn q-px-xl"
            icon-right="arrow_forward"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { BookCategory, useCategoryStore } from 'src/entities/book-category';

const emit = defineEmits<{
  (e: 'select-category', category: BookCategory): void;
  (e: 'continue'): void;
}>();

const categoryStore = useCategoryStore();
const categories = computed(() => categoryStore.categories);
const loading = computed(() => categoryStore.loading);
const selectedCategoryId = ref('');

onMounted(async () => {
  if (categories.value.length === 0) {
    await categoryStore.fetchCategories();
  }
});

function selectCategory(category: BookCategory) {
  selectedCategoryId.value = category.id;
  emit('select-category', category);
}

function continueToDetails() {
  const selectedCategory = categories.value.find(
    (c) => c.id === selectedCategoryId.value,
  );
  if (selectedCategory) {
    emit('select-category', selectedCategory);
    emit('continue');
  }
}
</script>

<style scoped lang="scss">
.category-selection {
  max-width: 1200px;
  margin: 0 auto;
}

.category-card {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  border-radius: 16px;
  border: 2px solid transparent;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  }

  &.selected-card {
    border: 2px solid var(--q-primary);
    background: linear-gradient(
      to right,
      rgba(var(--q-primary), 0.03),
      rgba(var(--q-primary), 0.07)
    );
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  }
}

.category-avatar {
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

  .selected-card & {
    transform: scale(1.05);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  }
}

.premium-btn {
  background: linear-gradient(135deg, $primary, darken($primary, 15%));
  color: white;
  border-radius: 10px;
  padding: 0 20px;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba($primary, 0.25);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  height: 48px;
  font-size: 16px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba($primary, 0.35);
  }
}
</style>
