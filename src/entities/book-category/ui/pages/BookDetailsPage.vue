<template>
  <div class="book-details-container q-pa-md">
    <div class="text-h5 q-mb-lg text-weight-bold text-primary">
      Book Details
    </div>

    <div class="row q-col-gutter-md justify-center">
      <div class="col-12">
        <q-card class="premium-card">
          <q-card-section class="card-header">
            <div class="card-title">
              Enter the details for your {{ categoryName }} book
            </div>
          </q-card-section>

          <q-card-section class="card-content">
            <q-form @submit.prevent="continueToSubcategory" class="q-gutter-lg">
              <div class="input-container">
                <div class="text-subtitle2 q-mb-sm text-weight-medium">
                  Book Title
                </div>
                <q-input
                  v-model="title"
                  placeholder="Enter a compelling title for your book"
                  outlined
                  :rules="[(val) => !!val || 'Title is required']"
                  class="modern-input"
                  bg-color="white"
                >
                  <template v-slot:append>
                    <q-btn
                      round
                      flat
                      icon="smart_toy"
                      size="sm"
                      class="ai-action-btn"
                      @click="generateTitle"
                      :loading="generatingTitle"
                    >
                      <q-tooltip>Generate with AI</q-tooltip>
                    </q-btn>
                  </template>
                </q-input>
              </div>

              <div class="input-container">
                <div class="text-subtitle2 q-mb-sm text-weight-medium">
                  Book Subtitle
                </div>
                <q-input
                  v-model="subtitle"
                  placeholder="Add a descriptive subtitle (optional)"
                  class="modern-input"
                  outlined
                  bg-color="white"
                >
                  <template v-slot:append>
                    <q-btn
                      round
                      flat
                      icon="smart_toy"
                      size="sm"
                      class="ai-action-btn"
                      @click="generateSubtitle"
                      :loading="generatingSubtitle"
                      :disable="!title"
                    >
                      <q-tooltip>Generate with AI</q-tooltip>
                    </q-btn>
                  </template>
                </q-input>
                <div class="text-caption text-grey-7 q-mt-xs">
                  A good subtitle helps explain what readers will gain from your
                  book
                </div>
              </div>

              <div class="input-container">
                <div class="text-subtitle2 q-mb-sm text-weight-medium">
                  Author Name
                </div>
                <q-input
                  v-model="authorName"
                  placeholder="Enter author's name"
                  class="modern-input"
                  outlined
                  bg-color="white"
                  :rules="[(val) => !!val || 'Author name is required']"
                />
              </div>

              <q-separator class="q-my-md" />

              <div class="row justify-between q-mt-lg">
                <q-btn
                  flat
                  color="primary"
                  label="Back"
                  class="q-px-md"
                  @click="$emit('back')"
                  icon="arrow_back"
                />

                <q-btn
                  color="primary"
                  label="Continue"
                  class="premium-btn q-px-xl"
                  type="submit"
                  :disable="!title || !authorName"
                  icon-right="arrow_forward"
                />
              </div>
            </q-form>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useQuasar } from 'quasar';
import { composeText } from 'src/shared/api/openai';
import { confirmOverrideText } from 'src/shared/lib/quasar-dialogs';
import type { BookCategory } from 'src/entities/book-category';

const props = defineProps<{
  category: BookCategory | null;
  title?: string;
  subtitle?: string;
  authorName?: string;
}>();

const emit = defineEmits<{
  (e: 'update:title', value: string): void;
  (e: 'update:subtitle', value: string): void;
  (e: 'update:authorName', value: string): void;
  (e: 'continue'): void;
  (e: 'back'): void;
}>();

const $q = useQuasar();

const title = ref(props.title || '');
const subtitle = ref(props.subtitle || '');
const authorName = ref(props.authorName || '');
const generatingTitle = ref(false);
const generatingSubtitle = ref(false);

// Watch for prop changes
watch(
  () => props.title,
  (newVal) => {
    if (newVal !== undefined) title.value = newVal;
  },
);

watch(
  () => props.subtitle,
  (newVal) => {
    if (newVal !== undefined) subtitle.value = newVal;
  },
);

watch(
  () => props.authorName,
  (newVal) => {
    if (newVal !== undefined) authorName.value = newVal;
  },
);

const categoryName = computed(() => props.category?.title || 'new');

async function generateTitle() {
  if (title.value && !(await confirmOverrideText($q))) {
    return;
  }

  generatingTitle.value = true;
  try {
    const prompt = `Generate a compelling title for a ${categoryName.value} book. The title should be concise, memorable, and engaging. Give me just the title without any explanation or additional text.`;

    const response = await composeText(prompt);
    title.value = response.trim().replace(/^"|"$/g, '');
    emit('update:title', title.value);
  } catch (error) {
    console.error('Error generating title:', error);
    $q.notify({
      color: 'negative',
      message: 'Failed to generate title',
      icon: 'error',
    });
  } finally {
    generatingTitle.value = false;
  }
}

async function generateSubtitle() {
  if (subtitle.value && !(await confirmOverrideText($q))) {
    return;
  }

  generatingSubtitle.value = true;
  try {
    const prompt = `Generate a compelling subtitle for a ${categoryName.value} book titled "${title.value}". The subtitle should complement the title, provide additional context, and be engaging. Give me just the subtitle without any explanation or additional text.`;

    const response = await composeText(prompt);
    subtitle.value = response.trim().replace(/^"|"$/g, '');
    emit('update:subtitle', subtitle.value);
  } catch (error) {
    console.error('Error generating subtitle:', error);
    $q.notify({
      color: 'negative',
      message: 'Failed to generate subtitle',
      icon: 'error',
    });
  } finally {
    generatingSubtitle.value = false;
  }
}

function continueToSubcategory() {
  emit('update:title', title.value);
  emit('update:subtitle', subtitle.value);
  emit('update:authorName', authorName.value);
  emit('continue');
}
</script>

<style scoped lang="scss">
.book-details-container {
  max-width: 1200px;
  margin: 0 auto;
}

.input-container {
  margin-bottom: 1.5rem;
}
</style>
