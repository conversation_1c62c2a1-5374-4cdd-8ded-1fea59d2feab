<template>
  <div class="subcategory-selection q-pa-md">
    <div class="text-h5 q-mb-xl text-weight-bold text-primary text-center">
      Select Book Subcategory
    </div>

    <div v-if="loading" class="text-center q-pa-xl">
      <q-spinner color="primary" size="3em" />
      <div class="q-mt-sm text-subtitle1">Loading subcategories...</div>
    </div>

    <div
      v-else-if="subcategories.length === 0"
      class="text-center q-pa-xl empty-state"
    >
      <q-icon name="folder_off" size="4em" color="grey-5" />
      <div class="text-h6 q-mt-md text-weight-medium">
        No subcategories found
      </div>
      <div class="q-mt-sm text-body1 text-grey-8">
        Please contact support to add subcategories
      </div>
    </div>

    <div class="row q-col-gutter-lg justify-center" v-else>
      <div
        v-for="subcategory in subcategories"
        :key="subcategory.id"
        class="col-12 col-md-6 col-lg-4"
      >
        <q-card
          class="subcategory-card cursor-pointer"
          :class="{ 'selected-card': selectedSubcategoryId === subcategory.id }"
          @click="selectSubcategory(subcategory)"
        >
          <q-card-section class="q-pa-lg">
            <div class="row items-center no-wrap">
              <div class="col-auto">
                <q-avatar
                  :color="
                    selectedSubcategoryId === subcategory.id
                      ? 'primary'
                      : 'amber'
                  "
                  text-color="white"
                  size="56px"
                  class="subcategory-avatar"
                >
                  <q-icon :name="subcategory.icon || 'folder'" size="28px" />
                </q-avatar>
              </div>
              <div class="col q-ml-lg">
                <div class="text-h6 text-weight-medium">
                  {{ subcategory.name }}
                </div>
                <p
                  class="q-mt-sm text-body2 text-grey-8 subcategory-description"
                >
                  {{
                    subcategory.description ||
                    `Questions about ${subcategory.name}`
                  }}
                </p>
              </div>
              <div class="col-auto selection-indicator">
                <q-icon
                  v-if="selectedSubcategoryId === subcategory.id"
                  name="check_circle"
                  color="primary"
                  size="28px"
                  class="pulse-animation"
                />
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- Questions section when subcategory is selected -->
    <div v-if="selectedSubcategoryId && showQuestions" class="q-mt-xl">
      <q-separator class="q-my-lg" />

      <QuestionsPage
        :category-id="categoryId"
        :subcategory="selectedSubcategory"
        :book-title="bookTitle"
        :book-subtitle="bookSubtitle"
        :initial-mission-questions="initialMissionQuestions"
        @update:missionQuestions="$emit('update:missionQuestions', $event)"
        @continue="$emit('continue')"
        @back="resetSelection"
      />
    </div>

    <!-- Navigation buttons when no subcategory is selected or questions are not shown -->
    <div v-else class="row justify-center q-mt-xl">
      <q-btn
        flat
        color="primary"
        @click="$emit('back')"
        label="Back"
        icon="arrow_back"
        class="q-px-md q-mr-md"
      />

      <q-btn
        color="primary"
        label="Continue"
        :disable="!selectedSubcategoryId"
        @click="showQuestionsForSubcategory"
        class="premium-btn q-px-xl"
        icon-right="arrow_forward"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useQuestionStore } from 'src/entities/question';
import {
  Subcategory,
  useCategoryStore,
  QuestionsPage,
} from 'src/entities/book-category';
import { useQuasar } from 'quasar';

const props = defineProps<{
  categoryId: string;
  initialSubcategoryId?: string;
  bookTitle?: string;
  bookSubtitle?: string;
  initialMissionQuestions?: Record<string, any>;
}>();

const emit = defineEmits<{
  (e: 'select-subcategory', subcategory: Subcategory): void;
  (e: 'update:missionQuestions', questions: Record<string, any>): void;
  (e: 'continue'): void;
  (e: 'back'): void;
}>();

const $q = useQuasar();
const categoryStore = useCategoryStore();
const questionStore = useQuestionStore();

const loading = ref(true);
const selectedSubcategoryId = ref(props.initialSubcategoryId || '');
const showQuestions = ref(false);

const subcategories = computed(() => {
  return questionStore.subcategories || [];
});

const selectedSubcategory = computed(() => {
  return (
    subcategories.value.find((s) => s.id === selectedSubcategoryId.value) ||
    null
  );
});

// Watch for changes in categoryId prop
watch(
  () => props.categoryId,
  async (newCategoryId, oldCategoryId) => {
    if (newCategoryId && newCategoryId !== oldCategoryId) {
      await fetchSubcategories(newCategoryId);
    }
  },
);

// Watch for changes in initialSubcategoryId prop
watch(
  () => props.initialSubcategoryId,
  (newId) => {
    if (newId) {
      selectedSubcategoryId.value = newId;
      if (props.initialMissionQuestions) {
        showQuestions.value = true;
      }
    }
  },
);

onMounted(async () => {
  if (props.categoryId) {
    await fetchSubcategories(props.categoryId);
  } else {
    loading.value = false;
  }

  // If initial subcategory is provided and we have initial questions,
  // show the questions section immediately
  if (props.initialSubcategoryId && props.initialMissionQuestions) {
    selectedSubcategoryId.value = props.initialSubcategoryId;
    showQuestions.value = true;
  }
});

async function fetchSubcategories(categoryId: string) {
  loading.value = true;
  try {
    await categoryStore.setActiveCategory(categoryId);
    await questionStore.fetchAllBySubcategory(categoryId);

    // Add a small delay to ensure the subcategories are loaded
    setTimeout(() => {
      loading.value = false;
    }, 300);
  } catch (error) {
    console.error('Error fetching subcategories:', error);
    $q.notify({
      color: 'negative',
      message: 'Failed to load subcategories',
      icon: 'error',
    });
    loading.value = false;
  }
}

function selectSubcategory(subcategory: Subcategory) {
  selectedSubcategoryId.value = subcategory.id;
  emit('select-subcategory', subcategory);
}

function showQuestionsForSubcategory() {
  if (selectedSubcategory.value) {
    showQuestions.value = true;
  }
}

function resetSelection() {
  showQuestions.value = false;
}
</script>

<style scoped lang="scss">
.subcategory-selection {
  max-width: 1200px;
  margin: 0 auto;
}

.empty-state {
  padding: 60px 0;
  opacity: 0.8;
}

.subcategory-card {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  border: 2px solid transparent;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  height: 100%;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  }

  &.selected-card {
    border: 2px solid var(--q-primary);
    background: linear-gradient(
      to right,
      rgba(var(--q-primary), 0.03),
      rgba(var(--q-primary), 0.07)
    );
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  }
}

.subcategory-avatar {
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

  .selected-card & {
    transform: scale(1.05);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  }
}

.subcategory-description {
  line-height: 1.5;
  margin-bottom: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.selection-indicator {
  min-width: 40px;
  display: flex;
  justify-content: center;
}

.pulse-animation {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.premium-btn {
  background: linear-gradient(135deg, $primary, darken($primary, 15%));
  color: white;
  border-radius: 10px;
  padding: 0 20px;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba($primary, 0.25);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  height: 42px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba($primary, 0.35);
  }
}
</style>
