<template>
  <div class="chapters-page q-pa-md">
    <div class="text-h5 q-mb-lg text-weight-bold text-primary text-center">
      Book Chapters
    </div>

    <div class="row justify-center">
      <div class="col-12">
        <q-card class="premium-card">
          <q-card-section class="card-header">
            <div class="card-title">
              <q-icon name="menu_book" class="q-mr-sm" />
              Organize Your Book Chapters
            </div>
          </q-card-section>

          <q-card-section class="card-content">
            <div class="text-body2 q-mb-lg">
              <p class="text-grey-8">
                <q-icon name="info" color="info" size="sm" class="q-mr-xs" />
                Drag and drop to reorder chapters. Click on a chapter to edit
                its content.
              </p>
            </div>

            <div v-if="generatingChapters" class="text-center q-py-xl">
              <q-spinner color="primary" size="3em" />
              <div class="q-mt-sm text-subtitle1">
                Generating chapters with AI...
              </div>
              <div class="text-caption text-grey-7 q-mt-xs">
                This may take a moment
              </div>
            </div>

            <div v-else>
              <draggable
                v-model="chaptersModel"
                group="chapters"
                item-key="id"
                handle=".drag-handle"
                class="q-mb-md chapters-list"
              >
                <template #item="{ element, index }">
                  <div class="chapter-item q-mb-md">
                    <q-expansion-item
                      :label="`Chapter ${index + 1}: ${element.title}`"
                      header-class="chapter-header"
                      expand-icon-class="chapter-expand-icon"
                    >
                      <template v-slot:header>
                        <q-item-section avatar>
                          <q-icon
                            name="drag_indicator"
                            class="drag-handle cursor-move text-grey-7"
                          />
                        </q-item-section>

                        <q-item-section>
                          <q-input
                            v-model="element.title"
                            dense
                            outlined
                            class="chapter-title-input"
                            bg-color="white"
                            placeholder="Chapter title"
                            @update:model-value="updateChapters"
                          />
                        </q-item-section>

                        <q-item-section side>
                          <q-btn
                            flat
                            round
                            icon="delete"
                            color="negative"
                            size="sm"
                            class="delete-btn"
                            @click.stop="removeChapter(index)"
                          >
                            <q-tooltip>Remove chapter</q-tooltip>
                          </q-btn>
                        </q-item-section>
                      </template>

                      <q-card class="chapter-content-card">
                        <q-card-section>
                          <q-input
                            v-model="element.content"
                            type="textarea"
                            outlined
                            autogrow
                            label="Chapter content"
                            placeholder="Write your chapter content here..."
                            rows="6"
                            class="content-textarea"
                            @update:model-value="updateChapters"
                          >
                            <template v-slot:append>
                              <q-btn
                                flat
                                round
                                icon="smart_toy"
                                color="primary"
                                class="ai-action-btn"
                                @click="generateChapterContent(index)"
                                :loading="loadingStates[index]"
                                :disable="loadingStates[index]"
                              >
                                <q-tooltip>Generate with AI</q-tooltip>
                              </q-btn>
                            </template>
                          </q-input>
                        </q-card-section>
                      </q-card>
                    </q-expansion-item>
                  </div>
                </template>
              </draggable>

              <div class="row q-mt-lg justify-center">
                <q-btn
                  color="primary"
                  outline
                  icon="add"
                  label="Add Chapter"
                  @click="addChapter"
                  class="action-btn q-mr-md"
                />

                <q-btn
                  color="secondary"
                  outline
                  icon="smart_toy"
                  label="Generate Chapters with AI"
                  @click="generateChaptersWithAI"
                  :disable="generatingChapters"
                  class="action-btn"
                />
              </div>
            </div>
          </q-card-section>
        </q-card>

        <div class="row justify-between q-mt-lg">
          <q-btn
            flat
            color="primary"
            @click="$emit('back')"
            label="Back"
            icon="arrow_back"
            class="q-px-md"
          />

          <q-btn
            color="primary"
            label="Save Book"
            :disable="chaptersModel.length === 0"
            @click="saveBook"
            class="premium-btn q-px-xl"
            icon-right="save"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useQuasar } from 'quasar';
import { composeText } from 'src/shared/api/openai';
import { v4 as uuidv4 } from 'uuid';
import draggable from 'vuedraggable';
import type { BookCategory, Subcategory } from 'src/entities/book-category';
import type { BookChapter } from 'src/entities/book';

const props = defineProps<{
  category: BookCategory | null;
  subcategory: Subcategory | null;
  bookTitle: string;
  bookSubtitle: string;
  missionQuestions: Record<string, any>;
  initialChapters?: BookChapter[];
}>();

const emit = defineEmits<{
  (e: 'update:chapters', value: BookChapter[]): void;
  (e: 'save'): void;
  (e: 'back'): void;
}>();

const $q = useQuasar();

const chaptersModel = ref<BookChapter[]>(props.initialChapters || []);
const generatingChapters = ref(false);
const loadingStates = ref<Record<number, boolean>>({});

watch(
  chaptersModel,
  () => {
    updateChapters();
  },
  { deep: true },
);

function updateChapters() {
  emit('update:chapters', [...chaptersModel.value]);
}

function addChapter() {
  const newChapter: BookChapter = {
    id: uuidv4(),
    title: `New Chapter ${chaptersModel.value.length + 1}`,
    content: '',
  };

  chaptersModel.value.push(newChapter);
  updateChapters();
}

function removeChapter(index: number) {
  $q.dialog({
    title: 'Confirm Deletion',
    message: 'Are you sure you want to remove this chapter?',
    cancel: true,
    persistent: true,
    ok: {
      color: 'negative',
      label: 'Delete',
    },
    cancel: {
      flat: true,
      label: 'Cancel',
    },
  }).onOk(() => {
    chaptersModel.value.splice(index, 1);
    updateChapters();
  });
}

async function generateChapterContent(index: number) {
  const chapter = chaptersModel.value[index];
  if (!chapter) return;

  loadingStates.value[index] = true;

  try {
    // Create context from mission questions
    const context = Object.entries(props.missionQuestions)
      .map(([id, answer]) => `${id}: ${answer}`)
      .join('\n');

    const prompt = `You are writing a book titled "${props.bookTitle}" ${
      props.bookSubtitle ? `with the subtitle "${props.bookSubtitle}"` : ''
    }.

    This is a ${props.category?.title} book in the ${props.subcategory
      ?.name} subcategory.

    ${
      context
        ? `Here is some context about the book's mission:\n${context}\n\n`
        : ''
    }

    Please write a detailed chapter for "${
      chapter.title
    }". The content should be engaging, informative, and relevant to the book's topic.
    Write approximately 500 words for this chapter.`;

    const response = await composeText(prompt);
    chapter.content = response;
    updateChapters();
  } catch (error) {
    console.error('Error generating chapter content:', error);
    $q.notify({
      color: 'negative',
      message: 'Failed to generate chapter content',
      icon: 'error',
    });
  } finally {
    loadingStates.value[index] = false;
  }
}

async function generateChaptersWithAI() {
  if (chaptersModel.value.length > 0) {
    const shouldProceed = await $q
      .dialog({
        title: 'Replace Existing Chapters',
        message:
          'This will replace your existing chapters. Do you want to continue?',
        cancel: true,
        persistent: true,
        ok: {
          color: 'primary',
          label: 'Continue',
        },
        cancel: {
          flat: true,
          label: 'Cancel',
        },
      })
      .onOk(() => true)
      .onCancel(() => false);

    if (!shouldProceed) return;
  }

  generatingChapters.value = true;

  try {
    // Create context from mission questions
    const context = Object.entries(props.missionQuestions)
      .map(([id, answer]) => `${id}: ${answer}`)
      .join('\n');

    const prompt = `You are planning a book titled "${props.bookTitle}" ${
      props.bookSubtitle ? `with the subtitle "${props.bookSubtitle}"` : ''
    }.

    This is a ${props.category?.title} book in the ${props.subcategory
      ?.name} subcategory.

    ${
      context
        ? `Here is some context about the book's mission:\n${context}\n\n`
        : ''
    }

    Please generate a chapter outline for this book. For each chapter, provide:
    1. A compelling chapter title
    2. A brief description of what the chapter will cover (2-3 sentences)

    Generate 8-12 chapters that would make sense for this type of book, including an introduction and conclusion.
    Format your response as JSON in this exact format:
    [
      {
        "title": "Chapter Title",
        "content": "Brief description of chapter content"
      }
    ]`;

    const response = await composeText(prompt);

    try {
      // Try to parse the response as JSON
      let chaptersData;

      // Extract JSON if it's wrapped in backticks
      const jsonMatch =
        response.match(/```json\s*([\s\S]*?)\s*```/) ||
        response.match(/```\s*([\s\S]*?)\s*```/);

      if (jsonMatch && jsonMatch[1]) {
        chaptersData = JSON.parse(jsonMatch[1]);
      } else {
        chaptersData = JSON.parse(response);
      }

      if (Array.isArray(chaptersData)) {
        chaptersModel.value = chaptersData.map((chapter) => ({
          id: uuidv4(),
          title: chapter.title,
          content: chapter.content,
        }));
        updateChapters();
      } else {
        throw new Error('Invalid response format');
      }
    } catch (parseError) {
      console.error('Error parsing AI response:', parseError);
      $q.notify({
        color: 'negative',
        message: 'Failed to parse AI response',
        icon: 'error',
      });
    }
  } catch (error) {
    console.error('Error generating chapters:', error);
    $q.notify({
      color: 'negative',
      message: 'Failed to generate chapters',
      icon: 'error',
    });
  } finally {
    generatingChapters.value = false;
  }
}

function saveBook() {
  emit('save');
}
</script>

<style scoped lang="scss">
.chapters-page {
  max-width: 1200px;
  margin: 0 auto;
}

.chapters-list {
  margin-top: 1rem;
}

.chapter-item {
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
}

.chapter-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  padding: 8px 0;
}

.chapter-expand-icon {
  color: $primary;
}

.chapter-title-input {
  width: 100%;

  :deep(.q-field__control) {
    border-radius: 8px;
  }
}

.chapter-content-card {
  border-top: none;
  border-radius: 0 0 12px 12px;
  box-shadow: none;
  background-color: #fafafa;
}

.content-textarea {
  :deep(.q-field__control) {
    border-radius: 8px;
  }
}

.drag-handle {
  cursor: move;
  transition: color 0.2s ease;

  &:hover {
    color: $primary !important;
  }
}

.delete-btn {
  opacity: 0.7;
  transition: all 0.2s ease;

  &:hover {
    opacity: 1;
    transform: scale(1.1);
  }
}

.action-btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}
</style>
