<template>
  <q-page class="summary-page q-pa-md">
    <div class="text-h4 q-mb-md text-center text-weight-bold text-primary">
      Book Summary
    </div>
    <div class="text-subtitle1 q-mb-lg text-center text-grey-8">
      Review your book details before finalizing
    </div>

    <div class="row justify-center">
      <div class="col-12 col-md-10 col-lg-8">
        <q-card class="premium-card q-mb-xl">
          <q-card-section class="card-header">
            <div class="card-title">
              <q-icon name="menu_book" class="q-mr-sm" />
              Book Overview
            </div>
          </q-card-section>

          <q-card-section class="q-pa-lg">
            <div class="q-mb-lg">
              <div class="section-title">
                <q-icon name="info" size="sm" class="q-mr-xs" />
                Book Information
              </div>

              <div class="info-container">
                <div class="info-item">
                  <div class="info-label">Category</div>
                  <div class="info-value">{{ category?.title }}</div>
                </div>

                <div class="info-item">
                  <div class="info-label">Subcategory</div>
                  <div class="info-value">{{ subcategory?.name }}</div>
                </div>

                <div class="info-item">
                  <div class="info-label">Title</div>
                  <div class="info-value text-weight-medium">{{ title }}</div>
                </div>

                <div class="info-item" v-if="subtitle">
                  <div class="info-label">Subtitle</div>
                  <div class="info-value">{{ subtitle }}</div>
                </div>

                <div class="info-item">
                  <div class="info-label">Author</div>
                  <div class="info-value">{{ authorName }}</div>
                </div>
              </div>
            </div>

            <div class="q-mb-lg">
              <div class="section-title">
                <q-icon name="help_outline" size="sm" class="q-mr-xs" />
                Mission Questions
              </div>

              <div class="mission-questions">
                <div
                  v-for="(answer, id) in missionQuestions"
                  :key="id"
                  class="question-item"
                >
                  <div class="question-text">{{ getQuestionText(id) }}</div>
                  <div class="answer-content">
                    <div v-if="Array.isArray(answer)" class="answer-list">
                      <div
                        v-for="(item, index) in answer"
                        :key="index"
                        class="answer-list-item"
                      >
                        <q-icon
                          name="check_circle"
                          size="xs"
                          color="primary"
                          class="q-mr-xs"
                        />
                        {{ item }}
                      </div>
                    </div>
                    <div v-else class="answer-text">{{ answer }}</div>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <div class="section-title">
                <q-icon name="view_list" size="sm" class="q-mr-xs" />
                Chapters ({{ chapters.length }})
              </div>

              <div class="chapters-container">
                <q-expansion-item
                  v-for="(chapter, index) in chapters"
                  :key="chapter.id"
                  :label="`Chapter ${index + 1}: ${chapter.title}`"
                  header-class="chapter-header"
                  expand-icon-class="chapter-expand-icon"
                  class="chapter-item"
                >
                  <template v-slot:header>
                    <q-item-section avatar>
                      <q-avatar
                        color="primary"
                        text-color="white"
                        size="28px"
                        font-size="12px"
                      >
                        {{ index + 1 }}
                      </q-avatar>
                    </q-item-section>

                    <q-item-section>
                      <q-item-label class="text-weight-medium">{{
                        chapter.title
                      }}</q-item-label>
                    </q-item-section>
                  </template>

                  <div class="chapter-content">
                    <div class="chapter-outline">{{ chapter.outline }}</div>
                  </div>
                </q-expansion-item>
              </div>
            </div>
          </q-card-section>

          <q-card-section class="card-footer">
            <div class="row justify-between">
              <q-btn
                flat
                color="primary"
                @click="emit('back')"
                label="Back"
                icon="arrow_back"
                class="q-px-md"
              />

              <q-btn
                color="primary"
                label="Save Book"
                @click="emit('save')"
                class="premium-btn q-px-xl"
                icon-right="save"
              />
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { useQuestionStore } from 'src/entities/question';
import type { BookCategory, Subcategory } from 'src/entities/book-category';
import { NewChapterOutline } from 'src/entities/book';

const props = defineProps<{
  category: BookCategory | null;
  subcategory: Subcategory | null;
  title: string;
  subtitle: string;
  authorName: string;
  missionQuestions: Record<string, any>;
  chapters: NewChapterOutline[];
}>();

const emit = defineEmits<{
  (e: 'back'): void;
  (e: 'save'): void;
}>();

const router = useRouter();
const $q = useQuasar();
const questionStore = useQuestionStore();

onMounted(() => {
  // Redirect if essential data is missing
  if (
    !props.category ||
    !props.subcategory ||
    !props.title ||
    Object.keys(props.missionQuestions || {}).length === 0 ||
    props.chapters.length === 0
  ) {
    emit('back');
  }
});

function getQuestionText(questionId: string) {
  const question = questionStore.questions.find((q) => q.id === questionId);
  return question ? question.question : questionId;
}
</script>

<style scoped lang="scss">
.summary-page {
  max-width: 1200px;
  margin: 0 auto;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: $primary;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba($primary, 0.1);
}

.info-container {
  background-color: rgba(0, 0, 0, 0.01);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.info-item {
  display: flex;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.03);

  &:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }

  .info-label {
    width: 120px;
    color: rgba(0, 0, 0, 0.6);
    font-size: 0.9rem;
    font-weight: 500;
  }

  .info-value {
    flex: 1;
    font-size: 1rem;
  }
}

.mission-questions {
  background-color: rgba(0, 0, 0, 0.01);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.question-item {
  padding: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);

  &:last-child {
    border-bottom: none;
  }

  .question-text {
    font-weight: 500;
    color: rgba(0, 0, 0, 0.7);
    margin-bottom: 8px;
    font-size: 0.95rem;
  }

  .answer-content {
    padding-left: 8px;
  }

  .answer-text {
    color: rgba(0, 0, 0, 0.8);
    font-size: 1rem;
    line-height: 1.5;
  }

  .answer-list {
    .answer-list-item {
      display: flex;
      align-items: center;
      margin-bottom: 4px;
      color: rgba(0, 0, 0, 0.8);
      font-size: 0.95rem;
    }
  }
}

.chapters-container {
  background-color: rgba(0, 0, 0, 0.01);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.chapter-item {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);

  &:last-child {
    border-bottom: none;
  }

  :deep(.q-item) {
    padding: 16px;
  }

  :deep(.q-expansion-item__content) {
    background-color: rgba($primary, 0.03);
  }
}

.chapter-header {
  &:hover {
    background-color: rgba(0, 0, 0, 0.02);
  }
}

.chapter-expand-icon {
  color: $primary;
}

.chapter-content {
  padding: 16px;

  .chapter-outline {
    color: rgba(0, 0, 0, 0.8);
    font-size: 0.95rem;
    line-height: 1.6;
    white-space: pre-line;
  }
}

.premium-btn {
  background: linear-gradient(135deg, $primary, darken($primary, 15%));
  color: white;
  border-radius: 10px;
  padding: 0 20px;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba($primary, 0.25);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  height: 42px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba($primary, 0.35);
  }
}
</style>
