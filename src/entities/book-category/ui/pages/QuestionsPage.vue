<template>
  <div class="questions-page q-pa-md">
    <div
      class="text-h5 q-mb-md text-weight-bold text-primary text-center"
      v-if="subcategory"
    >
      {{ subcategory.name }}
    </div>
    <div class="text-subtitle1 q-mb-lg text-center text-grey-8">
      Please answer the following questions to help define your book's mission
    </div>

    <div class="row justify-center">
      <div class="col-12">
        <q-card class="premium-card">
          <q-card-section class="card-header">
            <div class="card-title">
              <q-icon name="help_outline" class="q-mr-sm" />
              Book Mission Questions
            </div>
          </q-card-section>

          <q-card-section class="card-content">
            <div v-if="loading" class="text-center q-py-xl">
              <q-spinner color="primary" size="3em" />
              <div class="q-mt-sm text-subtitle1">Loading questions...</div>
              <div class="text-caption text-grey-7 q-mt-xs">
                This may take a moment
              </div>
            </div>

            <div
              v-else-if="filteredQuestions.length === 0"
              class="text-center q-py-xl empty-state"
            >
              <q-icon name="help_outline" size="4em" color="grey-5" />
              <div class="text-h6 q-mt-md text-weight-medium">
                No questions found
              </div>
              <div class="q-mt-sm text-body1 text-grey-8">
                Please select a different subcategory
              </div>
            </div>

            <div v-else>
              <div
                v-for="question in filteredQuestions"
                :key="question.id"
                class="question-item q-mb-lg"
              >
                <div class="text-subtitle1 text-weight-medium q-mb-sm">
                  {{ question.question }}
                </div>
                <div
                  class="text-caption text-grey-8 q-mb-md"
                  v-if="question.description"
                >
                  {{ question.description }}
                </div>

                <!-- Text input -->
                <div v-if="question.answerElement === 'text'">
                  <q-input
                    v-model="missionQuestionsModel[question.id]"
                    outlined
                    class="modern-input"
                    placeholder="Enter your answer"
                  >
                    <template v-slot:append>
                      <q-btn
                        v-if="question.hasAI"
                        flat
                        round
                        icon="smart_toy"
                        color="primary"
                        class="ai-action-btn"
                        :loading="loadingStates[question.id]"
                        :disable="loadingStates[question.id]"
                        @click="generateAnswer(question)"
                      >
                        <q-tooltip>Ask AI for suggestions</q-tooltip>
                      </q-btn>
                      <q-btn
                        flat
                        round
                        icon="mic"
                        color="primary"
                        class="input-action-btn"
                        @click="openTranscription(question.id)"
                      >
                        <q-tooltip>Voice input</q-tooltip>
                      </q-btn>
                    </template>
                  </q-input>
                </div>

                <!-- Textarea input -->
                <div v-else-if="question.answerElement === 'textarea'">
                  <q-input
                    v-model="missionQuestionsModel[question.id]"
                    type="textarea"
                    outlined
                    autogrow
                    class="modern-input"
                    placeholder="Enter your detailed answer"
                    rows="4"
                  >
                    <template v-slot:append>
                      <q-btn
                        v-if="question.hasAI"
                        flat
                        round
                        icon="smart_toy"
                        color="primary"
                        class="ai-action-btn"
                        :loading="loadingStates[question.id]"
                        :disable="loadingStates[question.id]"
                        @click="generateAnswer(question)"
                      >
                        <q-tooltip>Ask AI for suggestions</q-tooltip>
                      </q-btn>
                      <q-btn
                        flat
                        round
                        icon="mic"
                        color="primary"
                        class="input-action-btn"
                        @click="openTranscription(question.id)"
                      >
                        <q-tooltip>Voice input</q-tooltip>
                      </q-btn>
                    </template>
                  </q-input>
                </div>

                <!-- Radio input -->
                <div
                  v-else-if="question.answerElement === 'radio'"
                  class="option-group-container"
                >
                  <q-option-group
                    v-model="missionQuestionsModel[question.id]"
                    :options="
                      (question.options || []).map((opt) => ({
                        label: opt,
                        value: opt,
                      }))
                    "
                    type="radio"
                    color="primary"
                    class="custom-option-group"
                  />
                  <q-btn
                    v-if="question.hasAI"
                    flat
                    round
                    icon="smart_toy"
                    color="primary"
                    class="ai-action-btn q-mt-sm"
                    :loading="loadingStates[question.id]"
                    :disable="loadingStates[question.id]"
                    @click="generateAnswer(question)"
                  >
                    <q-tooltip>Ask AI for suggestions</q-tooltip>
                  </q-btn>
                </div>

                <!-- Options input (checkboxes) -->
                <div
                  v-else-if="question.answerElement === 'options'"
                  class="option-group-container"
                >
                  <q-option-group
                    v-model="missionQuestionsModel[question.id]"
                    :options="
                      (question.options || []).map((opt) => ({
                        label: opt,
                        value: opt,
                      }))
                    "
                    type="checkbox"
                    color="primary"
                    class="custom-option-group"
                  />
                  <q-btn
                    v-if="question.hasAI"
                    flat
                    round
                    icon="smart_toy"
                    color="primary"
                    class="ai-action-btn q-mt-sm"
                    :loading="loadingStates[question.id]"
                    :disable="loadingStates[question.id]"
                    @click="generateAnswer(question)"
                  >
                    <q-tooltip>Ask AI for suggestions</q-tooltip>
                  </q-btn>
                </div>

                <!-- Select dropdown -->
                <div
                  v-else-if="question.answerElement === 'select'"
                  class="select-container"
                >
                  <q-select
                    v-model="missionQuestionsModel[question.id]"
                    :options="question.options || []"
                    outlined
                    dense
                    class="modern-input"
                    emit-value
                    map-options
                    placeholder="Select an option"
                    :options-dense="true"
                    behavior="menu"
                  >
                    <template v-slot:append>
                      <q-btn
                        v-if="question.hasAI"
                        flat
                        round
                        icon="smart_toy"
                        color="primary"
                        class="ai-action-btn"
                        :loading="loadingStates[question.id]"
                        :disable="loadingStates[question.id]"
                        @click="generateAnswer(question)"
                      >
                        <q-tooltip>Ask AI for suggestions</q-tooltip>
                      </q-btn>
                    </template>
                  </q-select>
                </div>
              </div>
            </div>
          </q-card-section>

          <q-card-section
            class="card-footer"
            v-if="!loading && filteredQuestions.length > 0"
          >
            <div class="row justify-between">
              <q-btn
                flat
                color="primary"
                @click="$emit('back')"
                label="Back"
                icon="arrow_back"
                class="q-px-md"
              />

              <q-btn
                color="primary"
                label="Continue"
                :disable="!hasAnsweredRequiredQuestions"
                @click="continueToChapters"
                class="premium-btn q-px-xl"
                icon-right="arrow_forward"
              />
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- Transcription Dialog -->
    <q-dialog v-model="transcriptionDialog" persistent>
      <BookTranscriptionApp @transcription-complete="handleTranscription" />
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useQuasar } from 'quasar';
import { useQuestionStore } from 'src/entities/question';
import { composeText } from 'src/shared/api/openai';
import { confirmOverrideText } from 'src/shared/lib/quasar-dialogs';

import { Subcategory, useCategoryStore } from 'src/entities/book-category';
import { BookTranscriptionApp } from 'src/entities/book/ui/components';

const props = defineProps<{
  categoryId: string;
  subcategory: Subcategory | null;
  bookTitle: string;
  bookSubtitle: string;
  initialMissionQuestions?: Record<string, any>;
}>();

const emit = defineEmits<{
  (e: 'update:missionQuestions', value: Record<string, any>): void;
  (e: 'continue'): void;
  (e: 'back'): void;
}>();

const $q = useQuasar();
const questionStore = useQuestionStore();
const categoryStore = useCategoryStore();

const selectedCategoryId = ref(props.categoryId || '');

const loading = ref(false);
const missionQuestionsModel = ref<Record<string, any>>(
  props.initialMissionQuestions || {},
);
const loadingStates = ref<Record<string, boolean>>({});
const transcriptionDialog = ref(false);
const selectedField = ref('');

const filteredQuestions = computed(() => {
  if (!props.subcategory) return [];

  return questionStore.questions.filter(
    (q) => q.subcategoryId === props.subcategory?.id,
  );
});

const hasAnsweredRequiredQuestions = computed(() => {
  // For simplicity, we'll consider all questions as required
  return filteredQuestions.value.every((question) => {
    const answer = missionQuestionsModel.value[question.id];

    if (question.answerElement === 'options') {
      // For checkboxes, at least one option should be selected
      return Array.isArray(answer) && answer.length > 0;
    }

    // For other types, the answer should not be empty
    return answer !== undefined && answer !== null && answer !== '';
  });
});

watch(
  missionQuestionsModel,
  (newValue) => {
    emit('update:missionQuestions', { ...newValue });
  },
  { deep: true },
);

onMounted(async () => {
  if (props.categoryId && props.subcategory) {
    await fetchQuestions();
  }
});

async function fetchQuestions() {
  loading.value = true;
  try {
    await questionStore.fetchAllBySubcategory(props.categoryId);

    // Initialize any missing answers with empty values
    filteredQuestions.value.forEach((question) => {
      if (missionQuestionsModel.value[question.id] === undefined) {
        if (question.answerElement === 'options') {
          missionQuestionsModel.value[question.id] = [];
        } else {
          missionQuestionsModel.value[question.id] = '';
        }
      }
    });

    // Add a small delay to ensure the questions are loaded
    setTimeout(() => {
      loading.value = false;
    }, 300);
  } catch (error) {
    console.error('Error fetching questions:', error);
    $q.notify({
      color: 'negative',
      message: 'Failed to load questions',
      icon: 'error',
    });
    loading.value = false;
  }
}

function openTranscription(fieldId: string) {
  selectedField.value = fieldId;
  transcriptionDialog.value = true;
}

function handleTranscription(text: string) {
  if (selectedField.value) {
    const currentValue = missionQuestionsModel.value[selectedField.value] || '';

    if (currentValue) {
      // If there's already text, append the new text
      missionQuestionsModel.value[
        selectedField.value
      ] = `${currentValue} ${text}`;
    } else {
      // Otherwise, just set the new text
      missionQuestionsModel.value[selectedField.value] = text;
    }

    transcriptionDialog.value = false;
  }
}

async function generateAnswer(question: any) {
  loadingStates.value[question.id] = true;

  try {
    const currentValue = missionQuestionsModel.value[question.id];

    // If there's already a value, confirm before overriding
    if (
      currentValue &&
      ((typeof currentValue === 'string' && currentValue.trim() !== '') ||
        (Array.isArray(currentValue) && currentValue.length > 0))
    ) {
      const shouldOverride = await confirmOverrideText();
      if (!shouldOverride) {
        loadingStates.value[question.id] = false;
        return;
      }
    }

    // Create context for the AI
    const context = `
      Book Title: ${props.bookTitle}
      Book Subtitle: ${props.bookSubtitle || 'N/A'}
      Category: ${categoryStore.activeCategory?.title || 'Unknown'}
      Subcategory: ${props.subcategory?.name || 'Unknown'}
    `;

    // Generate the prompt based on the question type
    let prompt = `${context}\n\nQuestion: ${question.question}\n`;

    if (question.description) {
      prompt += `Description: ${question.description}\n`;
    }

    if (
      question.answerElement === 'radio' ||
      question.answerElement === 'options'
    ) {
      prompt += `Options: ${question.options?.join(', ') || ''}\n`;
    }

    prompt += `Please provide a thoughtful and specific answer to this question for my book.`;

    // Add specific instructions based on the answer element type
    if (question.answerElement === 'radio') {
      prompt += ` Choose the most appropriate option from the list and explain why it's the best choice.`;
    } else if (question.answerElement === 'options') {
      prompt += ` Select the most relevant options from the list and explain why they're appropriate.`;
    } else if (question.answerElement === 'text') {
      prompt += ` Keep your answer concise (1-2 sentences).`;
    } else if (question.answerElement === 'textarea') {
      prompt += ` Provide a detailed answer (3-5 sentences).`;
    }

    const response = await composeText(prompt);

    // Process the response based on the question type
    if (question.answerElement === 'radio') {
      // Try to extract one of the options from the response
      const selectedOption = question.options?.find((option: string) =>
        response.toLowerCase().includes(option.toLowerCase()),
      );

      if (selectedOption) {
        missionQuestionsModel.value[question.id] = selectedOption;
      } else {
        // If no option was found in the response, use the first option
        missionQuestionsModel.value[question.id] = question.options?.[0] || '';
      }
    } else if (question.answerElement === 'options') {
      // Try to extract multiple options from the response
      const selectedOptions = question.options?.filter((option: string) =>
        response.toLowerCase().includes(option.toLowerCase()),
      );

      if (selectedOptions && selectedOptions.length > 0) {
        missionQuestionsModel.value[question.id] = selectedOptions;
      } else {
        // If no options were found, select the first option
        missionQuestionsModel.value[question.id] = question.options
          ? [question.options[0]]
          : [];
      }
    } else {
      // For text and textarea, use the response directly
      missionQuestionsModel.value[question.id] = response;
    }

    // Notify the user
    $q.notify({
      color: 'positive',
      message: 'AI suggestion applied',
      icon: 'smart_toy',
      timeout: 2000,
    });
  } catch (error) {
    console.error('Error generating answer:', error);
    $q.notify({
      color: 'negative',
      message: 'Failed to generate answer',
      icon: 'error',
    });
  } finally {
    loadingStates.value[question.id] = false;
  }
}

function continueToChapters() {
  emit('continue');
}
</script>

<style scoped lang="scss">
.questions-page {
  max-width: 1200px;
  margin: 0 auto;
}

.empty-state {
  padding: 60px 0;
  opacity: 0.8;
}

.question-item {
  padding: 16px;
  border-radius: 12px;
  background-color: rgba(0, 0, 0, 0.01);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.02);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  }
}

.option-group-container,
.select-container {
  padding: 8px 12px;
  border-radius: 10px;
  background-color: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.select-container {
  padding: 0;
  overflow: hidden;

  :deep(.q-field__append) {
    padding-right: 4px;
  }

  :deep(.q-field__control) {
    border: none;
    box-shadow: none;
  }

  :deep(.q-menu) {
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }

  :deep(.q-item) {
    min-height: 40px;
    padding: 8px 16px;

    &.q-item--active {
      background-color: rgba($primary, 0.1);
    }

    &:hover {
      background-color: rgba($primary, 0.05);
    }
  }
}

.custom-option-group {
  :deep(.q-radio__label) {
    font-size: 0.95rem;
    color: rgba(0, 0, 0, 0.8);
  }

  :deep(.q-checkbox__label) {
    font-size: 0.95rem;
    color: rgba(0, 0, 0, 0.8);
  }

  :deep(.q-radio__inner) {
    transform: scale(0.9);
  }

  :deep(.q-checkbox__inner) {
    transform: scale(0.9);
  }
}

.ai-action-btn {
  color: $primary;
  background-color: rgba($primary, 0.12);
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

  &:hover {
    background-color: rgba($primary, 0.2);
    transform: scale(1.05);
  }

  .q-spinner {
    color: $primary;
  }
}

.input-action-btn {
  color: $primary;
  background-color: rgba($primary, 0.12);
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

  &:hover {
    background-color: rgba($primary, 0.2);
    transform: scale(1.05);
  }
}

.transcription-dialog {
  border-radius: 16px;
  min-width: 400px;
  max-width: 90vw;

  :deep(.q-card__section) {
    padding: 20px;
  }
}

.premium-btn {
  background: linear-gradient(135deg, $primary, darken($primary, 15%));
  color: white;
  border-radius: 10px;
  padding: 0 20px;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba($primary, 0.25);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  height: 42px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba($primary, 0.35);
  }
}
</style>
