import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { BookCategory, Subcategory } from 'src/entities/book-category';
import type { ChapterOutline } from 'src/entities/book/model/types';

export const useBookCreationStore = defineStore('bookCreation', () => {
  const selectedCategory = ref<BookCategory | null>(null);
  const selectedSubcategory = ref<Subcategory | null>(null);
  const title = ref('');
  const subtitle = ref('');
  const authorName = ref('');
  const missionQuestions = ref<Record<string, any>>({});
  const chapters = ref<ChapterOutline[]>([]);

  function setCategory(category: BookCategory) {
    selectedCategory.value = category;
  }

  function setSubcategory(subcategory: Subcategory) {
    selectedSubcategory.value = subcategory;
  }

  function setDetails(details: {
    title: string;
    subtitle: string;
    authorName: string;
  }) {
    title.value = details.title;
    subtitle.value = details.subtitle;
    authorName.value = details.authorName;
  }

  function setMissionQuestions(questions: Record<string, any>) {
    missionQuestions.value = questions;
  }

  function setChapters(chaptersList: ChapterOutline[]) {
    chapters.value = chaptersList;
  }

  function reset() {
    selectedCategory.value = null;
    selectedSubcategory.value = null;
    title.value = '';
    subtitle.value = '';
    authorName.value = '';
    missionQuestions.value = {};
    chapters.value = [];
  }

  return {
    selectedCategory,
    selectedSubcategory,
    title,
    subtitle,
    authorName,
    missionQuestions,
    chapters,
    setCategory,
    setSubcategory,
    setDetails,
    setMissionQuestions,
    setChapters,
    reset,
  };
});
