<template>
  <div class="rich-text-editor">
    <q-editor v-model="content" content-type="html" :toolbar="toolbarOptions" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const props = defineProps<{
  modelValue: string;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
}>();

const content = ref(props.modelValue);
const toolbarOptions = [
  ['bold', 'italic', 'underline'],
  [{ list: 'ordered' }, { list: 'bullet' }],
  ['link'],
  ['clean'],
];

watch(content, (newVal) => {
  emit('update:modelValue', newVal);
});
</script>
