<template>
  <div class="category-card">
    <q-card class="q-pa-md full-width">
      <q-card-section>
        <div class="text-h5 q-mb-md">Create Your Book</div>
        <q-stepper
          v-model="step"
          ref="stepper"
          color="primary"
          animated
          flat
          header-nav
        >
          <q-step
            :name="1"
            title="Book Category"
            icon="category"
            :done="step > 1"
          >
            <CategorySelection
              :categories="categories"
              @select-category="selectCategory"
            />
          </q-step>

          <q-step
            :name="2"
            title="Book Details"
            icon="description"
            :done="step > 2"
            :disable="!selectedCategory"
          >
            <BookDetails
              v-if="selectedCategory"
              :category="selectedCategory"
              v-model:title="title"
              v-model:subtitle="subtitle"
              v-model:author-name="authorName"
            />
          </q-step>

          <q-step
            :name="3"
            title="Mission Questions"
            icon="help"
            :done="step > 3"
            :disable="!selectedSubcategory"
          >
            <MissionQuestions
              v-if="selectedSubcategory"
              :subcategory="selectedSubcategory"
              :questions="filteredQuestions"
              v-model:mission-questions="missionQuestions"
              @select-subcategory="selectSubcategory"
            />
          </q-step>

          <q-step
            :name="4"
            title="Chapters"
            icon="menu_book"
            :done="step > 4"
            :disable="!isDetailsComplete"
          >
            <ChaptersStep
              v-model="chapters"
              :title="title"
              :subtitle="subtitle"
              :mission-questions="missionQuestions"
            />
          </q-step>

          <q-step :name="5" title="Summary" icon="summarize">
            <BookSummary :book="book" @save="saveBook" />
          </q-step>

          <template v-slot:navigation>
            <q-stepper-navigation>
              <div class="row justify-between q-mt-md">
                <q-btn
                  v-if="step > 1"
                  flat
                  color="primary"
                  @click="$refs.stepper.previous()"
                  label="Back"
                />
                <q-btn
                  color="primary"
                  @click="nextStep"
                  :label="step === 5 ? 'Create Book' : 'Continue'"
                />
              </div>
            </q-stepper-navigation>
          </template>
        </q-stepper>
      </q-card-section>
    </q-card>

    <q-dialog v-model="transcriptionDialog">
      <BookTranscriptionApp @completed="completedTranscript" @close="" />
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { createEmptyBook, setBook } from 'src/entities/book';
import { useRouter } from 'vue-router';
import {
  BookCategory,
  Subcategory,
  useCategoryStore,
  CategorySelection,
  BookSummary,
  ChaptersStep,
  BookDetails,
} from 'src/entities/book-category';
import { useQuestionStore } from 'src/entities/question';
import type { NewBook, ChapterOutline } from 'src/entities/book/model/types';
import { user } from 'src/entities/user';
import { BookTranscriptionApp } from 'src/entities/book/ui/components';

const categoryStore = useCategoryStore();
const questionStore = useQuestionStore();
const router = useRouter();

const step = ref(1);
const selectedCategory = ref<BookCategory | null>(null);
const selectedSubcategory = ref<Subcategory | null>(null);
const title = ref('');
const subtitle = ref('');
const authorName = ref(user.value?.displayName || '');
const authorId = computed(() => user.value?.uid || '');
const missionQuestions = ref<Record<string, any>>({});
const chapters = ref<ChapterOutline[]>([]);
const transcriptionDialog = ref(false);
const selectedField = ref('');

const categories = computed(() => categoryStore.categories);
const questions = computed(() => questionStore.questions);

const filteredQuestions = computed(() => {
  if (!selectedSubcategory.value) return [];
  return questions.value.filter(
    (q) => q.subcategoryId === selectedSubcategory.value?.id,
  );
});

const isDetailsComplete = computed(() => {
  return (
    title.value &&
    subtitle.value &&
    authorName.value &&
    Object.keys(missionQuestions.value).length > 0
  );
});

const bookCategory = computed(() => {
  if (selectedCategory.value?.title.toLowerCase().includes('autobiography')) {
    return 'autobiography';
  }
  return 'non-fiction';
});

const book = computed(() => {
  const newBook: NewBook = {
    ...createEmptyBook(bookCategory.value),
    title: title.value,
    subtitle: subtitle.value,
    authorId: authorId.value,
    authorName: authorName.value,
    category: bookCategory.value,
    chapters: chapters.value,
    missionQuestions: missionQuestions.value,
  };
  return newBook;
});

onMounted(async () => {
  await categoryStore.fetchCategories();
  await questionStore.fetchQuestions();
});

function selectCategory(category: BookCategory) {
  selectedCategory.value = category;
  selectedSubcategory.value = null;
  if (step.value === 1) {
    step.value = 2;
  }
}

function selectSubcategory(subcategory: Subcategory) {
  selectedSubcategory.value = subcategory;
}

function nextStep() {
  if (step.value === 5) {
    saveBook();
  } else {
    step.value++;
  }
}

function completedTranscript(data: any) {
  const content = data.content;
  if (selectedField.value && missionQuestions.value) {
    missionQuestions.value[selectedField.value] = content;
  }
  transcriptionDialog.value = false;
}

async function saveBook() {
  try {
    const bookId = await setBook(null, book.value);
    router.push(`/books/${bookId}`);
  } catch (error) {
    console.error('Error saving book:', error);
  }
}
</script>

<style scoped>
.category-card {
  max-width: 900px;
  margin: 0 auto;
}
</style>
