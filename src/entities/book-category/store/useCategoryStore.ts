import { defineStore } from 'pinia';
import { ref, Ref } from 'vue';
import { BookCategory } from 'src/entities/book-category';
import { categoryApi } from '../api/firestore';
import { questionApi } from 'src/entities/question';

export const useCategoryStore = defineStore('category', () => {
  // State
  const categories: Ref<BookCategory[]> = ref([]);
  const activeCategory: Ref<BookCategory | null> = ref(null);
  const loading: Ref<boolean> = ref(false);
  const error: Ref<Error | null> = ref(null);

  // Actions
  /**
   * Fetch all categories from Firestore
   */
  async function fetchCategories() {
    loading.value = true;
    try {
      categories.value = await categoryApi.getAll();
    } catch (err) {
      error.value = err as Error;
      console.error('Error fetching categories:', err);
    } finally {
      loading.value = false;
    }
  }

  /**
   * Set the active category and load its questions
   * @param categoryId - The ID of the category to set as active
   */
  async function setActiveCategory(categoryId: string) {
    loading.value = true;
    try {
      const category = categories.value.find((c) => c.id === categoryId);

      if (category) {
        // Create a new object to ensure reactivity
        activeCategory.value = { ...category };

        // Load questions for this category
        const questions = await questionApi.getAllByCategory(categoryId);
        if (activeCategory.value) {
          activeCategory.value.questions = questions;
        }
      } else {
        activeCategory.value = null;
      }
    } catch (err) {
      error.value = err as Error;
      console.error('Error setting active category:', err);
    } finally {
      loading.value = false;
    }
  }

  /**
   * Create a new category
   * @param category - The category data to create
   * @returns The ID of the newly created category
   */
  async function createCategory(category: Omit<BookCategory, 'id'>) {
    loading.value = true;
    try {
      const id = await categoryApi.create(category);

      // Add the new category to the local state
      const newCategory = {
        id,
        ...category,
        createdAt: new Date(),
        updatedAt: new Date(),
      } as BookCategory;

      categories.value.push(newCategory);
      return id;
    } catch (err) {
      error.value = err as Error;
      console.error('Error creating category:', err);
      return null;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Update an existing category
   * @param id - The ID of the category to update
   * @param data - The data to update
   * @returns True if successful, false otherwise
   */
  async function updateCategory(id: string, data: Partial<BookCategory>) {
    loading.value = true;
    try {
      await categoryApi.update(id, data);

      // Update the category in the local state
      const index = categories.value.findIndex((c) => c.id === id);
      if (index !== -1) {
        categories.value[index] = {
          ...categories.value[index],
          ...data,
          updatedAt: new Date(),
        };
      }

      // Update active category if it's the one being updated
      if (activeCategory.value?.id === id) {
        activeCategory.value = {
          ...activeCategory.value,
          ...data,
          updatedAt: new Date(),
        };
      }

      return true;
    } catch (err) {
      error.value = err as Error;
      console.error('Error updating category:', err);
      return false;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Delete a category
   * @param id - The ID of the category to delete
   * @returns True if successful, false otherwise
   */
  async function deleteCategory(id: string) {
    loading.value = true;
    try {
      await categoryApi.delete(id);

      // Remove the category from the local state
      categories.value = categories.value.filter((c) => c.id !== id);

      // Clear active category if it's the one being deleted
      if (activeCategory.value?.id === id) {
        activeCategory.value = null;
      }

      return true;
    } catch (err) {
      error.value = err as Error;
      console.error('Error deleting category:', err);
      return false;
    } finally {
      loading.value = false;
    }
  }

  // Return state and actions
  return {
    // State
    categories,
    activeCategory,
    loading,
    error,

    // Actions
    fetchCategories,
    setActiveCategory,
    createCategory,
    updateCategory,
    deleteCategory,
  };
});
