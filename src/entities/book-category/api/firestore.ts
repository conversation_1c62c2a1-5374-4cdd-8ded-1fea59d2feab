import {
  collection,
  doc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  serverTimestamp,
  Timestamp,
  collectionGroup,
  getDoc,
} from 'firebase/firestore';

import { firestore } from 'src/firebase';
import { BookCategory, Subcategory } from 'src/entities/book-category';

const categoriesCollection = collection(firestore, 'bookCategories');

export const categoryApi = {
  async getAll(): Promise<BookCategory[]> {
    const snapshot = await getDocs(categoriesCollection);
    return snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate() || new Date(),
    })) as BookCategory[];
  },

  async create(category: Omit<BookCategory, 'id'>): Promise<string> {
    const docRef = await addDoc(categoriesCollection, {
      ...category,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });
    return docRef.id;
  },

  async update(id: string, updates: Partial<BookCategory>): Promise<void> {
    await updateDoc(doc(firestore, 'bookCategories', id), {
      ...updates,
      updatedAt: serverTimestamp(),
    });
  },

  async delete(id: string): Promise<void> {
    // First, delete all subcategories
    const subcategories = await this.getSubcategories(id);
    for (const subcategory of subcategories) {
      await this.deleteSubcategory(id, subcategory.id);
    }

    // Then delete the category
    await deleteDoc(doc(firestore, 'bookCategories', id));
  },

  async getById(id: string): Promise<BookCategory | null> {
    const docRef = doc(firestore, 'bookCategories', id);
    const docSnap = await getDoc(docRef);

    if (!docSnap.exists()) {
      return null;
    }

    const data = docSnap.data();
    return {
      id: docSnap.id,
      ...data,
      createdAt: data.createdAt?.toDate() || new Date(),
      updatedAt: data.updatedAt?.toDate() || new Date(),
    } as BookCategory;
  },

  // Subcategory methods
  async createSubcategory(
    categoryId: string,
    name: string,
    icon?: string,
    isDefault?: boolean,
  ): Promise<string> {
    const subcategoriesCollection = collection(
      firestore,
      'bookCategories',
      categoryId,
      'subcategories',
    );

    const docRef = await addDoc(subcategoriesCollection, {
      name,
      icon: icon || 'folder',
      isDefault: isDefault || false,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });

    return docRef.id;
  },

  async getSubcategories(categoryId: string): Promise<Subcategory[]> {
    const subcategoriesCollection = collection(
      firestore,
      'bookCategories',
      categoryId,
      'subcategories',
    );

    const snapshot = await getDocs(subcategoriesCollection);
    return snapshot.docs.map((doc) => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate(),
        updatedAt: data.updatedAt?.toDate(),
      } as Subcategory;
    });
  },

  async getSubcategoryById(
    categoryId: string,
    subcategoryId: string,
  ): Promise<Subcategory | null> {
    const subcategoryRef = doc(
      firestore,
      'bookCategories',
      categoryId,
      'subcategories',
      subcategoryId,
    );

    const docSnap = await getDoc(subcategoryRef);

    if (!docSnap.exists()) {
      return null;
    }

    const data = docSnap.data();
    return {
      id: docSnap.id,
      ...data,
      createdAt: data.createdAt?.toDate(),
      updatedAt: data.updatedAt?.toDate(),
    } as Subcategory;
  },

  async updateSubcategory(
    categoryId: string,
    subcategoryId: string,
    updates: Partial<Omit<Subcategory, 'id' | 'createdAt' | 'updatedAt'>>,
  ): Promise<void> {
    await updateDoc(
      doc(
        firestore,
        'bookCategories',
        categoryId,
        'subcategories',
        subcategoryId,
      ),
      {
        ...updates,
        updatedAt: serverTimestamp(),
      },
    );
  },

  async deleteSubcategory(
    categoryId: string,
    subcategoryId: string,
  ): Promise<void> {
    // First, get all questions in this subcategory
    const questionsCollection = collection(
      firestore,
      'bookCategories',
      categoryId,
      'subcategories',
      subcategoryId,
      'questions',
    );

    const questionsSnapshot = await getDocs(questionsCollection);

    // Delete all questions in the subcategory
    for (const questionDoc of questionsSnapshot.docs) {
      await deleteDoc(
        doc(
          firestore,
          'bookCategories',
          categoryId,
          'subcategories',
          subcategoryId,
          'questions',
          questionDoc.id,
        ),
      );
    }

    // Then delete the subcategory
    await deleteDoc(
      doc(
        firestore,
        'bookCategories',
        categoryId,
        'subcategories',
        subcategoryId,
      ),
    );
  },
};
