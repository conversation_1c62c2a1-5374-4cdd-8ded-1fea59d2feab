export function required(value: any): boolean | string {
  if (typeof value === 'string') {
    return !!value.trim() || 'This field is required';
  }
  return !!value || 'This field is required';
}

export function minLength(min: number) {
  return (value: string): boolean | string => {
    return value.length >= min || `Minimum ${min} characters required`;
  };
}

export function maxLength(max: number) {
  return (value: string): boolean | string => {
    return value.length <= max || `Maximum ${max} characters allowed`;
  };
}

export function isValidEmail(value: string): boolean | string {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(value) || 'Please enter a valid email address';
}
