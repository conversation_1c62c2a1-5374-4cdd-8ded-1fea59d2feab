import { composeText } from 'src/shared/api/openai';
import { confirmOverrideText } from 'src/shared/lib/quasar-dialogs';

import type { Question } from 'src/entities/question';

/**
 * Generate an AI response for a question
 */
export async function generateAIResponse(
  question: Question,
  currentValue: string,
  context: string,
  $q: any,
): Promise<string> {
  // Check if we should override existing value
  if (currentValue) {
    const shouldOverride = await confirmOverrideText($q);
    if (!shouldOverride) {
      return currentValue;
    }
  }

  // Create the prompt with context
  let prompt = question.aiPrompt || '';
  if (context) {
    prompt = `${prompt}\n\nHere is some context from other questions:\n${context}`;
  }

  // Add instruction to avoid repetition
  prompt +=
    '\n\nPlease refrain from utilizing the following results or repeating the same sentences from previous responses.';

  // Add AI options if available
  const aiOptions = question.aiOptions || {
    temperature: 0.7,
    maxTokens: 500,
    topP: 1,
    frequencyPenalty: 0,
    presencePenalty: 0,
  };

  try {
    const response = await composeText(prompt);

    // For select or options, process the response to get clean options
    if (
      question.answerElement === 'select' ||
      question.answerElement === 'options'
    ) {
      return response
        .trim()
        .split('\n')
        .map((line) => line.replace(/^\d+\.\s*/, ''))
        .map((line) => line.replace(/^"|"$/g, ''))
        .join('\n');
    }

    return response;
  } catch (error) {
    console.error('Error generating AI response:', error);
    throw error;
  }
}

/**
 * Generate a title using AI
 */
export async function generateBookTitle(
  category: string,
  subcategory: string,
  $q: any,
  currentTitle: string = '',
): Promise<string> {
  // Check if we should override existing value
  if (currentTitle) {
    const shouldOverride = await confirmOverrideText($q);
    if (!shouldOverride) {
      return currentTitle;
    }
  }

  const prompt = `You are an expert book title creator. Create a compelling title for a ${category} book in the ${subcategory} subcategory. The title should be concise, memorable, and appealing to readers. Return only the title without any explanations or quotation marks.`;

  const response = await composeText(prompt);
  return response;
}

/**
 * Generate a subtitle using AI
 */
export async function generateBookSubtitle(
  category: string,
  subcategory: string,
  title: string,
  $q: any,
  currentSubtitle: string = '',
): Promise<string> {
  // Check if we should override existing value
  if (currentSubtitle) {
    const shouldOverride = await confirmOverrideText($q);
    if (!shouldOverride) {
      return currentSubtitle;
    }
  }

  const prompt = `You are an expert book subtitle creator. Create a compelling subtitle for a ${category} book titled "${title}" in the ${subcategory} subcategory. The subtitle should complement the title, provide clarity about the book's content, and be appealing to readers. Return only the subtitle without any explanations or quotation marks.`;

  const response = await composeText(prompt);
  return response;
}

/**
 * Generate chapter outlines using AI
 */
export async function generateChapterOutlines(
  title: string,
  subtitle: string,
  missionQuestions: Record<string, any>,
  $q: any,
): Promise<{
  introduction: string;
  chapters: Array<{ title: string; outline: string }>;
  conclusion: string;
}> {
  // Create a context from mission questions
  const context = Object.entries(missionQuestions)
    .map(([key, value]) => `${key}: ${value}`)
    .join('\n');

  const prompt = `
    You are an expert book outline creator. Create a detailed chapter outline for a book with the following details:
    
    Title: ${title}
    Subtitle: ${subtitle}
    
    Context from mission questions:
    ${context}
    
    Please provide:
    1. An introduction outline (100-150 words)
    2. 8-12 chapter titles with brief outlines (50-100 words each)
    3. A conclusion outline (100-150 words)
    
    Format your response as JSON with the following structure:
    {
      "introduction": "Introduction outline text here...",
      "chapters": [
        {"title": "Chapter 1 Title", "outline": "Chapter 1 outline text..."},
        {"title": "Chapter 2 Title", "outline": "Chapter 2 outline text..."}
      ],
      "conclusion": "Conclusion outline text here..."
    }
  `;

  const response = await composeText(prompt);

  try {
    return JSON.parse(response);
  } catch (error) {
    console.error('Error parsing AI response:', error);
    throw new Error('Failed to parse the AI response');
  }
}
